from diagrams import Diagram
from diagrams.aws.network import APIGateway
from diagrams.aws.compute import Lambda
from diagrams.aws.database import RDS
from diagrams.aws.general import Users
from diagrams.aws.management import Cloudwatch

with Diagram("Authorizer Gateway Stack", show=False, direction="LR"):
    users = Users("Clients")
    api = APIGateway("API Gateway")
    authorizer = Lambda("Authorizer Lambda")
    database = RDS("PostgreSQL")
    logs = Cloudwatch("CloudWatch Logs")

    users >> api >> authorizer
    authorizer >> database
    authorizer >> logs
    api >> <PERSON>da("Business Logic")
