from diagrams import Diagram, Cluster, Edge
from diagrams.aws.integration import EventbridgeCustomEventBusResource
from diagrams.aws.compute import LambdaFunction
from diagrams.aws.management import CloudwatchLogs
from diagrams.aws.security import IAMPermissions
from diagrams.aws.database import Dynamodb

with Diagram('Webhook Integration Stack', show=False, direction='TB'):
    # Event Bus Section
    with Cluster("Event Sources"):
        webhook_source = EventbridgeCustomEventBusResource("Webhook Events\n(source: webhook)\n(detailType: Webhook Request)")

    # Processing Section
    with Cluster("Processing"):
        event_bus = EventbridgeCustomEventBusResource("WebhookEventBus")
        rule = EventbridgeCustomEventBusResource("Routing Rule\n(source: webhook)\n(detailType: Webhook Request)")

        with Cluster("Lambda Function"):
            lambda_func = LambdaFunction("WebhookIntegrationLambda\n(Timeout: 10s)\n(Modules: dynamoose, nanoid)")
            logs_policy = IAMPermissions("CloudWatchLogs\n(Create/Write Logs)")
            dynamo_policy = IAMPermissions("DynamoDB\n(Full CRUD Access)")
            lambda_func << logs_policy
            lambda_func << dynamo_policy

        # Database Section
        dynamodb = Dynamodb("Webhook Data\n(Stores webhook payloads)")

    # Monitoring Section
    with Cluster("Monitoring"):
        log_group = CloudwatchLogs("WebhookEventLogGroup\n(Logs all events)")

    # Connections with labels
    webhook_source >> Edge(label="Incoming Webhooks") >> event_bus
    event_bus >> Edge(label="Routes matching events") >> rule
    rule >> Edge(label="Triggers") >> lambda_func
    rule >> Edge(label="Logs all events") >> log_group
    lambda_func >> Edge(label="Stores/Retrieves data") >> dynamodb
