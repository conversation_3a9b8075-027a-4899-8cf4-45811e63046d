from diagrams import Diagram, Cluster
from diagrams.aws.network import APIGateway
from diagrams.aws.compute import Lambda
from diagrams.aws.database import RDS
from diagrams.aws.general import Users
from diagrams.aws.management import Cloudwatch
from diagrams.aws.security import IAM

with Diagram("Authorizer Gateway Stack", show=False, direction="LR", filename="authorizer_gateway_diagram"):
    users = Users("Clients")

    with Cluster("VPC"):
        api = APIGateway("API Gateway")
        authorizer = Lambda("Authorizer Lambda")
        database = RDS("PostgreSQL")
        business_logic = Lambda("Business Logic")

    logs = Cloudwatch("CloudWatch Logs")
    iam = IAM("IAM Roles")

    users >> api >> authorizer
    authorizer >> database
    authorizer >> logs
    api >> business_logic
    authorizer << iam
    business_logic << iam
