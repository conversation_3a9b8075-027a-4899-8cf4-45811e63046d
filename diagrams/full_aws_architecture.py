from diagrams import Diagram, Cluster
from diagrams.aws.compute import Lambda
from diagrams.aws.database import RDS, Dynamodb
from diagrams.aws.network import APIGateway, Route53
from diagrams.aws.integration import SQS
from diagrams.aws.security import IAM
from diagrams.aws.management import Cloudwatch

with Diagram("Full AWS Architecture", show=False, direction="TB", filename="full_aws_architecture"):
    with <PERSON>luster("API Layer"):
        api = APIGateway("API Gateway")
        authorizer = Lambda("Authorizer")
        webhook = Lambda("Webhook Handler")

    with <PERSON>luster("Worker Layer"):
        worker = Lambda("Worker")
        dns = Route53("DNS Checks")

    with Cluster("Data Layer"):
        postgres = RDS("PostgreSQL")
        dynamo = Dynamodb("DynamoDB")
        queue = SQS("Event Queue")

    with Cluster("Monitoring"):
        logs = Cloudwatch("Logs")

    iam = IAM("IAM Roles")

    # Connections
    api >> authorizer >> postgres
    api >> webhook >> queue
    webhook >> dynamo
    worker >> postgres
    worker >> dynamo
    worker >> dns
    authorizer << iam
    webhook << iam
    worker << iam
    authorizer >> logs
    webhook >> logs
    worker >> logs
