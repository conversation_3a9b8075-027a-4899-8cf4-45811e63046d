from diagrams import Diagram, Cluster
from diagrams.aws.compute import Lambda
from diagrams.aws.integration import StepFunctions, SQS
from diagrams.aws.database import Dynamodb
from diagrams.aws.management import Cloudwatch
from diagrams.aws.security import IAM

with Diagram("Incident Escalation Stack", show=False, direction="TB", filename="incident_escalation_diagram"):
    with Cluster("Event Processing"):
        queue = SQS("Incident Queue")
        state_machine = StepFunctions("Escalation Workflow")
        enrichment = Lambda("Enrichment Lambda")
        notification = Lambda("Notification Lambda")

    with Cluster("Data Stores"):
        incidents_db = Dynamodb("Incidents Table")
        config_db = Dynamodb("Escalation Config")

    with Cluster("Monitoring"):
        logs = Cloudwatch("Logs")
        metrics = Cloudwatch("Metrics")

    iam = IAM("IAM Roles")

    # Connections
    queue >> state_machine
    state_machine >> enrichment >> incidents_db
    state_machine >> notification
    enrichment >> config_db
    notification << iam
    enrichment << iam
    state_machine << iam
    enrichment >> logs
    notification >> logs
    state_machine >> metrics
