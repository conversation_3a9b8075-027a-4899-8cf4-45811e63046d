from diagrams import Diagram, Cluster
from diagrams.aws.integration import EventbridgeCustomEventBusResource
from diagrams.aws.compute import LambdaFunction
from diagrams.aws.management import CloudwatchLogs
from diagrams.aws.security import IAMPermissions

with Diagram('Webhook Integration Stack', show=False, direction='LR'):
    with Cluster('AWS Components'):
        event_bus = EventbridgeCustomEventBusResource('WebhookEventBus')
        lambda_func = LambdaFunction('WebhookIntegrationLambda')
        log_group = CloudwatchLogs('WebhookEventLogGroup')

        # IAM Policies
        with Cluster('IAM Policies'):
            logs_policy = IAMPermissions('CloudWatchLogsPolicy')
            dynamo_policy = IAMPermissions('DynamoDBPolicy')

        # Connections
        event_bus >> lambda_func
        event_bus >> log_group
        lambda_func << logs_policy
        lambda_func << dynamo_policy
