# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist
tmp
/out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

.nx/cache
.nx/workspace-data

# dotenv environment variable files
.env*
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Credentials
credentials/

# Firebase emulator
firebase-data/

.firebaserc
firebase.json
.yarn/install-state.gz
src/metadata.ts
firebase-debug.log
ui-debug.log
ui-debug.log
/.cdk-graph


# AI generated filed
.aider*
.roo/
.roo*
.repomix/
.bmad/
.bmad-core/
.mcp.json

# Localstack
localstack-data/


