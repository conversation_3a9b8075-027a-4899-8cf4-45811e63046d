{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": ["{workspaceRoot}/bitbucket-pipelines.yml"]}, "nxCloudAccessToken": "MzFjZDZmNzktNjMzZS00YmFkLTg1NDktM2I0OWRjZGZlNmQxfHJlYWQtd3JpdGU=", "useDaemonProcess": false, "plugins": [{"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": []}, {"plugin": "nx-serverless-cdk/plugin", "options": {"cdkTargetName": "cdk", "deployTargetName": "deploy", "deployAllTargetName": "deploy-all", "destroyTargetName": "destroy", "diffTargetName": "diff", "lsTargetName": "ls", "synthTargetName": "synth", "watchTargetName": "watch", "generateEventTargetName": "generate-event", "invokeTargetName": "invoke", "startApiTargetName": "start-api", "startLambdaTargetName": "start-lambda"}}], "defaultBase": "origin/main"}