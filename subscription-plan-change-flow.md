# Subscription Plan Change Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend API
    participant Stripe API
    participant Webhook Handler
    participant Subscription Service
    participant Database

    %% Section 1: Plan Preview and Confirmation
    rect rgb(235, 245, 255)
        note over User, Database: Plan Selection
        User->>Frontend: Select New Plan
        Frontend->>Backend API: POST /subscriptions/plan-change-preview
        Backend API->>Subscription Service: getChangePlanPreview(orgId, newPlanId)
        Subscription Service->>Database: Get current subscription
        Subscription Service->>Stripe API: Retrieve upcoming invoice for preview
        Stripe API-->>Subscription Service: Upcoming invoice data (proration, etc.)
        Subscription Service-->>Backend API: Return preview data
        Backend API-->>Frontend: Show plan change preview (cost, new period)

        User->>Frontend: Confirm Plan Change
        Frontend->>Backend API: POST /subscriptions/plan-change
        Backend API->>Subscription Service: changeSubscriptionPlan(orgId, newPlanId)
        Subscription Service->>Database: Get current subscription & metered usage
        Subscription Service->>Stripe API: Update usage for all metered product items
        Subscription Service->>Stripe API: Update Stripe Subscription (proration_behavior: 'create_prorations')
        Stripe API-->>Subscription Service: Acknowledge update
        Subscription Service->>Database: Create new Subscription record (status: PENDING, same stripeSubscriptionId)
        Subscription Service->>Database: Update old Subscription status to SUPERSEDED
        Backend API-->>Frontend: Plan change initiated successfully
    end

    %% Section 2: Webhook Handling
    rect rgb(235, 255, 245)
        note over Stripe API, Database: Webhook Handling
        
        par Webhook: Successful Payment (invoice.paid)
            Stripe API->>Webhook Handler: invoice.paid (webhook)
            Webhook Handler->>Subscription Service: handleInvoicePaid(invoice)
            Subscription Service->>Database: Find subscription by stripeSubscriptionId
            
            rect rgb(240, 240, 240)
                note over Subscription Service: Idempotency Check
                alt If DB status is PENDING
                    Subscription Service->>Database: Update subscription status to ACTIVE
                    Subscription Service->>Stripe API: Remove old metered products (optional)
                    Subscription Service->>Stripe API: Create new metered products based on new plan
                    Subscription Service->>Database: Sync subscription items & apply new resource limits
                else Already Active or other status
                    note over Subscription Service: Log and ignore to prevent duplicate processing
                end
            end
            Webhook Handler-->>Stripe API: 200 OK

        and Webhook: Subscription State Change (customer.subscription.updated)
            Stripe API->>Webhook Handler: customer.subscription.updated (webhook)
            Webhook Handler->>Subscription Service: handleSubscriptionUpdated(subscription)
            Subscription Service->>Database: Find subscription by stripeSubscriptionId

            rect rgb(240, 240, 240)
                note over Subscription Service: Logic branching based on DB status
                alt If DB status is PENDING (Handles non-paid changes)
                    Subscription Service->>Database: Update subscription status to ACTIVE
                    Subscription Service->>Database: Sync items & apply new resource limits (for new plan)
                else If DB status is ACTIVE (Handles renewals, etc.)
                    Subscription Service->>Database: Sync period dates (current_period_start/end) & status
                end
            end
            Webhook Handler-->>Stripe API: 200 OK

        and Webhook: Failed Payment (invoice.payment_failed)
            Stripe API->>Webhook Handler: invoice.payment_failed (webhook)
            Webhook Handler->>Subscription Service: handleInvoicePaymentFailed(invoice)
            Subscription Service->>Database: Revert plan change in DB (delete PENDING subscription, restore SUPERSEDED to ACTIVE)
            note over Subscription Service, Database: Optionally, notify user of failure
            Webhook Handler-->>Stripe API: 200 OK
        end
    end
```

## Key Points

-   **Preview Step**: Users can preview the financial impact (prorated charges/credits) of a plan change before confirming.
-   **Accurate Proration**: Before updating the subscription, the system updates the usage for all metered products to ensure Stripe calculates prorations based on the latest consumption.
-   **State Management**: The backend uses `PENDING` and `SUPERSEDED` statuses to manage the transition between plans atomically.
-   **Webhook-Driven Activation**: The final plan activation and resource limit application are triggered by Stripe webhooks (`invoice.paid` or `customer.subscription.updated`), ensuring the change is only finalized after successful payment or confirmation from Stripe.
-   **Metered Product Transition on Payment**: After a successful payment (`invoice.paid`), the system removes the old metered product items and creates new ones corresponding to the new plan's resource limits. This does not occur for plan changes that don't require immediate payment.
-   **Failure Rollback**: A payment failure triggers a rollback mechanism, reverting the subscription change in the local database to maintain data integrity.