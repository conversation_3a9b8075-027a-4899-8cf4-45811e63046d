import { SmsIncidentPayload } from '@libs/notifier/incident-notifier/incident-notifier.type'

class SmsNotifier {
  async send(payload: SmsIncidentPayload) {
    const cleanNumber = (num: string) => {
      if (!num) return ''
      return num
        .replace(/^\+/, '') // Remove leading '+'
        .replace(/\s+/g, '') // Remove all whitespace
        .replace(/[()-]/g, '') // Remove parentheses and dashes
        .replace(/\./g, '') // Remove dots
        .replace(/[^\d]/g, '') // Remove any remaining non-digit characters
    }

    const cleanDomain = (url: string) => {
      let name = '',
        domain = ''

      try {
        const { hostname } = new URL(url)
        const parts = hostname.split('.')

        domain = parts.slice(-2).join('.')
        name = parts.length > 2 ? parts[parts.length - 3] : parts[0]
        if (name === 'www') name = parts[parts.length - 3] || ''
      } catch {
        name = ''
        domain = ''
      }

      return domain
    }

    const fromNumber = cleanNumber(payload.from)
    const toNumber = cleanNumber(payload.to)
    const cleanedDomain = cleanDomain(payload.serviceName)

    try {
      const headers = {
        'X-STRINGEE-AUTH': payload.token,
        'Content-Type': 'application/json',
      }

      const requestBody = {
        senderId: parseInt(fromNumber), // YOUR BRAND NAME's ID ON STRINGEE, contact sales for this
        to: parseInt(toNumber),
        // event_url: payload.IncidentEventCallbackUrl,
        content: `Monitoring Dog thông báo dịch vụ ${cleanedDomain} của bạn đang có sự cố, vui lòng truy cập vào web portal để kiểm tra.`,
      }

      console.log(
        'Stringee API request details:',
        JSON.stringify(
          {
            url: 'https://api.stringeex.com/v1/sms/send',
            method: 'POST',
            body: requestBody,
          },
          null,
          2,
        ),
      )

      const controller = new AbortController()
      const timeout = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      const response = await fetch('https://api.stringeex.com/v1/sms/send', {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      })

      clearTimeout(timeout)

      if (!response.ok) {
        throw new Error(
          `Stringee API error: ${response.status} ${response.statusText}`,
        )
      }

      const responseData = await response.json()
      console.log('Stringee API response:', responseData)
      return responseData
    } catch (error) {
      console.error('Stringee voice call failed:', error)
      throw error
    }
  }
}

export default SmsNotifier
