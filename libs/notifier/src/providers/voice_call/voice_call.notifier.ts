import { VoiceCallIncidentPayload } from '@libs/notifier/incident-notifier/incident-notifier.type'

class VoiceCallNotifier {
  async send(payload: VoiceCallIncidentPayload) {
    const cleanNumber = (num: string) => {
      if (!num) return ''
      return num
        .replace(/^\+/, '') // Remove leading '+'
        .replace(/\s+/g, '') // Remove all whitespace
        .replace(/[()-]/g, '') // Remove parentheses and dashes
        .replace(/\./g, '') // Remove dots
        .replace(/[^\d]/g, '') // Remove any remaining non-digit characters
    }

    const cleanDomain = (url: string) => {
      let name = '',
        domain = ''

      try {
        const { hostname } = new URL(url)
        const parts = hostname.split('.')

        domain = parts.slice(-2).join('.')
        name = parts.length > 2 ? parts[parts.length - 3] : parts[0]
        if (name === 'www') name = parts[parts.length - 3] || ''
      } catch {
        name = ''
        domain = ''
      }

      return domain
    }

    const fromNumber = cleanNumber(payload.from)
    const toNumber = cleanNumber(payload.to)
    const cleanedDomain = cleanDomain(payload.serviceName)

    try {
      const headers = {
        'X-STRINGEE-AUTH': payload.token,
        'Content-Type': 'application/json',
      }

      const requestBody = {
        from: {
          type: 'internal',
          number: fromNumber,
          alias: fromNumber,
        },
        to: [
          {
            type: 'external',
            number: toNumber,
            alias:
              payload.firstName +
              '-' +
              payload.lastName +
              '-' +
              payload.userId +
              '-' +
              payload.incidentId,
          },
        ],
        event_url: payload.IncidentEventCallbackUrl,
        actions: [
          {
            action: 'talk',
            text: `Xin chào, Monitoring Dog thông báo dịch vụ ${cleanedDomain} của bạn đang có sự cố, vui lòng nhấn 1 để acknowledge, nhấn 2 để escalate cho thành viên tiếp theo.`,
            // text: `Hello, Monitoring Dog detect your service ${cleanedDomain} has incident, press 1 to acknowledge, press 2 to escalate.`,
          },
          {
            action: 'input',
            eventUrl: payload.AckEscalateCallbackUrl,
            submitOnHash: 'false',
            customField: {
              incidentId: payload.incidentId,
              userId: payload.userId,
              userNumber: payload.to,
              userFirstname: payload.firstName,
              userLastname: payload.lastName,
              callBackUrl: payload.AckEscalateCallbackUrl,
            },
            timeout: '40',
          },
        ],
      }

      console.log(
        'Stringee API request details:',
        JSON.stringify(
          {
            url: 'https://api.stringee.com/v1/call2/callout',
            method: 'POST',
            body: requestBody,
          },
          null,
          2,
        ),
      )

      const controller = new AbortController()
      const timeout = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      const response = await fetch(
        'https://api.stringee.com/v1/call2/callout',
        {
          method: 'POST',
          headers,
          body: JSON.stringify(requestBody),
          signal: controller.signal,
        },
      )

      clearTimeout(timeout)

      if (!response.ok) {
        throw new Error(
          `Stringee API error: ${response.status} ${response.statusText}`,
        )
      }

      const responseData = await response.json()
      console.log('Stringee API response:', responseData)
      return responseData
    } catch (error) {
      console.error('Stringee voice call failed:', error)
      throw error
    }
  }
}

export default VoiceCallNotifier
