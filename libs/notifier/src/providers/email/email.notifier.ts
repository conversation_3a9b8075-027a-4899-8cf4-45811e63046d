import { createTransport, Transporter } from 'nodemailer'
import SMTPTransport = require('nodemailer/lib/smtp-transport')
import { render } from '@react-email/components'

import { EmailNotifierConfig, SendEmailPayload } from './types'

class EmailNotifier {
  private _transporter: Transporter<SMTPTransport.SentMessageInfo>
  constructor(config: EmailNotifierConfig) {
    this._transporter = createTransport(config)
  }

  async send(payload: SendEmailPayload) {
    const html =
      typeof payload.html === 'string'
        ? payload.html
        : await render(payload.html)

    return this._transporter.sendMail({
      from: payload.from,
      to: payload.to,
      subject: payload.subject,
      headers: payload.headers,
      html,
    })
  }
}

export default EmailNotifier
