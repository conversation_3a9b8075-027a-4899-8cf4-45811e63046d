import { PushNotiIncidentPayload } from '@libs/notifier/incident-notifier/incident-notifier.type'

class PushNotiNotifier {
  async send(payload: PushNotiIncidentPayload) {
    const headers = {
      Authorization: payload.token,
      accept: 'application/json',
      'Content-Type': 'application/json',
    }
    const body = JSON.stringify({
      app_id: '3a3e2e86-3427-48c9-ac49-5b5f8f97982a', //static
      include_external_user_ids: [payload.userId],
      contents: {
        en: `Your organization: ${payload.organizationName} just got new incident from ${payload.checkName}`,
      },
      headings: {
        en: 'Incident',
      },
      priority: 10, //static
      data: {
        userId: payload.userId,
        firebaseId: payload.firebaseId,
        teamId: payload.teamId,
        incidentId: payload.incidentId,
        organizationName: payload.organizationName,
        checkName: payload.checkName,
      },
      existing_android_channel_id: 'alarm_stream', //static
    })
    // console.log('🚀 ~ OneSignal API Request Headers:', headers);
    // console.log('🚀 ~ OneSignal API Request Body:', body);
    const response = await fetch('https://api.onesignal.com/notifications', {
      method: 'POST',
      headers,
      body: body,
    })
    console.log('🚀 ~ OneSignal API Response Status:', response.status)
    if (!response.ok) {
      const errorBody = await response.text()
      console.error('🚀 ~ OneSignal API Error Response Body:', errorBody)
      // throw new Error(`OneSignal API call failed with status ${response.status}: ${errorBody}`);
    }
    const responseBody = await response.json()
    console.log(
      '🚀 ~ OneSignal API Response Body:',
      JSON.stringify(responseBody, null, 2),
    )
    return responseBody
  }
}

export default PushNotiNotifier
