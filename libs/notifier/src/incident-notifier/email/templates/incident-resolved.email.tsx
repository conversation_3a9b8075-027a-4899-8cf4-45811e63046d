import { Container, Heading, Hr, Section, Text } from '@react-email/components'
import { formatDate } from '@libs/shared/email/utils'

import { EmailIncidentPayload } from '../../incident-notifier.type'
import { calcIncidentLength } from '../../utils'

import Item from './components/Item'
import Button from './components/Button'

const IncidentResolvedEmail = ({ data }: { data: EmailIncidentPayload }) => {
  const { incidentUrl, checkUrl, title, cause, method, startedAt, resolvedAt } =
    data
  const length = calcIncidentLength({ startedAt, resolvedAt })
  return (
    <Container style={{ fontFamily: 'Arial, sans-serif', maxWidth: '600px' }}>
      <Section>
        <Heading style={{ color: '#52c41a' }} as="h2">
          🟢 Incident resolved
        </Heading>
        <Text>Hello,</Text>
        <Text>The incident was automatically resolved.</Text>
        <Text>You can reply to this email to add a comment.</Text>

        <Button variant="secondary" href={incidentUrl}>
          View incident
        </Button>
      </Section>
      <Hr />
      <Section style={{ padding: '10px', backgroundColor: '#f9f9f9' }}>
        <Item label="Monitor" value={title || checkUrl} />
        <Item
          label="Checked URL"
          value={
            <>
              {method} <a href={checkUrl}>{checkUrl}</a>
            </>
          }
        />
        <Item label="Length" value={length} />
        <Item label="Cause" value={cause} />
        <Item label="Started at" value={formatDate(startedAt)} />
        <Item label="Resolved at" value={formatDate(resolvedAt)} />
      </Section>
    </Container>
  )
}

export default IncidentResolvedEmail
