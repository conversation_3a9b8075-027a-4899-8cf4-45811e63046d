import * as React from 'react'
import { Button as RButton } from '@react-email/components'

interface ButtonProps {
  variant?: 'primary' | 'secondary'
  style?: object
  href: string
}

const VARIANT = {
  primary: {
    backgroundColor: '#7367f0',
    color: '#ffffff',
    border: 'none',
  },
  secondary: {
    backgroundColor: 'transparent',
    color: '#333333',
    border: '1px solid #d9d9d9',
  },
}
const Button = ({
  variant = 'primary',
  style,
  children,
  href,
}: React.PropsWithChildren<ButtonProps>) => {
  return (
    <RButton
      href={href}
      style={{
        ...VARIANT[variant],

        padding: '10px 20px',
        borderRadius: '5px',
        fontWeight: 'bold',
        textDecoration: 'none',
        display: 'inline-block',
        ...style,
      }}
    >
      {children}
    </RButton>
  )
}

export default Button
