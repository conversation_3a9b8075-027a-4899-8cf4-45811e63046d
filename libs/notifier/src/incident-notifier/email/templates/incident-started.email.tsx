import { Container, Heading, Hr, Section, Text } from '@react-email/components'
import { EMAIL_ELEMENT_ID } from '@libs/shared/email/constants'
import { joinParams } from '@libs/shared/utils/query-string'
import { formatDate } from '@libs/shared/email/utils'

import { EmailIncidentPayload } from '../../incident-notifier.type'
import { calcIncidentLength } from '../../utils'

import Item from './components/Item'
import Button from './components/Button'

const IncidentStartedEmail = ({ data }: { data: EmailIncidentPayload }) => {
  const {
    incidentUrl,
    url,
    checkUrl,
    title,
    cause,
    method,
    startedAt,
    resolvedAt,
  } = data
  const length = calcIncidentLength({ startedAt, resolvedAt })
  return (
    <Container style={{ fontFamily: 'Arial, sans-serif', maxWidth: '600px' }}>
      <Section>
        <Heading style={{ color: '#ff4d4f' }} as="h2">
          🔴 New incident started
        </Heading>
        <Text>Hello,</Text>
        <Text>Please acknowledge the incident.</Text>
        <Text>You can reply to this email to add a comment.</Text>
        <Text>
          P.S. We can also call you next time, just upgrade your account.
        </Text>
        <Button
          // href={`${incidentUrl}?elementId=${EMAIL_ELEMENT_ID.ACKNOWLEDGE_BUTTON}`}
          href={joinParams(incidentUrl, {
            elementId: EMAIL_ELEMENT_ID.ACKNOWLEDGE_BUTTON,
          })}
          style={{
            marginRight: '10px',
          }}
        >
          Acknowledge incident
        </Button>
        <Button variant="secondary" href={incidentUrl}>
          View incident
        </Button>
      </Section>
      <Hr />
      <Section style={{ padding: '10px', backgroundColor: '#f9f9f9' }}>
        <Item label="Monitor" value={title || checkUrl} />
        <Item
          label="Checked URL"
          value={
            <>
              {method} <a href={url}>{url}</a>
            </>
          }
        />
        <Item label="Length" value={length} />
        <Item label="Cause" value={cause} />
        <Item label="Started at" value={formatDate(startedAt)} />
      </Section>
    </Container>
  )
}

export default IncidentStartedEmail
