import EmailNotifier, {
  EmailNotifierConfig,
} from '@libs/notifier/providers/email'
import {
  EMAIL_CUSTOM_HEADER_KEY,
  EmailCustomPayload,
} from '@libs/shared/email/constants'
import { stringifyHeaders } from '@libs/shared/email/utils'

import IncidentNotifierProvider from '../incident-notifier.abstract'
import { EmailIncidentPayload } from '../incident-notifier.type'

import IncidentStartedEmail from './templates/incident-started.email'
import IncidentResolvedEmail from './templates/incident-resolved.email'
import IncidentAcknowledgedEmail from './templates/incident-acknowledged.email'

class EmailIncidentNotifier
  implements IncidentNotifierProvider<'email', EmailIncidentPayload>
{
  private _notifier: EmailNotifier
  constructor(config: EmailNotifierConfig) {
    this._notifier = new EmailNotifier(config)
  }

  async started(payload: EmailIncidentPayload) {
    if (!payload?.to?.length || !payload?.from)
      throw new Error('Missing email data')

    return Promise.all(
      payload.to.map(({ email, userId }) => {
        return this._notifier.send({
          from: payload.from,
          to: email,
          subject: 'Incident Start',
          html: <IncidentStartedEmail data={payload} />,
          headers: {
            ...(payload.headers || {}),
            [EMAIL_CUSTOM_HEADER_KEY]: stringifyHeaders({
              incidentId: payload?.incidentId,
              userId,
            } as EmailCustomPayload),
          },
        })
      }),
    )
  }

  async resolved(payload: EmailIncidentPayload) {
    if (!payload?.to || !payload?.from) throw new Error('Missing email')
    return Promise.all(
      payload.to.map(({ email, userId }) => {
        return this._notifier.send({
          from: payload.from,
          to: email,
          subject: 'Incident Resolved',
          html: <IncidentResolvedEmail data={payload} />,
          headers: {
            ...(payload.headers || {}),
            [EMAIL_CUSTOM_HEADER_KEY]: stringifyHeaders({
              incidentId: payload?.incidentId,
              userId,
            } as EmailCustomPayload),
          },
        })
      }),
    )
  }

  async acknowledged(payload: EmailIncidentPayload) {
    if (!payload?.to?.length || !payload?.from)
      throw new Error('Missing email data')

    return Promise.all(
      payload.to.map(({ email, userId }) => {
        return this._notifier.send({
          from: payload.from,
          to: email,
          subject: 'Incident Acknowledged',
          html: <IncidentAcknowledgedEmail data={payload} />,
          headers: {
            [EMAIL_CUSTOM_HEADER_KEY]: stringifyHeaders({
              incidentId: payload?.incidentId,
              userId,
            } as EmailCustomPayload),
          },
        })
      }),
    )
  }
}

export default EmailIncidentNotifier
