import { AnyBlock } from '@slack/web-api'

import { SlackIncidentPayload } from '../incident-notifier.type'
import { calcIncidentLength } from '../utils'

export const IncidentInfoBlocks = ({
  checkUrl,
  title,
  cause,
  startedAt,
  resolvedAt,
  method,
  url,
}: SlackIncidentPayload): AnyBlock[] => {
  const length = calcIncidentLength({ startedAt, resolvedAt })
  return [
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Monitor:* ${checkUrl} ${title ? `(${title})` : ''}\n*Cause:* ${cause}\n*Checked URL:* ${'`'}${method} ${url}${'`'}\n*Length:* ${length}`,
      },
    },
  ]
}
