import { SLACK_ELEMENT_ID } from '@libs/shared/slack/constants'
import SlackNotifier from '@libs/notifier/providers/slack'
import { IncomingWebhookSendArguments } from '@slack/webhook'

import { SlackIncidentPayload } from '../incident-notifier.type'

import { IncidentInfoBlocks } from './shared'

const SlackIncidentStartedPayload = (
  incident: SlackIncidentPayload,
): IncomingWebhookSendArguments => {
  const { url, incidentUrl, checkUrl, startedAt, incidentId } = incident
  const value = JSON.stringify({ incidentId })
  const infoBlocks = IncidentInfoBlocks(incident)
  return {
    blocks: [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*New incident for* ${url}`,
        },
      },
    ],
    attachments: [
      {
        color: '#fc6653',
        blocks: [
          ...infoBlocks,
          {
            block_id: SLACK_ELEMENT_ID.PLEASE_ACKNOWLEDGE,
            type: 'context',
            elements: [
              {
                type: 'plain_text',
                text: 'Please acknowledge the incident:',
              },
            ],
          },
          {
            block_id: SLACK_ELEMENT_ID.ACTION_BLOCK,
            type: 'actions',
            elements: [
              {
                type: 'button',
                text: {
                  type: 'plain_text',
                  text: ':raised_back_of_hand: Acknowledge',
                  emoji: true,
                },
                style: 'primary',
                value,
                action_id: SLACK_ELEMENT_ID.ACKNOWLEDGE_BUTTON,
              },
              {
                type: 'button',
                text: {
                  type: 'plain_text',
                  text: ':rotating_light: Escalate',
                  emoji: true,
                },
                value,
                action_id: SLACK_ELEMENT_ID.ESCALATE_BUTTON,
              },
            ],
          },
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `:warning: <${incidentUrl}|*Incident*>`,
              },
              {
                type: 'mrkdwn',
                text: `:globe_with_meridians: <${checkUrl}|*Monitor*>`,
              },
            ],
          },

          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `*Started at:* ${SlackNotifier.formatDate(startedAt)}`,
              },
            ],
          },
        ],
      },
    ],
  }
}

export default SlackIncidentStartedPayload
