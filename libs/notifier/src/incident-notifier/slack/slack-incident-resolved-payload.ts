import SlackNotifier from '@libs/notifier/providers/slack'
import { IncomingWebhookSendArguments } from '@slack/webhook'

import { SlackIncidentPayload } from '../incident-notifier.type'

import { IncidentInfoBlocks } from './shared'

const SlackIncidentResolvedPayload = (
  incident: SlackIncidentPayload,
): IncomingWebhookSendArguments => {
  const { url, incidentUrl, checkUrl, startedAt } = incident
  const infoBlocks = IncidentInfoBlocks(incident)
  return {
    blocks: [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Automatically resolved ${url} incident*`,
        },
      },
    ],
    attachments: [
      {
        color: '#02cb91',
        blocks: [
          ...infoBlocks,
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `:warning: <${incidentUrl}|*Incident*>`,
              },
              {
                type: 'mrkdwn',
                text: `:globe_with_meridians: <${checkUrl}|*Monitor*>`,
              },
            ],
          },

          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `*Started at:* ${SlackNotifier.formatDate(startedAt)}`,
              },
            ],
          },
        ],
      },
    ],
  }
}

export default SlackIncidentResolvedPayload
