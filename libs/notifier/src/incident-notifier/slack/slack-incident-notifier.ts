import SlackNotifier from '@libs/notifier/providers/slack'

import IncidentNotifierProvider from '../incident-notifier.abstract'
import { SlackIncidentPayload } from '../incident-notifier.type'

import SlackIncidentStartedPayload from './slack-incident-started-payload'
import SlackIncidentResolvedPayload from './slack-incident-resolved-payload'

class SlackIncidentNotifier
  implements IncidentNotifierProvider<'slack', SlackIncidentPayload>
{
  private _notifier: SlackNotifier
  constructor() {
    this._notifier = new SlackNotifier()
  }

  async started(incident: SlackIncidentPayload) {
    if (!incident?.incomingWebhookUrl) return
    const notifier = this._notifier.incomingWebhook(incident.incomingWebhookUrl)

    const payload = SlackIncidentStartedPayload(incident)

    return notifier.send(payload)
  }

  async resolved(incident: SlackIncidentPayload) {
    if (!incident?.incomingWebhookUrl) return
    const notifier = this._notifier.incomingWebhook(incident.incomingWebhookUrl)
    const payload = SlackIncidentResolvedPayload(incident)

    return notifier.send(payload)
  }
}

export default SlackIncidentNotifier
