import { FORMATTER } from '@libs/shared/date/format'

export const calcIncidentLength = (incidents: {
  startedAt: Date
  resolvedAt?: Date
}) => {
  // diff time from startedAt to resolvedAt
  const startedAt = incidents.startedAt
  const resolvedAt = incidents.resolvedAt || new Date()

  if (!startedAt || !resolvedAt) return

  const diffTime =
    new Date(resolvedAt).getTime() - new Date(startedAt).getTime()

  return FORMATTER.formatIntervalToDuration(diffTime / 1000)
}
