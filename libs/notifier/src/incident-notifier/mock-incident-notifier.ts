import { NotifierProviderType } from '../@type/notifier.types'

import IncidentNotifierProvider from './incident-notifier.abstract'
import { IncidentPayloadMap } from './incident-notifier.type'

class MockIncidentNotifier<P extends NotifierProviderType>
  implements IncidentNotifierProvider<P, IncidentPayloadMap[P]>
{
  private providerType: P

  constructor(providerType: P) {
    this.providerType = providerType
  }

  async started(payload: IncidentPayloadMap[P]): Promise<any> {
    console.log(
      `MOCK NOTIFIER - ${this.providerType} started with payload:`,
      payload,
    )
    return {
      status: 'mock_success',
      message: `Mock ${this.providerType} notification sent`,
    }
  }

  async resolved(payload: IncidentPayloadMap[P]): Promise<any> {
    console.log(
      `MOCK NOTIFIER - ${this.providerType} resolved with payload:`,
      payload,
    )
    return {
      status: 'mock_success',
      message: `Mock ${this.providerType} notification resolved`,
    }
  }
}

export default MockIncidentNotifier
