import PushNotiNotifier from '@libs/notifier/providers/push_noti'

import IncidentNotifierProvider from '../incident-notifier.abstract'
import { PushNotiIncidentPayload } from '../incident-notifier.type'

class PushNotiIncidentNotifier
  implements IncidentNotifierProvider<'push_noti', PushNotiIncidentPayload>
{
  private _notifier: PushNotiNotifier
  constructor() {
    this._notifier = new PushNotiNotifier()
  }

  async started(payload: PushNotiIncidentPayload) {
    return this._notifier.send(payload)
  }

  async resolved(_: PushNotiIncidentPayload) {
    // We only trigger voice call when there is an incident
    // return this._notifier.send(payload)
  }
}

export default PushNotiIncidentNotifier
