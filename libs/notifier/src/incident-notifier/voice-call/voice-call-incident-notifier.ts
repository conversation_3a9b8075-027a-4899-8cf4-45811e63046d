import VoiceCallNotifier from '@libs/notifier/providers/voice_call'

import IncidentNotifierProvider from '../incident-notifier.abstract'
import { VoiceCallIncidentPayload } from '../incident-notifier.type'

class VoiceCallIncidentNotifier
  implements IncidentNotifierProvider<'voice_call', VoiceCallIncidentPayload>
{
  private _notifier: VoiceCallNotifier
  constructor() {
    this._notifier = new VoiceCallNotifier()
  }

  async started(payload: VoiceCallIncidentPayload) {
    return this._notifier.send(payload)
  }

  async resolved(_: VoiceCallIncidentPayload) {
    // We only trigger voice call when there is an incident
    // return this._notifier.send(payload)
  }
}

export default VoiceCallIncidentNotifier
