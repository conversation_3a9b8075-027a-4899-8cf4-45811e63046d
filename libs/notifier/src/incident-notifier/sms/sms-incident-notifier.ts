import SmsNotifier from '@libs/notifier/providers/sms'

import IncidentNotifierProvider from '../incident-notifier.abstract'
import { SmsIncidentPayload } from '../incident-notifier.type'

class SmsIncidentNotifier
  implements IncidentNotifierProvider<'sms', SmsIncidentPayload>
{
  private _notifier: SmsNotifier
  constructor() {
    this._notifier = new SmsNotifier()
  }

  async started(payload: SmsIncidentPayload) {
    return this._notifier.send(payload)
  }

  async resolved(_: SmsIncidentPayload) {
    // We only trigger sms when there is an incident
    // return this._notifier.send(payload)
  }
}

export default SmsIncidentNotifier
