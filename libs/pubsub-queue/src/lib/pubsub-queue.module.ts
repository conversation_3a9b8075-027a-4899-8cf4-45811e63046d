import { Module } from '@nestjs/common'
import { BullClient } from '@libs/shared/service-clients/bullmq.client'
import { RedisClient } from '@libs/shared/service-clients/redis.client'

import { PubsubQueue } from './pubsub-queue'
import { QueueModule } from './queue.module'

@Module({
  imports: [QueueModule],
  providers: [RedisClient, BullClient, PubsubQueue],
  exports: [PubsubQueue],
})
export class PubsubQueueModule {}
