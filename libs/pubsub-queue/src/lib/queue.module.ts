import { BullModule } from '@nestjs/bullmq'
import {
  ConfigurableModuleBuilder,
  DynamicModule,
  Module,
} from '@nestjs/common'
import { BullBoardModule } from '@bull-board/nestjs'
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter'
import { redisConnection } from '@libs/shared/service-clients/redis-connection'

interface QueueModuleOptions {
  queues: string[]
  flows?: string[]
}

export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN, OPTIONS_TYPE } =
  new ConfigurableModuleBuilder<QueueModuleOptions>().build()

@Module({})
export class QueueModule extends ConfigurableModuleClass {
  static register(options: typeof OPTIONS_TYPE): DynamicModule {
    const bullModules = options.queues.map((name) =>
      BullModule.registerQueue({ name, connection: redisConnection }),
    )

    const bullBoards = options.queues.map((name) =>
      BullBoardModule.forFeature({
        name,
        adapter: BullMQAdapter,
      }),
    )

    const flowProducers = (options.flows || []).map((flow) =>
      BullModule.registerFlowProducer({
        name: flow,
      }),
    )

    return {
      ...super.register(options),
      imports: [...bullModules, ...bullBoards, ...flowProducers],
      exports: [...bullModules, ...flowProducers],
    }
  }
}
