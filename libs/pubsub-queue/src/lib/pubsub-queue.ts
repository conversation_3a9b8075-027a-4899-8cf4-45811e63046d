import { BullClient } from '@libs/shared/service-clients/bullmq.client'
import { RedisClient } from '@libs/shared/service-clients/redis.client'
import { Inject, Injectable } from '@nestjs/common'

@Injectable()
export class PubsubQueue {
  constructor(
    @Inject(RedisClient) private readonly redisClient: RedisClient,
    @Inject(BullClient) private readonly bullClient: BullClient,
  ) {}

  onModuleDestroy(): void {
    this.redisClient.disconnect()
  }

  async publishToChannel(channel: string, message: string): Promise<number> {
    return this.redisClient.publish(channel, message)
  }

  async publishToQueue(
    queueName: string,
    jobName: string,
    message: object,
  ): Promise<void> {
    await this.bullClient.addJob(queueName, jobName, message)
  }

  async deleteJob(queueName: string, jobName: string): Promise<void> {
    await this.bullClient.deleteJob(queueName, jobName)
  }

  async deleteRepeatableJob(queueName: string, jobName: string): Promise<void> {
    await this.bullClient.deleteRepeatableJob(queueName, jobName)
  }
}
