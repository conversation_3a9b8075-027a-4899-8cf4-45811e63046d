/**
 * Comprehensive Subscription Status Enum
 *
 * This enum consolidates all subscription status values from:
 * - Stripe subscription statuses (core business logic)
 * - Legacy application-specific statuses
 *
 * Used across all apps: user-portal-backend, infrastructure (billing), orchestrator
 */
export enum SubscriptionStatus {
  // Core Stripe subscription statuses
  TRIALING = 'trialing',
  ACTIVE = 'active',
  PAST_DUE = 'past_due',
  UNPAID = 'unpaid',
  CANCELED = 'canceled',
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',

  // Additional business statuses
  INACTIVE = 'inactive', // Legacy support
  REFUNDED = 'refunded', // Business-specific status
  PENDING = 'pending', // Plan change initiated, awaiting payment confirmation
  SUPERSEDED = 'superseded', // Old subscription replaced by new plan
}
