/**
 * Resource Type Mapping and Classification
 *
 * Consolidates the relationship between ResourceType and ResourceType
 * and defines which resources are plan-limited vs transactional.
 */

// Define the enums locally to avoid circular dependency

export enum ResourceType {
  MEMBER = 'MEMBER',
  TEAM = 'TEAM',
  CHECK = 'CHECK',
  INTEGRATION = 'INTEGRATION',
  STATUS_PAGE = 'STATUS_PAGE',
  SMS = 'SMS',
  PHONE_CALL = 'PHONE_CALL',
}

/**
 * Mapping between ResourceType and ResourceType for plan-limited resources
 * These resources have limits defined in subscription plans
 */

/**
 * Transactional resource types (globally priced, not plan-dependent)
 * These resources are billed per-use regardless of subscription plan
 */
export const TRANSACTIONAL_RESOURCE_TYPES = [
  // ResourceType.SMS,
  // ResourceType.PHONE_CALLS,
] as const

/**
 * Plan-limited resource types
 * These resources are limited by subscription plan quotas
 */
export const PLAN_LIMITED_RESOURCE_TYPES = [
  ResourceType.MEMBER,
  ResourceType.TEAM,
  ResourceType.CHECK,
  ResourceType.INTEGRATION,
  ResourceType.STATUS_PAGE,
  ResourceType.SMS,
  ResourceType.PHONE_CALL,
] as const

/**
 * Helper functions for resource classification
 */
export const isTransactionalResource = (
  ResourceType: ResourceType,
): boolean => {
  return (TRANSACTIONAL_RESOURCE_TYPES as readonly ResourceType[]).includes(
    ResourceType,
  )
}

export const isPlanLimitedResource = (ResourceType: ResourceType): boolean => {
  return (PLAN_LIMITED_RESOURCE_TYPES as readonly ResourceType[]).includes(
    ResourceType,
  )
}

export const getResourceTypeForLimitation = (
  ResourceType: ResourceType,
): ResourceType | undefined => {
  return ResourceType
}
