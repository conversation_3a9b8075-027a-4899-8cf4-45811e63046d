/**
 * Plan Type Enum
 *
 * Categorizes different types of subscription plans in the system.
 * Used for plan management and billing logic.
 */
export enum PlanType {
  /**
   * Base subscription plan with included resource limits
   * Provides core functionality with fixed monthly/yearly pricing
   */
  BASE_PLAN = 'BASE_PLAN',

  /**
   * Add-on plan for metered usage billing
   * Used for services like SMS, phone calls, and overage billing
   */
  ADDON_METERED_USAGE = 'ADDON_METERED_USAGE',
}
