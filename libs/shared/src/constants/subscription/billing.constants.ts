/**
 * Billing-related constants
 *
 * Constants used across billing operations, payment processing, and subscription management.
 */

/**
 * Subscription source - how the subscription was created
 */
export const SUBSCRIPTION_SOURCE = {
  STRIPE: 'stripe',
  MANUAL: 'manual',
} as const

/**
 * Recurring billing intervals
 */
export const RECURRING_INTERVAL = {
  MONTH: 'month',
  YEAR: 'year',
} as const

/**
 * Product types for Stripe integration
 * Replaces the duplicate STRIPE_PRODUCT_TYPE from infrastructure
 */
export const SUBSCRIPTION_TYPE = {
  SUBSCRIPTION: 'subscription',
  ADDONS: 'addons',
} as const

/**
 * Type definitions for better type safety
 */
export type SubscriptionSource =
  (typeof SUBSCRIPTION_SOURCE)[keyof typeof SUBSCRIPTION_SOURCE]
export type RecurringInterval =
  (typeof RECURRING_INTERVAL)[keyof typeof RECURRING_INTERVAL]
export type SubscriptionType =
  (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE]
