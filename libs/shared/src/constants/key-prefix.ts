export const REDIS_CHECK_PREFIX = {
  CHECK_DATA: 'check_data',
  WORKER_NODE: 'worker_node',
  CHECK_STATUS: 'check_status',
  CHECK_WORKER_STATE: 'check_worker_state',
  WORKER_LIST: 'check_workers_list',
}

export const CHECK_WORKER_STATE = (checkId: string) =>
  `${REDIS_CHECK_PREFIX.CHECK_WORKER_STATE}:${checkId}`

export const REDIS_CHECK_STATUS_KEY = {
  IS_VERIFYING: 'isVerifying',
  IS_PAUSED: 'isPaused',
  STATUS: 'status',
  RESPONSE: 'response',
  WORKERS: 'workers',
  RECOVERY_JOB: 'recovery_job_id',
  CONFIRMATION_JOB: 'confirmation_job_id',
  WORKER_RESPONSE: 'response',
  LAST_CHECK_AT: 'last_check_at',
  LAST_PAUSE_AT: 'last_pause_at',
  IPV4: 'ipv4',
  IPV6: 'ipv6',
}

export const WORKER_RESPONSE = (workerId: string) =>
  `${REDIS_CHECK_STATUS_KEY.WORKER_RESPONSE}:${workerId}`

export const RESPONSE = (workerId: string) =>
  `${REDIS_CHECK_STATUS_KEY.RESPONSE}:${workerId}`

export const REDIS_ALIAS_TOKEN_PREFIX = {
  ALIAS_TOKEN: 'alias_token',
}
