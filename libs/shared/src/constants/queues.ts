export const QUEUE = {
  MAIN: 'monitoringdog',
  VERIFY: 'verify_queue',
  WORKER_VERIFY: 'worker_verify_queue',
  FINALIZE_VERIFY: 'finalize_verify_queue',
  SCHEDULER: 'scheduler_queue', // Queue for scheduling delay job
  TESTCHECK: (workerId: string) => `check_queue:${workerId}`,
  RECOVERY_RESET: 'recovery_Reset_queue',

  // Incident escalation SQS
}

export const WORKER_VERIFY_QUEUE = (workerId: string) =>
  `${QUEUE.WORKER_VERIFY}:${workerId}`

export const VALIDATION_PERIOD_JOB_NAME = {
  CONFIRMATION_PERIOD: 'confirmation_period',
  RECOVERY_PERIOD: 'recovery_period',
}

export const FLOW = {
  VERIFY: 'verify_queue_flow',
}
