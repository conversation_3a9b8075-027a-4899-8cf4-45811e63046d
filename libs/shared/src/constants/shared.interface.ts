import { CheckType } from './check.enum'
import {
  IncidentEventDetailType,
  IncidentEventType,
  IncidentStatus,
} from './incident'

export interface RecoveryResetRequest {
  checkId: string
  recoverPeriod: number
}

export interface CheckWorkerResponse {
  workerId: string
  response: CheckWorkerResponseBody
}

export interface CheckWorkerResponseBody {
  code: number
  message: string
  header: string
  data: string
}

export interface IncidentInterface {
  id: string
  checkId: string
  teamId: string
  status: IncidentStatus
  startedAt: Date
  resolvedAt?: Date | undefined
  resolvedBy?: string | undefined
  acknowledgedAt?: Date | undefined
  acknowledgedBy?: string | undefined
  escalationPolicy: string // Stringify of EscalationPolicy
  checkInfo: {
    type: CheckType
    url: string
    method: string
    locations: string[]
  }
  stepFunctionArn?: string | undefined
  createdAt: Date
  updatedAt: Date
  createdBy?: string | undefined
  updatedBy?: string | undefined // These two fields should not need to exist?
  cause: string | undefined
}

export interface IncidentEventInterface {
  id: string
  incidentId: string
  type: IncidentEventType
  user?: {
    id: string
    firstName: string
    lastName: string
    avatar?: string
  }
  comment?: string
  attachment?: string
  attribute: {
    type: IncidentEventDetailType
    value: {
      location: string | null
      message?: string
      waitingTime?: number | undefined
      receiver?: string | undefined
    }
  }
  createdAt: Date
  updatedAt: Date
  createdBy?: string | undefined
  updatedBy?: string | undefined
}

export interface EscalationPolicy {
  repeat: number[]
  repeatDelay: number
  incidentId: string
  escalationSteps: {
    contactType: EscalationContactType
    contactId?: string
    alerts: string[]
  }[]
}

export enum EscalationContactType {
  ENTIRE_TEAM = 'entire_team',
  ON_CALL = 'on_call',
  MEMBER = 'member',
  INTEGRATION = 'integration',
}
