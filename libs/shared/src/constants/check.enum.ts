export enum CheckType {
  STATUS = 'status', // Check for a 2XX HTTP status code.
  EXPECTED_STATUS_CODE = 'expected_status_code', // Check for returned value of the expectedStatusCodes
  PING = 'ping', // Check the service by pinging url.
  KEYWORD = 'keyword', // Check for the existence of the requiredKeyword in the response body.
  NO_KEYWORD = 'no_keyword',
  TCP_PORT = 'tcp_port',
  UDP_PORT = 'udp_port',
}

export enum CheckStatus {
  UP = 'up', // Checks are passing.
  DOWN = 'down', // Checks are failing.
  PENDING = 'pending', // The check is waiting for the first check.
  VALIDATING = 'validating', // The service seems to be back up, and waiting for recoverPeriod.
  PAUSED = 'paused', // The check is paused.
  MAINTENANCE = 'maintenance', // The monitor is paused because it is currently in its maintenance period.
}

export enum CheckStatusChange {
  UP_TO_DOWN = 'up_to_down',
  DOWN_TO_UP = 'down_to_up',
  STILL_DOWN = 'still_down',
  STILL_UP = 'still_up',
}

export enum SubscriptionPlan {
  FREE = 'free',
  BASIC = 'basic',
  TEAM = 'team',
  BUSINESS = 'business',
}

export enum HttpMethod {
  HEAD = 'HEAD',
  POST = 'POST',
  GET = 'GET',
  PATCH = 'PATCH',
  PUT = 'PUT',
}

export enum WorkerResult {
  SUCCESS = 'success',
  FAIL = 'fail',
  PENDING = 'pending',
}

export const FALSE = 'false'

export const BUCKET = 'monitoringdog-dev'

export const StatusChangeMessages = {
  [CheckStatusChange.STILL_UP]: '',
  [CheckStatusChange.STILL_DOWN]: '',
  [CheckStatusChange.UP_TO_DOWN]: 'API is DOWN, going for confirmation',
  [CheckStatusChange.DOWN_TO_UP]: 'API is UP again, going for confirmation',
  DEFAULT: (status: string) => `Abnormal status change detected ${status}`,
}

export const MaintenanceDays = [
  'mon',
  'tue',
  'wed',
  'thu',
  'fri',
  'sat',
  'sun',
] as const
