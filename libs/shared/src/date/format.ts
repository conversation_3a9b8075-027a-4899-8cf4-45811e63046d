import {
  differenceInSeconds,
  format,
  formatDistanceToNow,
  formatDuration,
  intervalToDuration,
  isThisWeek,
  isToday,
} from 'date-fns'

const FORMAT = {
  default: 'M/d/yyyy h:mm a',
  timezone: "d MMM 'at' hh:mma X",
}

const formatDate = (
  date?: Date,
  formatType: keyof typeof FORMAT = 'default',
) => {
  if (!date) return
  return format(date, FORMAT[formatType] || FORMAT.default)
}

const formatRelativeDate = (date?: Date) => {
  if (!date) return
  if (isToday(date) || isThisWeek(date))
    return formatDistanceToNow(date, { addSuffix: true })
  return format(date, FORMAT.timezone)
}

function formatIntervalToDuration(seconds: number): string {
  const duration = intervalToDuration({ start: 0, end: seconds * 1000 })

  const parts: string[] = []

  if (duration.hours) {
    parts.push(`${duration.hours} hour${duration.hours > 1 ? 's' : ''}`)
  }

  if (duration.minutes) {
    parts.push(`${duration.minutes} minute${duration.minutes > 1 ? 's' : ''}`)
  }

  if (duration.seconds) {
    parts.push(`${duration.seconds} second${duration.seconds > 1 ? 's' : ''}`)
  }

  if (parts.length === 2) {
    return parts.join(' and ')
  } else {
    return parts.join(', ').replace(/,([^,]*)$/, ' and$1')
  }
}

function remainingTime(futureDate: Date, fromDate = new Date()): string {
  const diffTime = differenceInSeconds(futureDate, fromDate)
  return formatIntervalToDuration(diffTime) + ' remaining'
}

// try to consistently format date in the app
export const FORMATTER = {
  date: formatDate,
  relative: formatRelativeDate,
  formatIntervalToDuration,
  duration: formatDuration,
  remainingTime,
}
