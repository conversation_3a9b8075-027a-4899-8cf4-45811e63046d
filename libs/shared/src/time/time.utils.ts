import moment = require('moment-timezone')

export class TimeUtils {
  public isCurrentTimeInMaintenanceWindow(
    maintenanceTimeZone: string,
    maintenanceFrom: string,
    maintenanceTo: string,
  ): boolean {
    const currentTime = moment.tz(maintenanceTimeZone)

    const maintenanceStart = moment.tz(
      maintenanceFrom,
      'HH:mm:ss',
      maintenanceTimeZone,
    )
    const maintenanceEnd = moment.tz(
      maintenanceTo,
      'HH:mm:ss',
      maintenanceTimeZone,
    )

    if (maintenanceEnd.isBefore(maintenanceStart)) {
      // If the end time is before the start time, it spans past midnight
      maintenanceEnd.add(1, 'day')
    }
    return currentTime.isBetween(maintenanceStart, maintenanceEnd)
  }

  public isTodayMaintenanceDay(
    maintenanceDays: string[],
    maintenanceTimeZone: string,
  ): boolean {
    const currentTime = moment.tz(maintenanceTimeZone)
    const today = currentTime.format('ddd').toLowerCase()
    return maintenanceDays.includes(today)
  }

  public getMaintenanceEnd(
    maintenanceTimeZone: string,
    maintenanceTo: string,
  ): Date {
    const maintenanceEnd = moment.tz(
      maintenanceTo,
      'HH:mm:ss',
      maintenanceTimeZone,
    )
    const currentTime = moment.tz(maintenanceTimeZone)
    if (maintenanceEnd.isBefore(currentTime)) {
      maintenanceEnd.add(1, 'day')
    }
    return maintenanceEnd.toDate()
  }

  public cronExpressionParser(seconds: number): string {
    const SECONDS_IN_MINUTE = 60
    const SECONDS_IN_HOUR = 3600

    let sec = 0
    let min = 0
    let hou = 0

    // Exactly 1 min to almost an hour
    if (seconds >= SECONDS_IN_MINUTE && seconds < SECONDS_IN_HOUR) {
      min = Math.floor(seconds / SECONDS_IN_MINUTE)
      sec = seconds % SECONDS_IN_MINUTE
      return `${sec} */${min} * * * *`
    }

    // Exactly an hour or more
    if (seconds >= SECONDS_IN_HOUR) {
      hou = Math.floor(seconds / SECONDS_IN_HOUR)
      min = Math.floor((seconds % SECONDS_IN_HOUR) / SECONDS_IN_MINUTE)
      sec = seconds % SECONDS_IN_MINUTE
      return `${sec} ${min} */${hou} * * *`
    }
    // Under 1 min
    return `*/${seconds} * * * * *`
  }
}
