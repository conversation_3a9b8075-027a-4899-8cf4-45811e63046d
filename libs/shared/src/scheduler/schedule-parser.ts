import {
  addMilliseconds,
  areIntervalsOverlapping,
  differenceInMilliseconds,
  endOfDay,
  getHours,
  getMilliseconds,
  getMinutes,
  getSeconds,
  set,
  startOfDay,
  subMilliseconds,
} from 'date-fns'
import { orderBy } from 'lodash'
import { RRule } from 'rrule'

import { OnCallSchedulerType } from '../constants/scheduler.enum'

export type Schedule<T> = T & {
  type?: `${OnCallSchedulerType}`
  startDate?: Date
  endDate?: Date
  allDay?: boolean
  recurringPattern?: string
}

export interface ScheduleParserOptions {
  date: Date
  toDate: Date
}

class ScheduleParser {
  static fromString(rule?: string) {
    if (!rule) return
    try {
      const result = RRule.fromString(rule)
      return result
    } catch (e) {
      return
    }
  }

  static parse<T>(
    currentItem: Schedule<T>,
    options: ScheduleParserOptions,
  ): Schedule<T>[] {
    const resultInstances: Schedule<T>[] = []

    // Base dates for RRule fetching (day-based for broad phase)
    const rruleBroadQueryStart = startOfDay(options.date)
    const rruleBroadQueryEnd = endOfDay(options.toDate || options.date)

    // Renamed 'item' to 'currentItem' for clarity
    const itemOriginalStartDate = currentItem.startDate
    const itemOriginalEndDate = currentItem.endDate

    if (!itemOriginalStartDate || !itemOriginalEndDate) {
      return []
    }

    switch (currentItem.type) {
      case OnCallSchedulerType.DateRange: {
        const itemToCheckInterval = {
          start: itemOriginalStartDate,
          end: itemOriginalEndDate,
        }
        const targetInterval = {
          start: options.date,
          end: options.toDate,
        }

        if (
          areIntervalsOverlapping(itemToCheckInterval, targetInterval, {
            inclusive: true,
          })
        ) {
          resultInstances.push(currentItem)
        }
        break
      }

      case OnCallSchedulerType.Recurring: {
        if (!currentItem.recurringPattern) return []
        const rrule = ScheduleParser.fromString(currentItem.recurringPattern)
        if (!rrule) return []

        const instanceDurationMs = differenceInMilliseconds(
          itemOriginalEndDate,
          itemOriginalStartDate,
        )

        // Determine the search window for RRule (day-based, broad phase)
        const rruleSearchWindowStart = subMilliseconds(
          rruleBroadQueryStart,
          instanceDurationMs + 1, // Add 1ms buffer for safety with subtractions
        )
        const rruleSearchWindowEnd = addMilliseconds(
          rruleBroadQueryEnd,
          1, // Add 1ms buffer
        )

        const candidateOccurrenceStartDates = rrule.between(
          rruleSearchWindowStart,
          rruleSearchWindowEnd,
          true, // inclusive
        )

        for (const candidateStartDate of candidateOccurrenceStartDates) {
          const actualInstanceStartDate = set(candidateStartDate, {
            hours: getHours(itemOriginalStartDate),
            minutes: getMinutes(itemOriginalStartDate),
            seconds: getSeconds(itemOriginalStartDate),
            milliseconds: getMilliseconds(itemOriginalStartDate),
          })
          const actualInstanceEndDate = addMilliseconds(
            actualInstanceStartDate,
            instanceDurationMs,
          )

          const currentInstanceInterval = {
            // This is the itemToCheckInterval for recurring instances
            start: actualInstanceStartDate,
            end: actualInstanceEndDate,
          }

          const targetInterval = {
            // Use exact times from options for non-allDay range query
            start: options.date,
            end: options.toDate,
          } // Determine targetInterval for this specific instance

          if (
            areIntervalsOverlapping(currentInstanceInterval, targetInterval, {
              inclusive: true,
            })
          ) {
            resultInstances.push({
              ...currentItem, // Spread the original recurring item
              startDate: actualInstanceStartDate,
              endDate: actualInstanceEndDate,
            })
          }
        }
        break
      }
    }

    return orderBy(resultInstances.filter(Boolean), ['startDate'], ['asc'])
  }
}

export default ScheduleParser
