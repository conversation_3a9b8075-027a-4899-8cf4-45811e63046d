/**
 * Overage System Interfaces
 *
 * Type definitions for the overage system that can handle
 * any resource type without hardcoded enums or variables.
 */

import { ResourceType } from '../constants/subscription/resource-mapping'

/**
 * Represents overage information for a specific resource type
 */
export interface ResourceOverage {
  resourceType: ResourceType
  currentUsage: number
  planLimit: number
  overageAmount: number
  stripePriceId: string | null
}

/**
 * Map of resource types to their current usage amounts
 */
export interface ResourceUsageMap {
  [ResourceType.MEMBER]: number
  [ResourceType.TEAM]: number
  [ResourceType.CHECK]: number
  [ResourceType.INTEGRATION]: number
  [ResourceType.STATUS_PAGE]: number
}

/**
 * Billing period information
 */
export interface BillingPeriod {
  startDate: Date
  endDate: Date
}

/**
 * Overage calculation result that can handle any number of resource types
 */
export interface OverageResult {
  hasOverage: boolean
  overageMap: Map<ResourceType, number>

  // Helper methods
  getOverageFor(resourceType: ResourceType): number
  getAllOverages(): ResourceOverage[]
  getTotalOverageCount(): number
  getResourceTypesWithOverage(): ResourceType[]
}

/**
 * Implementation class for OverageResult
 */
export class OverageResultImpl implements OverageResult {
  public readonly hasOverage: boolean
  public readonly overageMap: Map<ResourceType, number>

  constructor(
    overageMap: Map<ResourceType, number>,
    private readonly resourceOverageDetails?: Map<
      ResourceType,
      ResourceOverage
    >,
  ) {
    this.overageMap = overageMap
    this.hasOverage = overageMap.size > 0
  }

  getOverageFor(resourceType: ResourceType): number {
    return this.overageMap.get(resourceType) || 0
  }

  getAllOverages(): ResourceOverage[] {
    if (!this.resourceOverageDetails) {
      return []
    }

    return Array.from(this.resourceOverageDetails.values())
  }

  getTotalOverageCount(): number {
    return Array.from(this.overageMap.values()).reduce(
      (sum, amount) => sum + amount,
      0,
    )
  }

  getResourceTypesWithOverage(): ResourceType[] {
    return Array.from(this.overageMap.keys())
  }

  /**
   * Static factory method to create OverageResult from resource overages
   */
  static fromResourceOverages(
    resourceOverages: ResourceOverage[],
  ): OverageResult {
    const overageMap = new Map<ResourceType, number>()
    const detailsMap = new Map<ResourceType, ResourceOverage>()

    for (const overage of resourceOverages) {
      if (overage.overageAmount > 0) {
        overageMap.set(overage.resourceType, overage.overageAmount)
        detailsMap.set(overage.resourceType, overage)
      }
    }

    return new OverageResultImpl(overageMap, detailsMap)
  }

  /**
   * Static factory method to create empty result
   */
  static empty(): OverageResult {
    return new OverageResultImpl(new Map())
  }
}

/**
 * Input for overage calculation
 */
export interface OverageCalculationInput {
  plan: {
    getAllResourceLimits(): Array<{
      getProps(): {
        resourceType: ResourceType
        includedQuantity: number
        overageStripePriceId: string | null
      }
      calculateOverage(actualUsage: number): number
    }>
  }
  currentUsage: ResourceUsageMap
  billingPeriod: BillingPeriod
  organizationId: string
  subscriptionItemId?: string | null
}

/**
 * Usage event record for overage system
 */
export interface UsageEventRecord {
  usageType: string // Generated: "MEMBER_OVERAGE", "TEAM_OVERAGE", etc.
  usageValue: number
  organizationId: string
  subscriptionItemId?: string | null
  usageTimestamp: Date
  billingCycleStartDate?: Date | null
  billingCycleEndDate?: Date | null
  stripePriceId?: string | null
  notes?: string
}

// Backward compatibility exports
export { OverageResult as DynamicOverageResult }
export { OverageResultImpl as DynamicOverageResultImpl }
export { OverageCalculationInput as DynamicOverageCalculationInput }
export { UsageEventRecord as DynamicUsageEventRecord }
