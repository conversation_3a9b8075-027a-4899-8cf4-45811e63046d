import { Injectable } from '@nestjs/common'
import Redis from 'ioredis'

import { De<PERSON>ultLogger, ILogger } from '../logger'

import { redisConnection } from './redis-connection'

export const subscriber = new Redis(redisConnection)

@Injectable()
export class RedisClient {
  private readonly client: Redis
  private readonly logger: ILogger = DefaultLogger.getInstance('RedisClient')

  constructor() {
    this.client = new Redis({
      host: redisConnection.host,
      port: redisConnection.port,
      // Non-stop retrying, wait longer each times, max wait time is 1min.
      // DOES NOT STOP
      retryStrategy: (times) => {
        this.logger.warn({
          message: `Redis connection retry attempt: ${times}`,
          level: 'warn',
          timestamp: new Date().toISOString(),
        })
        return Math.min(times * 1000, 60000)
      },
      reconnectOnError: (err) => {
        const targetError = 'READONLY'
        // https://github.com/redis/ioredis#auto-reconnect
        if (err.message.includes(targetError)) {
          this.logger.error(err.message)
          return true
        }
        return false
      },
      // These are both default option, leave here for future reference
      enableOfflineQueue: false,
      enableAutoPipelining: false,
    })

    this.client.on('connect', () => {
      this.logger.log({
        message: 'Redis client connected',
        level: 'info',
        timestamp: new Date().toISOString(),
      })
    })

    this.client.on('reconnecting', () => {
      this.logger.warn({
        message: 'Redis client reconnecting',
        level: 'warn',
        timestamp: new Date().toISOString(),
      })
    })

    this.client.on('ready', () => {
      this.logger.log({
        message: 'Redis client reconnected successfully',
        level: 'info',
        timestamp: new Date().toISOString(),
      })
    })
  }

  retryOp = async (operation: any, retries = 3, delay = 500) => {
    let attempt = 0
    while (attempt < retries) {
      try {
        return await operation()
      } catch (err: any) {
        attempt++
        if (attempt >= retries) {
          this.logger.error(err)
        }
        await new Promise((resolve) => setTimeout(resolve, delay * attempt))
      }
    }
  }

  async connect(): Promise<void> {
    await this.client.connect()
  }

  async disconnect(): Promise<void> {
    await this.client.quit()
  }

  async setWithExpiry(key: string, value: any, expiry: number): Promise<'OK'> {
    return this.retryOp(async () => {
      return await this.client.set(`${key}`, value, 'EX', expiry)
    })
  }

  async get(key: string, prefix: string): Promise<unknown | null> {
    return await this.retryOp(async () => {
      const data = await this.client.get(`${prefix}:${key}`)
      if (!data) return null
      try {
        return JSON.parse(data) as string[]
      } catch (error) {
        return null
      }
    })
  }

  async set(
    key: string,
    prefix: string,
    value: any,
    expire?: number,
  ): Promise<'OK'> {
    return await this.retryOp(async () => {
      if (expire) {
        return await this.setWithExpiry(
          `${prefix}:${key}`,
          JSON.stringify(value),
          expire,
        )
      } else {
        return await this.client.set(`${prefix}:${key}`, JSON.stringify(value))
      }
    })
  }

  async delete(key: string): Promise<number> {
    return await this.retryOp(async () => {
      return await this.client.del(key)
    })
  }

  async find(key: string): Promise<string[] | null> {
    const data = await this.retryOp(async () => {
      return await this.client.get(key)
    })
    if (!data) return null
    try {
      return JSON.parse(data) as string[]
    } catch (error) {
      return null
    }
  }

  async create(key: string, value: any): Promise<'OK'> {
    return await this.retryOp(async () => {
      return await this.client.set(key, JSON.stringify(value))
    })
  }

  async publish(channel: string, message: string): Promise<number> {
    return this.client.publish(channel, message)
  }

  async lpush(key: string, value: any): Promise<void> {
    await this.client.lpush(`${key}`, value)
  }

  async rpoplpush(key: string, prefix: string): Promise<string | null> {
    try {
      const fullKey = `${prefix}:${key}`
      const returnValue = this.client.rpoplpush(fullKey, fullKey)
      return returnValue
    } catch (error) {
      this.logger.error('Error in rpoplpush method:', error as Error)
      return null
    }
  }

  async setHash(key: string, field: string, value: string): Promise<number> {
    return await this.client.hset(key, field, value)
  }

  async getHash(key: string, field: string): Promise<string | null> {
    return await this.client.hget(key, field)
  }

  async getHashAll(key: string) {
    return await this.client.hgetall(key)
  }

  async healthCheck() {
    try {
      const pong = await this.client.ping()
      if (pong !== 'PONG') {
        throw new Error('Redis did not respond with PONG')
      }
      return {
        redis: {
          status: 'up',
        },
      }
    } catch (error: any) {
      return {
        redis: {
          status: 'down',
          error: error.message,
        },
      }
    }
  }
}

export const REDIS_CLIENT = 'REDIS_CLIENT'
