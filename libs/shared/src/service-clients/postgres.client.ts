import { Injectable, OnModuleD<PERSON>roy } from '@nestjs/common'
import { Pool, QueryResult } from 'pg'

import { configService } from '../constants/config.service'
import { DefaultLogger, ILogger } from '../logger'
@Injectable()
export class PostgresClient implements OnModuleDestroy {
  private pool: Pool
  private readonly maxRetries: number = 10
  private retryDelay = 6000
  private readonly logger: ILogger = DefaultLogger.getInstance('PostgresClient')

  constructor() {
    const dataSource = configService.getDataSource()
    this.pool = new Pool({
      user: dataSource.username,
      host: dataSource.host,
      database: dataSource.database,
      password: dataSource.password,
      port: dataSource.port,
      max: 20,
      min: 4,
      idleTimeoutMillis: 300000, // 5 minutes
      connectionTimeoutMillis: 30000, // 30 seconds
      keepAlive: true,
      allowExitOnIdle: false,
    })

    // Enhanced connection state tracking
    this.pool.on('error', (err, client) => {
      this.logger.error({
        message: 'Postgres connection error',
        error: err.stack,
        level: 'error',
        timestamp: new Date().toISOString(),
        state: 'disconnected',
      })
    })

    this.pool.on('connect', (client) => {
      this.logger.log({
        message: 'Postgres client connected to pool',
        level: 'info',
        timestamp: new Date().toISOString(),
        state: 'connected',
      })
    })

    this.pool.on('remove', () => {
      this.logger.warn({
        message: 'Postgres client removed from pool',
        level: 'warn',
        timestamp: new Date().toISOString(),
        state: 'disconnected',
      })
    })

    // Add periodic health check
    setInterval(() => {
      this.pool.query('SELECT 1').catch((err) => {
        this.logger.error({
          message: 'Postgres health check failed',
          error: err.stack,
          level: 'error',
          timestamp: new Date().toISOString(),
          state: 'disconnected',
        })
      })
    }, 30000) // 30 seconds

    this.connect()
  }

  public getClient() {
    return this.pool
  }

  async onModuleDestroy() {
    await this.closeConnection()
  }

  private async connect(retryCount = 0) {
    try {
      await this.pool.query('SELECT 1')
      this.logger.log({
        message: `Connected to PostgreSQL pool [${this.constructor.name}]`,
        level: 'info',
        timestamp: new Date().toISOString(),
      })
    } catch (err: any) {
      this.logger.warn({
        message: `Connection error (attempt ${retryCount + 1} of ${this.maxRetries})`,
        error: err.stack,
        level: 'warn',
        timestamp: new Date().toISOString(),
      })
      if (retryCount < this.maxRetries - 1) {
        setTimeout(() => this.connect(retryCount + 1), this.retryDelay)
      } else {
        this.logger.error(
          `Unable to connect to PostgreSQL after ${this.maxRetries} attempts`,
        )
      }
    }
  }

  async closeConnection() {
    try {
      await this.pool.end()
      this.logger.log('Disconnected from PostgreSQL')
    } catch (err: any) {
      this.logger.error('Error closing connection', err.stack)
    }
  }

  async query(query: string, params: any[] = []): Promise<QueryResult> {
    let client
    try {
      client = await this.pool.connect()
      return await client.query(query, params)
    } catch (err: any) {
      this.logger.error({
        message: 'Postgres query error',
        error: (err as any).stack,
        level: 'error',
        timestamp: new Date().toISOString(),
      })
      throw err
    } finally {
      if (client) {
        client.release()
      }
    }
  }

  async getValueByKey(
    query: string,
    key: string,
  ): Promise<QueryResult<any> | null> {
    try {
      return await this.query(query, [key])
    } catch (err: any) {
      this.logger.error('Query error', err.stack)
      return null
    }
  }

  async getValues(
    select: string,
    from: string,
    id: string,
  ): Promise<any | null> {
    try {
      // Code from the internet
      const query = `SELECT ${select} FROM ${from} WHERE id = $1`
      const res = await this.query(query, [id])
      if (!res || res.rows.length === 0) return null
      const result: { [key: string]: any } = {}
      const row = res.rows[0]
      for (const column of select.split(',')) {
        result[column.trim()] = row[column.trim()]
      }
      return result
    } catch (err: any) {
      this.logger.error('Query error', err.stack)
      return null
    }
  }

  async getFields() {
    const query = `SELECT * FROM checks;`
    const res = await this.query(query)

    this.logger.log(res.rows)
  }

  async healthCheck() {
    try {
      const res = await this.pool.query('SELECT 1')
      if (res.rows[0] && res.rows[0]['?column?'] === 1) {
        return {
          postgres: {
            status: 'up',
          },
        }
      } else {
        throw new Error('Unexpected response from PostgreSQL')
      }
    } catch (error: any) {
      return {
        postgres: {
          status: 'down',
          error: error.message,
        },
      }
    }
  }
}
