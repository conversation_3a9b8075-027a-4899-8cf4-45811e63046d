import { Injectable } from '@nestjs/common'
import { Queue, Job, QueueOptions } from 'bullmq'

import { redisConnection } from './redis-connection'

@Injectable()
export class BullClient {
  private queues: Map<string, Queue> = new Map()

  private readonly defaultQueueOptions: QueueOptions = {
    connection: redisConnection,
    defaultJobOptions: {
      removeOnComplete: true,
      removeOnFail: 5,
    },
  }

  private initializeQueue(queueName: string): Queue {
    const queue = new Queue(`${queueName}`, this.defaultQueueOptions)
    this.queues.set(queueName, queue)
    return queue
  }

  private getQueue(queueName: string) {
    let queue = this.queues.get(queueName)
    if (!queue) queue = this.initializeQueue(queueName)
    return queue
  }
  //
  // Basic Job section
  //
  async addJob(queue: string, name: string, data: object): Promise<Job> {
    return this.getQueue(queue).add(name, data)
  }

  async listJobs(queue: string): Promise<Job[]> {
    return this.getQueue(queue).getJobs(['active'])
  }

  async getJob(queue: string, jobName: string): Promise<Job | undefined> {
    return this.getQueue(queue).getJob(jobName)
  }

  async deleteJob(queue: string, jobName: string): Promise<void> {
    await this.getQueue(queue).remove(jobName)
  }

  async deleteRepeatableJob(queue: string, jobName: string): Promise<boolean> {
    const repeatableJobs = await this.getQueue(queue).getRepeatableJobs()
    const foundJob = repeatableJobs.find((job) => job.name === jobName)
    if (!foundJob) return false
    return await this.getQueue(queue).removeRepeatableByKey(foundJob.key)
  }

  async addRepeatedJobPatternWithStartDate(
    queue: string,
    jobName: string,
    data: any,
    cronExpression: string,
    startDate: Date,
  ): Promise<Job> {
    const job = await this.getQueue(queue).add(jobName, data, {
      repeat: {
        pattern: cronExpression,
        startDate: startDate,
      },
      removeOnComplete: true,
    })
    return job
  }
}

export const BULL_CLIENT = 'BULL_CLIENT'
