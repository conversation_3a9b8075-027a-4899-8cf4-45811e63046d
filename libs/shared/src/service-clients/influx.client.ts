import { Injectable } from '@nestjs/common'
import { InfluxDBClient, Point } from '@influxdata/influxdb3-client'

import { configService } from '../constants/config.service'
import { DefaultLogger, ILogger } from '../logger'

@Injectable()
export class InfluxClient {
  private client: InfluxDBClient
  private readonly logger: ILogger = DefaultLogger.getInstance('InfluxClient')

  constructor() {
    const influxConfig = configService.getInfluxConfig()
    this.client = new InfluxDBClient({
      host: influxConfig.url,
      token: influxConfig.token,
    })
  }

  async write(
    measurement: string,
    tags: { [key: string]: string },
    fields: { [key: string]: number | string },
    database: string,
  ): Promise<void> {
    try {
      const point = Point.measurement(measurement)
      for (const [tagName, tagValue] of Object.entries(tags)) {
        point.setTag(tagName, tagValue)
      }
      for (const [fieldName, fieldValue] of Object.entries(fields)) {
        if (typeof fieldValue === 'number') {
          point.setFloatField(fieldName, fieldValue)
        } else if (typeof fieldValue === 'string') {
          point.setStringField(fieldName, fieldValue)
        }
      }
      return await this.client.write(point, database)
    } catch (err: any) {
      this.logger.error('Write to InfluxDB error', err.stack)
      return
    }
  }
}
