import { join } from 'lodash'

export function formatDate(date?: Date) {
  if (!date) return ''
  return date.toISOString()
}

// input object, output string format key:value, key2:value2
export const stringifyHeaders = (headers: object): string => {
  return join(
    Object.entries(headers).map((d) => d.join('=')),
    ', ',
  )
}

export const parseHeaders = (headerString?: string): Record<string, string> => {
  if (!headerString) return {}
  return headerString.split(', ').reduce(
    (acc, pair) => {
      const [key, value] = pair.split('=')
      acc[key] = value
      return acc
    },
    {} as Record<string, string>,
  )
}
