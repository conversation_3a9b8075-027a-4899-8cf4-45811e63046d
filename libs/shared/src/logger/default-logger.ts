// Import required modules and configuration
import * as winston from 'winston'
import { utilities as nestWinstonModuleUtilities } from 'nest-winston'

import { format } from 'util'

import { ILogger } from './logger.interface'

// Define formats similar to backend implementation
export const devFormat = winston.format.combine(
  winston.format.errors({ stack: true }),
  winston.format.timestamp(),
  winston.format.ms(),
  nestWinstonModuleUtilities.format.nestLike('SharedLib', {
    colors: process.env['NODE_ENV'] !== 'production',
    prettyPrint: true,
  }),
)

export const prodFormat = winston.format.combine(
  winston.format.uncolorize(),
  winston.format.json(),
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
)

// Logger configuration
export const loggerConfig = {
  format: process.env['NODE_ENV'] === 'production' ? prodFormat : devFormat,
  transports: [
    new winston.transports.Console({
      format: process.env['NODE_ENV'] === 'production' ? prodFormat : devFormat,
    }),
  ],
}

export class DefaultLogger implements ILogger {
  private static instance: DefaultLogger | null = null
  protected logger: winston.Logger
  private context: string

  constructor(context = 'Default') {
    this.context = context
    this.logger = winston.createLogger(loggerConfig)
  }

  // Singleton pattern implementation
  public static getInstance(context = 'Default'): DefaultLogger {
    if (!DefaultLogger.instance) {
      DefaultLogger.instance = new DefaultLogger(context)
    }
    return DefaultLogger.instance
  }

  // Factory method to create loggers with different contexts
  public static createLogger(context: string): ILogger {
    const instance = DefaultLogger.getInstance()

    return {
      log: (message: unknown, ctx?: string) =>
        instance.log(message, ctx || context),
      error: (message: unknown, trace?: string | Error) =>
        instance.error(message, trace),
      warn: (message: unknown, ctx?: string) =>
        instance.warn(message, ctx || context),
      debug: (message: unknown) => instance.debug(message),
      verbose: (message: unknown) => instance.verbose(message),
    }
  }

  private formatMessage(message: unknown, context?: string) {
    if (typeof message === 'object') {
      return {
        ...message,
        context: context || this.context,
        timestamp: new Date().toISOString(),
      }
    }
    return {
      message: format(message),
      context: context || this.context,
      timestamp: new Date().toISOString(),
    }
  }

  log(message: unknown, context?: string): void {
    this.logger.info(this.formatMessage(message, context))
  }

  error(message: unknown, trace?: string | Error): void {
    const formatted = this.formatMessage(message)
    if (trace && formatted) {
      ;(formatted as any).error =
        typeof trace === 'string' ? trace : trace.stack
    }
    this.logger.error(formatted)
  }

  warn(message: unknown, context?: string): void {
    this.logger.warn(this.formatMessage(message, context))
  }

  debug(message: unknown): void {
    this.logger.debug(this.formatMessage(message))
  }

  verbose(message: unknown): void {
    this.logger.verbose(this.formatMessage(message))
  }
}

// Export a default instance for direct use
export const Logger = DefaultLogger.getInstance()
