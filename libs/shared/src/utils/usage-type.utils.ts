/**
 * Usage Type Utilities
 *
 * Provides utilities for generating and manipulating usage types.
 * This enables the system to handle any resource type without hardcoded enums.
 */

import { ResourceType } from '../constants/subscription/resource-mapping'
import { UsageType } from '../constants/subscription/usage-type.enum'

export class UsageTypeUtils {
  /**
   * Generate overage usage type enum value for a given resource type
   * Example: ResourceType.MEMBER -> UsageType.MEMBER_OVERAGE
   */
  static generateOverageUsageType(resourceType: ResourceType): UsageType {
    const usageTypeString = `${resourceType}_OVERAGE`

    // Map to actual enum values for type safety
    switch (usageTypeString) {
      case 'MEMBER_OVERAGE':
        return UsageType.MEMBER_OVERAGE
      case 'TEAM_OVERAGE':
        return UsageType.TEAM_OVERAGE
      case 'CHECK_OVERAGE':
        return UsageType.CHECK_OVERAGE
      case 'INTEGRATION_OVERAGE':
        return UsageType.INTEGRATION_OVERAGE
      case 'STATUS_PAGE_OVERAGE':
        return UsageType.STATUS_PAGE_OVERAGE
      case 'SMS_OVERAGE':
        return UsageType.SMS_OVERAGE
      case 'PHONE_CALL_OVERAGE':
        return UsageType.PHONE_CALL_OVERAGE
      default:
        throw new Error(
          `Unsupported resource type for overage: ${resourceType}. Add ${usageTypeString} to UsageType enum.`,
        )
    }
  }

  /**
   * Check if a usage type string represents an overage type
   * Example: "MEMBER_OVERAGE" -> true, "SMS" -> false
   */
  static isOverageUsageType(usageType: string): boolean {
    return usageType.endsWith('_OVERAGE')
  }

  /**
   * Extract resource type from overage usage type enum
   * Example: UsageType.MEMBER_OVERAGE -> ResourceType.MEMBER
   */
  static extractResourceTypeFromOverageUsage(
    usageType: UsageType | string,
  ): ResourceType | null {
    const usageTypeString =
      typeof usageType === 'string' ? usageType : String(usageType)

    if (!this.isOverageUsageType(usageTypeString)) {
      return null
    }

    const resourceTypeString = usageTypeString.replace(
      '_OVERAGE',
      '',
    ) as keyof typeof ResourceType

    // Validate that the extracted string is a valid ResourceType
    if (
      Object.values(ResourceType).includes(resourceTypeString as ResourceType)
    ) {
      return resourceTypeString as ResourceType
    }

    return null
  }

  /**
   * Get all possible overage usage types for all defined resource types
   */
  static getAllOverageUsageTypes(): UsageType[] {
    return Object.values(ResourceType).map((resourceType) =>
      this.generateOverageUsageType(resourceType),
    )
  }

  /**
   * Validate that a usage type follows the expected overage pattern
   */
  static isValidOverageUsageType(usageType: string): boolean {
    const extractedResourceType =
      this.extractResourceTypeFromOverageUsage(usageType)
    return extractedResourceType !== null
  }

  /**
   * Get all transactional (non-overage) usage types
   * These are globally priced usage types like SMS, PHONE_CALL
   */
  static getTransactionalUsageTypes(): string[] {
    return ['SMS', 'PHONE_CALL']
  }

  /**
   * Check if a usage type is transactional (not resource overage)
   */
  static isTransactionalUsageType(usageType: string): boolean {
    return this.getTransactionalUsageTypes().includes(usageType)
  }

  /**
   * Categorize usage types into transactional and overage categories
   */
  static categorizeUsageTypes(usageTypes: string[]): {
    transactional: string[]
    overage: string[]
    unknown: string[]
  } {
    const transactional: string[] = []
    const overage: string[] = []
    const unknown: string[] = []

    for (const usageType of usageTypes) {
      if (this.isTransactionalUsageType(usageType)) {
        transactional.push(usageType)
      } else if (this.isValidOverageUsageType(usageType)) {
        overage.push(usageType)
      } else {
        unknown.push(usageType)
      }
    }

    return { transactional, overage, unknown }
  }
}
