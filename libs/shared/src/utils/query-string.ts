import queryString = require('query-string')
import { type StringifiableRecord, type ParsedQuery } from 'query-string'

export function parseQueryParams<T = ParsedQuery>(locationHash = '') {
  return queryString.parse(locationHash) as T
}

export const stringifyQueryParams = (params: StringifiableRecord = {}) => {
  return queryString.stringify(params, {
    skipEmptyString: true,
  })
}

export const joinParams = (url: string, params: StringifiableRecord = {}) => {
  if (!params) return url
  return `${url}?${stringifyQueryParams(params)}`
}
