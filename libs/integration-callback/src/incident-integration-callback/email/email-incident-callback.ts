import EmailCallback from '@libs/integration-callback/providers/email'
import { MailEvent } from '@libs/integration-callback/providers/email/types'
import {
  EMAIL_ELEMENT_ID,
  EmailCustomPayload,
} from '@libs/shared/email/constants'

import {
  IncidentIntegrationCallbackAction,
  IncidentIntegrationCallbackConfig,
} from '../types'

class EmailIncidentCallback {
  private action: IncidentIntegrationCallbackAction
  private _parser: EmailCallback

  constructor({ action }: IncidentIntegrationCallbackConfig<'email'>) {
    this.action = action
    this._parser = new EmailCallback()
  }

  async on(event: MailEvent) {
    const eventData = this._parser.parse<EmailCustomPayload>(event)

    const { elementId, incidentId, userId } = eventData.payload
    if (!incidentId) return

    switch (eventData.eventType) {
      case 'click':
        if (elementId === EMAIL_ELEMENT_ID.ACKNOWLEDGE_BUTTON) {
          await this.action?.acknowledgeIncident?.({ incidentId, userId })
          return
        }

        break
      case 'open':
        await this.action?.viewIncident?.({ incidentId, userId })
        break
      default:
        throw new Error(`Unsupported event type: ${eventData.eventType}`)
    }
  }
}

export default EmailIncidentCallback
