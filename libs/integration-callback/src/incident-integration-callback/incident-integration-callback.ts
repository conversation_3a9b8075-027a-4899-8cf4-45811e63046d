import { IntegrationCallbackProviderType } from '../@type/integration-callback.types'
import { PlivoProvider } from '../providers/plivo/plivo.provider' // Added import

import { IncidentIntegrationCallbackConfig } from './types'
import SlackIncidentCallback from './slack/slack-incident-callback'
import VoiceCallIncidentCallback from './voice-call/voice-call-incident-callback'
import EmailIncidentCallback from './email/email-incident-callback'

type IncidentIntegrationCallbackProvider = {
  slack: SlackIncidentCallback
  voice_call: VoiceCallIncidentCallback
  email: EmailIncidentCallback
  plivo: PlivoProvider // Added plivo provider type
}

class IncidentIntegrationCallback<P extends IntegrationCallbackProviderType> {
  private _provider: IncidentIntegrationCallbackProvider[P]
  constructor(providerType: P, config?: IncidentIntegrationCallbackConfig<P>) {
    switch (providerType) {
      case 'slack':
        if (!config) throw new Error('Slack config is required')
        this._provider = new SlackIncidentCallback(
          config as IncidentIntegrationCallbackConfig<'slack'>,
        ) as IncidentIntegrationCallbackProvider[P]
        break
      case 'voice_call': {
        this._provider = new VoiceCallIncidentCallback(
          config as IncidentIntegrationCallbackConfig<'voice_call'>,
        ) as IncidentIntegrationCallbackProvider[P]
        break
      }
      case 'email': {
        this._provider = new EmailIncidentCallback(
          config as IncidentIntegrationCallbackConfig<'email'>,
        ) as IncidentIntegrationCallbackProvider[P]
        break
      }
      case 'plivo': {
        if (!config?.action) throw new Error('Plivo actions config is required')
        this._provider = new PlivoProvider(
          config.action,
        ) as IncidentIntegrationCallbackProvider[P]
        break
      }
      default:
        throw new Error(`Unsupported provider: ${providerType}`)
    }
  }

  get provider() {
    return this._provider
  }
}

export default IncidentIntegrationCallback
