import { SLACK_ELEMENT_ID } from '@libs/shared/slack/constants'

import { IncidentIntegrationCallbackAction } from '../../types'
import { SlackIncidentCallbackConfig, SlackMiddleListener } from '../types'
import {
  fetchUserEmail,
  getActionValueById,
  insertBlockAfter,
  removeButtonBlock,
  updateBlockContent,
} from '../utils'

import { checkIfIncidentResolved } from './shared'

const updateButtonText =
  (action: IncidentIntegrationCallbackAction): SlackMiddleListener =>
  async ({ body, respond, client }) => {
    const { channel, message, user } = body

    if (!channel || !message?.ts) {
      throw new Error('Missing channel or ts (timestamp) for the message.')
    }

    const email = await fetchUserEmail(client, user.id)

    if (action?.acknowledgeIncident) {
      const value = getActionValueById<{ incidentId: string }>(
        body.actions,
        SLACK_ELEMENT_ID.ACKNOWLEDGE_BUTTON,
      )

      if (value?.incidentId) {
        await action?.acknowledgeIncident({
          incidentId: value.incidentId,
          email,
        })
      }
    }

    await respond({
      ...message,
      ...updateBlockContent(message, (blocks) =>
        insertBlockAfter(
          removeButtonBlock(
            blocks,
            SLACK_ELEMENT_ID.ACTION_BLOCK,
            SLACK_ELEMENT_ID.ACKNOWLEDGE_BUTTON,
          ),
          SLACK_ELEMENT_ID.ACTION_BLOCK,
          {
            type: 'context',
            elements: [
              {
                type: 'mrkdwn',
                text: `Incidet acknowledged by ${user.username} (${email})`,
              },
            ],
          },
        ),
      ),
      replace_original: true,
    })
  }

const AcknowledgeButton: SlackIncidentCallbackConfig = {
  event: {
    action_id: SLACK_ELEMENT_ID.ACKNOWLEDGE_BUTTON,
  },
  listeners: (action: IncidentIntegrationCallbackAction) => [
    checkIfIncidentResolved(action, SLACK_ELEMENT_ID.ACKNOWLEDGE_BUTTON),
    updateButtonText(action),
  ],
}

export default AcknowledgeButton
