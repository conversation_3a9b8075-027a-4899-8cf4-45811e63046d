import { SLACK_ELEMENT_ID } from '@libs/shared/slack/constants'
import { IncidentStatus } from '@libs/shared/constants/incident'

import { IncidentIntegrationCallbackAction } from '../../types'
import { SlackMiddleListener } from '../types'
import { getActionValueById, updateBlockContent } from '../utils'

const replaceActionsByResolvedBlocks = (message) => {
  return updateBlockContent(message, (blocks) =>
    blocks.map((block) => {
      if (block.block_id === SLACK_ELEMENT_ID.ACTION_BLOCK) {
        return {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `:sparkles: This incident was already resolved :sparkles:`,
            },
          ],
        }
      }
      if (block.block_id === SLACK_ELEMENT_ID.PLEASE_ACKNOWLEDGE) {
        return null
      }
      return block
    }),
  )
}

export const checkIfIncidentResolved =
  (
    action: IncidentIntegrationCallbackAction,
    valueId: string,
  ): SlackMiddleListener =>
  async ({ ack, body, respond, next }) => {
    await ack()

    const { channel, message } = body

    if (!channel || !message?.ts) {
      throw new Error('Missing channel or ts (timestamp) for the message.')
    }

    if (action?.getIncidentStatus) {
      const value = getActionValueById<{ incidentId: string }>(
        body.actions,
        valueId,
      )

      if (value?.incidentId) {
        const status = await action?.getIncidentStatus(value.incidentId)
        if (status === IncidentStatus.RESOLVED) {
          // Update the message with a new block

          await respond({
            ...message,
            ...replaceActionsByResolvedBlocks(message),
            replace_original: true,
          })
          return
        }
      }

      await next()
    }
  }
