import { SLACK_ELEMENT_ID } from '@libs/shared/slack/constants'

import { IncidentIntegrationCallbackAction } from '../../types'
import { SlackIncidentCallbackConfig, SlackMiddleListener } from '../types'
import { getActionValueById } from '../utils'

import { checkIfIncidentResolved } from './shared'

const triggerEscalate =
  (action: IncidentIntegrationCallbackAction): SlackMiddleListener =>
  async ({ body }) => {
    if (action?.escalate) {
      const value = getActionValueById<{ incidentId: string }>(
        body.actions,
        SLACK_ELEMENT_ID.ACKNOWLEDGE_BUTTON,
      )

      if (value?.incidentId) {
        await action?.escalate(value.incidentId)
      }
    }
  }

const EscalateButton: SlackIncidentCallbackConfig = {
  event: {
    action_id: SLACK_ELEMENT_ID.ESCALATE_BUTTON,
  },
  listeners: (action: IncidentIntegrationCallbackAction) => [
    checkIfIncidentResolved(action, SLACK_ELEMENT_ID.ESCALATE_BUTTON),
    triggerEscalate(action),
  ],
}

export default EscalateButton
