import SlackCallback from '@libs/integration-callback/providers/slack'

import {
  IncidentIntegrationCallbackAction,
  IncidentIntegrationCallbackConfig,
} from '../types'

import AcknowledgeButton from './events/acknowledge-button'
import EscalateButton from './events/escalate-button'

class SlackIncidentCallback {
  private action: IncidentIntegrationCallbackAction
  private listener: SlackCallback
  constructor({
    action,
    botToken,
    signingSecret,
  }: IncidentIntegrationCallbackConfig<'slack'>) {
    this.listener = new SlackCallback({
      botToken,
      signingSecret,
    })
    this.action = action
    this.setupEvents()
  }

  private async setupEvents() {
    // Register all the listeners
    ;[AcknowledgeButton, EscalateButton].forEach(({ event, listeners }) => {
      this.listener.listenAction(event, ...listeners(this.action))
    })
  }

  start(port: number) {
    console.log('Starting Slack Incident Callback port', port)
    return this.listener.start(port)
  }

  get handler() {
    return this.listener.handler
  }
}

export default SlackIncidentCallback
