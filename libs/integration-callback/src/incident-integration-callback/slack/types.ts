/* eslint-disable @typescript-eslint/no-explicit-any */
import { BlockAction, Middleware, SlackActionMiddlewareArgs } from '@slack/bolt'

import { SlackCallbackAction } from '../../providers/slack'
import { IncidentIntegrationCallbackAction } from '../types'

type ParamAt<T, N extends number> = T extends (...args: infer P) => any
  ? P[N]
  : never

export type SlackMiddleListener = Middleware<
  SlackActionMiddlewareArgs<BlockAction>
>
export interface SlackIncidentCallbackConfig {
  event: ParamAt<SlackCallbackAction, 0>
  listeners: (
    handler: IncidentIntegrationCallbackAction,
  ) => SlackMiddleListener[] // call next middleware function in the chain
}
