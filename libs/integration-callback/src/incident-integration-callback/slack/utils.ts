import { ActionsBlockElement, KnownBlock, WebClient } from '@slack/web-api'
import cloneDeep from 'lodash/cloneDeep'

/* eslint-disable @typescript-eslint/no-explicit-any */
export function updateActionBlock(
  blocks: KnownBlock[],
  {
    block_id,
    action_id,
    replacement,
  }: {
    block_id: string
    action_id?: string
    replacement: (
      _: ActionsBlockElement | ActionsBlockElement[],
    ) => ActionsBlockElement | ActionsBlockElement[] | null
  },
) {
  return blocks.map((block) => {
    if (block.type === 'actions' && block.block_id === block_id) {
      // Update the text of the button
      if (action_id) {
        block.elements = block.elements
          .map((element) => {
            if (
              replacement &&
              element.type === 'button' &&
              element.action_id === action_id
            ) {
              return replacement(element)
            }
            return element
          })
          .filter(Boolean) as ActionsBlockElement[]
      } else if (replacement) {
        block.elements = replacement(block.elements) as ActionsBlockElement[]
      }
    }
    return block
  })
}

export function getActionValueById<T>(
  actions: any[],
  actionId: string,
): T | undefined {
  const found = actions.find((action) => action?.action_id === actionId)
  try {
    if (!found?.value) return
    const parsed = JSON.parse(found?.value)
    return parsed
  } catch (e) {
    return found?.value
  }
}

export const removeButtonBlock = (
  blocks: KnownBlock[],
  block_id: string,
  action_id: string,
) => {
  return updateActionBlock(blocks, {
    block_id,
    action_id,
    replacement: () => null,
  })
}

export const fetchUserEmail = async (
  client: WebClient,
  userId: string,
  defaultValue = '',
) => {
  try {
    const { user } = await client.users.info({
      user: userId,
    })
    return user?.profile?.email
  } catch (e) {
    console.error('Failed to fetch user email:', e)
    return defaultValue
  }
}

export const insertBlockAfter = (
  blocks: KnownBlock[],
  block_id: string,
  newBlock: KnownBlock,
) => {
  const index = blocks.findIndex((block) => block.block_id === block_id)
  if (index === -1) {
    return blocks
  }

  return [...blocks.slice(0, index + 1), newBlock, ...blocks.slice(index + 1)]
}

export const updateBlockContent = (
  payload: any,
  replacement: (blocks: any) => any,
) => {
  const updatedPayload = cloneDeep(payload) // Create a deep clone to avoid mutating the original payload

  if (updatedPayload.blocks) {
    updatedPayload.blocks = replacement(updatedPayload.blocks)?.filter?.(
      Boolean,
    )
  }

  if (updatedPayload.attachments) {
    updatedPayload.attachments = updatedPayload.attachments.map(
      (attachment: any) => {
        attachment.blocks = replacement(attachment.blocks).filter?.(Boolean)

        return attachment
      },
    )
  }

  return updatedPayload
}
