/* eslint-disable @typescript-eslint/no-explicit-any */

import { IncidentStatus } from '@libs/shared/constants/incident'
import { IncidentEventInterface } from '@libs/shared/constants/shared.interface'

import {
  IntegrationCallbackProviderType,
  LogIncidentEventPayload,
} from '../@type/integration-callback.types'
import { SlackCallbackConfig } from '../providers/slack'

export type IntegrationCallbackConfigMap = {
  slack: SlackCallbackConfig
  voice_call: unknown
  email: unknown
  plivo: unknown // Added plivo config type
}

export type IncidentIntegrationCallbackAction = Partial<{
  getIncidentStatus: (incidentId: string) => Promise<IncidentStatus>
  logIncidentEvent: (payload: LogIncidentEventPayload) => Promise<void> | void // Added for logging
  acknowledgeIncident: (_: {
    incidentId: string
    userId?: string
    email?: string
  }) => Promise<void>
  escalate: (incidentId: string) => Promise<void>
  viewIncident: (_: {
    incidentId: string
    userId?: string
    email?: string
  }) => Promise<void>
  addIncidentEvent: (
    incidentEventPayload: IncidentEventInterface,
  ) => Promise<void>
}>

export type IncidentIntegrationCallbackConfig<
  P extends IntegrationCallbackProviderType = IntegrationCallbackProviderType,
> = IntegrationCallbackConfigMap[P] & {
  action: IncidentIntegrationCallbackAction
}
