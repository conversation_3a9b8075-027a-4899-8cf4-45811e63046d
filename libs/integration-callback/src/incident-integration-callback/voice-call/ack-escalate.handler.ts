import { IncidentEventInterface } from '@libs/shared/constants/shared.interface'
import { v4 as uuid } from 'uuid'
import { IncidentEventDetailType } from '@libs/shared/constants/incident'

import { IVRInputPayload } from './voice_call.interface'

class AckEscalateHandler {
  public async payloadMapper(payload: IVRInputPayload) {
    let eventType: string
    switch (payload.dtmf) {
      case '1':
        eventType = IncidentEventDetailType.ACKNOWLEDGED_INCIDENT
        break
      case '2':
        eventType = IncidentEventDetailType.ESCALATED
        break
      default:
        eventType = payload.dtmf
        break // This shouldn't be possible
    }
    const customField =
      typeof payload.customField === 'string'
        ? JSON.parse(payload.customField)
        : payload.customField
    const mappedBody = {
      id: uuid(),
      incidentId: customField.incidentId,
      type: 'notification',
      user: {
        id: customField.userId,
        firstName: customField.userFirstname,
        lastName: customField.userLastname,
      },
      attribute: {
        type: eventType,
        value: {
          receiver: customField.userNumber,
          location: null,
        },
      },
      createdAt: new Date(),
    }
    return mappedBody as IncidentEventInterface
  }
}

export default AckEscalateHandler
