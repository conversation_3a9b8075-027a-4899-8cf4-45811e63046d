export interface VoiceCallStatusPayload {
  endCallCause?: string
  type?: string
  call_status: string
  from: {
    number: string
    alias: string
    is_online: boolean
    type: string
  }
  to: {
    number: string
    alias: string
    is_online: boolean
    type: string
  }
}

// Make sure this match with the format in notifier
export interface IVRInputPayload {
  time: string
  dtmf: string
  customField: AckEscalateCustomFieldInterface
}

interface AckEscalateCustomFieldInterface {
  incidentId: string
  userId: string
  userNumber: string
  userFirstname: string
  userLastname: string
  callBackUrl: string
}
