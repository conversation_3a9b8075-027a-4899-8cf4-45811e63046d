import { IncidentEventDetailType } from '@libs/shared/constants/incident'

import {
  IncidentIntegrationCallbackAction,
  IncidentIntegrationCallbackConfig,
} from '../types'

import CallStatusHandler from './call-status.handler'
import AckEscalateHandler from './ack-escalate.handler'
import { VoiceCallStatusPayload, IVRInputPayload } from './voice_call.interface'
import { IVRInputHandler } from './call-input.handler'

class VoiceCallIncidentCallback {
  private action: IncidentIntegrationCallbackAction

  private ackEscalateHandler: AckEscalateHandler
  private callStatusHandler: CallStatusHandler
  private callInputHandler: IVRInputHandler

  constructor({ action }: IncidentIntegrationCallbackConfig<'voice_call'>) {
    this.action = action
    this.ackEscalateHandler = new AckEscalateHandler()
    this.callStatusHandler = new CallStatusHandler()
    this.callInputHandler = new IVRInputHandler()
  }

  public async updateCallStatus(
    payload: VoiceCallStatusPayload,
  ): Promise<{ statusCode: number; body: string }> {
    try {
      const callStatusIncidentEventPayload =
        await this.callStatusHandler.payloadMapper(payload)
      if (this.action.addIncidentEvent)
        await this.action.addIncidentEvent(callStatusIncidentEventPayload)
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Voice call status updated' }),
      }
    } catch (error) {
      console.error('ERROR_CALLSTATUS', error)
      return {
        statusCode: 500,
        body: JSON.stringify({ message: `Internal server error' ${error}` }),
      }
    }
  }

  public async IVRInputHandler(
    payload: IVRInputPayload,
  ): Promise<{ statusCode: number; body: string }> {
    try {
      if (payload.dtmf == '1' || payload.dtmf == '2') {
        return await this.ackEscalateVoiceCall(payload)
      } else {
        const callStatusIncidentEventPayload =
          await this.callInputHandler.payloadMapperWrongInput(payload)
        return {
          statusCode: 200,
          body: callStatusIncidentEventPayload,
        }
      }
    } catch (error) {
      console.error('IVRInputHandler ERROR', error)
      return {
        statusCode: 500,
        body: JSON.stringify({ message: `Internal server error' ${error}` }),
      }
    }
  }

  private async ackEscalateVoiceCall(
    payload: IVRInputPayload,
  ): Promise<{ statusCode: number; body: string }> {
    try {
      const ackEscalateVoiceCallPayload =
        await this.ackEscalateHandler.payloadMapper(payload)

      console.log(ackEscalateVoiceCallPayload)
      console.log(this.action)
      if (
        ackEscalateVoiceCallPayload.attribute.type ==
          IncidentEventDetailType.ACKNOWLEDGED_INCIDENT &&
        this.action.acknowledgeIncident
      )
        await this.action.acknowledgeIncident({
          incidentId: ackEscalateVoiceCallPayload.incidentId,
          userId: ackEscalateVoiceCallPayload.user?.id,
        })
      else if (
        ackEscalateVoiceCallPayload.attribute.type ==
          IncidentEventDetailType.ESCALATED &&
        this.action.escalate
      )
        await this.action.escalate(ackEscalateVoiceCallPayload.incidentId)
      if (this.action.addIncidentEvent)
        await this.action.addIncidentEvent(ackEscalateVoiceCallPayload)
      return {
        statusCode: 200,
        body:
          payload.dtmf == '1'
            ? await this.callInputHandler.payloadMapperAck(payload)
            : await this.callInputHandler.payloadMapperEsc(payload),
      }
    } catch (error) {
      console.error('ACKESCALATE_ERROR:', error)
      return {
        statusCode: 500,
        body: JSON.stringify({ message: `Internal server error' ${error}` }),
      }
    }
  }
}

export default VoiceCallIncidentCallback
