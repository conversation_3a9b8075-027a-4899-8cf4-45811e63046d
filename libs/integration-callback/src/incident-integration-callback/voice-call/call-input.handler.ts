import { IVRInputPayload } from './voice_call.interface'

export class IVRInputHandler {
  public async payloadMapperWrongInput(
    payload: IVRInputPayload,
  ): Promise<string> {
    const customField =
      typeof payload.customField === 'string'
        ? JSON.parse(payload.customField)
        : payload.customField
    const mappedBody = JSON.stringify([
      {
        action: 'talk',
        text: 'Phím ấn không hợp lệ, vui lòng nhập lại. Ấn 1 để acknowledge incident, ấn 2 để escalate thông báo cho thành viên ở bươc tiếp theo. Xin cảm ơn.',
        // text: 'Invalid input, please try again. Press 1 to acknowledge incident, press 2 to escalate to next step in escalation policy. Thank you.',
        bargeIn: true,
      },
      {
        action: 'input',
        eventUrl: customField.callBackUrl,
        submitOnHash: 'false',
        customField: {
          incidentId: customField.incidentId,
          userId: customField.userId,
          userNumber: customField.userNumber,
          userFirstname: customField.userFirstname,
          userLastname: customField.userLastname,
          callBackUrl: customField.callBackUrl,
        },
        timeout: '40',
      },
    ])
    return mappedBody
  }

  public async payloadMapperAck(payload: IVRInputPayload): Promise<string> {
    const customField =
      typeof payload.customField === 'string'
        ? JSON.parse(payload.customField)
        : payload.customField
    const mappedBody = JSON.stringify([
      {
        action: 'talk',
        text: 'Cảm ơn bạn đã acknowledge incident, hệ thống sẽ ngừng gửi thông báo cho incident này.',
        // text: 'Incident has been Acknowledged, we will stop sending notifications for this incident. Thank you.',
        bargeIn: true,
      },
    ])
    return mappedBody
  }

  public async payloadMapperEsc(payload: IVRInputPayload): Promise<string> {
    const customField =
      typeof payload.customField === 'string'
        ? JSON.parse(payload.customField)
        : payload.customField
    const mappedBody = JSON.stringify([
      {
        action: 'talk',
        text: 'Cảm ơn bạn đã Escalate incident, hệ thống sẽ gửi thông báo cho thành viên ở bước tiếp theo.',
        // text: 'Incident has been Escalated, we will send notifications to the next step in the escalation policy. Thank you.',
        bargeIn: true,
      },
    ])
    return mappedBody
  }
}

export default IVRInputHandler
