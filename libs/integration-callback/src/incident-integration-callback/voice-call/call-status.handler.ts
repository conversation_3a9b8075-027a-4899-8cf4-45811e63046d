import { IncidentEventInterface } from '@libs/shared/constants/shared.interface'
import { v4 as uuid } from 'uuid'
import { IncidentEventDetailType } from '@libs/shared/constants/incident'

import { VoiceCallStatusPayload } from './voice_call.interface'

export enum VoiceCallStatus {
  ANSWERED = 'answered',
  CREATED = 'created',
  BUSY = '486 Busy Here',
  RINGING = 'ringing',
  ENDED = 'ended',
}

class CallStatusHandler {
  public async payloadMapper(
    payload: VoiceCallStatusPayload,
  ): Promise<IncidentEventInterface> {
    //Maybe we can move this out to somewhere
    let eventType: string
    if (payload.call_status == 'answered')
      eventType = IncidentEventDetailType.ANSWERED_CALL
    else if (payload.call_status == 'created')
      eventType = IncidentEventDetailType.CALLING
    else if (payload.endCallCause == '486 Busy Here')
      eventType = IncidentEventDetailType.REJECT_CALL
    else if (payload.endCallCause == 'ringing')
      eventType = IncidentEventDetailType.CALLING
    else if (payload.call_status == 'ended')
      eventType = IncidentEventDetailType.CALL_END
    else eventType = payload.call_status
    const aliasParts = payload.to.alias.split('-')
    const firstName = aliasParts[0]
    const lastName = aliasParts[1]
    const userId = aliasParts[2]
    const incidentId = aliasParts[3]
    const mappedBody = {
      id: uuid(),
      incidentId: incidentId,
      type: 'notification',
      user: {
        id: userId,
        firstName: firstName,
        lastName: lastName,
      },
      attribute: {
        type: eventType,
        value: {
          receiver: payload.to.number,
          location: null,
        },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    return mappedBody as IncidentEventInterface
  }
}

export default CallStatusHandler
