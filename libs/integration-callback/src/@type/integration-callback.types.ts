import { IncidentEventDetailType } from '@libs/shared/constants/incident'

// Union of supported provider types
export type IntegrationCallbackProviderType =
  | 'slack'
  | 'voice_call'
  | 'email'
  | 'plivo'

export interface LogIncidentEventPayload {
  incidentId: string
  userId?: string // User associated with the event (optional)
  type: IncidentEventDetailType // The specific type of event
  value?: Record<string, any> // Additional details about the event
}
