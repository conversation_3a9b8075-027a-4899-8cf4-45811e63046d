import { toLower } from 'lodash'
import { EMAIL_CUSTOM_HEADER_KEY } from '@libs/shared/email/constants'
import { parseHeaders } from '@libs/shared/email/utils'
import { parseQueryParams } from '@libs/shared/utils/query-string'

import { ClickEvent, EmailCallbackPayload, MailEvent } from './types'

class EmailCallback {
  private pullCustomHeaderData(headers: MailEvent['mail']['headers'] = []) {
    const value = headers.find(
      ({ name }) => toLower(name) === EMAIL_CUSTOM_HEADER_KEY,
    )?.value

    return parseHeaders(value)
  }

  private pullElementIdFromLink(url: string): string | undefined {
    const queryParams = parseQueryParams<{ elementId: string }>(url)
    return queryParams?.elementId
  }

  parse<P>(event: ClickEvent | MailEvent): EmailCallbackPayload<P> {
    const payload = this.pullCustomHeaderData(event?.mail?.headers)

    let elementId: string | undefined
    let clickLink: string | undefined
    switch (event.eventType) {
      case 'Click':
        clickLink = (event as ClickEvent)?.click?.link
        elementId = this.pullElementIdFromLink(clickLink)
        break
    }

    return {
      eventType: toLower(event.eventType), // TODO: change this if eventType is not Open or Click
      payload: {
        ...payload,
        clickLink,
        elementId,
      } as P,
    } as EmailCallbackPayload<P>
  }
}

export default EmailCallback
