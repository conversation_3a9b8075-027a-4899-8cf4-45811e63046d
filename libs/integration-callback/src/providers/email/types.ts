export interface MailEvent {
  eventType: string
  mail: {
    commonHeaders: {
      from: string[]
      messageId: string
      subject: string
      to: string[]
    }
    destination: string[]
    headers: {
      name: string
      value: string
    }[]
    headersTruncated: boolean
    messageId: string
    sendingAccountId: string
    source: string
    tags: {
      [key: string]: string[] // Tags are dynamic, key-value pairs
    }
    timestamp: string
  }
  open?: {
    ipAddress: string
    timestamp: string
    userAgent: string
  }
}

export interface ClickEvent extends MailEvent {
  click: {
    ipAddress: string
    link: string
    linkTags: {
      [key: string]: string[] // Dynamic key-value pairs for link tags
    }
    timestamp: string
    userAgent: string
  }
}

export interface EmailCallbackPayload<Payload> {
  eventType: 'open' | 'click'
  payload: Payload & {
    elementId?: string
    clickLink?: string
  }
}
