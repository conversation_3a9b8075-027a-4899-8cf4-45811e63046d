import { IncidentEventDetailType } from '@libs/shared/constants/incident'

// Payload for logging a generic incident event via callback
export interface LogIncidentEventPayload {
  incidentId: string
  userId?: string // User associated with the event (optional)
  type: IncidentEventDetailType // The specific type of event
  value?: Record<string, any> // Additional details about the event
}

// Payload structure expected from Plivo callback
export interface PlivoCallbackPayload {
  incidentId: string
  userId: string // Assuming this is the user identifier from Plivo
  status: 'calling_app' | 'calling' | 'call_answered' | 'call_failed' | 'no_answer' | 'reject_call' // Call outcome status
  // Add other relevant fields from Plivo if necessary
  [key: string]: unknown // Allow other potential fields
}
