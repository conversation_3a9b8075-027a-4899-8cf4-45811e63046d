import { IncidentEventDetailType } from '@libs/shared/constants/incident' // Assuming path based on project structure
import { IncidentIntegrationCallbackAction } from '@libs/integration-callback/incident-integration-callback/types'

import { PlivoCallbackPayload } from './types'

export class PlivoProvider {
  private actions: Partial<IncidentIntegrationCallbackAction>

  constructor(actions: Partial<IncidentIntegrationCallbackAction>) {
    this.actions = actions
  }

  /**
   * Handles the incoming callback payload from Plivo.
   * @param payload The parsed JSON payload from the Plivo callback.
   */
  async handleCallback(payload: PlivoCallbackPayload): Promise<{ status: string }> {
    console.log('Handling Plivo callback:', payload)

    const { incidentId, userId, status } = payload

    if (!incidentId || !userId || !status) {
      console.error(
        'Invalid Plivo payload: Missing incidentId, userId, or status',
        payload,
      )
      // Consider throwing an error or returning early depending on desired behavior
      return { status: "Invalid Plivo payload: Missing incidentId, userId, or status" }
    }

    try {
      switch (status) {
        case 'calling_app':
          console.log(
            `Plivo calling app for incident ${incidentId}, user ${userId}. Logging event.`,
          )
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.CALLING_APP,
              value: { status, rawPayload: payload }, // Include raw payload for context
            })
          return { status: "caling_app success" }
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break
        case 'calling':
          console.log(
            `Plivo calling app for incident ${incidentId}, user ${userId}. Logging event.`,
          )
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.CALLING,
              value: { status, rawPayload: payload }, // Include raw payload for context
            })
            return { status: "calling success" }
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break
        case 'call_answered':
          console.log(
            `Plivo call answered for incident ${incidentId}, user ${userId}. Acknowledging.`,
          )
          // Acknowledge the incident
          // TODO: I'm putting an ACK request in PHLO already, delete that one if dupe happen
          if (this.actions.acknowledgeIncident) {
            await this.actions.acknowledgeIncident({
              incidentId,
              userId: userId,
            })
            return { status: "call answered success" }
          } else {
            console.warn('acknowledgeIncident action not provided')
          }

          // Log the answered event
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.ANSWERED_CALL,
              value: { status, rawPayload: payload }, // Include raw payload for context
            })
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break

        case 'call_failed':
          console.log(
            `Plivo call failed (busy) for incident ${incidentId}, user ${userId}. Logging event.`,
          )
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.CALL_FAILED, // Use the new type
              value: { status, reason: 'failed', rawPayload: payload },
            })
            return { status: "call failed success" }
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break

        case 'no_answer':
          console.log(
            `Plivo call timed out (no answer) for incident ${incidentId}, user ${userId}. Logging event.`,
          )
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.NO_ANSWER, // Use the new type
              value: { status, reason: 'no_answer', rawPayload: payload },
            })
            return { status: "no answer success" }
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break
        
        case 'reject_call':
          console.log(
            `Plivo call rejected for incident ${incidentId}, user ${userId}. Logging event.`,
          )
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.REJECT_CALL,
              value: { status, reason: 'rejected', rawPayload: payload },
            })
            return { status: "reject call success" }
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break
        default:
          console.warn(
            `Received unknown Plivo status '${status}' for incident ${incidentId}. Ignoring.`,
            payload,
          )
          return { status:`unknown status '${status}' for incident ${incidentId}` }
          break
      }
    } catch (error) {
      console.error(
        `Error processin  g Plivo callback for incident ${incidentId}:`,
        error,
      )
      // Re-throw or handle error appropriately
      throw error
    }
  }
}
