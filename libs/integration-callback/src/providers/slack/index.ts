import { App, StringIndexed, AwsLambdaR<PERSON><PERSON>ver, LogLevel } from '@slack/bolt'
import { AwsHandler } from '@slack/bolt/dist/receivers/AwsLambdaReceiver'

export interface SlackCallbackConfig {
  botToken: string
  signingSecret: string
}

export type SlackCallbackAction = App['action']

class SlackCallback {
  private app: App<StringIndexed>
  private receiver: AwsLambdaReceiver

  constructor({ botToken, signingSecret }: SlackCallbackConfig) {
    this.receiver = new AwsLambdaReceiver({
      signingSecret,
    })

    this.app = new App({
      token: botToken,
      // signingSecret, // this is local testing
      receiver: this.receiver,
      logLevel: LogLevel.DEBUG,
    })

    // A more generic, global error handler
    this.app.error(async (error) => {
      // Check the details of the error to handle cases where you should retry sending a message or stop the app
      console.error(error)
    })
  }

  // this is local testing
  public async start(port: number) {
    await this.app.start(port)
  }

  public listenAction: SlackCallbackAction = (event, ...listener) => {
    this.app.action(event, ...listener)
  }

  handler: AwsHandler = async (event, context, callback) => {
    const handler = await this.receiver.start()
    return handler(event, context, callback)
  }
}

export default SlackCallback
