import { Schema } from 'dynamoose'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'

export interface IncidentEventInterface {
  id: string
  incidentId: string
  type: IncidentEventType
  user?: {
    id?: string
    firstName?: string
    lastName?: string
    avatar?: string
  }
  comment?: string
  attachment?: string
  attribute: {
    type: IncidentEventDetailType
    value?: {
      location?: string
      message?: string
      waitingTime?: number | undefined
      receiver?: string | undefined
    }
  }
  createdAt?: Date
  updatedAt?: Date
  createdBy?: string | undefined
  updatedBy?: string | undefined
}

export const IncidentEventSchema = new Schema(
  {
    id: {
      type: String,
      hashKey: true,
      required: true,
    },
    incidentId: {
      type: String,
      required: true,
      index: [
        {
          name: 'incidentId-index',
          type: 'global',
        },
        {
          name: 'incidentId-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
      ],
    },
    type: {
      type: String,
      required: true,
    },
    user: {
      type: Object,
      schema: {
        id: {
          type: String,
        },
        firstName: {
          type: String,
        },
        lastName: {
          type: String,
        },
        avatar: {
          type: String,
        },
      },
    },
    comment: {
      type: String,
    },
    attachment: {
      type: String,
    },
    attribute: {
      type: Object,
      schema: {
        type: {
          type: String,
        },
        value: {
          type: Object,
          schema: {
            location: {
              type: String,
            },
            message: {
              type: String,
            },
            waitingTime: {
              type: Number,
            },
            receiver: {
              type: String,
            },
          },
        },
      },
    },
  },
  {
    timestamps: {
      createdAt: {
        createdAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
          rangeKey: true,
        },
      },
      updatedAt: {
        updatedAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
        },
      },
    },
  },
)

export const INCIDENT_EVENT_SCHEMA = 'IncidentEventSchema'
export const INCIDENT_EVENT_TABLE = DYNAMO_DB_TABLE.INCIDENT_EVENTS
