import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'
import { Schema } from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'

export const IncidentSchema = new Schema(
  {
    id: {
      type: String,
      hashKey: true,
      required: true,
    },
    checkId: {
      type: String,
      required: true,
      index: [
        {
          name: 'checkId-index',
          type: 'global',
        },
        {
          name: 'checkId-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
      ],
    },
    customIncidentId: {
      type: String,
      required: false,
    },
    teamId: {
      type: String,
      required: true,
      index: [
        {
          name: 'teamId-index',
          type: 'global',
        },
        {
          name: 'teamId-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
      ],
    },
    status: {
      type: String, // pending | acknowledged | resolved
      required: true,
      index: {
        name: 'status-index',
        type: 'global',
      },
    },
    startedAt: {
      type: Date,
    },
    acknowledgedAt: {
      type: Date,
    },
    acknowledgedBy: {
      type: String,
    },
    resolvedAt: {
      type: Date,
    },
    resolvedBy: {
      type: String,
    },
    escalationPolicy: {
      type: String,
    },
    checkInfo: {
      type: Object,
      schema: {
        url: {
          type: String,
        },
        locations: {
          type: Array,
          schema: [String],
        },
        checkType: {
          type: String,
        },
        method: {
          type: String,
        },
      },
    },
    cause: {
      type: String,
    },
    stepFunctionArn: {
      type: String,
    },
    createdBy: {
      type: String,
    },
    updatedBy: {
      type: String,
    },
    requester_email: {
      type: String,
      required: false,
    },
    name: {
      type: String,
      required: false,
    },
    summary: {
      type: String,
      required: false,
    },
    description: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: {
      createdAt: {
        createdAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
          rangeKey: true,
        },
      },
      updatedAt: {
        updatedAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
        },
      },
    },
  },
)

export const INCIDENT_SCHEMA = 'IncidentSchema'
export const INCIDENT_TABLE = DYNAMO_DB_TABLE.INCIDENTS

export class IncidentItem extends Item {
  id!: string
  checkId!: string
  teamId!: string
  status!: string
  startedAt!: Date
  acknowledgedAt!: Date
  acknowledgedBy!: string
  resolvedAt!: Date
  resolvedBy!: string
  escalationPolicy!: string
  cause!: string
  stepFunctionArn!: string
  createdBy!: string
  updatedBy!: string
}
