import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'
import { Schema } from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'

export const StatusPageSchema = new Schema(
  {
    id: {
      type: String,
      hashKey: true,
      required: true,
    },
    teamId: {
      type: String,
      required: true,
      index: [
        {
          name: 'teamId-index',
          type: 'global',
        },
        {
          name: 'teamId-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
      ],
    },
    companyName: {
      type: String,
    },
    subDomain: {
      type: String,
      index: [
        {
          name: 'subDomain-index',
          type: 'global',
        },
      ],
    },
    getInTouchUrl: {
      type: String,
    },
    customdomain: {
      type: String,
    },
    announcement: {
      type: String,
    },
    minIncidentLength: {
      type: Number,
    },
    timezone: {
      type: String,
    },
    statusPageDay: {
      type: Number,
    },
    autoUpdate: {
      type: Boolean,
    },
    publishStatusPage: {
      type: Boolean,
    },
    statusSections: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            sectionName: {
              type: String,
              required: true,
            },
            resources: {
              type: Array,
              schema: [
                {
                  type: Object,
                  schema: {
                    id: String,
                    resourceType: String,
                    resourceId: String,
                    resourceName: String,
                    explanation: String,
                    widgetType: String,
                  },
                },
              ],
            },
          },
        },
      ],
    },
    createdBy: {
      type: String,
      required: true,
    },
    updatedBy: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: {
      createdAt: {
        createdAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
          rangeKey: true,
        },
      },
      updatedAt: {
        updatedAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
        },
      },
    },
  },
)

export const STATUS_PAGE_SCHEMA = 'StatusPageSchema'
export const STATUS_PAGE_TABLE = DYNAMO_DB_TABLE.STATUS_PAGES

export class StatusPageItem extends Item {
  id!: string
  teamId!: string
  companyName!: string
  subDomain!: string
  getInTouchUrl!: string
  customdomain!: string
  announcement!: string
  minIncidentLength!: number
  timezone!: string
  statusPageDay!: number
  autoUpdate!: boolean
  publishStatusPage!: boolean
  statusSections!: [
    {
      sectionName: string
      resources: [
        {
          id: string
          resourceType: string
          resourceId: string
          resourceName: string
          explanation: string
          widgetType: string
        },
      ]
    },
  ]
  createdAt!: Date
  updatedAt!: Date
  createdBy!: string
  updatedBy!: string
}
