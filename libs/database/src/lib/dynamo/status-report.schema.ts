import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'
import { Schema } from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'

export const StatusReportSchema = new Schema(
  {
    id: {
      type: String,
      hashKey: true,
      required: true,
    },
    statusPageId: {
      type: String,
      required: true,
      index: [
        {
          name: 'statusPageId-index',
          type: 'global',
        },
        {
          name: 'statusPageId-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
        {
          name: 'statusPageId-reportType-createdAt-index',
          type: 'global',
          rangeKey: 'reportTypeCreatedAt',
          project: true,
        },
      ],
    },
    summary: {
      type: String,
      required: true,
    },
    reportType: {
      type: String,
      required: true,
    },
    reportTypeCreatedAt: {
      type: String,
      required: true,
    },
    from: {
      type: Date,
    },
    to: {
      type: Date,
    },
    reportUpdates: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            id: { type: String, required: true },
            message: { type: String, required: true },
            status: { type: String, required: true },
            affectedResources: {
              type: Array,
              schema: [
                {
                  type: Object,
                  schema: {
                    id: { type: String, required: true },
                    resourceId: {
                      type: String,
                      required: true,
                    },
                    resourceType: { type: String, required: true },
                    resourceName: { type: String, required: true },
                    status: { type: String, required: true },
                  },
                },
              ],
            },
            publishedAt: {
              type: {
                value: Date,
                settings: {
                  storage: 'iso',
                },
              },
            },
          },
        },
      ],
    },
    status: {
      type: String,
      required: true,
    },
    affectedResources: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            id: { type: String, required: true },
            resourceId: { type: String, required: true },
            resourceType: { type: String, required: true },
            resourceName: { type: String, required: true },
            status: { type: String, required: true },
          },
        },
      ],
    },
  },
  {
    timestamps: {
      createdAt: {
        createdAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
          rangeKey: true,
        },
      },
      updatedAt: {
        updatedAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
        },
      },
    },
  },
)

export const STATUS_REPORT_SCHEMA = 'StatusReportSchema'
export const STATUS_REPORT_TABLE = DYNAMO_DB_TABLE.STATUS_REPORTS

export enum ResourceType {
  CHECK = 'check',
  CUSTOMINCIDENT = 'customincident',
}

export enum StatusReportType {
  REPORT = 'report',
  MAINTENANCE = 'maintenance',
}

export enum StatusMaintenanceUpdateStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETE = 'complete',
}

export enum StatusReportUpdateStatus {
  INVESTIGATING = 'investigating',
  IDENTIFIED = 'identified',
  MONITORING = 'monitoring',
  RESOLVED = 'resolved',

  MAINTENANCE = 'maintenance',
}

export enum AffectedResourceStatus {
  NONE = 'none',
  DOWNTIME = 'downtime',
  DEGRADED = 'degraded',
  RESOLVED = 'resolved',

  MAINTENANCE = 'maintenance',
}

export class StatusReportItem extends Item {
  id!: string
  statusPageId!: string
  summary!: string
  reportType!: StatusReportType
  reportTypeCreatedAt!: string

  from!: Date
  to!: Date

  reportUpdates!: StatusReportUpdate[]

  // Map from lastest update
  status!: StatusReportUpdateStatus | StatusMaintenanceUpdateStatus
  affectedResources!: StatusReportUpdateAffectedResource[]

  createdAt!: Date
  updatedAt!: Date
  createdBy!: string
  updatedBy!: string
}

export interface StatusReportUpdate {
  id: string // GENERATED
  message: string // updatable

  status: StatusReportUpdateStatus
  affectedResources: StatusReportUpdateAffectedResource[] // NOT UPDATABLE
  publishedAt?: Date // updatable but in range of report from and to
}

export interface StatusReportUpdateAffectedResource {
  id: string // resource generated id
  resourceId: string // resource id (ex: check id)
  resourceType: ResourceType // ex: check
  resourceName: string // readable data purpose
  status: AffectedResourceStatus
}
