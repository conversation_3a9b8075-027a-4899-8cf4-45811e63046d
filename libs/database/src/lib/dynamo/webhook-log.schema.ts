import { Schema } from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'

export const WebhookLogSchema = new Schema(
  {
    id: {
      type: String,
      hashKey: true,
      required: true,
    },
    webhookId: {
      type: String,
      required: true,
      index: [
        {
          name: 'webhookId-index',
          type: 'global',
        },
        {
          name: 'webhookId-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
      ],
    },
    requestMethod: {
      type: String,
      required: true,
    },
    requestSize: {
      type: Number,
      required: true,
    },
    responseCode: {
      type: Number,
      required: true,
    },
    responseStatus: {
      type: String,
      required: true,
    },
    requestPayload: {
      type: String,
      required: false,
    },
    responsePayload: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: {
      createdAt: {
        createdAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
          rangeKey: true,
        },
      },
      updatedAt: {
        updatedAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
        },
      },
    },
  },
)

export class WebhookLogItem extends Item {
  id!: string
  webhookId!: string
  requestMethod!: string
  requestSize!: number
  responseCode!: number
  responseStatus!: string
  requestPayload?: string
  responsePayload?: string | null

  createdAt!: Date
  updatedAt!: Date
}
