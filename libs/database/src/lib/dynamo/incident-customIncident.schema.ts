import { Schema } from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'

export const IncidentCustomIncidentSchema = new Schema(
  {
    id: {
      type: String,
      hashKey: true,
      required: true,
    },
    customIncidentId: {
      type: String,
      required: true,
      index: [
        {
          name: 'customIncident-index',
          type: 'global',
        },
        {
          name: 'customIncident-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
      ],
    },
    teamId: {
      type: String,
      required: true,
      index: [
        {
          name: 'teamId-index',
          type: 'global',
        },
        {
          name: 'teamId-createdAt-index',
          type: 'global',
          rangeKey: 'createdAt',
          project: true,
        },
      ],
    },
    status: {
      type: String, // pending | acknowledged | resolved
      required: true,
      index: {
        name: 'status-index',
        type: 'global',
      },
    },
    startedAt: {
      type: Date,
    },
    acknowledgedAt: {
      type: Date,
    },
    acknowledgedBy: {
      type: String,
    },
    resolvedAt: {
      type: Date,
    },
    resolvedBy: {
      type: String,
    },
    escalationPolicy: {
      type: String,
    },
    cause: {
      type: String,
    },
    stepFunctionArn: {
      type: String,
    },
    createdBy: {
      type: String,
    },
    updatedBy: {
      type: String,
    },
    requester_email: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    summary: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: {
      createdAt: {
        createdAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
          rangeKey: true,
        },
      },
      updatedAt: {
        updatedAt: {
          type: {
            value: Date,
            settings: {
              storage: 'iso',
            },
          },
        },
      },
    },
  },
)

export const INCIDENT_CUSTOM_SCHEMA = 'IncidentCustomSchema'

export class IncidentCustomIncidentItem extends Item {
  id!: string
  customIncidentId!: string
  teamId!: string
  status!: string
  requester_email!: string
  name!: string
  summary!: string
  description!: string

  startedAt!: Date
  acknowledgedAt!: Date
  acknowledgedBy!: string
  resolvedAt!: Date
  resolvedBy!: string
  escalationPolicy!: string
  cause!: string
  stepFunctionArn!: string
  createdBy!: string
  updatedBy!: string
}
