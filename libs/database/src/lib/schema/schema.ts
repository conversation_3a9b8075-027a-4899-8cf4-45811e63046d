import { relations } from 'drizzle-orm'
import {
  pgTable,
  varchar,
  timestamp,
  boolean,
  integer,
  jsonb,
  text,
  json,
  date,
} from 'drizzle-orm/pg-core'

export const customIncidentSchema = pgTable('custom_incidents', {
  id: varchar('id').primaryKey().notNull(),
  teamId: varchar('team_id').notNull(),
  escalationId: varchar('escalation_id').notNull(),
  status: varchar('status').notNull(),
})

export const customIncidentRelations = relations(
  customIncidentSchema,
  ({ one }) => ({
    escalation: one(escalations, {
      fields: [customIncidentSchema.escalationId],
      references: [escalations.id],
    }),
  }),
)

export const integrationSetting = pgTable('integration-settings', {
  id: varchar('id').primaryKey().notNull(),
  name: varchar('name').notNull(),
  type: varchar('type').notNull(),
  identity: varchar('identity').notNull(),
  config: json('config').notNull(),
  teamId: varchar('team_id').notNull(),
  deletedAt: timestamp('deleted_at', { mode: 'string' }),
})

export const escalations = pgTable('escalations', {
  id: varchar('id').primaryKey().notNull(),
  name: varchar('name').notNull(),
  repeatCount: integer('repeat_count').default(0).notNull(),
  repeatDelay: integer('repeat_delay').default(0).notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  teamId: varchar('team_id').notNull(),
})

export const escalationsRelations = relations(escalations, ({ many }) => ({
  escalationSteps: many(escalationSteps), // Define a relation to escalation_steps
}))

export const escalationSteps = pgTable('escalation_steps', {
  id: varchar('id').primaryKey().notNull(),
  stepDelay: integer('step_delay').notNull(),
  severityId: varchar('severity_id').notNull(),
  escalationId: varchar('escalation_id')
    .references(() => escalations.id)
    .notNull(),
})

export const severity = pgTable('severities', {
  id: varchar('id').primaryKey().notNull(),
  name: varchar('name').notNull(),
  teamId: varchar('team_id'),
  alerts: varchar('alerts').array(),
})

export const escalationContacts = pgTable('escalation_contacts', {
  id: varchar('id').primaryKey().notNull(),
  contactType: varchar('contact_type').notNull(),
  contactId: varchar('contact_id'),
  escalationStepId: varchar('escalation_step_id')
    .references(() => escalationSteps.id)
    .notNull(),
})

export const escalationStepsRelations = relations(
  escalationSteps,
  ({ one, many }) => ({
    escalationContacts: many(escalationContacts),
    escalation: one(escalations, {
      fields: [escalationSteps.escalationId],
      references: [escalations.id],
    }),
    severity: one(severity, {
      fields: [escalationSteps.severityId],
      references: [severity.id],
    }),
  }),
)

export const escalationContactsRelations = relations(
  escalationContacts,
  ({ one }) => ({
    escalationStep: one(escalationSteps, {
      fields: [escalationContacts.escalationStepId],
      references: [escalationSteps.id],
    }),
  }),
)

export const workers = pgTable('workers', {
  id: varchar('id').primaryKey().notNull(),
  ip: varchar('ip').notNull(),
  location: varchar('location').notNull(),
  type: varchar('type').notNull(),
  region: varchar('region').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
})

export const checks = pgTable('checks', {
  id: varchar('id').primaryKey().notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  deletedAt: timestamp('deleted_at', { mode: 'string' }),
  title: varchar('title').notNull(),
  tags: text('tags').array(),
  url: varchar('url').notNull(),
  port: integer('port').notNull(),
  locations: text('locations').array().notNull(),
  interval: integer('interval').notNull(),
  status: varchar('status').notNull(),
  recoverPeriod: integer('recover_period').notNull(),
  confirmPeriod: integer('confirm_period').notNull(),
  handleRedirect: boolean('handle_redirect').default(true),
  timeout: integer('timeout').notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
  teamId: varchar('team_id').notNull(),
  requestHeaders: jsonb('request_headers'),
  requestBody: text('request_body'),
  requiredKeyword: text('required_keyword'),
  expectedStatusCodes: integer('expected_status_codes').array(),
  lastCheckedAt: timestamp('last_checked_at', { mode: 'string' }),
  pausedAt: timestamp('paused_at', { mode: 'string' }),
  maintenanceDays: varchar('maintenance_days').array(),
  maintenanceFrom: varchar('maintenance_from'),
  maintenanceTo: varchar('maintenance_to'),
  maintenanceTimeZone: varchar('maintenance_time_zone'),
  authUsername: varchar('auth_username'),
  authPassword: varchar('auth_password'),
  type: varchar('type').notNull(),
  method: varchar('method').notNull(),
  // incidentId: varchar('incident_id'),
  escalationId: varchar('escalation_id'),
})

export const checksRelations = relations(checks, ({ one }) => ({
  escalation: one(escalations, {
    fields: [checks.escalationId],
    references: [escalations.id],
  }),
}))

export const users = pgTable('users', {
  id: varchar('id').primaryKey().notNull(),
  email: varchar('email').notNull(),
  organizationId: varchar('organization_id'),
  status: varchar('status'),
  deletedAt: timestamp('deleted_at'),
})

export const teams = pgTable('teams', {
  id: varchar('id').primaryKey().notNull(),
  name: varchar('name').notNull(),
  organizationId: varchar('organization_id').notNull(),
  deletedAt: timestamp('deleted_at'),
})

export const tokens = pgTable('tokens', {
  id: varchar('id').primaryKey().notNull(),
  level: varchar('level').notNull(),
  owner_id: varchar('owner_id').notNull(),
})

export const onCallSchedulers = pgTable('on_call_schedulers', {
  id: varchar('id').primaryKey().notNull(),
  type: varchar('type').notNull(),
  startDate: timestamp('start_date', {
    withTimezone: true,
    mode: 'string',
  }).notNull(),
  endDate: timestamp('end_date', { withTimezone: true, mode: 'string' }),
  allDay: boolean('all_day'),
  recurringPattern: varchar('recurring_pattern'),
  teamId: varchar('team_id').notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  deletedAt: timestamp('deleted_at', { mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
})

export const onCallSchedulersUsers = pgTable('on_call_schedulers_users', {
  onCallSchedulerId: varchar('on_call_schedulers_id')
    .references(() => onCallSchedulers.id)
    .notNull(),
  userId: varchar('users_id')
    .references(() => users.id)
    .notNull(),
})

export const onCallSchedulerUserRelations = relations(
  onCallSchedulersUsers,
  ({ one }) => ({
    onCallScheduler: one(onCallSchedulers),
    user: one(users),
  }),
)

export const onCallSchedulersRelations = relations(
  onCallSchedulers,
  ({ many, one }) => ({
    users: many(users),
    team: one(teams),
  }),
)

// Subscription and billing schemas
export const organizations = pgTable('organizations', {
  id: varchar('id').primaryKey(),
  stripeCustomerId: varchar('stripe_customer_id'),
})

export const subscriptions = pgTable('subscriptions', {
  id: varchar('id').primaryKey(),
  organizationId: varchar('organization_id').notNull(),
  stripeSubscriptionId: varchar('stripe_subscription_id').notNull().unique(),
  stripeCustomerId: varchar('stripe_customer_id').notNull(),
  status: varchar('status').notNull(),
  currentPeriodStartDate: date('current_period_start_date').notNull(),
  currentPeriodEndDate: date('current_period_end_date').notNull(),
  gracePeriodEndsAt: timestamp('grace_period_ends_at'),
  usageBillingEnabled: boolean('usage_billing_enabled').default(true).notNull(),
  overagePolicyFixedResources: varchar('overage_policy_fixed_resources')
    .default('BLOCK')
    .notNull(),
  canceledAt: timestamp('canceled_at'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
})

export const subscriptionItems = pgTable('subscription_items', {
  id: varchar('id').primaryKey(),
  subscriptionId: varchar('subscription_id').notNull(),
  stripeSubscriptionItemId: varchar('stripe_subscription_item_id')
    .notNull()
    .unique(),
  stripePriceId: varchar('stripe_price_id').notNull(),
  subscriptionPlanId: varchar('subscription_plan_id').notNull(),
  itemType: varchar('item_type').notNull(),
  quantity: integer('quantity').default(1).notNull(),
  status: varchar('status').notNull(),
  resourceType: varchar('resource_type'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
  startDate: date('start_date'),
  endDate: date('end_date'),
})

export const subscriptionPlans = pgTable('subscription_plans', {
  id: varchar('id').primaryKey(),
  name: varchar('name').notNull(),
  planType: varchar('plan_type').notNull(),
  stripePriceId: varchar('stripe_price_id').notNull(),
  membersIncluded: integer('members_included').default(0).notNull(),
  teamsIncluded: integer('teams_included').default(0).notNull(),
  checksIncluded: integer('checks_included').default(0).notNull(),
  integrationsIncluded: integer('integrations_included').default(0).notNull(),
  memberOverageStripePriceId: varchar(
    'member_overage_stripe_price_id',
  ).notNull(),
  teamOverageStripePriceId: varchar('team_overage_stripe_price_id').notNull(),
  checkOverageStripePriceId: varchar('check_overage_stripe_price_id').notNull(),
  integrationOverageStripePriceId: varchar(
    'integration_overage_stripe_price_id',
  ),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
})

export const planResourceLimits = pgTable('plan_resource_limits', {
  id: varchar('id').primaryKey(),
  subscriptionPlanId: varchar('subscription_plan_id').notNull(),
  resourceType: varchar('resource_type').notNull(),
  includedQuantity: integer('included_quantity').default(0).notNull(),
  overageStripePriceId: varchar('overage_stripe_price_id').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
})

export const subscriptionUsage = pgTable('subscription_usage', {
  id: varchar('id').primaryKey(),
  organizationId: varchar('organization_id').notNull(),
  subscriptionItemId: varchar('subscription_item_id'),
  usageType: varchar('usage_type').notNull(),
  usageValue: integer('usage_value').notNull(),
  usageTimestamp: timestamp('usage_timestamp').notNull(),
  billingCycleStartDate: date('billing_cycle_start_date'),
  billingCycleEndDate: date('billing_cycle_end_date'),
  reportedToStripe: boolean('reported_to_stripe'),
  stripePriceId: varchar('stripe_price_id'),
  stripeUsageRecordId: varchar('stripe_usage_record_id'),
  isBillable: boolean('is_billable'),
  notes: varchar('notes'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
  createdBy: varchar('created_by'),
  updatedBy: varchar('updated_by'),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
    .defaultNow()
    .notNull(),
})

// Subscription relations
export const subscriptionsRelations = relations(
  subscriptions,
  ({ one, many }) => ({
    organization: one(organizations, {
      fields: [subscriptions.organizationId],
      references: [organizations.id],
    }),
    subscriptionItems: many(subscriptionItems),
  }),
)

export const subscriptionItemsRelations = relations(
  subscriptionItems,
  ({ one }) => ({
    subscription: one(subscriptions, {
      fields: [subscriptionItems.subscriptionId],
      references: [subscriptions.id],
    }),
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionItems.subscriptionPlanId],
      references: [subscriptionPlans.id],
    }),
  }),
)

export const subscriptionPlansRelations = relations(
  subscriptionPlans,
  ({ many }) => ({
    subscriptionItems: many(subscriptionItems),
    resourceLimits: many(planResourceLimits),
  }),
)

export const planResourceLimitsRelations = relations(
  planResourceLimits,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [planResourceLimits.subscriptionPlanId],
      references: [subscriptionPlans.id],
    }),
  }),
)

export const subscriptionUsageRelations = relations(
  subscriptionUsage,
  ({ one }) => ({
    subscriptionItem: one(subscriptionItems, {
      fields: [subscriptionUsage.subscriptionItemId],
      references: [subscriptionItems.id],
    }),
    organization: one(organizations, {
      fields: [subscriptionUsage.organizationId],
      references: [organizations.id],
    }),
  }),
)
