import { Inject, Injectable } from '@nestjs/common'
import { drizzle, NodePgDatabase } from 'drizzle-orm/node-postgres'
import { InfluxClient } from '@libs/shared/service-clients/influx.client'
import { PostgresClient } from '@libs/shared/service-clients/postgres.client'
import { RedisClient } from '@libs/shared/service-clients/redis.client'
import { PgTable } from 'drizzle-orm/pg-core'

import * as schema from './schema/schema'

@Injectable()
export class Database {
  public db: NodePgDatabase<typeof schema>

  constructor(
    @Inject(InfluxClient) private readonly influx: InfluxClient,
    @Inject(PostgresClient) private readonly postgres: PostgresClient,
    @Inject(RedisClient) private readonly redis: RedisClient,
  ) {
    this.db = drizzle(this.postgres.getClient(), { schema })
  }

  drizzle() {
    return this.db
  }

  pgQuery() {
    return this.db.query
  }

  pgUpdate(table: PgTable) {
    return this.db.update(table)
  }

  async getValuesFromPersistentDB(
    select: string,
    from: string,
    id: string,
  ): Promise<any | null> {
    return this.postgres.getValues(select, from, id)
  }

  async writeToTimeSeriesDB(
    measurement: string,
    tags: { [key: string]: string },
    fields: { [key: string]: number | string },
    database: string,
  ): Promise<any> {
    return this.influx.write(measurement, tags, fields, database)
  }

  redisGet(key: string, prefix: string): any {
    return this.redis.get(key, prefix)
  }

  redisSet(key: string, prefix: string, data: any) {
    return this.redis.set(key, prefix, data)
  }

  redisSetHash(key: string, field: string, value: string) {
    return this.redis.setHash(key, field, value)
  }

  redisGetHash(key: string, field: string) {
    return this.redis.getHash(key, field)
  }

  redisGetHashAll(key: string) {
    return this.redis.getHashAll(key)
  }

  redisCycleList(key: string, prefix: string) {
    return this.redis.rpoplpush(key, prefix)
  }

  redisDelete(key: string, prefix: string) {
    return this.redis.delete(`${prefix}:${key}`)
  }

  redisLPush(key: string, prefix: string, value: any) {
    return this.redis.lpush(`${prefix}:${key}`, value)
  }

  redisSetWithExpired(key: string, prefix: string, data: any, expire: number) {
    return this.redis.set(key, prefix, data, expire)
  }

  public async redisClientHealthCheck() {
    return this.redis.healthCheck()
  }

  public async pgClientHealthCheck() {
    return this.postgres.healthCheck()
  }
}
