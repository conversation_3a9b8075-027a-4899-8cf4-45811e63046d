import { Global, Module } from '@nestjs/common'
import { InfluxClient } from '@libs/shared/service-clients/influx.client'
import { PostgresClient } from '@libs/shared/service-clients/postgres.client'
import { RedisClient } from '@libs/shared/service-clients/redis.client'

import { Database } from './database'

@Global()
@Module({
  providers: [PostgresClient, RedisClient, InfluxClient, Database],
  exports: [Database],
})
export class DatabaseModule {}
