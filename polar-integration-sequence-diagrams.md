# Polar.sh Integration Sequence Diagrams

This document contains sequence diagrams for the Polar.sh integration flows in MonitoringDog's subscription system.

## 1. Subscription Creation Flow

```mermaid
sequenceDiagram
    participant Client
    participant SubscriptionController
    participant PolarUseCase
    participant PolarService
    participant PolarAP<PERSON> as Polar.sh API
    participant Database
    participant PolarWebhook as Polar Webhook

    Client->>SubscriptionController: POST /subscription/create-checkout-session
    SubscriptionController->>PolarUseCase: createCheckoutSession()
    
    PolarUseCase->>PolarService: ensurePolarCustomer()
    PolarService->>PolarAPI: polar.customers.list({organizationId})
    PolarAPI-->>PolarService: existing customer or null
    
    alt Customer doesn't exist
        PolarService->>PolarAPI: polar.customers.create()
        PolarAPI-->>PolarService: new customer
    end
    
    PolarService-->>PolarUseCase: customer object
    
    PolarUseCase->>PolarService: createCheckoutSession()
    PolarService->>PolarAPI: polar.checkouts.create()
    PolarAPI-->>PolarService: checkout session
    PolarService-->>PolarUseCase: session data
    
    PolarUseCase-->>SubscriptionController: {sessionId, checkoutUrl}
    SubscriptionController-->>Client: checkout session response
    
    Note over Client: User completes payment on Polar
    
    PolarAPI->>PolarWebhook: subscription.created webhook
    PolarWebhook->>SubscriptionSyncService: syncFromPolarWebhook()
    SubscriptionSyncService->>Database: create subscription record
```

## 2. Plan Change Flow

```mermaid
sequenceDiagram
    participant Client
    participant SubscriptionController
    participant PolarAPI
    participant PolarWebhook
    participant PolarService
    participant Database

    Client->>SubscriptionController: POST /subscription/plan-change
    SubscriptionController->>PolarService: changePlan()
    
    PolarService->>Database: findCurrentSubscription()
    Database-->>PolarService: current subscription
    
    PolarService->>Database: findNewPlan()
    Database-->>PolarService: new plan details
    
    PolarService->>PolarService: calculateCurrentUsage()
    
    PolarService->>PolarAPI: polar.events.ingest()
    Note over PolarAPI: Ingest usage events to Polar
    
    PolarService->>PolarAPI: polar.subscriptions.update()
    PolarAPI-->>PolarService: updated subscription
    
    PolarService-->>SubscriptionController: plan change result
    SubscriptionController-->>Client: success response
    
    Note over PolarAPI: Polar processes plan change
    
    PolarAPI->>PolarWebhook: subscription.updated webhook
    PolarWebhook->>PolarService: handleSubscriptionUpdated()
    PolarService->>Database: update subscription record
    PolarService->>Database: sync subscription items & apply new resource limits
    PolarService->>Database: cancel old subscription
```

## 3. Plan Change Preview Flow

```mermaid
sequenceDiagram
    participant Client
    participant SubscriptionController
    participant PolarUseCase
    participant UsageCalculationService
    participant Database

    Client->>SubscriptionController: POST /subscription/plan-change-preview
    SubscriptionController->>PolarUseCase: previewPlanChange()
    
    PolarUseCase->>Database: findCurrentSubscription()
    Database-->>PolarUseCase: current subscription
    
    PolarUseCase->>Database: findNewPlan()
    Database-->>PolarUseCase: new plan details
    
    PolarUseCase->>UsageCalculationService: calculateCurrentUsage()
    UsageCalculationService-->>PolarUseCase: current usage data
    
    PolarUseCase->>PolarUseCase: calculateOverageCharges()
    PolarUseCase->>PolarUseCase: calculatePriceChanges()
    PolarUseCase->>PolarUseCase: generatePreviewSummary()
    
    PolarUseCase-->>SubscriptionController: preview details
    SubscriptionController-->>Client: {immediateCharges, nextInvoice, summaryMessage}
```

## 4. Subscription Cancellation Flow

```mermaid
sequenceDiagram
    participant Client
    participant SubscriptionController
    participant PolarUseCase
    participant PolarService
    participant PolarAPI as Polar.sh API
    participant Database

    Client->>SubscriptionController: POST /subscription/cancel
    SubscriptionController->>PolarUseCase: cancelSubscription()
    
    PolarUseCase->>Database: findCurrentSubscription()
    Database-->>PolarUseCase: active subscription
    
    PolarUseCase->>PolarService: cancelSubscription()
    PolarService->>PolarAPI: polar.subscriptions.revoke()
    PolarAPI-->>PolarService: cancellation confirmed
    
    PolarService-->>PolarUseCase: success
    
    PolarUseCase->>Database: updateSubscriptionStatus(CANCELED)
    Database-->>PolarUseCase: updated record
    
    PolarUseCase-->>SubscriptionController: cancellation result
    SubscriptionController-->>Client: {success: true, effectiveDate}
```

## 5. Webhook Processing Flow

```mermaid
sequenceDiagram
    participant PolarAPI as Polar.sh API
    participant PolarWebhookController
    participant PolarService
    participant SubscriptionSyncService
    participant Database

    PolarAPI->>PolarWebhookController: webhook event
    
    PolarWebhookController->>PolarService: verifyWebhookSignature()
    PolarService-->>PolarWebhookController: signature valid
    
    alt subscription.created
        PolarWebhookController->>SubscriptionSyncService: syncFromPolarWebhook('created')
        SubscriptionSyncService->>SubscriptionSyncService: findOrganizationByCustomerId()
        SubscriptionSyncService->>SubscriptionSyncService: createLocalSubscriptionFromPolar()
        SubscriptionSyncService->>Database: create subscription record
    
    else subscription.updated
        PolarWebhookController->>SubscriptionSyncService: syncFromPolarWebhook('updated')
        SubscriptionSyncService->>SubscriptionSyncService: handleSubscriptionUpdated()
        SubscriptionSyncService->>Database: update/create subscription
        SubscriptionSyncService->>Database: cancel old subscription (if plan change)
    
    else subscription.canceled
        PolarWebhookController->>SubscriptionSyncService: syncFromPolarWebhook('canceled')
        SubscriptionSyncService->>SubscriptionSyncService: cancelLocalSubscription()
        SubscriptionSyncService->>Database: update status to CANCELED
    
    else checkout.updated
        PolarWebhookController->>SubscriptionSyncService: handleCheckoutCompleted()
        Note over SubscriptionSyncService: Log checkout completion<br/>Subscription created via separate webhook
    
    else customer.created
        PolarWebhookController->>SubscriptionSyncService: updateOrganizationWithPolarCustomer()
        SubscriptionSyncService->>Database: update organization record
    end
    
    PolarWebhookController-->>PolarAPI: 200 OK
```

## 6. Subscription Details Retrieval Flow

```mermaid
sequenceDiagram
    participant Client
    participant SubscriptionController
    participant PolarUseCase
    participant PolarService
    participant PolarAPI as Polar.sh API
    participant UsageCalculationService
    participant Database

    Client->>SubscriptionController: GET /subscription/current-subscription
    SubscriptionController->>PolarUseCase: getSubscriptionDetails()
    
    PolarUseCase->>Database: findLocalSubscription()
    Database-->>PolarUseCase: local subscription record
    
    alt Local subscription exists
        PolarUseCase->>PolarService: getSubscription()
        PolarService->>PolarAPI: polar.subscriptions.get()
        PolarAPI-->>PolarService: latest subscription data
        PolarService-->>PolarUseCase: polar subscription
        
        alt Status differs
            PolarUseCase->>Database: updateSubscriptionStatus()
        end
    end
    
    PolarUseCase->>UsageCalculationService: calculateCurrentUsage()
    UsageCalculationService-->>PolarUseCase: current usage data
    
    PolarUseCase-->>SubscriptionController: {subscription, organization, currentUsage}
    SubscriptionController-->>Client: subscription details
```

## 7. Usage Event Reporting Flow (Future Implementation)

```mermaid
sequenceDiagram
    participant PolarUseCase
    participant UsageCalculationService
    participant PolarService
    participant PolarAPI as Polar.sh API
    participant Database

    Note over PolarUseCase: Called before plan changes

    PolarUseCase->>Database: getCurrentSubscription()
    Database-->>PolarUseCase: active subscription
    
    PolarUseCase->>UsageCalculationService: calculateCurrentUsage()
    UsageCalculationService-->>PolarUseCase: {members, teams, checks}
    
    PolarUseCase->>PolarUseCase: getPlanResourceLimits()
    PolarUseCase->>PolarUseCase: calculateOverages()
    
    loop For each resource with overage
        PolarUseCase->>PolarService: reportUsageEvent()
        Note over PolarService: Currently logs only<br/>API structure TBD
        PolarService->>PolarAPI: polar.events.create() (future)
        PolarAPI-->>PolarService: event recorded (future)
    end
```

## 8. Error Handling Flow

```mermaid
sequenceDiagram
    participant Client
    participant Controller
    participant UseCase
    participant Service
    participant PolarAPI as Polar.sh API

    Client->>Controller: API request
    Controller->>UseCase: business operation
    UseCase->>Service: polar operation
    Service->>PolarAPI: API call
    
    PolarAPI-->>Service: error response
    Service->>Service: log error details
    Service-->>UseCase: throw error
    UseCase->>UseCase: log business context
    UseCase-->>Controller: throw error
    Controller->>Controller: log request context
    Controller-->>Client: HTTP error response
    
    Note over Controller,Service: All errors logged with context<br/>at each layer for debugging
```

## Key Architecture Components

### Service Layer Responsibilities

1. **PolarService**
   - Direct SDK integration
   - API authentication
   - Webhook signature verification
   - Low-level error handling

2. **PolarUseCase**
   - Business logic orchestration
   - Usage calculation integration
   - Local database synchronization
   - High-level error handling

3. **SubscriptionSyncService**
   - Webhook data processing
   - Local subscription lifecycle management
   - Data consistency maintenance

### Data Flow Patterns

1. **Provider-Agnostic Design**
   - Generic `providerSubscriptionId`, `providerCustomerId`, `providerProductId` fields
   - Support for both Stripe and Polar simultaneously
   - Clean migration path between providers

2. **Event-Driven Architecture**
   - Webhook-first subscription lifecycle
   - Usage-based billing through structured events
   - Eventual consistency between Polar and local data

3. **Error Resilience**
   - Comprehensive logging at all layers
   - Graceful error handling
   - Webhook retry mechanisms (handled by Polar)

### Integration Points

1. **Database Synchronization**
   - Local subscription records updated via webhooks
   - Status synchronization on API calls
   - Usage data calculated from local resources

2. **Billing Integration**
   - Event-based overage reporting
   - Simplified billing model vs complex Stripe calculations
   - Real-time usage tracking

3. **Frontend Integration**
   - Checkout session URLs for payment flows
   - Subscription status and usage dashboards
   - Plan change previews with cost calculations