# User Portal Backend #

This service provide APIs for user portal and integration with other services.

### Repository info
* Version: 0.0.1
* Lasted release: N/A

### How do I get set up?
* Node: v18
* Package Manager: pnpm
* NestJS CLI
* TypeORM CLI
* Nx CLI

## Pnpm installation
```bash
$ corepack enable pnpm
$ corepack use pnpm@latest
```

## Installation

```bash
$ pnpm install
```

- Unpack credential secrets (ask teammate for the password):
```bash
$ brew install gnupg gnupg2
$ yarn install
```

## Run tasks

To run the dev server for your app, use:

```sh
npx nx serve user-portal-backend
```

To create a production bundle:

```sh
npx nx build user-portal-backend
```

To see all available targets to run for a project, run:

```sh
npx nx show project user-portal-backend
```
        
These targets are either [inferred automatically](https://nx.dev/concepts/inferred-tasks?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) or defined in the `project.json` or `package.json` files.

[More about running tasks in the docs &raquo;](https://nx.dev/features/run-tasks?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)

## Add new projects

While you could add new projects to your workspace manually, you might want to leverage [Nx plugins](https://nx.dev/concepts/nx-plugins?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) and their [code generation](https://nx.dev/features/generate-code?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) feature.

Use the plugin's generator to create new projects.

To generate a new application, use:

```sh
npx nx g @nx/nest:app demo
```

To generate a new library, use:

```sh
npx nx g @nx/node:lib mylib
```

You can use `npx nx list` to get a list of installed plugins. Then, run `npx nx list <plugin-name>` to learn about more specific capabilities of a particular plugin. Alternatively, [install Nx Console](https://nx.dev/getting-started/editor-setup?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) to browse plugins and generators in your IDE.

[Learn more about Nx plugins &raquo;](https://nx.dev/concepts/nx-plugins?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) | [Browse the plugin registry &raquo;](https://nx.dev/plugin-registry?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)

## TypeORM Migration
```bash
# Create a new migration
$  npm run migration:create --name <migration-name>

# Run migration
$  npm run migration:run
```

## Docker

```bash
# Build and run Docker-compose for Postgres and Redis
# Postgres port:5432;  Redis port:6379
$ docker-compose build   
$ docker-compose up -d
```

```bash
# Build and run app with the name "app_dev"
# Try send GET request to http://localhost:3000/user to see if this work
# $ docker build -t app_dev .
# $ docker run -p 3000:3000 app_dev
```

## Code generation
```bash
$ yarn codegen <module> && yarn lint
```


### Firebase Emulator Authentication

- [Install Firebase CLI](https://firebase.google.com/docs/cli#setup_update_cli)

- Run the following commands to start the Firebase emulator and authenticate with the emulator
```bash
$ firebase login
$ pnpm firebase:start
```
- Access http://127.0.0.1:8181/auth to create user with email and password

```bash
$ pnpm firebase:auth
```

- Use firebase token to authenticate with authentication APIs

```bash
$ pnpm start:debug
```

## Email template generation

- Use MJML to generate email template [MJML](https://mjml.io/try-it-live)

## Postgres RRULE
- Doc https://github.com/volkanunsal/postgres-rrule
