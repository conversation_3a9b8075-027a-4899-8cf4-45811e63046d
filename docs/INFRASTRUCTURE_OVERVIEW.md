# Infrastructure Overview

## AWS CDK Stack Architecture

Our infrastructure is built using AWS CDK (Cloud Development Kit) with TypeScript, following infrastructure-as-code principles. The system manages multiple AWS services through defined stacks.

### Core Components

1. **Environment Management**
   - Supports both Non-Production and Production environments
   - Configured via environment variables (`CDK_NON_PROD_ACCOUNT`, `CDK_PROD_ACCOUNT`, etc.)
   - Follows AWS CDK best practices for multi-environment deployment

2. **Current Stacks**
   - **IncidentIntegrationCallbackStack**: Handles incident integration callbacks
   - **IncidentEscalationStack**: Manages incident escalation workflows
   - **IncidentSESConfigSetStack**: Configures SES (Simple Email Service) for incident notifications
   - **WebhookIntegrationStack**: Webhook for certain management events (CRUD, Incident, etc)
   - **AuthorizerAPIGatewayStack**: For some direct API calls (Check and Custom Incident)

## Deployment Process

1. **Synthesis**
   - CDK app is synthesized into CloudFormation templates
   - Environment-specific configurations are applied

2. **Environment Separation**
   - Clear separation between NonProd and Prod environments
   - Different AWS accounts/regions for each environment

3. **CI/CD Integration**
   - Likely integrated with Bitbucket Pipelines (based on bitbucket-pipelines.yml in root)
   - Deployment controlled through environment variables

## Technical Stack

- **AWS CDK v2**: Primary infrastructure-as-code framework
- **TypeScript**: Implementation language for CDK constructs
- **AWS Services**:
  - Lambda (for serverless components)
  - API Gateway (for HTTP endpoints)
  - SES (for email notifications)
  - (Additional services inferred from directory structure)

## Directory Structure

```
apps/infrastructure/src/
├── core/            # Foundational constructs and shared infrastructure
├── data-handlers/   # Data processing components
├── domains/         # Domain-specific stacks
│   └── incidents/   # Incident management domain
├── lambda/          # Lambda function code
├── shared/          # Shared utilities and constants
├── app.ts           # Main CDK application definition
└── main.ts          # Entry point
```

## Key Patterns

1. **Construct-Based Architecture**
   - Uses CDK Constructs to encapsulate logical components
   - EnvironmentStacks class manages environment-specific deployments

2. **Separation of Concerns**
   - Clear separation between:
     - Core infrastructure (core/)
     - Domain logic (domains/)
     - Shared utilities (shared/)

3. **Configuration Management**
   - Environment variables drive account/region configuration
## Implementation Details

### 1. Core Infrastructure Patterns

* **Environment Management** (`environment-stack.ts`):
  - Base stack class handling account/region configuration
  - Implements environment-specific resource naming
  - Manages shared resources across stacks
  - See [AUTHORIZER_GATEWAY.md] for API Gateway patterns

* **Construct Composition**:
  - Standardized construct interfaces
  - Reusable patterns for common services
  - Consistent tagging strategy

### 2. Stack Implementations

* **Webhook Integration** (`webhook-integration-stack.ts`):
  - API Gateway with Lambda integration
  - Request validation and transformation
  - Error handling and logging setup
  - Related to [WEBHOOK_INTEGRATION.md] documentation

* **Incident Integration Callback** (`incident-integration-callback-stack.ts`):
  - Handles callback integrations (Slack, Email, Voice)
  - Event processing pipeline
  - Response validation

* **Authorizer API Gateway** (`authorizer-api-gateway-stack.ts`):
  - Authentication/authorization layer
  - Request validation
  - Rate limiting configuration
  - Detailed in [AUTHORIZER_GATEWAY.md]

* **Incident Escalation** (`incident-escalation-stack.ts`):
  - Escalation workflows
  - Notification channels
  - Timeout handling

### 3. Deployment Architecture

* **CI/CD Pipeline**:
  - Environment promotion workflow
  - Automated testing integration
  - Rollback mechanisms
  - Integrated with Bitbucket Pipelines

* **Infrastructure Testing**:
  - CDK assertions
  - Integration test patterns
  - Security compliance checks

### 4. Operational Considerations

* **Monitoring Setup**:
  - CloudWatch alarms and dashboards
  - Log aggregation configuration
  - Performance metrics collection
  - Connected to worker monitoring in [WORKER_DETAILED.md]

* **Security Implementation**:
  - IAM least privilege policies
  - Encryption configurations
  - Network isolation patterns
   - .env file support through dotenv (visible in main.ts)
