# Worker Service - Comprehensive Technical Documentation

## Core Architecture Components

### 1. Worker Types
Key implementations:
- **MainCheckWorker** ([`apps/worker/src/worker/workers/check.worker.ts`])
  - Listens for check assignments via Redis pub/sub
  - Processes checks using appropriate handler
- **VerifyCheckWorker** ([`apps/worker/src/worker/workers/verify-check.worker.ts`])
  - Handles verification of check status changes
- **TestCheckWorker** ([`apps/worker/src/worker/workers/test-check.worker.ts`])
  - Processes one-off test checks

### 2. Check Handlers
Base class: **BaseCheckHandler** ([`apps/worker/src/worker/check-handler/check.base.ts`])

Concrete implementations:
1. **Status<PERSON>hecker** - Basic HTTP status code validation
2. **ExpectedStatusChecker** - Validates against expected status codes
3. **PingChecker** - ICMP ping checks
4. **KeywordChecker** - Content keyword validation
5. **NoKeywordChecker** - Negative keyword validation

### 3. Data Management
Key components:
- **CheckData<PERSON>and<PERSON>** ([`apps/worker/src/worker/data-handler/check.data.ts`])
  ```typescript
  async getDataFromFastDB(checkId: string): Promise<CheckData> {
    const checkData = await this.db.redisGet(
      checkId,
      REDIS_CHECK_PREFIX.CHECK_DATA,
    )
    if (!checkData) return this.convertToCheckData(await this.fallBackConfig(checkId))
    return this.convertToCheckData(checkData)
  }
  ```
- **CheckStatusHandler** ([`apps/worker/src/worker/data-handler/check-status.data.ts`])
  - Manages check state in Redis

## Detailed Workflows

### Check Processing Flow
1. Worker receives check ID via Redis pub/sub
2. Retrieves check configuration:
```typescript
// From check.ts
async initialize(checkId: string, workerId: string): Promise<void> {
  this.workerId = workerId
  this.checkData = await this.dataHandler.getDataFromFastDB(checkId)
  this.handler = await this.createHandler(this.checkData.type)
}
```

3. Executes check based on type:
```typescript
// From check.base.ts
async getResponse(
  url: string,
  method: HttpMethod,
  timeout: number,
  handleRedirect: boolean,
  requestBody?: string,
  requestHeader?: string
): Promise<GetResponseData> {
  // HTTP request implementation
}
```

### Status Change Handling
1. Detects status changes:
```typescript
// From check.base.ts
async handleCheckStatusChange(checkData: CheckData): Promise<void> {
  const isInVerificationOrPaused = await this.dataHandler.isCheckWorkerVerifying(checkData.id)
  const savedApiStatus = await this.dataHandler.getAPIStatus(checkData.id, this.isAPIUp)
  const howStatusChange = this.resolveStatus(this.isAPIUp, savedApiStatus)
  
  switch (howStatusChange) {
    case 'DOWN_TO_UP':
    case 'UP_TO_DOWN':
      this.requestVerifyCheck(checkData.id, howStatusChange)
      break
    // ... other cases
  }
}
```

2. Triggers verification:
```typescript
// From check.base.ts
async requestVerifyCheck(checkId: string, howStatusChange: CheckStatusChange) {
  const verifyRequest: VerifyCheckRequest = {
    checkId: checkId,
    workerId: configService.getWorkerConfig().workerId,
    time: Date.now(),
    howStatusChange: howStatusChange
  }
  await this.pubsubqueue.publishToQueue(QUEUE.VERIFY, checkId, verifyRequest)
}
```

## Data Handling

### Redis State Management
Key patterns:
- `{checkId}:CHECK_DATA` - Check configuration
- `CHECK_WORKER_STATE:{checkId}` - Current check state
- `{checkId}:WORKER_NODE` - Assigned worker

### Metrics Collection
InfluxDB integration:
```typescript
// From check.base.ts
async writeToInflux(checkId: string, workerId: string) {
  const tags = {
    check_id: checkId,
    subscription: 'team', // TODO: this is hard coded
    worker: workerId,
  }
  const fields = {
    response_time: this.checkResult.totalTime,
    status_code: this.checkResult.statusCode,
    check_result: this.isAPIUp ? WorkerResult.SUCCESS : WorkerResult.FAIL,
  }
  await this.db.writeToTimeSeriesDB('check_health', tags, fields, BUCKET)
}
```

## Configuration

### Worker Configuration
Environment variables:
```typescript
// From config.service.ts
getWorkerConfig() {
  return {
    workerId: this.getValue('WORKER_ID'),
  }
}
```

### Queue Configuration
```typescript
// From worker.module.ts
const workers = [MainCheckWorker, VerifyCheckWorker, TestCheckWorker]
```

## Error Handling

### Check Type Errors
```typescript
// From error.checktypes.ts
export class CheckTypeError extends BaseError {
  constructor(message: string) {
    super(message, 500)
    this.message = `Unknown check type: ${this.message}`
  }
}
```

### Network Errors
```typescript
// From error.general.ts
export class NetworkError extends BaseError {
  constructor(message: string, public timings?: StatusData) {
    super(message, 502, timings)
    this.message = `Network error occurred during API check: ${this.message}`
  }
}
```

## Integration Points

### With Orchestrator
- Receives check assignments via Redis pub/sub
- Publishes results to Redis state store

### With Monitoring
- Writes metrics to InfluxDB
- Sends telemetry via OpenTelemetry:
```typescript
// From telemetry.service.ts
recordMetrics(checkId: string, workerId: string) {
  const countMetric = this.meter.createCounter('count', {
    description: 'Number of checks performed'
  })
  countMetric.add(1, { workerId, checkId })
## Implementation Details

### 1. Core Worker Implementation (`apps/worker/src/worker/workers/`)

* **MainCheckWorker** (`check.worker.ts`):
  - Primary check execution logic
  - Handles initial check processing
  - Implements base worker functionality

* **VerifyCheckWorker** (`verify-check.worker.ts`):
  - Verification/retry logic
  - Handles status change confirmation
  - Implements exponential backoff

* **TestCheckWorker** (`test-check.worker.ts`):
  - One-off test execution
  - Simplified processing flow
  - Immediate result reporting

### 2. Check Handler Implementation (`apps/worker/src/worker/check-handler/`)

* **Base Handler** (`check.base.ts`):
  - Common HTTP request logic
  - Status evaluation
  - Metrics collection
  - Error handling

* **Specialized Handlers**:
  - Status check (`check.status.ts`)
  - Keyword check (`check.keyword.ts`)
  - Expected status (`check.expected-status.ts`)
  - Ping check (`check.ping.ts`)
  - Negative keyword (`check.no_keyword.ts`)

### 3. Data Processing Implementation (`apps/worker/src/worker/data-handler/`)

* **CheckDataHandler** (`check.data.ts`):
  - Retrieves check configuration
  - Manages Redis state
  - Handles fallback logic

* **CheckStatusHandler** (`check-status.data.ts`):
  - State transition management
  - Verification triggering
  - Incident/recovery flow initiation

### 4. Key Technical Considerations

* **Redis Integration**:
  - Pub/sub for check assignments
  - State persistence
  - Worker coordination

* **Metrics Collection**:
  - InfluxDB for performance metrics
  - OpenTelemetry for observability
  - Custom tagging system

* **Error Recovery**:
  - Automatic retry mechanism
  - Circuit breaker pattern
  - Graceful degradation
}
