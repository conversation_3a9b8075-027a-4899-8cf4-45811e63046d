# Authorizer Gateway Stack

## Summary
The Authorizer Gateway provides centralized authentication and authorization for API endpoints with:
- JWT token validation
- Role-based access control
- Database-backed token verification
- API Gateway integration
- Comprehensive logging

## Overview
The Authorizer Gateway provides centralized authentication and authorization for API Gateway endpoints using JWT tokens. It validates requests before they reach business logic endpoints.

### Key Components
- **API Gateway**: REST API endpoint router
- **Lambda Authorizer**: Token validation handler
- **Logging**: CloudWatch Logs for auditing
- **Database**: PostgreSQL for token validation

## Architecture
```mermaid
flowchart LR
    A[Client Request] --> B[API Gateway]
    B --> C[Lambda Authorizer]
    C --> D[Business Logic Endpoints]
    C --> E[PostgreSQL DB]
```

## Authorization Flow
1. Client includes `Auth-Token` in request headers
2. API Gateway routes request to Authorizer Lambda
3. Authorizer validates token against database
4. If valid, returns IAM policy allowing access
5. Request proceeds to business endpoint

## Configuration

### Lambda Authorizer
- **Runtime**: Node.js
- **Memory**: 1024MB
- **Timeout**: 10 seconds
- **Environment Variables**:
  - DB connection parameters
  - Logging configuration

### API Gateway
- **Authorization Type**: Token (JWT)
- **Identity Source**: `method.request.header.Auth-Token`
- **Logging**: One week retention

## Security
- **Token Validation**: Verifies against database records
- **Least Privilege**: Minimal IAM permissions
- **Network Isolation**: VPC connectivity for database

## Error Handling
- Invalid tokens return 401 Unauthorized
- Database failures return 500 Internal Server Error
- All errors logged to CloudWatch

## Implementation

**1. Authorizer Handler (`apps/infrastructure/src/domains/auth/lambda/handlers/authorizer-handler.ts`)**

* Core validation logic:
  - Extracts token from `Authorization` header
  - Validates token against PostgreSQL database
  - Checks resource access permissions
  - Generates IAM policy for API Gateway

* Database integration:
  - Uses connection pooling for PostgreSQL
  - Validates team and organization level tokens
  - Queries resource-specific data (checks/custom incidents)

* Error handling:
  - Invalid tokens return 401 Unauthorized
  - Database errors return 500 Internal Server Error
  - All errors logged to CloudWatch

**2. Infrastructure Stack (`apps/infrastructure/src/domains/auth/infra/stacks/api-gateway-authorizer-stack.ts`)**

* Creates:
  - API Gateway authorizer configuration
  - Lambda function deployment
  - CloudWatch log groups
  - IAM permissions

* Integration:
  - Connects to VPC for database access
  - Configures token caching (5 minutes default)
  - Sets up proper logging retention

**3. Standard Construct (`apps/infrastructure/src/shared/constructs/standard-lambda-authorizer.construct.ts`)**

* Provides reusable authorizer components:
  - Token-based authorization
  - Configurable identity sources
  - Customizable caching
  - Default naming conventions

**4. Database Schema**

* Token table:
  - Stores active API tokens
  - Tracks token level (team/organization)
  - Records owner IDs

* Team table:
  - Maintains team membership
  - Links to organization hierarchy
  - Stores access policies

**5. Endpoint Integration**

* Applied to:
  - Check endpoints (`/check/{id}`)
  - Custom incident endpoints (`/customIncident/{id}`)
  - Other protected resources

* Configuration:
  - Uses `TokenAuthorizer` type
  - Sets `Authorization` header as identity source
  - Passes context to downstream handlers
