# Incident Stack Overview

## Summary
The Incident Stack handles the full lifecycle of incident management including:
- Real-time incident detection via DynamoDB streams
- Multi-channel notification system (<PERSON>lack, Email, Voice, SMS)
- Automated escalation workflows
- Custom incident handling
- Integration with monitoring systems

## 1. Core Components

### 1.1 Incident Notifier System
- **Location**: `libs/notifier/src/incident-notifier/`
- **Functionality**:
  - Centralized incident notification system
  - Supports multiple notification channels:
    - Slack (`slack-incident-notifier.ts`)
    - <PERSON>ail (`email-incident-notifier.tsx`)
    - Voice calls (`voice-call-incident-notifier.ts`)
    - Push notifications (`push-noti-incident-notifier.ts`)
    - SMS/Plivo (`plivo-incident-notifier.ts`)

### 1.2 Incident Payload Structure
- **Location**: `libs/notifier/src/incident-notifier/incident-notifier.type.ts`
- **Key Fields**:
  ```typescript
  interface IncidentPayload {
    incidentId: string
    incidentUrl: string
    checkUrl: string
    startedAt: Date
    resolvedAt?: Date
    // Additional channel-specific payloads
  }
  ```

## 2. Notification Channels

### 2.1 Slack Integration
- **Features**:
  - Incident started/resolved notifications
  - Interactive buttons for acknowledgment
  - Detailed incident information blocks

### 2.2 Voice Call Integration
- **Workflow**:
  - Automated calls with IVR options
  - Press 1 to acknowledge
  - Press 2 to escalate

### 2.3 Email Notifications
- **Templates**:
  - Incident started
  - Incident acknowledged
  - Incident resolved

## 3. Escalation Flow
1. Initial notification via preferred channel
2. Timeout-based escalation if not acknowledged
3. Secondary notifications to backup contacts
4. Final escalation to on-call manager

## 4. Escalation Stack Architecture

### 4.1 Core Components
- **IncidentEscalationStack**: Main CDK construct that orchestrates escalation workflows
- **State Machines**:
  - `IncidentEscalationStateMachine`: Standard incident workflow
  - `CustomIncidentEscalationStateMachine`: Specialized workflow for custom incidents

### 4.2 Event Processing
- **DynamoDB Stream Integration**:
  - Triggers on new incident records
  - Filters for non-resolved incidents
  - Routes to appropriate state machine
- **EventBridge Pipes**:
  - Connects DynamoDB streams to Step Functions
  - Handles both standard and custom incidents

### 4.3 Key Features
- **Multi-channel notifications** via SQS queue
- **Custom incident handling** with separate workflow
- **Automatic resolution tracking**

## 5. Integration Points
- **Webhook Support**: Custom callback URLs
- **Status Page Updates**: Automatic incident posting
- **Monitoring Systems**: InfluxDB integration for metrics
- **DynamoDB Streams**: Real-time incident detection

## Implementation Details

**1. Incident Notifier Implementation (`libs/notifier/src/incident-notifier/`)**

* **Core Components**:
  - `incident-notifier.ts`: Base abstract class with common notification logic
  - Channel-specific implementations (Slack, Email, Voice, etc.)
  - Payload transformation utilities

* **Key Features**:
  - Template-based message generation
  - Channel-specific payload formatting
  - Error handling and retry logic
  - Rate limiting protection

**2. State Machine Definitions**

* **Standard Incident Workflow**:
  - Defined in CDK infrastructure code
  - Initial notification phase
  - Escalation timers and conditions
  - Multi-channel notification branching
  - Resolution tracking

* **Custom Incident Workflow**:
  - Defined in CDK infrastructure code
  - Specialized handling logic
  - Custom timeout configurations
  - Alternative notification paths

**3. Database Schema (`libs/database/src/lib/dynamo/`)**

* **Incident Tables**:
  - `incident-event.schema.ts`: Tracks incident lifecycle events
  - `incident-check.schema.ts`: Links incidents to monitoring checks
  - `status-report.schema.ts`: Maintains status page updates

* **Key Fields**:
  - Timestamps for all state transitions
  - Escalation policy references
  - Notification delivery status

**4. Infrastructure Components**

* **Core CDK Constructs**:
  - DynamoDB stream integrations
  - EventBridge pipe configurations
  - IAM permission management
  - SQS queues for notifications
  - Lambda function deployments

* **Monitoring Integration**:
  - CloudWatch alarms for failed executions
  - InfluxDB metrics for notification stats
  - SQS dead-letter queues for failed notifications
