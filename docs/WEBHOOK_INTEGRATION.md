# Webhook Integration Implementation

This document outlines the implementation of webhook integration handling within the incident management system.

## Requirements

- Handle incoming webhook notifications via API Gateway endpoint (`/webhook/events`)
- Support multiple event types (CREATED, UPDATED, RESOLVED, etc.)
- Validate webhook configurations before processing
- Transform payloads based on event type
- Log all webhook delivery attempts
- Support basic authentication and custom headers
- Handle timeouts (default 5s) and errors

## Implementation Details

### 1. Shared Constants

* **File:** `libs/shared/src/constants/webhook.interface.ts`
* **Contents:**
  * Webhook event types enum
  * Webhook configuration interface
  * Payload transformation templates

### 2. Webhook Processor Implementation

* **Location:** `apps/user-portal-backend/src/modules/integration-setting/`
* **Key Files:**
  * `webhook.usecase.ts` - Main webhook processing logic
  * `webhook.repository.ts` - Data access layer
  * `webhook.infrastructure.ts` - Infrastructure integration

### 3. Webhook Processing Flow

1. Request received at `/webhook/events`
2. Configuration validation (check if webhook exists and is enabled)
3. Resource data retrieval (fetch related check/incident)
4. Payload transformation based on event type
5. Request execution with timeout handling
6. Response logging (success/failure)

## Supported Event Types

| Event Type       | Description                          | Trigger Condition          |
|------------------|--------------------------------------|----------------------------|
| CREATED          | Resource creation                   | New check/incident created |
| UPDATED          | Resource modification               | Check/incident updated     |
| PAUSED/RESUMED   | Monitoring state change              | Check paused/resumed       |
| DELETED          | Resource removal                    | Check/incident deleted     |
| NEW              | New incident detected               | Incident triggered         |
| ACKNOWLEDGED     | Incident acknowledged by team       | Status update              |
| RESOLVED         | Incident resolution                 | Incident closed            |

## Sequence Diagram

```mermaid
sequenceDiagram
    participant ExternalService
    participant APIGateway
    participant WebhookLambda
    participant Database
    participant TargetEndpoint

    ExternalService->>+APIGateway: POST /webhook/events
    APIGateway->>+WebhookLambda: Invoke handler
    WebhookLambda->>+Database: Validate config
    Database-->>-WebhookLambda: Config data
    WebhookLambda->>+Database: Get resource data
    Database-->>-WebhookLambda: Resource details
    WebhookLambda->>+TargetEndpoint: Send transformed payload
    TargetEndpoint-->>-WebhookLambda: Response
    WebhookLambda->>+Database: Log attempt
    Database-->>-WebhookLambda: Success
    WebhookLambda-->>-APIGateway: Return status
    APIGateway-->>-ExternalService: Response
```

## Data Storage

### Webhook Configurations (PostgreSQL)
```typescript
{
  id: string,             // Unique identifier
  teamId: string,         // Associated team
  config: {
    url: string,          // Endpoint URL
    requestMethod: string,// HTTP method
    requestTimeout: number,
    basicUsername?: string,
    basicPassword?: string,
    headerFields: Record<string, string>
  },
  sendOnCreated: boolean, // Event type triggers
  sendOnUpdated: boolean,
  // ...other event flags
}
```

### Webhook Logs (DynamoDB)
```typescript
{
  webhookId: string,       // ID of the webhook configuration
  requestMethod: string,   // HTTP method (GET/POST/etc.)
  requestSize: number,     // Payload size in bytes
  responseCode: number,    // HTTP status code
  responseStatus: string,  // 'Success' or 'Failed'
  requestPayload: string,  // Full request body
  responsePayload: string  // Full response body
}
```

## Error Handling

- **Invalid Configuration:** Return 400 Bad Request
- **Resource Not Found:** Return 404 Not Found
- **Timeout:** Abort after 5s (configurable)
- **Authentication Failure:** Return 401 Unauthorized
- **Processing Error:** Return 500 Internal Server Error

## Monitoring

All webhook events are logged to:
- CloudWatch Logs (for real-time monitoring)
- DynamoDB `webhook-logs` table (for historical analysis)
- InfluxDB metrics (for performance tracking)

## Implementation

**1. Webhook Configuration Module (`apps/user-portal-backend/src/modules/integration-setting/`)**

* **webhook.usecase.ts**:
  - Contains core business logic for webhook processing
  - Handles event validation, payload transformation, and request execution
  - Implements timeout and error handling

* **webhook.repository.ts**:
  - Manages database operations for webhook configurations
  - Provides CRUD operations for webhook settings
  - Handles querying enabled webhooks for specific events

* **webhook.infrastructure.ts**:
  - Interfaces with external services (HTTP clients, etc.)
  - Implements request signing and authentication
  - Handles connection pooling and retry logic

**2. Shared Types and Constants (`libs/shared/src/constants/webhook.interface.ts`)**

* Defines:
  - WebhookEventType enum (CREATED, UPDATED, etc.)
  - WebhookConfig interface for endpoint configurations
  - WebhookLog interface for tracking delivery attempts
  - Payload templates for different event types

**3. Lambda Handler (`apps/infrastructure/src/domains/webhooks/`)**

* **webhook-handler.ts**:
  - Entry point for API Gateway requests
  - Validates incoming webhook payloads
  - Routes requests to appropriate processors
  - Handles response formatting and error codes

**4. Database Schema (`libs/database/src/lib/dynamo/webhook-log.schema.ts`)**

* Defines DynamoDB table structure for:
  - Webhook delivery logs
  - Request/response payload storage
  - Status tracking and timestamps

**5. Monitoring Integration (`apps/worker/src/cores/metric-collector/`)**

* **telemetry.service.ts**:
  - Tracks webhook delivery metrics
  - Records success/failure rates
  - Monitors response times and payload sizes
