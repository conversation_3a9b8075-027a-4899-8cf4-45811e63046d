# Orchestrator Service - Comprehensive Technical Documentation

## Core Architecture Components

### 1. Worker Management System
Key files:
- [`apps/orchestrator/src/orchestrator/workers/`] - Contains all worker implementations
- [`apps/orchestrator/src/orchestrator/data-handlers/worker.data.ts`] - Manages worker state

Orchestrator Worker types (diffrerent from the system's Worker - the other service in apps/worker):
1. **CyclingWorker** - Distributes checks to worker nodes
2. **FinalizeVerifyWorker** - Handles verification completion
3. **ValidationPeriodWorker** - Manages confirmation/recovery periods
4. **VerifyWorker** - Performs status verification

### 2. State Management
Key components:
- **CheckStatusHandler** (`check-status.data.ts`)
- **WorkerDataHandler** (`worker.data.ts`)

### 3. Incident Management
Key files:
- [`apps/orchestrator/src/orchestrator/handlers/incident.handler.ts`]
- [`apps/orchestrator/src/orchestrator/data-handlers/incident.data.ts`]

Incident lifecycle methods:
```typescript
// From incident.handler.ts
async startIncident(checkId: string, workerResponse: CheckWorkerResponse[]) {
  const check = await this.findCheckById(checkId);
  const incident = await this.incidentDataHandler.create(
    { checkId, date: new Date(), workerResponse },
    check
  );
  // ... notification logic
}
```

## Detailed Workflows

### Check Distribution Flow
1. CyclingWorker receives check ID
2. Gets worker node from WorkerDataHandler
3. Publishes to worker channel:
```typescript
// From worker.data.ts
async sendCheckToWorker(checkId: string, workerNode: string) {
  await this.pubsub.publishToChannel(
    CHANNEL.WORKER_MAIN(workerNode), 
    checkId
  );
}
```

### Incident Verification Flow
1. VerifyWorker detects status change
2. FinalizeVerifyWorker confirms status
3. ValidationPeriodWorker handles timing:
```typescript
// From validation-period.worker.ts
switch(period) {
  case 'CONFIRMATION_PERIOD':
    return this.finalizeVerifyHandler.handleConfirmationPeriod(checkId);
  case 'RECOVERY_PERIOD': 
    return this.finalizeVerifyHandler.handleRecoveryPeriod(checkId);
}
```

## Data Handling

### Redis State Management
Key patterns:
- `CHECK_WORKER_STATE:{checkId}` - Stores worker states
- `{checkId}:WORKER_NODE` - Tracks worker assignments

### DynamoDB Schemas
- IncidentSchema - Tracks incident lifecycle
- IncidentEventSchema - Logs incident changes

## Configuration

### Queue Configuration
```typescript
// From flow-manager.ts
const flow: FlowJob = {
  name: `${queueName}:${checkId}`,
  queueName,
  data: { checkId },
  opts: {
    attempts: 3,
    backoff: { type: 'fixed', delay: 1000 }
  }
};
```

### Worker Configuration
Each worker extends BaseWorkerProcessor:
```typescript
// From base.worker.ts
@Processor(QUEUE)
export abstract class BaseWorkerProcessor {
  @Process()
  abstract process(job: Job): Promise<void>;
}
```

## Error Handling

### Worker Failure Recovery
1. Detect missed heartbeats
2. Retry affected jobs:
```typescript
// From incident.handler.ts
async handleWorkerFailure(workerId: string) {
  const jobs = await this.queue.getJobs(['active']);
  await Promise.all(
    jobs.filter(j => j.data.workerId === workerId)
      .map(job => this.queue.retryJob(job.id))
  );
}
```

## Integration Points

### With Workers
- Publishes to `CHANNEL.WORKER_MAIN`
- Listens for responses via BullMQ

### With Backend
- Receives check configurations
- Updates status via Redis/DynamoDB

### With Monitoring
- Logs metrics to InfluxDB
## Implementation Details

### 1. Core Worker Implementations (`apps/orchestrator/src/orchestrator/workers/`)

* **CyclingWorker** (`cycling.worker.ts`):
  - Distributes checks to available worker nodes
  - Implements round-robin assignment
  - Handles worker capacity management

* **VerifyWorker** (`verify.worker.ts`):
  - Performs initial status verification
  - Implements exponential backoff
  - Handles network timeouts

* **FinalizeVerifyWorker** (`finalize-verify.worker.ts`):
  - Confirms status changes
  - Triggers incident creation/recovery
  - Manages state transitions

* **ValidationPeriodWorker** (`validation-period.worker.ts`):
  - Handles confirmation/recovery periods
  - Implements timing logic
  - Manages delayed actions

### 2. Data Handler Implementations (`apps/orchestrator/src/orchestrator/data-handlers/`)

* **CheckStatusHandler** (`check-status.data.ts`):
  - Manages check state in Redis
  - Handles status transitions
  - Coordinates with workers

* **WorkerDataHandler** (`worker.data.ts`):
  - Tracks worker nodes
  - Manages worker assignments
  - Handles worker failures

* **IncidentDataHandler** (`incident.data.ts`):
  - Creates/updates incidents
  - Manages incident lifecycle
  - Coordinates with notification system

### 3. Key Technical Considerations

* **Redis Integration**:
  - Pub/sub for worker communication
  - State persistence
  - Distributed coordination

* **DynamoDB Schemas**:
  - Incident tracking
  - Event logging
  - Status history

* **Error Recovery**:
  - Worker failure detection
  - Automatic job retries
  - Circuit breaker pattern

* **Performance Optimization**:
  - Batch processing
  - Connection pooling
  - Cached lookups
- Sends alerts via WebhookHandler
