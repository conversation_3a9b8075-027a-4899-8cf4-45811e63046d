# Orchestrator Service - Technical Documentation

## Summary
The Orchestrator Service coordinates monitoring workflows with:
- Distributed check execution
- State transition management
- Incident/recovery automation
- Worker task distribution
- Queue-based communication

## Core Functionality
The Orchestrator manages endpoint monitoring workflows by:
1. Processing check requests from Backend
2. Creating appropriate worker tasks
3. Tracking check statuses
4. Triggering incident/recovery flows

## Main Components

### 1. Task Dispatcher
- Receives check requests from Backend via BullMQ
- Creates worker-specific tasks based on check type:
  - `Status<PERSON>hecker`: HTTP status code verification
  - `ExpectedStatusChecker`: Expected response validation
  - `PingChecker`: Basic connectivity checks
  - `KeywordChecker`: Content validation
  - `NoKeywordChecker`: Negative content validation

### 2. Worker Management
- Manages three worker types:
  - `MainCheckWorker`: Primary check execution
  - `VerifyCheckWorker`: Verification/retry logic
  - `TestCheckWorker`: Testing/validation

### 3. State Processing
- Handles results through data handlers:
  - `CheckDataHandler`: Processes raw check data
  - `CheckStatusHandler`: Manages state transitions
  - `DataHandler`: Base handler functionality

## Detailed Check Flow Example

### 1. Check Signal Reception
- Received via BullMQ queue in `apps/worker/src/worker/workers/check.worker.ts`
- Processed by `Check.createHandler()` which routes to appropriate check type handler

### 2. Status Check Execution
1. Worker makes HTTP request (implemented in check handlers):
   - Status check: `apps/worker/src/worker/check-handler/check.status.ts`
   - Keyword check: `apps/worker/src/worker/check-handler/check.keyword.ts`

2. Response processing:
```typescript
// Simplified flow from check.status.ts
const response = await httpRequest(url);
const status = response.statusCode;
const isUp = status >= 200 && status < 300;
```

### 3. State Transition Handling
Processed in `apps/worker/src/worker/data-handler/check-status.data.ts`:
```typescript
function handleStatusChange(previous: CheckState, current: CheckState) {
  if (previous.status === 'up' && current.status === 'down') {
    // Trigger incident flow
    this.incidentHandler.create(/*...*/);
  } else if (previous.status === 'down' && current.status === 'up') {
    // Trigger recovery flow  
    this.recoveryHandler.resolve(/*...*/);
  }
  // No change case handled silently
}
```

### 4. Incident Creation Flow (Up→Down)
1. Incident created via `apps/orchestrator/src/orchestrator/handlers/incident.handler.ts`
2. State persisted in DynamoDB via `apps/orchestrator/src/orchestrator/dynamo/`
3. Notifications dispatched through `libs/notifier/`

### 5. Recovery Handling (Down→Up)
1. Recovery processed via `apps/orchestrator/src/orchestrator/handlers/recovery-reset.handler.ts`
2. State updated in DynamoDB
3. Recovery notifications sent

### 6. No-Change Scenario
- Status remains unchanged
- Metrics still recorded in InfluxDB
- No additional actions triggered

## Key Implementation Files
1. Check handlers: `apps/worker/src/worker/check-handler/`
2. State processing: `apps/worker/src/worker/data-handler/check-status.data.ts`
3. Incident logic: `apps/orchestrator/src/orchestrator/handlers/incident.handler.ts`
4. Recovery logic: `apps/orchestrator/src/orchestrator/handlers/recovery-reset.handler.ts`

## Queue Communication
- Uses BullMQ with Redis for:
  - Receiving check requests
  - Sending worker tasks
  - Publishing status updates
  - Handling retries

## Integration Points

### With Backend
- Receives: Check configurations
- Sends: Status updates, incident alerts

### With Workers
- Sends: Specific check instructions
- Receives: Raw check results

### With Monitoring
- Publishes: Metrics via TelemetryModule
- Logs: Check history via DatabaseModule

## Error Handling
1. Worker failure triggers retry via VerifyCheckWorker
2. Persistent failures escalate to incident flow
3. Success after failure triggers recovery flow
4. All transitions logged via DataHandlers
