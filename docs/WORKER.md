# Worker Service - Technical Documentation

## Summary
The Worker Service executes monitoring checks with:
- Specialized check handlers (HTTP/Ping/Content)
- State transition detection
- Result processing pipelines
- Queue-based task processing
- Integrated metrics collection

## Core Functionality
The Worker executes monitoring checks by:
1. Processing tasks from Orchestrator
2. Performing endpoint checks (HTTP/Ping/Content)
3. Reporting results back
4. Handling retries and verification

## Main Components

### 1. Check Handlers
- Specialized handlers for each check type:
  - `StatusCheck`: HTTP status validation (`check.status.ts`)
  - `KeywordCheck`: Content validation (`check.keyword.ts`)  
  - `PingCheck`: Connectivity checks (`check.ping.ts`)
  - `ExpectedStatusCheck`: Response validation (`check.expected-status.ts`)

### 2. Data Processors
- `CheckDataHandler`: Processes raw check results
- `CheckStatusHandler`: Manages state transitions
- `DataHandler`: Base processing functionality

### 3. Worker Types
- `CheckWorker`: Primary execution worker
- `VerifyCheckWorker`: Retry/verification logic
- `TestCheckWorker`: Testing/validation

## Detailed Execution Flow

### 1. Task Reception
- Receives tasks via BullMQ in [`apps/worker/src/worker/workers/check.worker.ts`]:
```typescript
// Simplified worker initialization
new Worker('checks', async (job: Job) => {
  const handler = Check.createHandler(job.data.type);
  return handler.execute(job.data);
});
```

### 2. Check Execution
Example HTTP status check flow from [`apps/worker/src/worker/check-handler/check.status.ts`]:
```typescript
async execute(check: CheckRequest) {
  const response = await this.httpService.request({
    method: 'GET',
    url: check.url,
    timeout: check.timeout
  });
  
  return {
    statusCode: response.status,
    responseTime: response.responseTime,
    status: response.status >= 200 && response.status < 300 ? 'up' : 'down'
  };
}
```

### 3. Result Processing
State handling in [`apps/worker/src/worker/data-handler/check-status.data.ts`]:
```typescript
processResult(check: Check, result: CheckResult) {
  const newState = this.determineState(result);
  const previousState = check.status;
  
  if (previousState !== newState) {
    this.emitStateChange(previousState, newState, check);
  }
  
  return this.saveMetrics(check, result);
}
```

## Key Implementation Files
1. Check handlers: `apps/worker/src/worker/check-handler/`
2. Data processing: `apps/worker/src/worker/data-handler/`
3. Worker implementations: `apps/worker/src/worker/workers/`
4. Core models: `apps/worker/src/worker/interface/check.interface.ts`

## Integration Points

### With Orchestrator
- Receives: Check tasks via BullMQ
- Sends: Results via Redis pub/sub

### With Monitoring
- Records: Metrics via TelemetryService
- Logs: Check history via DatabaseModule

### With Notification System
- Triggers: Immediate alerts for critical failures
