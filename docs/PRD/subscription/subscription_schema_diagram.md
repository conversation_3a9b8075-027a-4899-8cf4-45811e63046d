```mermaid
erDiagram
    Organization {
        string id PK
        string name
    }

    StripeCustomer {
        string stripeCustomerId PK
        string email
    }

    StripeSubscription {
        string stripeSubscriptionId PK
        string status
        date currentPeriodStartDate
        date currentPeriodEndDate
    }

    StripePrice {
        string stripePriceId PK
        string type "recurring, metered, one-time"
        float unitAmount
        string currency
    }

    StripeSubscriptionItem {
        string stripeSubscriptionItemId PK
        integer quantity
    }

    StripeUsageRecord {
        string stripeUsageRecordId PK
        integer quantity
        datetime timestamp
    }

    SubscriptionPlan {
        string id PK
        string name
        PlanType planType "BASE_PLAN, ADDON_METERED_USAGE"
        string stripePriceId FK "to StripePrice (base plan recurring price or metered service fee)"
        boolean isActive
    }

    PlanResourceLimit {
        string id PK
        string subscriptionPlanId FK "to SubscriptionPlan"
        string resourceType "e.g., MEMBERS, TEAMS, CHECKS, INTEGRATIONS, STATUS_PAGES"
        integer includedQuantity
        string overageStripePriceId FK "to StripePrice (specific overage price for this resource)"
    }

    Subscription {
        string id PK
        string organizationId FK "to Organization"
        string stripeSubscriptionId FK "to StripeSubscription"
        string stripeCustomerId FK "to StripeCustomer"
        SubscriptionStatus status
        date currentPeriodStartDate
        date currentPeriodEndDate
        datetime gracePeriodEndsAt "(nullable) For payment delinquency on transactional services"
        boolean usageBillingEnabled
        string overagePolicyFixedResources
    }

    SubscriptionItem {
        string id PK
        string subscriptionId FK "to Subscription"
        string stripeSubscriptionItemId FK "to StripeSubscriptionItem"
        string stripePriceId FK "to StripePrice (price of this item, e.g. base plan recurring price)"
        string subscriptionPlanId FK "to SubscriptionPlan (template for this item)"
        PlanType itemType "BASE_PLAN, ADDON_METERED_USAGE (denormalized from SubscriptionPlan)"
        integer quantity "Usually 1 for BASE_PLAN"
        SubscriptionItemStatus status
    }

    SubscriptionUsage {
        string id PK
        string subscriptionItemId FK "to SubscriptionItem (e.g., links to BASE_PLAN item for overages, or specific metered service item)"
        string organizationId FK "to Organization (denormalized)"
        UsageType usageType "SMS, PHONE_CALL, MEMBER_OVERAGE, TEAM_OVERAGE, CHECK_OVERAGE, INTEGRATION_OVERAGE, STATUS_PAGE_OVERAGE"
        float usageValue
        date usageTimestamp
        date billingCycleStartDate
        date billingCycleEndDate
        boolean reportedToStripe
        string stripePriceId FK "to StripePrice (CRITICAL: specific price ID used for reporting)"
        string stripeUsageRecordId FK "to StripeUsageRecord (optional)"
    }

    Organization ||--o{ Subscription : has
    StripeCustomer ||--|| Subscription : is
    StripeSubscription ||--|| Subscription : "is represented by"
    StripeSubscriptionItem ||--|| SubscriptionItem : "is represented by"
    StripePrice ||--o{ SubscriptionPlan : "defines price for"
    StripePrice ||--o{ PlanResourceLimit : "defines overage price for"
    StripePrice ||--o{ SubscriptionItem : "defines price for"
    StripeUsageRecord ||--o{ SubscriptionUsage : "can result in"

    SubscriptionPlan }o--|| SubscriptionItem : "can be instantiated as"
    Subscription ||--o{ SubscriptionItem : "consists of"
    SubscriptionItem ||--o{ SubscriptionUsage : "can have usage recorded against"
    SubscriptionPlan ||--o{ PlanResourceLimit : "defines resource limits for"
``` 
