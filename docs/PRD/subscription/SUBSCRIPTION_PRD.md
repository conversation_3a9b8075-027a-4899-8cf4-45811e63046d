# Product Requirements Document: Enhanced Subscription System

## 1. Introduction

**Purpose**:
To implement a robust, scalable, and flexible subscription management and billing system for MonitoringDog. This system will support tiered subscription plans, resource add-ons, usage-based billing for specific services, and seamless integration with Stripe for payment processing and subscription lifecycle management.

**Goals**:
*   Provide clear subscription options and resource entitlements to customers.
*   Enable customers to easily upgrade, downgrade, and manage their subscriptions and add-ons.
*   Implement accurate and timely usage-based billing for services like SMS and phone calls.
*   Improve the reliability and scalability of the billing and subscription infrastructure.
*   Reduce manual intervention in subscription management and billing processes.
*   Ensure data consistency and integrity for all subscription-related information.

**Target Users**:
*   Organizations using MonitoringDog (Customers)
*   MonitoringDog Admin/Support Staff (for managing subscriptions, resolving issues)

## 2. Key Features & User Stories

**2.1. Subscription Plans & Tiers**
*   **Description**: The system will offer various subscription plans (e.g., Free, Basic, Pro, Enterprise) with different pricing and resource allocations.
*   **Requirements (FR)**:
    *   FR1.1: System must define and store multiple subscription plans, each with a name, type (`BASE_PLAN`, `ADDON_METERED_USAGE`), Stripe Price ID, price, currency, recurring interval.
    *   FR1.2: Each base plan must have associated resource limits (e.g., for members, teams, checks, integrations), defining the included quantity and the Stripe Price ID for overages for each resource type. These limits are stored in a dedicated `PlanResourceLimit` entity linked to the `SubscriptionPlan`.
    *   FR1.3: System must allow administrators to activate or deactivate plans.
    *   FR1.4: Users (Org Admins) must be able to view available subscription plans and their details (pricing, resource limits).

**2.2. Subscription Lifecycle Management**
*   **Description**: Customers can subscribe to plans, and the system manages the lifecycle of these subscriptions (trial, active, past due, canceled, etc.) synchronized with Stripe.
*   **Requirements (FR)**:
    *   **FR2.2.1 (New Subscription via Stripe Checkout):** System must allow an organization to initiate a new subscription to a selected base plan via Stripe Checkout.
        *   FR2.2.1.1: The backend (User Portal Backend) will create a Stripe Checkout Session for the selected plan. This session will be configured with line items for the base plan's recurring price and any relevant metered price components for plan-specific overages and transactional services that should be part of the Stripe Subscription from inception.
        *   FR2.2.1.2: The user will be redirected from the MonitoringDog UI to the Stripe-hosted Checkout page to enter payment details and confirm the subscription.
        *   FR2.2.1.3: Upon successful completion of the Stripe Checkout session, the User Portal Backend will process Stripe webhooks (e.g., `checkout.session.completed`, `customer.subscription.created`, `invoice.payment_succeeded`) to:
            *   Create or retrieve the Stripe Customer ID and store it locally.
            *   Create local `Subscription` and `SubscriptionItem` records, mirroring Stripe's subscription ID, status (e.g., `active`, `trialing`), current period start/end dates, and linking to the correct local `SubscriptionPlan` and all relevant Stripe Price IDs (base, overage, transactional).
    *   FR2.2.2: System must support the following subscription statuses, kept in sync with Stripe: `trialing`, `active`, `past_due`, `unpaid`, `canceled`, `incomplete`, `incomplete_expired`.
    *   FR2.2.3: System must allow an organization to cancel their subscription via the MonitoringDog UI. The backend will then call the Stripe API to cancel the Stripe Subscription.
        *   FR2.2.3.1: Cancellations can be effective at the period end.
        *   FR2.2.3.2: Record `canceled_at` timestamp.
    *   FR2.2.4: System must handle subscription updates originating from Stripe (e.g., via webhooks for renewals, payment failures, successful payments), including those initiated by users directly in the Stripe Customer Portal (e.g., payment method updates).
    *   FR2.2.5: Only one active (non-canceled, non-expired) base subscription should exist per organization.
    *   FR2.2.6: System should support trial periods for subscriptions, with a defined `trial_end_date`.
    *   **FR2.2.7 (Payment Delinquency Grace Period for Transactional Services):**
        *   When a subscription enters a `past_due` status (typically triggered by Stripe webhooks like `invoice.payment_failed` or `customer.subscription.past_due`), a configurable grace period (e.g., 7 days, managed via system configuration) should be initiated.
        *   During this grace period, the organization should retain entitlement to use essential transactional services (e.g., SMS, Phone Call notifications as per Section 2.5). Usage of these services during the grace period will continue to be tracked and billed as per normal procedures.
        *   The enrichment flow that determines entitlement for transactional services (referenced in FR2.5.1.1) must check for an active payment delinquency grace period.
        *   If payment is not successfully processed by the end of the grace period, the subscription status may transition further (e.g., to `unpaid` or `suspended`), and entitlement to use transactional services (and potentially other services) may be revoked. The specifics of service suspension post-grace period are to be detailed under account suspension policies.

**2.3. Resource Overage Billing**
*   **Description**: If an organization consumes more of a specific resource (e.g., members, teams, checks) than what is included in their base plan, they will be billed for the excess usage. The per-unit price for this overage can vary depending on the organization's current base subscription plan.
*   **Requirements (FR)**:
    *   FR3.1: For each `BASE_PLAN`, the included quantities for primary resources (e.g., members, teams, checks) must be defined in associated `PlanResourceLimit` records.
    *   FR3.2: Each `PlanResourceLimit` record associated with a `BASE_PLAN` must also define the specific Stripe Price ID to be used for billing overages of its respective resource. This allows overage pricing to be plan-dependent and resource-specific.
    *   FR3.3: The system will periodically calculate resource usage against included quotas (defined in `PlanResourceLimit`) to determine if overage has occurred.
    *   FR3.4: Overage quantities will be reported to Stripe against the corresponding plan-specific metered Stripe Price ID associated with the organization's subscription.
    *   FR3.5: Resource limits for an organization are based on their base plan's included quantities. The overage policy (see FR4.4) determines behavior when these limits are approached or exceeded.

**2.3.1. Handling Subscription Upgrades and Downgrades**
*   **Description**: When an organization changes their base subscription plan (upgrades or downgrades), the system must correctly handle changes to included resource quotas, overage pricing, and ensure accurate billing for usage under both the old and new plan terms, especially for mid-cycle changes.
*   **Requirements (FR)**:
    *   FR3.6.1: Plan changes are primarily driven by the application, which calls the Stripe API to update the subscription. Local system records are updated in real-time and also synced via Stripe webhooks (`customer.subscription.updated`).
    *   FR3.6.2: Upon a plan change, the organization's included resource quotas (members, teams, checks, etc.) and the Stripe Price IDs for billing overages of these resources must be immediately updated in the local system to reflect the new plan's terms.
    *   **FR3.6.3 (Immediate Overage Calculation for Outgoing Plan):** Before the Stripe subscription's plan items are updated to the new plan's items, an immediate, on-demand overage calculation must be performed for the outgoing plan.
        *   FR3.6.3.1: This calculation will determine any accrued overage for resources (e.g., members, teams) from the beginning of the current billing cycle up to the moment of the plan change.
        *   FR3.6.3.2: Any determined overage quantity must be reported to Stripe using the outgoing plan's specific overage Stripe Price IDs.
        *   FR3.6.3.3: A `SubscriptionUsage` record must be created locally to log this reported overage, detailing the quantities, the outgoing plan's overage Stripe Price IDs, and the relevant period.
    *   FR3.6.4: After the immediate overage calculation (FR3.6.3) is complete and the Stripe subscription has been updated to the new plan, subsequent periodic overage calculations for the remainder of the billing cycle (and future cycles) will use the new plan's included quotas and new plan-specific overage Stripe Price IDs.
    *   FR3.6.5: Proration of the base plan's recurring charges due to upgrades/downgrades will be handled by Stripe according to its configured proration behavior. The immediate overage calculation (FR3.6.3) ensures metered usage is accurately attributed to the correct plan's rates.

**IMPLEMENTATION REQUIREMENTS (Missing Components):**

*   **FR3.6.6 (Automatic Billing Cycle Reset with Proration):** Use Stripe's built-in proration with billing cycle reset for clean plan transitions.

    **Technical Implementation Details:**
    
    *   **FR3.6.6.1 (Billing Cycle Reset Strategy):** Update subscription with billing cycle anchor reset:
        ```typescript
        await stripe.subscriptions.update(subscriptionId, {
          items: [{ 
            id: subscriptionItemId, 
            price: newPlanStripePriceId 
          }],
          proration_behavior: 'create_prorations',
          billing_cycle_anchor: Math.floor(Date.now() / 1000) // Reset cycle to NOW
        })
        ```
    
    *   **FR3.6.6.2 (Immediate Charge Calculation):** With billing cycle reset, immediate charge includes:
        ```
        immediateCharge = proratedCredit + fullNewPlanPrice + totalOverageAmount
        ```
        Where:
        - `proratedCredit`: Refund for unused current plan time (negative amount)
        - `fullNewPlanPrice`: Complete price for new plan's full billing cycle
        - `totalOverageAmount`: Sum of all resource overage charges
    
    *   **FR3.6.6.3 (Automatic Invoice Behavior):** Stripe automatically:
        - Creates invoice immediately with proration + new plan charges
        - Attempts payment using customer's default payment method
        - Handles tax calculation and receipt generation
        - Updates subscription to new plan and resets billing cycle
    
    *   **FR3.6.6.4 (Preview Calculation Update):** Preview must reflect billing cycle reset approach:
        
        **New Preview Logic:**
        ```typescript
        // Calculate immediate charges for billing cycle reset
        const currentDate = new Date()
        const currentPeriodEnd = new Date(subscription.currentPeriodEnd)
        const daysRemaining = Math.ceil((currentPeriodEnd.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24))
        const daysInCurrentPeriod = Math.ceil((currentPeriodEnd.getTime() - new Date(subscription.currentPeriodStart).getTime()) / (1000 * 60 * 60 * 24))
        
        // Calculate prorated credit for unused current plan time
        const currentPlanMonthlyPrice = currentPlan.price // in cents
        const proratedCredit = Math.round((daysRemaining / daysInCurrentPeriod) * currentPlanMonthlyPrice)
        
        // New plan full price for complete billing cycle starting now
        const newPlanMonthlyPrice = newPlan.price // in cents
        
        // Calculate total overage charges (existing logic)
        const totalOverageAmount = calculateOverageCharges(organizationId, currentPlan)
        
        const preview = {
          immediateCharges: {
            proratedCredit: -proratedCredit,              // e.g., -500 (refund)
            newPlanCharge: newPlanMonthlyPrice,           // e.g., 5000 (full new plan)
            overageCharges: { members: 1000, teams: 500 }, // overage amounts
            totalAmountDue: -proratedCredit + newPlanMonthlyPrice + totalOverageAmount // e.g., 5500
          },
          nextInvoice: {
            nextInvoiceDate: addMonths(currentDate, 1).toISOString(), // New cycle: +1 month from now
            nextInvoiceTotal: newPlanMonthlyPrice         // Regular monthly price
          },
          billingCycleChange: {
            message: `Your billing cycle will change from the ${getOrdinalDay(subscription.billingCycleAnchor)} to the ${getOrdinalDay(currentDate.getDate())} of each month`,
            currentBillingDate: getOrdinalDay(subscription.billingCycleAnchor),
            newBillingDate: getOrdinalDay(currentDate.getDate()),
            nextBillingDate: addMonths(currentDate, 1).toISOString()
          },
          summaryMessage: `Switching from ${currentPlan.name} ($${currentPlanMonthlyPrice/100}/mo) to ${newPlan.name} ($${newPlanMonthlyPrice/100}/mo). You will be charged $${(totalAmountDue/100).toFixed(2)} today and your billing date will change to the ${getOrdinalDay(currentDate.getDate())} of each month.`
        }
        ```
        
        **Key Changes from Previous Preview Logic:**
        - **No Stripe preview API calls needed** - calculate manually
        - **Full new plan price** instead of prorated amount
        - **Billing cycle change notification** - inform user of date change
        - **Higher immediate charges** - prorated credit + full new plan + overages
        - **Updated next invoice calculation** - based on new billing cycle date
    
    *   **FR3.6.6.5 (Error Handling):** Handle Stripe subscription update failures:
        - Payment declined: Subscription update may still occur, handle partial state
        - Insufficient funds: Provide clear error message and retry options
        - Authentication required: Return payment URL for customer action
        - API failures: Implement retry with exponential backoff

*   **FR3.6.7 (Local Database Synchronization):** After successful Stripe subscription update, the system must update all local database records to reflect the new plan.
    *   FR3.6.7.1: Update the existing `SubscriptionItem.subscriptionPlanId` to reference the new plan ID (maintaining Stripe subscription item ID continuity).
    *   FR3.6.7.2: Update organization's resource quotas cache to reflect new plan limits immediately.
    *   FR3.6.7.3: Create audit trail record in `PlanChangeAudit` table documenting: old plan ID, new plan ID, change timestamp, triggering user, and reason.
    *   FR3.6.7.4: Implement database transaction wrapping both Stripe API calls and local database updates to ensure atomicity.
    *   FR3.6.7.5: Implement rollback mechanism to revert Stripe changes if local database updates fail.

**2.4. Resource Limit Enforcement**
*   **Description**: The system checks if an organization has sufficient resources before allowing actions that consume limited resources (e.g., adding a team member, creating a check), based on the quotas included in their base plan.
    While the system supports distinct overage policies, the standard business practice for quantifiable plan resources such as members, teams, and checks will be to utilize the `charge` policy to enable overage billing as detailed in Section 2.6. The `block` and `grace` policies offer flexibility for specific future plan variations or exceptional circumstances.
*   **Requirements (FR)**:
    *   FR4.1: System must be able to check the current usage of a specific resource (members, teams, checks) for an organization.
    *   FR4.2: System must determine the included limit for a specific resource based on the organization's active base plan.
    *   FR4.3: System must determine if an organization is allowed to consume more of a resource based on current usage vs. included limit and the overage policy.
    *   FR4.4: System must support an overage policy for fixed resources (`block`, `charge`, `grace`) as defined in the subscription.
        *   If `block`: Prevents exceeding the included quota. No overage billing occurs.
        *   If `charge`: Allows exceeding the included quota. Overage is calculated and billed as per Section 2.5, Part B. This is the typical mode for enabling overage billing.
        *   If `grace`: Allows exceeding the quota up to a certain threshold or for a period without immediate charge. Billing for overage beyond the grace allowance would follow the `charge` model. (Detailed grace period logic is TBD and adds complexity).

**2.5. Transactional Usage Billing (e.g., SMS, Phone Calls)**
*   **Description**: Services like SMS and Phone Calls are billed based on actual consumption. Usage is aggregated and reported to Stripe at the end of the billing cycle.
*   **Requirements (FR)**:
    *   FR2.5.1: The system will track usage of transactional services (e.g., SMS, Phone Calls) and store them as `SubscriptionUsage` records in the PostgreSQL database.
    *   FR2.5.2: At the end of each billing cycle, a scheduled job will aggregate the usage for each transactional service and report it to Stripe's Meter Events API.

**2.6. Plan Resource Overage Billing (e.g., Members, Teams, Checks)**
*   **Description**: Usage of base plan resources (like members, teams, checks) beyond the included quantities defined in an organization's subscription plan is billed as overage. This is calculated in near real-time based on domain events indicating resource manipulation (e.g., a member is added, a check is created) and reported to Stripe using plan-dependent overage prices.
*   **Requirements (FR)**:
    *   FR2.6.1: When a plan-limited resource (e.g., member, team, check) is created, deleted, or modified, the responsible module within `user-portal-backend` will trigger the `ResourceBillingService`.
    *   FR2.6.2: The `ResourceBillingService` will publish a message to an SQS queue (`ResourceManipulationQueue`) for asynchronous processing.
    *   FR2.6.3: A dedicated AWS Lambda function (`OverageCalculationHandler`) will process messages from the queue, calculate the current overage, and report it to Stripe.
    *   FR2.6.4: The overage calculation will be based on the current resource usage, the plan's included limits, and the overage policy.
    *   FR2.6.5: The calculated overage quantity will be reported to Stripe's Meter Events API using the appropriate plan-dependent overage Stripe Price ID.
    *   FR2.6.6: A `SubscriptionUsage` record will be created in the PostgreSQL database to log the reported overage.

**2.7. Stripe Integration**
*   **Description**: Deep integration with Stripe for customer management, subscription handling, payment processing, and usage reporting.
*   **Requirements (FR)**:
    *   FR2.7.1: System must use Stripe API to create and manage Stripe Customer objects.
    *   **FR2.7.2 (Stripe API for Subscriptions):** System must use the Stripe API to create Checkout Sessions for new subscriptions. For existing subscriptions, it must use the Stripe API to manage (update for plan changes, cancel) Stripe Subscription objects. Local subscription records are created and kept in sync primarily through Stripe webhooks following Checkout completion and for ongoing lifecycle events.
    *   FR2.7.3: System must securely handle Stripe API keys.
    *   FR2.7.4: All Stripe API calls must implement retry logic for transient errors (e.g., 429, 5xx), with exponential backoff and jitter. Non-retryable errors (e.g., 4xx other than 429) should be handled appropriately.
    *   FR2.7.5: System must expose a webhook endpoint to receive and process events from Stripe.
        *   FR2.7.5.1: Webhook handler must verify Stripe signatures.
        *   FR2.7.5.2: Webhook handler must be idempotent.
        *   FR2.7.5.3: Handle key events like `customer.subscription.created/updated/deleted`, `invoice.payment_succeeded/failed`, `invoice.upcoming`, etc., to update local subscription status and data.
    *   FR2.7.6: System must use Stripe's Meter Events API (or equivalent) for reporting metered usage.
        *   FR2.7.6.1: Stripe Price IDs for transactional services (e.g., SMS, Phone Calls) will be globally configured (e.g., via environment variables). Stripe Price IDs for plan resource overages will be stored on the `PlanResourceLimit` entities associated with a `SubscriptionPlan`. The system must use the correct Price ID when reporting usage.
    *   **FR2.7.7 (Stripe Customer Portal Integration):** The system will integrate with the Stripe Customer Portal. Initial subscription creation will be handled via a Stripe-hosted payment page (e.g., Stripe Checkout). Users will primarily use the Stripe Customer Portal for managing their payment methods and viewing their invoice history. Subsequent lifecycle actions like plan changes and cancellations will be initiated through the MonitoringDog application UI to ensure all internal pre-processing (like immediate overage calculation) and post-processing logic is correctly executed.

**2.8. API & User Interface (High-Level)**
*   **Description**: APIs for managing subscriptions and viewing usage, and UIs for customers to interact with their subscriptions.
*   **Requirements (FR)**:
    *   FR2.8.1: Provide REST API endpoints for:
        *   Listing available subscription plans (`GET /api/v1/plans`).
        *   Fetching current organization's subscription details (`GET /api/v1/subscription`).
        *   **FR2.8.1.1 (Create Stripe Checkout Session):** Creating a Stripe Checkout Session for initiating a new subscription to a selected plan.
        *   Fetching usage history for metered services (`GET /api/v1/usage/history`) with filtering by date range and usage type.
        *   **FR2.8.1.2 (Stripe Customer Portal Session):** Generating a secure session URL to allow an authenticated organization user to access the Stripe Customer Portal.
    *   FR2.8.2: Frontend UI (Web UI) must allow users to:
        *   View and select subscription plans, and initiate a new subscription by being redirected to a Stripe Checkout page to complete payment and subscribe.
        *   Manage their current subscription (e.g., view status). Plan changes (upgrades/downgrades) and cancellations are initiated through the MonitoringDog UI.
        *   **FR2.8.2.1 (Access Stripe Customer Portal):** Access a link/button to redirect to the Stripe Customer Portal for managing their payment methods and viewing invoice history.
        *   View current resource usage against limits (as per FR2.4).

## 3. Data & Schema (Key Entities)

*   **Organizations**: Represents customer entities.
*   **Subscription Plans**: Catalog of available base plans with their properties (like base Stripe Price ID). Included resource quotas and plan-specific Stripe Price IDs for resource overages are defined in associated `PlanResourceLimit` records. Also includes configurations for globally priced transactional metered services (e.g. SMS, Phone Calls, whose Stripe Price IDs are managed via environment variables but entitlement may be checked against plan features).
*   **Subscriptions**: Active or past subscriptions for an organization, linking to Stripe Customer and Subscription. Defines overall status and billing period. The Stripe Subscription will contain line items for the base recurring price and metered prices for potential overages (sourced from `PlanResourceLimit`).
*   **Subscription Items**: Line items within a local subscription, representing the base plan component or specific metered service components. For base plans, this item references the recurring Stripe Price ID. Associated overage components are managed via their specific metered Stripe Price IDs stored on the linked `PlanResourceLimit` records and used for reporting.
*   **Subscription Usage**: Records individual metered usage events (SMS, Phone Call) and also records calculated overage quantities for plan resources (Members, Teams, Checks, etc., as defined by `PlanResourceLimit.resourceType`) reported to Stripe. Crucially stores the specific `stripePriceId` used for reporting any usage (which for overages would come from the relevant `PlanResourceLimit.overageStripePriceId`).
*   **PlanResourceLimit (New Entity)**: Defines a specific resource limit for a `SubscriptionPlan`. Includes `resourceType` (e.g., MEMBERS, TEAMS), `includedQuantity`, and `overageStripePriceId`.

## 4. Design & Architectural Guidelines

*   **Simplicity**: Aim for a clear and understandable architecture, with a clear separation of concerns between use cases and services.
*   **Reliability**: Ensure robust error handling, retries for external calls, and idempotent operations, especially for billing.
*   **Scalability**: Leverage asynchronous processing for tasks that can be deferred, such as sending notifications or generating reports.
*   **Consistency**: Use PostgreSQL as the single source of truth for local subscription data, synchronized with Stripe. All usage, whether transactional or overage, should be logged with sufficient detail for auditing.
*   **Caching**: Implement caching (e.g., Redis) for frequently accessed, rarely changing data like included resource limits (derived from `SubscriptionPlan`) to improve performance of checks.
*   **Configuration**: Key integration details (Stripe keys, SQS queue URLs, Stripe Price IDs for transactional services, scheduling parameters for overage calculation) must be configurable via environment variables or a dedicated configuration service.

## 5. Success Criteria

*   **Functional**:
    *   Organizations can subscribe to plans and manage them.
    *   Add-ons can be purchased and contribute to resource limits.
    *   SMS/Phone usage is accurately tracked, reported to Stripe, and billed.
    *   Stripe integration (subscriptions, webhooks, metered billing) works end-to-end.
    *   Resource limits are enforced according to the defined policy.
*   **Non-Functional**:
    *   Achieve NFRs for uptime, performance, and data integrity.
*   **Operational**:
    *   Monitoring dashboards deployed and providing actionable insights.
    *   Alerts configured and tested for critical system events.
    *   Runbooks documented for common operational procedures and troubleshooting.
    *   Team trained on the new system and its operational aspects. 
