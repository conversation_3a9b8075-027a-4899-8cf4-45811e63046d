# Brownfield Architecture Document: Subscription System Rework

## 1. Introduction

### 1.1. Purpose
This document details the proposed architectural changes to the MonitoringDog Subscription System. The goal is to refactor the existing system to align with the requirements outlined in `SUBSCRIPTION_PRD.md`, `subscription_schema_diagram.md`, and the high-level flows in `subscription_flows_and_architecture.md`. This rework aims to create a robust, scalable, and flexible subscription management and billing system.

### 1.2. Scope
The scope of this architecture document covers:
- Refactoring of core subscription entities.
- Enhancements to subscription lifecycle management (new subscriptions, plan changes, cancellations).
- Implementation of transactional usage billing (e.g., SMS, Calls).
- Implementation of plan resource overage billing (e.g., members, teams, checks).
- Refinement of Stripe integration, including webhook handling.
- Necessary changes within the User Portal Backend and Infrastructure App subsystems.

### 1.3. Goals
- Align system implementation with the detailed PRD.
- Establish a clear, relational data model for subscription and usage data.
- Ensure accurate and timely billing for all services.
- Improve maintainability and scalability of the subscription system.

### 1.4. Target Audience
- Development Team
- Product Management
- QA Team
- Operations Team

### 1.5. Referenced Documents
- `SUBSCRIPTION_PRD.md`
- `subscription_schema_diagram.md`

## 2. Current Architecture Assessment (Brownfield Context)

### 2.1. Overview of Existing System
The existing subscription system has foundational components for managing subscriptions and interacting with Stripe. Key components reside in `apps/user-portal-backend/src/modules/subscription/` and parts of `apps/infrastructure/`.

### 2.2. Key Pain Points & Gaps
- **Data Model Discrepancies:** Existing entities (`SubscriptionPlan`, `Subscription`, `SubscriptionItem`, `SubscriptionUsage`) significantly deviate from the target schema (`subscription_schema_diagram.md`) and PRD requirements, leading to potential data inconsistencies and difficulties in implementing new billing logic.
- **Transactional Usage Billing Flow (PRD 2.5):** Largely missing. The "Enrichment Flow" and the SQS-based reporting mechanism are not implemented.
- **Plan Resource Overage Billing (PRD 2.6):** The event-driven architecture for overage billing is now implemented.
- **Subscription Lifecycle Management Gaps:**
    - Plan Changes (Upgrade/Downgrade): The implementation in `subscription.usecase.ts` directly handles plan changes, which differs from a purely webhook-driven approach.
    - Incomplete Stripe Webhook Handling: Key handlers like `handleInvoicePaymentFailed` are incomplete.

### 2.3. Impact on Current Implementation & Refactoring Strategy (No Data Migration)
Given that the subscription feature is still in development and no data migration is required, a comprehensive refactoring approach will be taken to align the system with this target architecture.

#### 2.3.1. Core Data Entities (`apps/user-portal-backend/src/modules/subscription/entities/`)
The existing entities will be **fundamentally refactored or replaced** to match the definitions in Section 3.2 of this document.

#### 2.3.2. Use Cases & Services (`apps/user-portal-backend/src/modules/subscription/`)
- **`SubscriptionUseCase`**:
    - **Refactor**: `createCheckoutSession` to pass all plan-specific overage `stripePriceId`s to Stripe Checkout.
    - **Add New Methods**: `initiatePlanChange` (with immediate overage calculation for outgoing plan).
- **`StripeUseCase`**:
    - **Refactor Webhook Handlers**:
        - `handleCheckoutSessionCompleted`: Align with new relational entities.
        - **Implement Fully**: `handleInvoicePaymentFailed` (set `past_due` status, `gracePeriodEndsAt`). `handleCustomerSubscriptionUpdated` (sync all local entities, handle plan changes).

#### 2.3.3. Infrastructure Components (`apps/infrastructure/` and New)
- **`OverageCalculationLambda (Infrastructure)`**: The Lambda function that processes messages from `ResourceManipulationQueue` for real-time overage calculation and reporting to Stripe is implemented.

#### 2.3.4. Domain Event-Driven Overage Architecture (Implemented)
- **✅ Domain Events Implementation**: Core business modules (User, Team, Check) now emit domain events using NestJS EventEmitter2 pattern when plan-limited resources are manipulated.
- **✅ Event Handlers in Subscription Module**: Event handlers in the subscription module listen for domain events and publish structured messages to `ResourceManipulationQueue` SQS.
- **✅ ResourceManipulationQueueService**: Moved from frameworks/queue/sqs to subscription/infras/sqs for better architectural boundaries and clean architecture compliance.
- **✅ OverageCalculationLambda (Infrastructure)**: Lambda function processes messages from `ResourceManipulationQueue` to perform real-time overage calculation and reporting to Stripe.
- **✅ Clean Architecture Compliance**: Removed direct dependencies between core business logic and infrastructure services, implementing proper separation of concerns.

#### 2.3.5. Database (PostgreSQL)
- The schemas for `SubscriptionPlans`, `Subscriptions`, `SubscriptionItems`, and `SubscriptionUsage` will be updated to match the new definitions (Section 3.2).

This refactoring strategy prioritizes alignment with the PRD and the target architecture, leveraging the flexibility of not needing data migration.

## 3. Proposed Architecture

This section details the target architecture based on the PRD, schema diagrams, and Winston's architectural plan.

### 3.1. Guiding Principles
- **Align with PRD:** All components and flows will be designed to meet the requirements specified in `SUBSCRIPTION_PRD.md`.
- **Single Source of Truth:** PostgreSQL will be the source of truth for local subscription data, plans, and usage records, synchronized with Stripe where appropriate.
- **Clear Data Model:** Adherence to the relational model defined in `subscription_schema_diagram.md`.
- **Event-Driven & Asynchronous Processing:** For scalable usage tracking and reporting.
- **Idempotency:** Critical for billing operations and webhook handling.
- **Modularity & Separation of Concerns:** Clear responsibilities for different services and lambdas.

### 3.2. Data Model (PostgreSQL)
The following entities will be implemented/refactored in `apps/user-portal-backend/src/modules/subscription/entities/` to strictly match `subscription_schema_diagram.md`.

#### 3.2.1. `Organization`
- `id: string (PK)`
- `name: string`

#### 3.2.2. `StripeCustomer`
- `stripeCustomerId: string (PK)`
- `email: string`
- `organizationId: string (FK to Organization)`

#### 3.2.3. `StripeSubscription`
- `stripeSubscriptionId: string (PK)`
- `status: string`
- `currentPeriodStartDate: date`
- `currentPeriodEndDate: date`

#### 3.2.4. `StripePrice`
- `stripePriceId: string (PK)`
- `type: string ("recurring", "metered", "one-time")`
- `unitAmount: float`
- `currency: string`

#### 3.2.5. `StripeSubscriptionItem`
- `stripeSubscriptionItemId: string (PK)`
- `quantity: integer`

#### 3.2.6. `StripeUsageRecord`
- `stripeUsageRecordId: string (PK)`
- `quantity: integer`
- `timestamp: datetime`

#### 3.2.7. `SubscriptionPlan`
- `id: string (PK)`
- `name: string`
- `planType: PlanType (enum: "BASE_PLAN", "ADDON_METERED_USAGE")`
- `stripePriceId: string (FK to StripePrice)`
- `isActive: boolean`
- `createdAt: datetime`
- `updatedAt: datetime`

#### 3.2.8. `PlanResourceLimit` (New Entity)
- `id: string (PK)`
- `subscriptionPlanId: string (FK to SubscriptionPlan)`
- `resourceType: string`
- `includedQuantity: integer`
- `overageStripePriceId: string (FK to StripePrice)`
- `createdAt: datetime`
- `updatedAt: datetime`

#### 3.2.9. `Subscription`
- `id: string (PK)`
- `organizationId: string (FK to Organization)`
- `stripeSubscriptionId: string (FK to StripeSubscription)`
- `stripeCustomerId: string (FK to StripeCustomer)`
- `status: SubscriptionStatus (enum)`
- `currentPeriodStartDate: date`
- `currentPeriodEndDate: date`
- `gracePeriodEndsAt: datetime (nullable)`
- `usageBillingEnabled: boolean`
- `overagePolicyFixedResources: OveragePolicyType (enum)`
- `canceledAt: datetime (nullable)`
- `createdAt: datetime`
- `updatedAt: datetime`

#### 3.2.10. `SubscriptionItem`
- `id: string (PK)`
- `subscriptionId: string (FK to Subscription)`
- `stripeSubscriptionItemId: string (FK to StripeSubscriptionItem)`
- `stripePriceId: string (FK to StripePrice)`
- `subscriptionPlanId: string (FK to SubscriptionPlan)`
- `itemType: PlanType (enum)`
- `quantity: integer`
- `status: SubscriptionItemStatus (enum)`
- `createdAt: datetime`
- `updatedAt: datetime`

#### 3.2.11. `SubscriptionUsage`
- `id: string (PK)`
- `subscriptionItemId: string (FK to SubscriptionItem)`
- `organizationId: string (FK to Organization)`
- `usageType: UsageType (enum)`
- `usageValue: float`
- `usageTimestamp: datetime`
- `billingCycleStartDate: date`
- `billingCycleEndDate: date`
- `reportedToStripe: boolean`
- `stripePriceId: string (FK to StripePrice)`
- `stripeUsageRecordId: string (FK to StripeUsageRecord, nullable)`
- `isBillable: boolean (nullable)`
- `notes: string (nullable)`
- `createdAt: datetime`

### 3.3. Component Design & Responsibilities

#### 3.3.1. User Portal Backend (`apps/user-portal-backend/`)

##### *******. Modules & Services
- **`SubscriptionModule`**:
    - **Entities**: Refactored `SubscriptionPlan`, `Subscription`, `SubscriptionItem`, `SubscriptionUsage` as defined in 3.2.
    - **Repositories**: Corresponding repositories for each entity.
    - **`SubscriptionPlanUseCase/Service`**:
        - CRUD operations for `SubscriptionPlan` entities.
    - **`SubscriptionUseCase/Service`**:
        - `createCheckoutSession(userId, planId, organizationId)`
        - `initiatePlanChange(userId, organizationId, newPlanId)`
        - `cancelSubscription(userId, organizationId)`
        - `getSubscriptionDetails(organizationId)`
        - `getUsageHistory(organizationId, filters)`
    - **`StripeUseCase/Service`**:
        - Handles all direct interactions with the Stripe API.
        - `findOrCreateCustomer(email, name, metadata)`
        - `createCheckoutSessionForSubscription(customerId, userId, basePriceId, meteredPriceIds[], metadata, successUrl, cancelUrl)`
        - `updateStripeSubscriptionPlan(stripeSubscriptionId, oldBasePlanStripeSubscriptionItemId, newBasePlanStripePriceId, newOverageStripePriceIds[])`
        - `cancelStripeSubscriptionAtPeriodEnd(stripeSubscriptionId)`
        - `reportUsage(stripeCustomerId, stripePriceId, quantity, timestamp, idempotencyKey)`
        - **Webhook Handlers (called by `StripeWebhookController`):**
            - `handleCheckoutSessionCompleted(sessionData)`
            - `handleInvoicePaid(invoiceData)`
            - `handleInvoicePaymentFailed(invoiceData)`
            - `handleCustomerSubscriptionUpdated(subscriptionData)`
            - `handleCustomerSubscriptionDeleted(subscriptionData)`
    - **`Domain Event-Driven Overage Billing` (PRD 2.6):**
        - **Event Emission**: Core business modules (User, Team, Check) emit domain events using EventEmitter2 when plan-limited resources are manipulated.
        - **Event Handlers**: Subscription module event handlers listen for domain events and publish structured messages to `ResourceManipulationQueue` SQS.
        - **Real-time Processing**: `OverageCalculationLambda` processes messages from the queue to calculate and report overage in near real-time.
    - **`SubscriptionLimitService` (PRD 2.4):**
        - Method `checkLimit(organizationId, resourceType)`
- **`StripeWebhookController`**:
    - Endpoint `/stripe/webhooks`.
    - Verifies Stripe signature.
    - Delegates to appropriate handler in `StripeUseCase`.

#### 3.3.2. Infrastructure App (`apps/infrastructure/`) & Shared Components

- **Transactional Usage Billing**:
    - The system will track usage of transactional services (e.g., SMS, Phone Calls) and store them as `SubscriptionUsage` records in the PostgreSQL database.
    - At the end of each billing cycle, a scheduled job will aggregate the usage for each transactional service and report it to Stripe's Meter Events API.
- **`ResourceManipulationQueue` (Implemented SQS Queue for Overage Calculation):**
    - Standard SQS queue configured for real-time overage calculation.
    - Receives messages when plan-limited resources (member, team, check) are manipulated via domain events.
- **`OverageCalculationLambda` (Implemented Lambda for Plan Resource Overage - PRD 2.6):**
    - **Location**: `apps/infrastructure/src/domains/billing/lambda/handlers/overage-calculation-handler.ts`
    - **Trigger**: `ResourceManipulationQueue` SQS events.
    - **Dependencies**: Direct database access via Drizzle ORM, Stripe integration for meter events.

### 3.4. Key Flows & Interactions

1.  **New Subscription Creation:**
    - UI -> UPB API (`createCheckoutSession`) -> Stripe Checkout -> Stripe Webhooks -> UPB `StripeWebhookController` -> `StripeUseCase` -> DB.
2.  **Plan Change (Upgrade/Downgrade):**
    - UI -> UPB API (`initiatePlanChange`) -> `SubscriptionUseCase` -> `StripeUseCase` -> Stripe API -> Stripe Webhook -> UPB `StripeWebhookController` -> `StripeUseCase` -> DB.
3.  **Subscription Cancellation:**
    - UI -> UPB API (`cancelSubscription`) -> `SubscriptionUseCase` -> `StripeUseCase` -> Stripe API -> Stripe Webhook -> UPB `StripeWebhookController` -> `StripeUseCase` -> DB.
4.  **Plan Resource Overage Billing (Domain Event-Driven - Implemented):**
    - Resource Manipulation in UPB module -> Emit Domain Event -> Subscription Module Event Handlers -> SQS `ResourceManipulationQueue` -> `OverageCalculationLambda` -> Stripe Meter API -> PostgreSQL `SubscriptionUsage` table.

### 3.5. API Design (Key new/changed endpoints in User Portal Backend)

- `POST /api/v1/subscription/checkout-session`
- `POST /api/v1/subscription/change-plan`
- `POST /api/v1/subscription/cancel`
- `GET /api/v1/subscription`
- `GET /api/v1/plans`
- `GET /api/v1/usage/history`
- `POST /api/v1/stripe/customer-portal-session`
- `POST /stripe/webhooks`

### 3.6. Database Design
- **Primary Database**: PostgreSQL for `user-portal-backend`.
- **Key Tables**: `Organizations`, `SubscriptionPlans`, `Subscriptions`, `SubscriptionItems`, `SubscriptionUsage`.
- **Relationships**: Enforce via Foreign Keys.
- **Indexes**: On FKs, `organizationId`, `stripeSubscriptionId`, `stripeCustomerId`, `usageTimestamp`, `usageType`.

### 3.7. Stripe Integration Details
- **Webhook Endpoint**: Single, secure endpoint in `user-portal-backend`.
- **Stripe Price IDs**:
    - Base recurring plan prices: Stored on `SubscriptionPlan.stripePriceId`.
    - Plan-specific overage prices: Stored on `PlanResourceLimit.overageStripePriceId`.
    - Global transactional service prices: Configured via environment variables.
- **Meter Events API**: Used for all metered usage reporting.
- **Idempotency**: Critical for all Stripe calls.

### 3.8. Security Considerations
- Secure Stripe API Keys.
- Stripe Webhook Signature Verification is mandatory.
- Protect database credentials.
- Input validation for all API endpoints.
- Authorization checks to ensure users can only manage their own organization's subscription.

### 3.9. Deployment Considerations
- Phased rollout if possible.
- Infrastructure changes (SQS, new Lambdas for transactional billing) to be deployed via IaC (CDK).
- Database schema migrations to be handled carefully.
- Ensure environment variables for Stripe keys, Price IDs, queue URLs are correctly configured per environment.

## 4. Migration Plan (High-Level)

If existing subscriptions and usage data need to be mapped to the new model:
1.  **Analyze existing data**.
2.  **Develop Migration Scripts**.
3.  **Test Thoroughly**.
4.  **Execute during a maintenance window**.

## 5. Non-Functional Requirements (NFRs)

- **Reliability**: Webhook handling and usage reporting must be highly reliable.
- **Scalability**: Transactional usage reporting path should scale with event volume.
- **Performance**: API endpoints should be performant.
- **Maintainability**: Clear separation of concerns, well-documented code.
- **Data Integrity**: Ensured by RDBMS constraints and careful data handling.

## 6. Open Issues & Risks

- **Data Migration**: Migrating existing subscriptions to the new model can be complex.
- **Stripe Meter Configuration**: Ensuring Stripe Meters are correctly configured is crucial.
- **Idempotency Implementation**: Requires robust implementation.
- **Testing Complexity**: End-to-end testing of billing flows will be complex.

## 7. Implementation Status Summary

### 7.1. Completed Components (✅)

**Domain Events Architecture**:
- ✅ `UserAddedToOrganizationEvent`
- ✅ `TeamResourceEvent`
- ✅ `CheckResourceEvent`
- ✅ EventEmitter2 integration

**Event Handlers in Subscription Module**:
- ✅ `UserAddedToOrganizationHandler`
- ✅ `TeamResourceHandler`
- ✅ `CheckResourceHandler`
- ✅ Fire-and-forget pattern

**Infrastructure Components**:
- ✅ `ResourceManipulationQueueService`
- ✅ `OverageCalculationLambda`
- ✅ Stripe Meter Events API integration
- ✅ Database integration via Drizzle ORM

**Clean Architecture Implementation**:
- ✅ Removed direct dependencies between core modules and infrastructure services
- ✅ Domain events provide complete decoupling between business logic and billing
- ✅ Repository pattern maintained with proper infrastructure abstractions

### 7.2. Key Architectural Benefits Achieved

1. **Real-time Overage Calculation**
2. **Clean Architecture Compliance**
3. **Reliability**
4. **Scalability**
5. **Auditability**

### 7.3. Integration Points Verified

- Domain events properly trigger billing calculations
- SQS message format matches Lambda handler expectations
- Database queries correctly count actual resource usage
- Stripe integration follows idempotency best practices
- Error handling preserves system reliability
