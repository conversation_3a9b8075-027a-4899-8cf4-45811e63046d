module.exports = {
  root: true,
  ignorePatterns: ['.eslintrc.js', '**/*'],
  plugins: [
    '@typescript-eslint/eslint-plugin',
    'import',
    'unused-imports',
    '@nx',
  ],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  env: {
    node: true,
    jest: true,
  },
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    'no-unused-vars': 'off',
    "no-unsafe-finally": "off",
    "endOfLine": 0,
    'prettier/prettier': [
      'error',
      {
        endOfLine: 'auto',
      },
    ],
    'import/order': [
      'warn',
      {
        pathGroups: [
          {
            pattern: '@backend/**',
            group: 'external',
            position: 'after',
          },
        ],
        pathGroupsExcludedImportTypes: ['builtin'],
        groups: [
          'external',
          'builtin',
          'parent',
          'sibling',
          'index',
          'type',
          'object',
        ],
        'newlines-between': 'always',
      },
    ],
    'unused-imports/no-unused-imports': 'warn',
    'unused-imports/no-unused-vars': [
      'warn',
      {
        vars: 'all',
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_',
      },
    ],
  },
  overrides: [
    // {
    //   files: ['*.ts', '*.tsx', '*.js', '*.jsx'],
    //   rules: {
    //     '@nx/enforce-module-boundaries': [
    //       'error',
    //       {
    //         enforceBuildableLibDependency: true,
    //         allow: [],
    //         depConstraints: [
    //           {
    //             sourceTag: '*',
    //             onlyDependOnLibsWithTags: ['*'],
    //           },
    //         ],
    //       },
    //     ],
    //   },
    // },
    {
      files: ['*.ts', '*.tsx'],
      extends: ['plugin:@nx/typescript'],
      rules: {},
    },
    {
      files: ['*.js', '*.jsx'],
      extends: ['plugin:@nx/javascript'],
      rules: {},
    },
    {
      files: ['*.spec.ts', '*.spec.tsx', '*.spec.js', '*.spec.jsx'],
      env: {
        jest: true,
      },
      rules: {},
    },
  ],
};
