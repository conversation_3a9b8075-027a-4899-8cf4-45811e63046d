image: atlassian/default-image:4

definitions:
  services:
    docker-6g:
      type: docker
      memory: 6144
  steps:
    - step: &deploy-user-portal-ecs
        name: Deploy User Portal to ECS
        size: 2x
        services:
          - docker-6g
        caches:
          - docker
        script:
          - export DOCKER_BUILDKIT=1
          - ./secrets-scripts/unpack-secrets.sh -p $SECRETS_KEY_PHRASE
          - docker build -t user-portal-backend:latest -t user-portal-backend:$NAMESPACE . -f apps/user-portal-backend/Dockerfile

          - pipe: atlassian/aws-ecr-push-image:2.4.0
            variables:
              IMAGE_NAME: user-portal-backend
              TAGS: '${NAMESPACE} latest'

          - pipe: atlassian/aws-ecs-deploy:1.12.1
            variables:
              CLUSTER_NAME: $ECS_CLUSTER
              SERVICE_NAME: $ECS_SERVICE
              FORCE_NEW_DEPLOYMENT: 'true'
              TASK_DEFINITION: deployment/development/user-portal.json

    - step: &deploy-orchestration-ecs
        name: Deploy Orchestrator to ECS
        size: 2x
        services:
          - docker-6g
        caches:
          - docker
        script:
          - export DOCKER_BUILDKIT=1
          - docker build -t orchestrator:latest -t orchestrator:$NAMESPACE . -f apps/orchestrator/Dockerfile

          - pipe: atlassian/aws-ecr-push-image:2.4.0
            variables:
              IMAGE_NAME: orchestrator
              TAGS: '${NAMESPACE} latest'

          - pipe: atlassian/aws-ecs-deploy:1.12.1
            variables:
              CLUSTER_NAME: $ECS_CLUSTER
              SERVICE_NAME: $ECS_SERVICE
              FORCE_NEW_DEPLOYMENT: 'true'
              TASK_DEFINITION: deployment/development/orchestrator.json

    - step: &deploy-worker-ecs
        name: Deploy Worker to ECS
        size: 2x
        services:
          - docker-6g
        caches:
          - docker
        script:
          - export DOCKER_BUILDKIT=1
          - docker build -t worker:latest -t worker:$NAMESPACE . -f apps/worker/Dockerfile

          - pipe: atlassian/aws-ecr-push-image:2.4.0
            variables:
              IMAGE_NAME: worker
              TAGS: '${NAMESPACE} latest'

          - pipe: atlassian/aws-ecs-deploy:1.12.1
            variables:
              CLUSTER_NAME: $ECS_CLUSTER
              SERVICE_NAME: $ECS_SERVICE
              FORCE_NEW_DEPLOYMENT: 'true'
              TASK_DEFINITION: deployment/development/worker.json

pipelines:
  branches:
    development:
      - parallel:
          - step:
              <<: *deploy-user-portal-ecs
              deployment: development
              condition:
                  changesets:
                    includePaths:
                        - 'apps/user-portal-backend/**'
          - step:
              <<: *deploy-orchestration-ecs
              deployment: development-orch
              condition:
                  changesets:
                    includePaths:
                        - 'apps/orchestrator/**'
          - step:
              <<: *deploy-worker-ecs
              deployment: development-worker
              condition:
                  changesets:
                    includePaths:
                        - 'apps/worker/**'
  custom:
    deploy-all:
      - parallel:
          - step:
              <<: *deploy-user-portal-ecs
              deployment: development
          - step:
              <<: *deploy-orchestration-ecs
              deployment: development-orch
          - step:
              <<: *deploy-worker-ecs
              deployment: development-worker
    deploy-user-portal:
      - step:
          <<: *deploy-user-portal-ecs
          deployment: development
    deploy-orchestrator:
      - step:
          <<: *deploy-orchestration-ecs
          deployment: development-orch
    deploy-worker:
      - step:
          <<: *deploy-worker-ecs
          deployment: development-worker
