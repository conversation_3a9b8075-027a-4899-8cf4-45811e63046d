import inquirer from 'inquirer'
import { initializeApp } from 'firebase/app'
import {
  getAuth,
  connectAuthEmulator,
  signInWithEmailAndPassword,
} from 'firebase/auth'

const app = initializeApp({
  apiKey: 'AIzaSyD-0kTJQ5QJ1Z1bJ1Jy1Jy1Jy1Jy1Jy1Jy', // For testing purposes only
})

const auth = getAuth(app)

connectAuthEmulator(auth, 'http://127.0.0.1:9099')

inquirer
  .prompt([
    {
      type: 'input',
      name: 'email',
      message: 'Email:',
      default: '<EMAIL>',
    },
    {
      type: 'input',
      name: 'password',
      message: 'Password:',
      default: 'password',
    },
  ])
  .then((answers) => {
    const { email, password } = answers

    signInWithEmailAndPassword(auth, email, password).then((userCredential) => {
      console.log('Signed in as:', userCredential.user.email)
      userCredential.user.getIdToken(true).then((token) => {
        console.log('Token:', token)
      })
    })
  })
