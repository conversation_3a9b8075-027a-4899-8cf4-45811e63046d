#!/usr/bin/env node

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { parse, stringify } from 'envfile';
import { getSecretsClient } from 'mysterio';
import _ from 'lodash';
import chalk from 'chalk';
import path from 'path';
import { promises as fs } from 'fs';

// 1. Parse CLI args
const argv = yargs(hideBin(process.argv))
  .option('secretName', {
    type: 'string',
    describe: 'Name of the secret in AWS Secrets Manager',
    demandOption: false,
  })
  .option('env', {
    type: 'string',
    default: process.env.NODE_ENV || 'development',
    describe: 'Environment (development, staging, production, etc.)',
  })
  .option('fileName', {
    type: 'string',
    default: '.env',
    describe: 'Target file for writing the secrets (e.g. .env.development)',
  })
  .option('packageName', {
    type: 'string',
    default: '@monitoring-dog/source',
    describe: 'Package name, used to build default secretName if not provided',
  })
  .argv;

const fetchSecrets = (secretName) => {
  const getSecrets = getSecretsClient({});
  return getSecrets(secretName);
};

const getFilePath = (fileName) => {
  return path.join(process.cwd(), fileName);
};

const readFile = async (fileName) => {
  try {
    const fileContent = await fs.readFile(getFilePath(fileName), 'utf8');
    console.log(chalk.blue('readFile: file content: '), fileContent);
    return parse(fileContent);
  } catch (e) {
    if (!e.message.includes('no such file or directory')) throw e;
    console.log(chalk.yellow(`readFile: ${fileName} not found`));
    return {};
  }
};

const writeFile = (fileName, data) => {
  const fileContent = stringify(data);
  console.log(chalk.blue('writeFile: file content: '), fileContent);
  return fs.writeFile(getFilePath(fileName), fileContent, 'utf8');
};

const diff = (currentObj, newObj) => {
  const added = _.omitBy(newObj, (value, key) => _.isEqual(currentObj[key], value));
  const deleted = _.omitBy(currentObj, (value, key) => _.has(newObj, key));
  const updated = _.omitBy(
    _.mapValues(newObj, (value, key) => (currentObj[key] === value ? undefined : value)),
    _.isUndefined
  );

  return { added, deleted, updated };
};

async function smenv() {
  const { secretName, env, fileName, packageName } = argv;

  console.log(
    chalk.green(
      `Called with secretName=${secretName}, packageName=${packageName}, env=${env}, fileName=${fileName}`
    )
  );

  // Build a fallback secret name if not explicitly provided
  const calcSecretName = secretName || `${packageName}/${env}`;
  console.log(chalk.green(`Pulling secret from AWS for ${calcSecretName}`));

  // 2. Fetch secrets
  const awsSecrets = await fetchSecrets(calcSecretName);
  console.log(chalk.blue(`AWS Secrets: ${JSON.stringify(awsSecrets)}`));

  // 3. Read the current local env file
  const currentSecret = await readFile(fileName);
  console.log(chalk.blue(`Current secrets: ${JSON.stringify(currentSecret)}`));

  // 4. Compare
  const filesDiff = diff(currentSecret, awsSecrets);
  console.log(chalk.blue('Diff:'), filesDiff);

  if (
    _.isEmpty(filesDiff.added) &&
    _.isEmpty(filesDiff.deleted) &&
    _.isEmpty(filesDiff.updated)
  ) {
    console.log(
      chalk.yellow(`No changes detected. Local file "${fileName}" is already synced.`)
    );
    return;
  }

  // 5. Write updated secrets to file
  await writeFile(fileName, awsSecrets);
  console.log(chalk.green(`Secrets written to "${fileName}". Sync complete!`));

  // Optionally log changes in a more concise way
  if (!_.isEmpty(filesDiff.added)) {
    console.log(
      chalk.green(
        `Added ${Object.keys(filesDiff.added).length} secrets: ${JSON.stringify(
          filesDiff.added
        )}`
      )
    );
  }
  if (!_.isEmpty(filesDiff.deleted)) {
    console.log(
      chalk.red(
        `Removed ${Object.keys(filesDiff.deleted).length} secrets: ${JSON.stringify(
          filesDiff.deleted
        )}`
      )
    );
  }
  if (!_.isEmpty(filesDiff.updated)) {
    console.log(
      chalk.yellow(
        `Updated ${Object.keys(filesDiff.updated).length} secrets: ${JSON.stringify(
          filesDiff.updated
        )}`
      )
    );
  }
}

// Entry Point
smenv().catch((err) => {
  console.error(chalk.red('Error during secret sync:'), err);
  process.exit(1);
});
