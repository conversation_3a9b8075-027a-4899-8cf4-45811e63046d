#!/usr/bin/env node
/* eslint-disable @typescript-eslint/no-var-requires */

const { Command } = require('commander')
const fs = require('fs-extra')

const path = require('path')

const program = new Command()

program
  .version('1.0.0')
  .description('CLI for generating folder/file structure with sample template')

program
  .command('create-module <moduleName>')
  .description('Create a new module with the given name')
  .action(async (moduleName) => {
    const projectRoot = process.cwd()
    const src = 'apps/user-portal-backend/src'
    const moduleRoot = path.join(projectRoot, `${src}/modules`, moduleName)

    const entity = {
      [`entities/index.ts`]: `
      export * from './${moduleName}.entity'
      export * from './${moduleName}.mapper'
      `,
      [`entities/${moduleName}.entity.ts`]: `
      import { AggregateRoot } from '@/cores/base/aggregate-root.base'
      import { generateId } from '@/commons/id'
      import { Id } from '@/cores/base/id.type'

      export interface ${capitalize(moduleName)}Props {}

      export interface ${capitalize(moduleName)}Response {
        id: Id
        createdAt: Date
        updatedAt: Date
      }

      export class ${capitalize(moduleName)} extends AggregateRoot<${capitalize(moduleName)}Props> {
        static create(props: ${capitalize(moduleName)}Props) {
          const ${moduleName} = new ${capitalize(moduleName)}({
            id: generateId(),
            props,
          })

          return ${moduleName}
        }
      }

      `,
      [`entities/${moduleName}.mapper.ts`]: `
          import { Injectable } from '@nestjs/common'
          import { plainToClass } from 'class-transformer'

          import { ${capitalize(moduleName)} as ${capitalize(moduleName)}Model } from '@/frameworks/database/entities/${moduleName}.entity'
          import { Mapper } from '@/cores/base/mapper.interface'

          import {  ${capitalize(moduleName)} as ${capitalize(moduleName)}Entity, ${capitalize(moduleName)}Response } from './${moduleName}.entity'

          @Injectable()
          export class ${capitalize(moduleName)}Mapper implements Mapper<${capitalize(moduleName)}Entity, ${capitalize(moduleName)}Model> {
            toPersistence(entity: ${capitalize(moduleName)}Entity): ${capitalize(moduleName)}Model {
              const props = entity.getProps()
              return plainToClass(${capitalize(moduleName)}Model, props)
            }

            toDomain(record: any): ${capitalize(moduleName)}Entity {
              if (!record) {
                return null
              }
              const entity = new ${capitalize(moduleName)}Entity({
                id: record.id,
                createdAt: record.createdAt,
                updatedAt: record.updatedAt,
                props: {},
              })

              return entity
            }

            toResponse(entity: ${capitalize(moduleName)}Entity): ${capitalize(moduleName)}Response {
              if (!entity) {
                return null
              }

              const props = entity.getProps()

              return {
                id: props.id,
                createdAt: props.createdAt,
                updatedAt: props.updatedAt,
              }
            }
          }

          export const ${upperCase(moduleName)}_MAPPER = '${upperCase(moduleName)}_MAPPER'
      `,
    }

    const controller = {
      [`controllers/${moduleName}.controller.ts`]: `
      import { Controller, Get } from '@nestjs/common';
      import { ${capitalize(moduleName)}UseCase } from '../usecases/${moduleName}.usecase';

      @Controller('${moduleName}')
          export class ${capitalize(moduleName)}Controller {
          constructor(private readonly ${moduleName}UseCase: ${capitalize(moduleName)}UseCase) {}
          @Get()
          findAll() {
              return this.${moduleName}UseCase.findAll();
          }
      }
      `,
    }

    const useCase = {
      [`usecases/${moduleName}.usecase.ts`]: `
        import { Inject, Injectable } from '@nestjs/common'
        import { ${capitalize(moduleName)} } from '../entities/${moduleName}.entity'
        import ${capitalize(moduleName)}Repository, {
        ${upperCase(moduleName)}_REPOSITORY,
        } from '../applications/${moduleName}.repository'
        import { Id } from '@/cores/base/id.type'
        import { ${upperCase(moduleName)}_MAPPER, ${capitalize(moduleName)}Mapper } from '../entities/${moduleName}.mapper'

        @Injectable()
        export class ${capitalize(moduleName)}UseCase {
            constructor(
                @Inject(${upperCase(moduleName)}_REPOSITORY) private readonly repo: ${capitalize(moduleName)}Repository,
                @Inject(${upperCase(moduleName)}_MAPPER) private readonly ${moduleName}Mapper: ${capitalize(moduleName)}Mapper,
            ) {}

            findAll() {
                return (await this.repo.findAll()).map((data) => this.${moduleName}Mapper.toResponse(data))
            }
        }

        `,
    }

    const repository = {
      [`applications/${moduleName}.repository.ts`]: `
        import { Id } from '@/cores/base/id.type'
        import { ${capitalize(moduleName)} } from '../entities'

        interface ${capitalize(moduleName)}Repository {
            findAll(): Promise<${capitalize(moduleName)}[]>
            // findOne(id: Id): Promise<${capitalize(moduleName)} | null>
            // findOneBy(conditions: any): Promise<${capitalize(moduleName)} | null>

            // create(user: ${capitalize(moduleName)}): Promise<${capitalize(moduleName)}>
            // update(id: Id, user: Partial<${capitalize(moduleName)}>): Promise<${capitalize(moduleName)}>

            // delete(id: Id): Promise<boolean>
        }

        export default ${capitalize(moduleName)}Repository
        export const ${upperCase(moduleName)}_REPOSITORY = '${upperCase(moduleName)}_REPOSITORY'

        `,
    }

    const infra = {
      [`infras/${moduleName}.infrastructure.ts`]: `
        import { Inject, Injectable } from '@nestjs/common'
        import TypeORMDriver from '@/frameworks/database/drivers/typeorm/typeorm.database'
        import { ${capitalize(moduleName)} as ${capitalize(moduleName)}Model } from '@/frameworks/database/entities/${moduleName}.entity'
        import Database from '@/frameworks/database/database'
        import { ${upperCase(moduleName)}_MAPPER, ${capitalize(moduleName)}Mapper } from '../entities/${moduleName}.mapper'
        import ${capitalize(moduleName)}Repository from '../applications/${moduleName}.repository'
        import { ${capitalize(moduleName)} } from '../entities/${moduleName}.entity'
        
        @Injectable()
        export class ${capitalize(moduleName)}Infrastructure implements ${capitalize(moduleName)}Repository {
        constructor(
            @Inject(Database) private database: Database,
            @Inject(${upperCase(moduleName)}_MAPPER) private ${moduleName}Mapper: ${capitalize(moduleName)}Mapper
        ) {
            this.${moduleName}Model = this.database.typeorm<${capitalize(moduleName)}Model>('${capitalize(moduleName)}')
        }

            private readonly ${moduleName}Model: TypeORMDriver<${capitalize(moduleName)}Model>

            async findAll(): Promise<${capitalize(moduleName)}[]> {
                const data = await this.${moduleName}Model.findAll()
                return data.map((${moduleName}) => this.${moduleName}Mapper.toDomain(${moduleName}))
            }
        }

        `,
    }

    const model = {
      [`frameworks/database/entities/${moduleName}.entity.ts`]: `
            import { Column, Entity } from 'typeorm'

            import { BaseSchema } from './schema.base'

            @Entity('${moduleName}s')
            export class ${capitalize(moduleName)} extends BaseSchema {}
        `,
    }

    // Create sample files
    const moduleFolder = {
      ...entity,
      ...controller,
      ...useCase,
      ...repository,
      ...infra,
    }

    for (const [filePath, content] of Object.entries(moduleFolder)) {
      const fullPath = path.join(
        moduleRoot,
        filePath.replace('module', moduleName),
      )
      await fs.outputFile(fullPath, content)
    }

    const frameworkModule = {
      ...model,
    }

    for (const [filePath, content] of Object.entries(frameworkModule)) {
      const fullPath = path.join(projectRoot, src, filePath)
      await fs.outputFile(fullPath, content)
    }

    await fs.outputFile(
      path.join(moduleRoot, `${moduleName}.module.ts`),
      `import { Module } from '@nestjs/common';
            import { ${capitalize(moduleName)}Controller } from './controllers/${moduleName}.controller';

            @Module({
            controllers: [${capitalize(moduleName)}Controller],
            })
            export class ${capitalize(moduleName)}Module {}
        `,
    )

    await fs.outputFile(
      path.join(moduleRoot, `${moduleName}.module.ts`),
      `
            import { Module } from '@nestjs/common'
            import { DatabaseModule } from '@/frameworks/database/database.module'
            import { ${capitalize(moduleName)}UseCase } from './usecases/${moduleName}.usecase'
            import { ${capitalize(moduleName)}Controller } from './controllers/${moduleName}.controller'
            import { ${capitalize(moduleName)}Infrastructure } from './infras/${moduleName}.infrastructure'
            import { ${upperCase(moduleName)}_REPOSITORY } from './applications/${moduleName}.repository'
            import { ${upperCase(moduleName)}_MAPPER, ${capitalize(moduleName)}Mapper } from './entities/${moduleName}.mapper'

            @Module({
            imports: [DatabaseModule],
            controllers: [${capitalize(moduleName)}Controller],
            providers: [
                { provide: ${upperCase(moduleName)}_REPOSITORY, useClass: ${capitalize(moduleName)}Infrastructure },
                { provide: ${upperCase(moduleName)}_MAPPER, useClass: ${capitalize(moduleName)}Mapper },
                ${capitalize(moduleName)}UseCase,
            ],
            exports: [${capitalize(moduleName)}UseCase],
            })
            export class ${capitalize(moduleName)}Module {}

        `,
    )

    console.log(`Module '${moduleName}' created successfully!`)
  })

program.parse(process.argv)

function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

function upperCase(str) {
  return str.toUpperCase()
}
