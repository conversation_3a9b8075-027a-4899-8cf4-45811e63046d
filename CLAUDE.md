This file provides comprehensive guidance to <PERSON> (claude.ai/code) when working with this monitoring and alerting platform codebase.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Core Architecture](#core-architecture)
3. [Core Services](#core-services)
4. [Tech Stack](#tech-stack)
5. [Repository Deep Dives](#repository-deep-dives)
   - [User Portal Backend](#user-portal-backend-appsuser-portal-backend)
   - [Orchestrator](#orchestrator-appsorchestrator)
   - [Worker](#worker-appsworker)
   - [Infrastructure](#infrastructure-appsinfrastructure)
6. [Library Architecture](#library-architecture)
7. [Cross-Repository Collaboration](#cross-repository-collaboration)
8. [Import/Export Patterns](#importexport-patterns)
9. [Development Workflow](#development-workflow)
10. [Architecture Patterns](#architecture-patterns)
11. [Common Tasks](#common-tasks)
12. [Future Documentation Considerations](#future-documentation-considerations)

## Project Overview

**MonitoringDog** is a sophisticated monitoring and alerting platform built as an **Nx monorepo**. The system monitors websites/services, manages incidents with escalation workflows, and provides comprehensive notification capabilities across multiple channels.

### Core Architecture

This is a **distributed, event-driven platform** with clear separation of concerns:

- **Multi-Service Architecture**: NestJS microservices with specialized responsibilities
- **Event-Driven Design**: EventBridge, DynamoDB Streams, Redis pub/sub, BullMQ queues
- **Multi-Database Strategy**: PostgreSQL (config), DynamoDB (time-series), Redis (cache), InfluxDB (metrics)
- **Multi-Tenant**: Organization → Teams → Users hierarchy with RBAC permissions
- **AWS-Native**: CDK-based infrastructure with serverless components

### Core Services

1. **User Portal Backend** (`apps/user-portal-backend/`) - Main REST API serving the web interface
   - **Port**: 3336 (development)
   - **Framework**: NestJS with TypeScript
   - **Purpose**: User management, subscription billing, check configuration, incident management
   - **Database**: PostgreSQL (primary), DynamoDB (incidents), Redis (cache)

2. **Orchestrator** (`apps/orchestrator/`) - Coordinates monitoring workflows and incident management
   - **Port**: 3000 (development)
   - **Framework**: NestJS with TypeScript
   - **Purpose**: Workflow coordination, state management, incident lifecycle
   - **Components**: CyclingWorker, ValidationPeriodWorker, FinalizingWorker

3. **Worker** (`apps/worker/`) - Executes monitoring checks and reports results
   - **Port**: 3002 (development)
   - **Framework**: NestJS with PM2 clustering
   - **Purpose**: HTTP/ping checks, metrics collection, status verification
   - **Check Types**: HTTP status, keyword presence/absence, ping, expected status

4. **Infrastructure** (`apps/infrastructure/`) - AWS CDK infrastructure-as-code
   - **Framework**: AWS CDK v2
   - **Purpose**: Lambda functions, Step Functions, EventBridge, API Gateway
   - **Environments**: NonProd, Prod

5. **Test URL** (`apps/test-url/`) - Simple test endpoint for monitoring validation
   - **Purpose**: Testing check configurations and validation

## Tech Stack

### Core Technologies
- **Framework**: NestJS v10+ with TypeScript 5.5+
- **Build System**: Nx 19.6.0 monorepo with pnpm workspaces
- **Package Manager**: pnpm 9.9.0 (enforced via `only-allow`)
- **Runtime**: Node.js with Webpack bundling

### Databases & Storage
- **PostgreSQL**: Primary database (users, organizations, checks, subscriptions)
  - **ORM**: TypeORM 0.3+ with Drizzle ORM schemas
  - **Migrations**: TypeORM CLI with custom naming strategies
- **DynamoDB**: Time-series data (incidents, events, usage tracking, webhook logs)
  - **ODM**: Dynamoose v4 with custom schemas
  - **Streams**: EventBridge Pipes integration
- **Redis**: Caching and session management
  - **Client**: ioredis v5+ with clustering support
  - **Features**: Pub/Sub, worker coordination, BullMQ backing
- **InfluxDB**: Metrics and time-series analytics
  - **Client**: InfluxDB3 client v0.9+
  - **Purpose**: Check performance, response times, availability metrics

### Queue & Messaging
- **BullMQ v5+**: Primary job queue system
  - **Queues**: Main monitoring, verify, scheduler, test checks
  - **Features**: Retry logic, delayed jobs, job priorities
  - **UI**: Bull Board for queue monitoring
- **Redis Pub/Sub**: Real-time worker coordination
- **EventBridge**: AWS event routing for incident workflows
- **SQS**: Notification delivery queuing

### Authentication & Authorization
- **Firebase Auth**: Identity provider with admin SDK
- **JWT Tokens**: Access/refresh token pattern with 5-minute cache
- **RBAC**: CASL-based permissions with organization/team scoping
- **Custom Authorizer**: Lambda-based API Gateway authorization

### Infrastructure & Deployment
- **AWS CDK v2**: Infrastructure as Code
- **Lambda Functions**: Serverless compute for incidents and notifications
- **Step Functions**: Incident escalation workflows
- **API Gateway**: REST APIs with custom authorizers
- **EventBridge**: Event-driven architecture
- **SES**: Email service with tracking
- **LocalStack**: Local AWS service emulation

### Notifications & Integrations
- **Slack**: Bolt SDK with interactive buttons
- **Email**: SES with MJML templates and React Email
- **SMS/Voice**: Plivo integration with usage billing
- **Push**: OneSignal mobile notifications
- **Webhooks**: Custom webhook integration with retry logic

### Payments & Billing
- **Stripe**: Subscription management and usage billing
- **Usage Tracking**: DynamoDB-based current/historical usage
- **Overage Policies**: Block, charge, or grace period handling

### Monitoring & Observability
- **Winston**: Structured logging with request context
- **OpenTelemetry**: Metrics and telemetry collection
- **CloudWatch**: AWS service monitoring
- **InfluxDB**: Custom metrics dashboards
- **Health Checks**: Terminus-based health endpoints

## Development Workflow

### Key Development Practices

- For many files, consider using parallel tasks to check and fix issues efficiently

### Initial Setup
```bash
# Clone and install dependencies
git clone <repository>
cd user-portal-backend
pnpm install                    # Install all dependencies

# Start required services
docker-compose up -d           # PostgreSQL, Redis, LocalStack
pnpm firebase:start            # Firebase emulators with data persistence

# Database setup
pnpm migration:run             # Apply database migrations

# Environment configuration
cp .env.example .env
cp credentials/.env.development .env.development
# Configure environment variables (see Environment Configuration section)
```

### Development Servers
```bash
# Start all services in development mode
pnpm backend:dev               # User Portal Backend (http://localhost:8080)
pnpm orch:dev                  # Orchestrator (http://localhost:3000)
pnpm worker:dev                # Worker with PM2 clustering (http://localhost:3002)

# Individual service commands
nx run user-portal-backend:serve --configuration=development
nx run orchestrator:serve --configuration=development
nx run worker:start:multi --configuration=development
```

### Database Operations
```bash
# Migrations
pnpm migration:create --name <migration-name>  # Create new migration
pnpm migration:run                            # Apply pending migrations
nx run user-portal-backend:typeorm-migration-revert  # Revert last migration

# Direct database access
psql $DATABASE_URL                            # Connect to PostgreSQL
pnpm typeorm                                  # TypeORM CLI access

# Database utilities
docker logs -f user-portal-postgres           # View PostgreSQL logs
docker logs -f user-portal-redis               # View Redis logs
```

### Testing & Quality Assurance
```bash
# Testing
nx test <project-name>                        # Test specific project
nx run-many --target=test --all               # Run all tests
nx e2e <project-name>                         # E2E tests

# Code quality
pnpm lint                                     # Check linting across all projects
pnpm lint:fix                                 # Auto-fix linting issues
prettier --write .                            # Format code

# Nx utilities
nx dep-graph                                  # Visualize project dependencies
nx affected:test                              # Test only affected projects
```

### Infrastructure & Deployment
```bash
# CDK operations
cd apps/infrastructure
nx run infrastructure:synth:dev               # Synthesize CloudFormation
nx run infrastructure:deploy:dev              # Deploy to NonProd
nx run infrastructure:deploy:prod             # Deploy to Production
nx run infrastructure:watch:dev --hotswap-fallback  # Watch mode

# Local AWS services
nx run infrastructure:cdklocal                # Deploy to LocalStack

# Build operations
pnpm backend:build                           # Build user-portal-backend
nx build <project-name>                       # Build specific project
nx run-many --target=build --all             # Build all projects
```

### Utilities
```bash
# Secret management
pnpm pack-secrets                             # Encrypt secrets for deployment
pnpm unpack-secrets                           # Decrypt secrets locally

# Environment synchronization
pnpm sync-env                                 # Sync env vars from AWS SSM

# Firebase utilities
pnpm firebase:auth                           # Firebase auth utilities

# Queue monitoring
npx bull-board                                # BullMQ dashboard
redis-cli                                     # Redis CLI access
> keys bull:*                                # List BullMQ keys
```

### Docker Operations
```bash
# Service management
docker-compose up -d                          # Start all services
docker-compose down                           # Stop all services
docker-compose down -v                        # Stop and remove volumes

# Individual services
docker-compose up postgres                    # Start only PostgreSQL
docker-compose up redis                       # Start only Redis
docker-compose up localstack                  # Start only LocalStack
```

## Project Structure

```
apps/
├── user-portal-backend/     # Main API
│   ├── src/
│   │   ├── modules/        # Feature modules (check, incident, subscription, etc.)
│   │   ├── frameworks/     # Database, cache, queue implementations
│   │   ├── cores/          # Core services (auth, config, RBAC)
│   │   └── commons/        # Shared utilities
├── orchestrator/           # Workflow coordinator
├── worker/                # Check executor
└── infrastructure/        # AWS CDK stacks

libs/
├── database/              # Shared database schemas
├── notifier/             # Notification library
├── integration-callback/ # Integration callbacks
└── shared/              # Shared constants and utilities
```

## Architecture Patterns

1. **Domain-Driven Design**: Each module in `apps/user-portal-backend/src/modules/` represents a domain
2. **Repository Pattern**: Separates data access from business logic
3. **Event-Driven**: Uses EventBridge and BullMQ for async operations
4. **Multi-tenant**: Organization/Team hierarchy with permission management
5. **Clean Architecture**: Entities, Use Cases, Repositories, Infrastructure layers

## Key Development Patterns

### Adding a New Feature Module
```typescript
// 1. Create module structure
modules/
└── feature/
    ├── controllers/
    ├── entities/
    ├── usecases/
    ├── applications/
    └── feature.module.ts

// 2. Register in app.module.ts
```

### Database Models
- PostgreSQL models: `apps/user-portal-backend/src/frameworks/database/models/`
- DynamoDB schemas: `libs/database/src/lib/dynamo/`

### Adding Notification Channels
1. Implement provider in `libs/notifier/src/providers/`
2. Create notifier in `libs/notifier/src/incident-notifier/`
3. Register in notification factory

## Environment Configuration

```bash
# Copy and configure environment files
cp .env.example .env
cp credentials/.env.development .env.development

# Key environment variables:
DATABASE_URL          # PostgreSQL connection
REDIS_URL            # Redis connection
FIREBASE_PRIVATE_KEY # Firebase auth
STRIPE_SECRET_KEY    # Stripe payments
AWS_REGION          # AWS services region
```

## Code Style

- **No semicolons** (enforced by Prettier)
- **2 spaces** indentation
- **Import ordering** enforced by ESLint
- **NestJS decorators** and dependency injection patterns
- **TypeScript strict mode** enabled

## Testing Strategy

- **Unit tests**: Jest with `.spec.ts` files alongside source
- **Integration tests**: Test full request/response cycles
- **E2E tests**: Test complete workflows

## Security Considerations

- JWT authentication with refresh tokens
- RBAC using CASL for permissions
- Environment-based secrets management
- AWS SSM for production secrets

## Common Tasks

### Working with Incidents
- Incident states: STARTED → ACKNOWLEDGED → RESOLVED
- Escalation flow handled by Lambda functions
- Multi-channel notifications based on user preferences

### Working with Checks
- Check types: HTTP status, ping, keyword presence/absence
- Worker pools execute checks based on region
- Results stored in InfluxDB for charting

### Debugging Queue Issues
```bash
# Monitor BullMQ queues
pnpm bull-board

# Check Redis for queue data
redis-cli
> keys bull:*
```

## Useful Commands for Troubleshooting

```bash
# Check service logs
docker logs -f user-portal-postgres
docker logs -f user-portal-redis

# Database connection
psql $DATABASE_URL

# Clear local data
docker-compose down -v
```

---

## Repository Deep Dives

### User Portal Backend (`apps/user-portal-backend/`)

**Architecture Pattern**: Clean Architecture with Domain-Driven Design (DDD)

#### Internal Structure
```
src/
├── @type/           # Global TypeScript type definitions
├── commons/         # Shared utilities, decorators, DTOs, guards
├── cores/           # Core business infrastructure (auth, RBAC, config)
├── frameworks/      # Technical infrastructure adapters
├── modules/         # Domain feature modules (DDD bounded contexts)
├── migration/       # Database migrations
├── resources/       # Static assets (email templates)
├── app.module.ts    # Root application module
└── main.ts          # Application entry point
```

#### Key Architectural Patterns
- **Domain-Driven Design**: Each module represents a bounded context
- **Clean Architecture**: Entities → Use Cases → Repositories → Infrastructure
- **Event-Driven**: Domain events with EventEmitter2
- **Repository Pattern**: Abstract data access with infrastructure implementations
- **CQRS-like**: Separation between read/write operations

#### Module Structure Standard
Every feature module follows this consistent pattern:
```
modules/feature-name/
├── feature-name.module.ts          # NestJS module configuration
├── controllers/                    # HTTP request handlers
├── entities/                       # Domain entities & business logic
│   ├── events/                     # Domain events
│   └── index.ts                    # Barrel export
├── usecases/                       # Application services/business logic
├── applications/                   # Application layer contracts
│   ├── dto/                        # Data Transfer Objects
│   └── feature-name.repository.ts  # Repository interface
├── infras/                         # Infrastructure implementations
├── interfaces/                     # Module-specific interfaces
├── constants/                      # Module constants
├── services/                       # Additional domain services
├── types/                          # Type definitions
└── utils/                          # Module utilities
```

#### Adding a New Feature Module
1. **Create Directory Structure**:
   ```bash
   mkdir -p src/modules/new-feature/{controllers,entities,usecases,applications/{dto},infras,interfaces}
   ```

2. **Create Entity with Zod Schema**:
   ```typescript
   export const NewFeatureSchema = z.object({
     name: z.string(),
     status: z.nativeEnum(Status)
   })
   
   export class NewFeature extends AggregateRoot<NewFeatureProps, NewFeatureResponse> {
     static create(props: NewFeatureProps) {
       const entity = new NewFeature({ id: generateId(), props })
       entity.addEvent(new NewFeatureCreatedEvent({ aggregateId: entity.id, props }))
       return entity
     }
   }
   ```

3. **Define Repository Interface**:
   ```typescript
   interface NewFeatureRepository extends CrudRepository<NewFeature, NewFeatureProps> {
     // Custom methods
   }
   
   export const NEW_FEATURE_REPOSITORY = 'NEW_FEATURE_REPOSITORY'
   ```

4. **Implement Infrastructure Layer**:
   ```typescript
   @Injectable()
   export class NewFeatureInfrastructure implements NewFeatureRepository {
     private readonly model: TypeORMDriver<NewFeatureModel>
     
     constructor(@Inject(Database) private database: Database) {
       this.model = this.database.typeorm<NewFeatureModel>('NewFeatureModel')
     }
   }
   ```

5. **Create Use Case**:
   ```typescript
   @Injectable()
   export class NewFeatureUseCase {
     constructor(
       @Inject(NEW_FEATURE_REPOSITORY) private readonly repo: NewFeatureRepository
     ) {}
   }
   ```

6. **Configure Module**:
   ```typescript
   @Module({
     imports: [DatabaseModule],
     controllers: [NewFeatureController],
     providers: [
       { provide: NEW_FEATURE_REPOSITORY, useClass: NewFeatureInfrastructure },
       NewFeatureUseCase
     ],
     exports: [NewFeatureUseCase]
   })
   export class NewFeatureModule {}
   ```

#### Framework Architecture
- **Database Framework**: Multi-database support (PostgreSQL, DynamoDB, Redis, InfluxDB)
- **Queue Framework**: BullMQ integration with SQS support
- **Cache Framework**: Redis client with clustering support
- **Notification Framework**: Provider pattern for multi-channel notifications

---

### Orchestrator (`apps/orchestrator/`)

**Architecture Pattern**: Event-Driven Workflow Coordination

#### Core Worker Architecture
The orchestrator implements 5 primary workers handling different monitoring workflow aspects:

1. **CyclingWorker** (`QUEUE.MAIN`): Main orchestrator entry point for check distribution
2. **ValidationPeriodWorker** (`QUEUE.SCHEDULER`): Handles confirmation and recovery period validation
3. **FinalizeVerifyWorker** (`QUEUE.FINALIZE_VERIFY`): Final verification after multi-node checks
4. **VerifyWorker** (`QUEUE.VERIFY`): Handles verification requests when status changes detected
5. **RecoveryResetWorker** (`QUEUE.RECOVERY_RESET`): Handles recovery reset operations

#### State Management
- **Redis State**: Check configurations, worker state, status tracking
- **DynamoDB State**: Long-term incident storage, event history, usage data
- **PostgreSQL State**: Check configurations, worker registry, team/organization data

#### Workflow Coordination Patterns
```
Check Execution → Status Change Detection → Multi-Node Verification → 
Consensus Building → Validation Period → Final Decision → Incident Management
```

#### Adding New Workers
```typescript
// 1. Create worker class extending BaseWorkerProcessor
@Processor(QUEUE.NEW_WORKER)
@Injectable()
export class NewWorker extends BaseWorkerProcessor {
  constructor(
    @Inject(NewHandler) private readonly newHandler: NewHandler,
  ) {
    super()
  }

  process(job: Job): Promise<void> {
    return this.newHandler.handleNewWork(job.data)
  }
}

// 2. Register in orchestrator.module.ts workers array
export const workers = [
  CyclingWorker,
  NewWorker, // Add here
]

// 3. Register queue in QueueModule.register
QueueModule.register({
  queues: [
    QUEUE.MAIN,
    QUEUE.NEW_WORKER, // Add here
  ],
})
```

#### Dependencies and Integration
- **Libraries Used**: `@libs/database`, `@libs/pubsub-queue`, `@libs/shared`
- **Queue Integration**: BullMQ with Redis backing, Redis Pub/Sub for coordination
- **External Integration**: AWS EventBridge, DynamoDB streams, TypeORM/Drizzle

---

### Worker (`apps/worker/`)

**Architecture Pattern**: Geographic Multi-Region Check Execution

#### PM2 Clustering Setup
**Geographic Workers**:
- **US Regions**: Ohio (`usa_oh`), North Virginia (`usa_nv`), North Carolina (`usa_nc`)
- **Vietnam Regions**: Hanoi (`vie_hn`), Da Nang (`vie_dn`), Saigon (`vie_sg`)

**Configuration**: Single instance per region with cluster mode, environment-specific worker IDs

#### Check Execution Architecture
1. **MainCheckWorker**: Redis pub/sub pattern for main check execution
2. **VerifyCheckWorker**: BullMQ-based worker for confirmation checks
3. **TestCheckWorker**: On-demand testing for configuration validation

#### Metrics Collection
- **OpenTelemetry**: OTLP exporter to AWS CloudWatch, 30-second intervals
- **InfluxDB**: Time-series data (tcp_connection, tls_handshake, data_transfer, total_time)
- **Redis**: Status updates for orchestrator coordination

#### Adding New Check Types
1. **Create Check Handler**:
   ```typescript
   @Injectable({ scope: Scope.TRANSIENT })
   export class CustomChecker extends BaseCheckHandler {
     constructor(moduleRef: ModuleRef) {
       super(moduleRef)
     }

     async performCheck(checkData: CheckData): Promise<void> {
       // Implement custom check logic
       // Set this.isAPIUp, this.checkResult, this.responseMessage
     }
   }
   ```

2. **Register in WorkerModule** and **Add to Check Factory**
3. **Update CheckType Enum** in `libs/shared/constants/check.enum.ts`

#### Result Reporting Patterns
- **Orchestrator Updates**: Redis hash storage for coordinator
- **Status Change Handling**: Detection and verification requests
- **Metrics Reporting**: Multiple channels (InfluxDB, OpenTelemetry, Redis)

---

### Infrastructure (`apps/infrastructure/`)

**Architecture Pattern**: Domain-Driven CDK with Multi-Environment Support

#### CDK Stack Organization
```
src/
├── app.ts                    # Main CDK app entry point
├── shared/                   # Reusable constructs and utilities
│   ├── constructs/          # Standard construct library
│   └── constants/           # Resource naming and configuration
├── domains/                 # Business domain stacks
│   ├── auth/               # Authentication & authorization
│   ├── billing/            # Usage billing and Stripe integration
│   ├── incidents/          # Incident escalation workflows
│   └── webhooks/           # Webhook integration
└── core/                   # Core infrastructure (SES, etc.)
```

#### Environment-First Design
- All stacks deployed for both `NonProd` and `Prod` environments
- Environment configuration set at synthesis time
- Resource namespace per environment (`nonprod-*`, `prod-*`)

#### Lambda Functions Architecture
- **Domain Organization**: Organized by business domain (auth, billing, incidents)
- **Standard Deployment**: Node.js 20.x runtime, Docker bundling, 128MB memory
- **Environment Variables**: Shared via `SecretProvider` construct

#### Step Functions Implementation
**Event-Driven Escalation Workflows**:
```
UpdateStepFunctionArn → RepeatEscalationPolicies → EscalationPolicies → NotificationMap
                                ↑                        ↓
                          WaitForRepeat ←─── Choice (Wait for escalation?)
                                                  ↓
                                            CheckStatus → Choice (Resolved/Acknowledged?)
```

#### Adding New Stacks
```typescript
export interface MyFeatureStackProps extends cdk.StackProps {
  environment: string
  // Additional required props
}

export class MyFeatureStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: MyFeatureStackProps) {
    super(scope, id, props)
    // Implementation
  }
}

// Register in app.ts
new MyFeatureStack(this, 'MyFeatureStack', props)
```

#### Standard Constructs Available
- `StandardLambdaConstruct`: Lambda functions with monitoring
- `StandardStateMachineConstruct`: Step Functions with logging
- `StandardSQSQueueConstruct`: SQS with DLQ and alarms
- `StandardLambdaAuthorizerConstruct`: API Gateway authorizers
- `SecretProvider`: Environment-based secret management

---

## Library Architecture

### Overview
The MonitoringDog platform uses 5 specialized libraries providing well-structured foundation components:

### `libs/database/` - Unified Database Access Layer
**Purpose**: Unified interface to multiple database technologies (PostgreSQL, DynamoDB, Redis, InfluxDB)

**Architecture Pattern**: Facade Pattern - Single service hiding complexity of multiple data stores

**Key Components**:
- **Database Service**: Central facade providing access to all data stores
- **Drizzle ORM Schema**: PostgreSQL schema definitions with relations
- **DynamoDB Schemas**: Dynamoose schemas for time-series data
- **Global Module**: NestJS module exported globally

**Public API**:
```typescript
class Database {
  drizzle()                    // Direct Drizzle ORM access
  pgQuery()                    // Query builder access
  redisGet/Set()              // High-level Redis operations
  writeToTimeSeriesDB()       // InfluxDB operations
  getValuesFromPersistentDB() // PostgreSQL operations
}
```

### `libs/notifier/` - Multi-Channel Notification System
**Purpose**: Handles incident notifications across multiple communication channels

**Architecture Pattern**: Factory + Strategy Pattern

**Supported Channels**: Slack (Bolt SDK), Email (SES + React Email), Voice/SMS (Plivo), Push (OneSignal)

**Usage Pattern**:
```typescript
const notifier = new IncidentNotifier('email', emailConfig)
await notifier.provider.notify(incidentPayload)
```

### `libs/integration-callback/` - External Integration Response Handling
**Purpose**: Handles callbacks from external integrations (Slack buttons, voice responses, email tracking)

**Architecture Pattern**: Factory + Command Pattern

**Provider Types**: Slack interactive buttons, Plivo voice/SMS callbacks, email tracking

### `libs/shared/` - Foundation Library
**Purpose**: Shared constants, utilities, service clients, and cross-cutting concerns

**Key Components**:
- **Service Clients**: PostgresClient, RedisClient, BullClient, InfluxClient
- **Constants**: Queue names, incident status/events, check types, channel definitions
- **Utilities**: Logger (Winston), time manipulation, scheduler (cron parsing)

**Architecture Pattern**: Shared Kernel + Service Layer

### `libs/pubsub-queue/` - Message Queue Abstraction
**Purpose**: Unified interface for Redis pub/sub and BullMQ job queues

**Architecture Pattern**: Facade Pattern

**Public API**:
```typescript
class PubsubQueue {
  publishToChannel()     // Redis pub/sub
  publishToQueue()       // BullMQ job queues
  deleteJob()           // Queue management
  deleteRepeatableJob() // Scheduled job cleanup
}
```

---

## Cross-Repository Collaboration

### System Communication Patterns

#### Core System Flow: "Creating and Monitoring an HTTP Check"
1. **User Interaction**: User calls `POST /checks` on `user-portal-backend`
2. **API Logic**: `CreateCheckUseCase` validates input and persists configuration to PostgreSQL
3. **Event Publishing**: On success, publishes `CheckCreated` event via EventBridge
4. **Orchestration**: EventBridge rule triggers `orchestrator`'s BullMQ producer, adding recurring job
5. **Job Distribution**: `CyclingWorker` distributes check to appropriate geographic `worker` nodes
6. **Execution**: `Worker` performs HTTP check, collects metrics, reports results
7. **Status Analysis**: If status change detected, triggers verification workflow
8. **Incident Management**: On confirmed failure, creates incident and triggers notification workflows

#### Event-Driven Communication
```
user-portal-backend → EventBridge → orchestrator → Redis Pub/Sub → worker
                                         ↓
                               DynamoDB (incidents) ← infrastructure (Lambda)
                                         ↓
                               SQS → notifier (notifications)
```

#### Data Flow Patterns
- **Configuration Data**: PostgreSQL (user-portal-backend) → Redis Cache (orchestrator) → worker
- **Time-Series Data**: worker → InfluxDB → user-portal-backend (charts/dashboards)
- **Incident Data**: orchestrator → DynamoDB → infrastructure (Step Functions) → notifier
- **Usage Data**: All services → DynamoDB → billing system

### Inter-Service Dependencies

#### Database Dependencies
- **PostgreSQL**: Primary config store (user-portal-backend manages, all read)
- **DynamoDB**: Incident/event store (orchestrator writes, infrastructure reads)
- **Redis**: Cache and coordination (all services)
- **InfluxDB**: Metrics (worker writes, user-portal-backend reads)

#### Queue Dependencies
- **BullMQ**: orchestrator → worker (verification jobs)
- **Redis Pub/Sub**: orchestrator → worker (check distribution)
- **SQS**: infrastructure → various (notifications, billing events)
- **EventBridge**: user-portal-backend → orchestrator → infrastructure

#### Authentication/Authorization Flow
```
Client → API Gateway (infrastructure) → Lambda Authorizer → Firebase Auth
                         ↓
              user-portal-backend (JWT validation + RBAC)
```

---

## Import/Export Patterns

### TypeScript Path Mapping
```typescript
// From tsconfig.base.json
"paths": {
  "@libs/database/*": ["libs/database/src/*"],
  "@libs/shared/*": ["libs/shared/src/*"], 
  "@libs/notifier": ["libs/notifier/src/index.ts"],
  "@libs/integration-callback": ["libs/integration-callback/src/index.ts"],
  "@libs/pubsub-queue/*": ["libs/pubsub-queue/src/*"]
}
```

### Library Import Patterns

#### From Apps to Libraries (✅ Allowed)
```typescript
// user-portal-backend importing from libs
import { Database } from '@libs/database/lib/database'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { QUEUE } from '@libs/shared/constants/queues'
import { IncidentNotifier } from '@libs/notifier'

// orchestrator importing from libs
import { PubsubQueueModule } from '@libs/pubsub-queue/lib/pubsub-queue.module'
import { BullClient } from '@libs/shared/service-clients/bullmq.client'

// worker importing from libs
import { CheckType } from '@libs/shared/constants/check.enum'
import { RedisClient } from '@libs/shared/service-clients/redis.client'

// infrastructure (Lambda) importing from libs
import IncidentNotifier from '@libs/notifier'
import IncidentIntegrationCallback from '@libs/integration-callback'
```

#### Between Libraries (✅ Allowed with Caution)
```typescript
// libs/database importing from libs/shared
import { PostgresClient } from '@libs/shared/service-clients/postgres.client'
import { RedisClient } from '@libs/shared/service-clients/redis.client'

// libs/notifier importing from libs/shared
import { IncidentStatus } from '@libs/shared/constants/incident'
import { Logger } from '@libs/shared/logger'
```

#### Cross-App Imports (❌ Prohibited)
```typescript
// ❌ NEVER import between apps
// orchestrator should NOT import from user-portal-backend
// worker should NOT import from orchestrator
// user-portal-backend should NOT import from worker

// Communication between apps should only happen through:
// - Shared libraries
// - Database/queue/event systems
// - REST APIs (rare)
```

### Library Export Patterns

#### Global Module Pattern (`libs/database`)
```typescript
@Global()
@Module({
  providers: [PostgresClient, RedisClient, InfluxClient, Database],
  exports: [Database],
})
export class DatabaseModule {}

// Usage: Automatically available in all modules
constructor(@Inject(Database) private database: Database)
```

#### Barrel Export Pattern (`libs/notifier`, `libs/integration-callback`)
```typescript
// libs/notifier/src/index.ts
export * from './incident-notifier'

// Usage:
import { IncidentNotifier } from '@libs/notifier'
```

#### Direct Import Pattern (`libs/shared`)
```typescript
// No barrel exports - import directly
import { IncidentStatus } from '@libs/shared/constants/incident'
import { PostgresClient } from '@libs/shared/service-clients/postgres.client'
```

### Dependency Guidelines

#### Library Dependency Hierarchy
```
libs/shared (foundation - no dependencies on other libs)
    ↑
    ├── libs/database (depends on shared service clients)
    ├── libs/pubsub-queue (depends on shared service clients)
    ├── libs/notifier (depends on shared constants/types)
    └── libs/integration-callback (depends on shared constants/types)
```

#### App Dependency Rules
1. **Apps can import from any library**: No restrictions on library usage
2. **Apps cannot import from other apps**: Maintains separation of concerns
3. **Apps communicate via shared infrastructure**: Database, queues, events, APIs
4. **Shared business logic goes in libraries**: Extract common functionality to libs

#### Best Practices
- **Use `@libs/shared` for cross-cutting concerns**: Constants, utilities, service clients
- **Use specific libraries for domain logic**: Notifications, database access, integration callbacks
- **Avoid circular dependencies**: Keep library dependencies unidirectional
- **Extract common patterns**: If multiple apps need similar functionality, create a library
- **Keep apps focused**: Each app should have a single primary responsibility

---

## Future Documentation Considerations

As the MonitoringDog platform continues to grow in complexity and team size, consider evolving toward a **federated documentation model**:

### Current State
The comprehensive `CLAUDE.md` provides immediate value by centralizing all architectural knowledge in a single, searchable document.

### Future Challenges
As the organization scales, a single document may become:
- Too large to manage effectively
- A bottleneck for contributions (merge conflicts, ownership issues)
- Difficult to keep current across many independent teams

### Federated Model Transition
When the current approach shows signs of strain, consider transitioning to a "hub-and-spoke" model:

- **Hub**: Central `CLAUDE.md` providing system overview and linking to detailed documentation
- **Spokes**: Individual `README.md` files in each `apps/*/` and `libs/*/` directory containing deep-dive information
- **Benefits**: Decentralized ownership, better context for code, easier maintenance by feature teams

### Trigger for Transition
Consider this transition when experiencing:
- Excessive document length (>1000 lines)
- Frequent merge conflicts on documentation
- Outdated sections due to unclear ownership
- Team requests for more modular documentation

### Implementation Considerations
- Organizational alignment and buy-in required
- Clear guidelines for distributed documentation
- Potential tooling support (static site generators)
- Link management and validation processes
