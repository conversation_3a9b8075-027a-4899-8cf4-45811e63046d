# Ignore files and directories commonly used in development
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Ignore build artifacts
dist
build

# Ignore editor and IDE files
.vscode
.idea
*.sublime-project
*.sublime-workspace

# Ignore version control files
.git
.gitignore

# # Ignore environment-specific files
# .env
# .env.local
# .env.*.local
# .env.development
# .env.test
# .env.production

# Ignore miscellaneous files
.DS_Store
Thumbs.db
