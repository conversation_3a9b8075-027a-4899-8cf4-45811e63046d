// WORKER_ID:
// usa_nv
// usa_oh
// usa_nc
// vie_hn
// vie_sg
// vie_dn

module.exports = {
  apps: [
    {
      name: 'check-worker-US-Ohio',
      script: 'dist/apps/worker/main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'usa_oh',
      },
    },
    {
      name: 'check-worker-US-NorthVirginia',
      script: 'dist/apps/worker/main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'usa_nv',
      },
    },
    {
      name: 'check-worker-US-NorthCarolina',
      script: 'dist/apps/worker/main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'usa_nc',
      },
    },
    {
      name: 'check-worker-<PERSON><PERSON>-<PERSON><PERSON>',
      script: 'dist/apps/worker/main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'vie_hn',
      },
    },
    {
      name: 'check-worker-VN-<PERSON><PERSON><PERSON>',
      script: 'dist/apps/worker/main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'vie_dn',
      },
    },
    {
      name: 'check-worker-VN-Saigon',
      script: 'dist/apps/worker/main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'vie_sg',
      },
    },
  ],
}
