import { Controller, Get } from '@nestjs/common'
import { HealthCheck } from '@nestjs/terminus'

import { HealthCheckServiceWrapper } from '../service/health.service'

@Controller('health')
export class HealthController {
  constructor(
    private readonly healthCheckServiceWrapper: HealthCheckServiceWrapper,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.healthCheckServiceWrapper.checkHealth()
  }
}
