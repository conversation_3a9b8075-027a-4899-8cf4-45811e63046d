import { Inject, Injectable } from '@nestjs/common'
import { HealthCheckService, HealthIndicatorResult } from '@nestjs/terminus'
import { Database } from '@libs/database/lib/database'

@Injectable()
export class HealthCheckServiceWrapper {
  constructor(
    @Inject(Database) private readonly db: Database,
    private readonly health: HealthCheckService,
  ) {}

  async checkHealth() {
    return this.health.check([
      async () =>
        this.db.redisClientHealthCheck() as Promise<HealthIndicatorResult>,
      async () =>
        this.db.pgClientHealthCheck() as Promise<HealthIndicatorResult>,
    ])
  }
}
