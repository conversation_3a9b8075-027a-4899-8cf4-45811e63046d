import { NestFactory } from '@nestjs/core'
import { Logger } from '@nestjs/common'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'

import { AppModule } from './app.module'

declare const module: any

async function bootstrap() {
  const app = await NestFactory.create(AppModule)

  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER))

  Logger.log(`Worker ${process.env.WORKER_ID} running`, 'Bootstrap')

  const workerId = process.env.WORKER_ID || 'unknown'
  Logger.log(`Worker ${workerId} running`, 'Bootstrap')

  const port = process.env.HEALTH_CHECK_PORT || 9999
  await app.listen(port)

  if (module.hot) {
    module.hot.accept()
    module.hot.dispose(() => app.close())
  }
}

bootstrap()
