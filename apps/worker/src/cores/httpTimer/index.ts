import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import {
  type Timings,
  type ClientRequestWithTimings,
} from '@szmarczak/http-timer'
import { StatusData } from '@worker/worker/interface/check-status.interface'
import { UnexpectedError } from '@worker/errors/error.general'
import { HttpMethod } from '@libs/shared/constants/check.enum'

import * as https from 'https'

import * as followRedirects from './followRedirect'

export interface TimingsAxiosResponse extends AxiosResponse {
  request: {
    timings: Timings
  }
}

export interface RequestConfig extends AxiosRequestConfig {
  handleRedirect?: boolean
}

const MAXIMUM_REDIRECTS = 10

const DEFAULT_HEADERS = {
  // Prevent reusing the connection
  Connection: 'close',
  'User-Agent': 'Monitoring-Dog Cloud Worker/1.0 (Language=Typescript)',
}

class HttpTimer {
  private static instance: HttpTimer
  private timer: (request: ClientRequestWithTimings) => Timings

  public async getInstance() {
    if (!HttpTimer.instance) {
      HttpTimer.instance = new HttpTimer()
      await HttpTimer.instance.initializeTimer()
    }
    return HttpTimer.instance
  }

  private async initializeTimer() {
    const module = await eval('import("@szmarczak/http-timer")')
    this.timer = module.default
  }

  private axios(handleRedirect = true) {
    const transport = {
      request: (...args) => {
        if (handleRedirect) {
          return followRedirects.https.request.apply(null, args)
        }

        const request = https.request.apply(null, args)
        this.timer(request)
        return request
      },
    }

    const instance = axios.create({
      transport,
    })

    instance.defaults.headers.common = DEFAULT_HEADERS

    return instance
  }

  async request(
    url: string,
    config: RequestConfig,
    requestBody?: { [key: string]: any } | string,
  ): Promise<TimingsAxiosResponse> {
    if (!config.handleRedirect) {
      config.maxRedirects = MAXIMUM_REDIRECTS
    }

    switch (config.method) {
      case HttpMethod.GET:
        delete config.data
        return this.axios(config.handleRedirect).get(url, config)
      case HttpMethod.HEAD:
        delete config.data
        return this.axios(config.handleRedirect).head(url, config)
      case HttpMethod.PATCH:
        return this.axios(config.handleRedirect).patch(url, requestBody, config)
      case HttpMethod.POST:
        return this.axios(config.handleRedirect).post(url, requestBody, config)
      case HttpMethod.PUT:
        return this.axios(config.handleRedirect).put(url, requestBody, config)
      default:
        throw new UnexpectedError(
          `Unsupported HTTP method: ${config.method}`,
          500,
        )
    }
  }

  parseTiming(response: AxiosResponse): StatusData {
    const timings = response.request.timings
    if (!timings) {
      throw new UnexpectedError('No timing information available.', 500)
    }
    // TODO: MOVE THIS TO HTTP TIMER TO PARSE TIMINGS
    const tcpConnection = parseFloat((timings.phases.tcp ?? 0).toFixed(1))
    const tlsHandshake = parseFloat((timings.phases.tls ?? 0).toFixed(1))
    const dataTransfer = parseFloat(
      (
        (timings.phases.firstByte ?? 0) + (timings.phases.download ?? 0)
      ).toFixed(1),
    )
    const totalTime = parseFloat((timings.phases.total ?? 0).toFixed(1))
    const statusCode = response.status
    return {
      tcpConnection: tcpConnection,
      tlsHandshake: tlsHandshake,
      dataTransfer: dataTransfer,
      totalTime: totalTime,
      statusCode: statusCode,
    }
  }
}

export default new HttpTimer()
