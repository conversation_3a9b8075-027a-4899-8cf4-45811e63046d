/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config({
  path: 'apps/worker/.env',
})

class ConfigService {
  constructor(private env: { [k: string]: string | undefined }) {
    if (
      process.env.SECRETS &&
      Object.keys(JSON.parse(process.env.SECRETS)).length
    ) {
      const secrets = JSON.parse(process.env.SECRETS)
      Object.entries(secrets).forEach(([key, value]) => {
        // @ts-expect-error: we will inject key later on?
        process.env[key] = value
      })
    }
  }

  public getValue(key: string, throwOnMissing = false): string {
    const value = this.env[key]
    if (!value && throwOnMissing) {
      throw new Error(`config error - missing env.${key}`)
    }

    return value as string
  }

  public ensureValues(keys: string[]) {
    keys.forEach((k) => this.getValue(k, true))
    return this
  }

  public isProduction() {
    const mode = this.getValue('NODE_ENV') || 'development'
    return mode !== 'development'
  }

  public getDataSource() {
    return {
      host: this.getValue('DB_HOST'),
      port: parseInt(this.getValue('DB_PORT')),
      username: this.getValue('DB_USER'),
      password: this.getValue('DB_PASSWORD'),
      database: this.getValue('DB_NAME'),
      poolSize: Number(this.getValue('POOL_SIZE') || 5),
    }
  }

  public getWorkerConfig() {
    return {
      workerId: this.getValue('WORKER_ID'),
    }
  }

  public getInfluxConfig() {
    return {
      url: this.getValue('INFLUX_URL'),
      token: this.getValue('INFLUX_TOKEN'),
      bucket: this.getValue('BUCKET'),
    }
  }

  public getRedisConfig() {
    if (this.isProduction()) {
      // Prod
      return {
        host: this.getValue('REDIS_HOST'),
        port: parseInt(this.getValue('REDIS_PORT')),
      }
    } // Dev
    return {
      host: this.getValue('REDIS_HOST'),
      port: parseInt(this.getValue('REDIS_PORT')),
    }
  }
}

const configService = new ConfigService(process.env).ensureValues([])

export { configService }
