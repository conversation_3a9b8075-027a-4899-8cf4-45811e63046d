import { Injectable, OnModuleInit } from '@nestjs/common'
import {
  MeterProvider,
  PeriodicExportingMetricReader,
} from '@opentelemetry/sdk-metrics'
import { Meter } from '@opentelemetry/api'
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-grpc'
import { Resource } from '@opentelemetry/resources'
import {
  ATTR_SERVICE_NAME,
  SEMRESATTRS_SERVICE_NAMESPACE,
  SEMRESATTRS_DEPLOYMENT_ENVIRONMENT,
} from '@opentelemetry/semantic-conventions'

@Injectable()
export class TelemetryService implements OnModuleInit {
  private meter: Meter
  private exporter: OTLPMetricExporter
  private reader: PeriodicExportingMetricReader

  constructor() {
    // Define resource attributes for better identification in CloudWatch
    const resource = new Resource({
      [ATTR_SERVICE_NAME]: process.env.OTEL_SERVICE_NAME || 'monitoring-worker',
      [SEMRESATTRS_SERVICE_NAMESPACE]:
        process.env.OTEL_SERVICE_NAMESPACE || 'monitoring-dog',
      [SEMRESATTRS_DEPLOYMENT_ENVIRONMENT]:
        process.env.OTEL_DEPLOYMENT_ENVIRONMENT || 'development',
    })
    this.exporter = new OTLPMetricExporter({
      // url: 'http://aws-ot-collector:4317', # Default using variable from variable OTEL_EXPORTER_OTLP_ENDPOINT
    })
    this.reader = new PeriodicExportingMetricReader({
      exporter: this.exporter,
      exportIntervalMillis: parseInt(
        process.env.OTEL_EXPORT_INTERVAL || '30000',
      ),
    })
    const meterProvider = new MeterProvider({
      resource: resource,
      readers: [this.reader],
    })
    this.meter = meterProvider.getMeter(
      process.env.CHECK_METER || 'check_meter',
    )
  }

  onModuleInit() {
    console.log('Telemetry service initialized')
  }

  recordMetrics(checkId: string, workerId: string) {
    const countMetric = this.meter.createCounter('count', {
      description: 'Counts the number of occurrences',
    })
    countMetric.add(1, { workerId, checkId })
    console.log('Metrics updated:', {
      count: 1,
      check_id: checkId,
      worker_id: workerId,
    })
  }
}
