/* eslint-disable no-unsafe-finally */
import { Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import * as ping from 'ping'
import { TimeoutError } from '@worker/errors/error.general'
import { BUCKET, WorkerResult } from '@libs/shared/constants/check.enum'

import { format } from 'util'

import { CheckData, TestCheckResponse } from '../interface/check.interface'
import { EmptyStatusData } from '../interface/check-status.interface'

import { BaseCheckHandler } from './check.base'

@Injectable({ scope: Scope.TRANSIENT })
export class <PERSON><PERSON><PERSON><PERSON> extends BaseCheckHandler {
  protected CONTEXT = this.constructor.name

  constructor(moduleRef: ModuleRef) {
    super(moduleRef)
  }

  async performCheck(checkData: CheckData): Promise<void> {
    this.isAPIUp = await this.getPing(checkData.url, checkData.timeout * 1000)
  }

  private async getPing(checkUrl: string, timeout: number): Promise<boolean> {
    const host = new URL(checkUrl)
    const hostname = host.hostname

    return new Promise((resolve, reject) => {
      const timeoutPromise = new Promise<never>((_, reject) => {
        const timeoutId = setTimeout(() => {
          clearTimeout(timeoutId)
          reject(new TimeoutError(timeout.toString(), EmptyStatusData))
        }, timeout)
      })
      const pingPromise = new Promise<boolean>((resolve) => {
        ping.sys.probe(hostname, (isAlive: boolean) => {
          resolve(isAlive)
        })
      })
      Promise.race([pingPromise, timeoutPromise]).then(resolve).catch(reject)
    })
  }

  async writeToInflux(checkId: string, workerId: string): Promise<void> {
    const tags = {
      check_id: checkId,
      subscription: 'free', // TODO: this is hard coded
      // subscription: data.checkData['subscription'],
      worker: workerId,
    }
    const fields = {
      check_result: this.isAPIUp ? WorkerResult.SUCCESS : WorkerResult.FAIL,
    }
    await this.db.writeToTimeSeriesDB('check_health', tags, fields, BUCKET)
    this.logger.log(`Writing result to InfluxDB:
    Measurement: ${'check_health'}
    ${format(tags)}
    ${format(fields)}
    Bucket: ${BUCKET}`)
  }

  async performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse> {
    this.logger.log(`Receive request to check: ${testData.url}`)
    try {
      await this.performCheck(testData)
    } finally {
      return {
        status: this.isAPIUp ? 'success' : 'fail',
        worker: workerId,
        message: this.isAPIUp ? 'the host is up' : 'the host is down',
      }
    }
  }
}
