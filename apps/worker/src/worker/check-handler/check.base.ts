import { Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { AxiosResponse } from 'axios'
import {
  StatusChangeError,
  StatusCheckError,
} from '@worker/errors/error.checktypes'
import httpTimer, { RequestConfig } from '@worker/cores/httpTimer'
import { AxiosExpectedErrorHandler } from '@worker/errors/error.general'
import { configService } from '@worker/cores/config/config.service'
import { Database } from '@libs/database/lib/database'
import { PubsubQueue } from '@libs/pubsub-queue/lib/pubsub-queue'
import {
  BUCKET,
  CheckStatus,
  CheckStatusChange,
  HttpMethod,
  StatusChangeMessages,
  WorkerResult,
} from '@libs/shared/constants/check.enum'
import { QUEUE } from '@libs/shared/constants/queues'
import {
  CHECK_WORKER_STATE,
  WORKER_RESPONSE,
} from '@libs/shared/constants/key-prefix'
import {
  CheckWorkerResponseBody,
  RecoveryResetRequest,
} from '@libs/shared/constants/shared.interface'
import { TelemetryService } from '@worker/cores/metric-collector/telemetry.service'
import { ILogger, DefaultLogger } from '@libs/shared/logger'

import { format } from 'util'

import {
  CheckData,
  GetResponseData,
  TestCheckResponse,
  VerifyCheckRequest,
} from '../interface/check.interface'
import { StatusData } from '../interface/check-status.interface'
import { DataHandler } from '../data-handler/data.index'

@Injectable({ scope: Scope.TRANSIENT })
export abstract class BaseCheckHandler {
  protected CONTEXT = this.constructor.name
  protected isAPIUp: boolean
  protected checkResult: StatusData
  protected responseMessage = 'check success'
  protected responseHeader: string
  protected responseData: string
  protected logger: ILogger
  protected db: Database
  protected pubsubqueue: PubsubQueue
  protected dataHandler: DataHandler
  protected metricHandler: TelemetryService

  constructor(protected moduleRef: ModuleRef) {
    this.metricHandler = this.moduleRef.get(TelemetryService, { strict: false })
    this.db = this.moduleRef.get(Database, { strict: false })
    this.pubsubqueue = this.moduleRef.get(PubsubQueue, { strict: false })
    this.dataHandler = this.moduleRef.get(DataHandler, { strict: false })
    this.logger = DefaultLogger.createLogger(this.CONTEXT)
  }

  abstract performCheck(checkData: CheckData): Promise<void>
  abstract performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse>
  //  ===================
  //  Reuseable functions
  //  ===================

  protected async getResponse(
    url: string,
    method: HttpMethod,
    timeout: number,
    handleRedirect: boolean,
    requestBody?: { [key: string]: any } | string,
    requestHeader?: { [key: string]: any },
  ): Promise<GetResponseData> {
    try {
      const timerInstance = await httpTimer.getInstance()
      const config: RequestConfig = {
        method,
        timeout,
        handleRedirect,
        validateStatus: (status) =>
          status >= 200 && status < (handleRedirect ? 400 : 300),
        headers: requestHeader,
      }
      const response: AxiosResponse = await timerInstance.request(
        url,
        config,
        requestBody,
      )
      const timings = response.request.timings
      if (!timings) {
        new StatusCheckError('No timing information available').logError()
      }
      return {
        timings: httpTimer.parseTiming(response),
        responseData: response.data,
        responseHeader: JSON.stringify(response.headers),
        responseMessage: 'check success',
      }
    } catch (error) {
      const errorResponse = AxiosExpectedErrorHandler.getResponseFromError(
        error,
        timeout,
      )
      return errorResponse
    }
  }

  handleTimeout(timeoutInSecond: number): StatusData {
    return {
      tcpConnection: 0,
      tlsHandshake: 0,
      dataTransfer: 0,
      totalTime: timeoutInSecond,
      statusCode: 408,
    } as StatusData
  }

  async sendMetric(checkId: string, workerId: string) {
    this.metricHandler.recordMetrics(checkId, workerId)
    return
  }

  async writeToInflux(checkId: string, workerId: string) {
    const data: StatusData = this.checkResult
    const tags = {
      check_id: checkId,
      subscription: 'team', // TODO: this is hard coded
      worker: workerId,
    }
    const fields = {
      tcp_connection: data.tcpConnection,
      tls_handshake: data.tlsHandshake,
      data_transfer: data.dataTransfer,
      total_time: data.totalTime,
      status_code: data.statusCode,
      check_result: this.isAPIUp ? WorkerResult.SUCCESS : WorkerResult.FAIL, // TODO: Should this be here? Do user need this?
    }
    await this.db.writeToTimeSeriesDB('check_health', tags, fields, BUCKET)
    this.logger.log(`Writing result to InfluxDB:
    Measurement: ${'check_health'}
    ${format(tags)}
    ${format(fields)}
    Bucket: ${BUCKET}`)
  }

  async handleCheckStatusChange(checkData: CheckData): Promise<void> {
    const isInVerificationOrPaused =
      await this.dataHandler.isCheckWorkerVerifying(checkData.id)

    if (isInVerificationOrPaused) return

    const savedApiStatus = (await this.dataHandler.getAPIStatus(
      checkData.id,
      this.isAPIUp,
    )) as CheckStatus
    // console.log(this.isAPIUp, savedApiStatus)
    const howStatusChange = this.resolveStatus(this.isAPIUp, savedApiStatus)
    this.logger.log(`${checkData.id} is currently ${howStatusChange}`)
    switch (howStatusChange) {
      case CheckStatusChange.STILL_UP:
        break
      case CheckStatusChange.STILL_DOWN: {
        this.logger.warn(
          `Check ${checkData.id} is still DOWN, sending recovery reset request`,
        )
        const recoveryResetMessage: RecoveryResetRequest = {
          checkId: checkData.id,
          recoverPeriod: checkData.recoverPeriod,
        }
        this.pubsubqueue.publishToQueue(
          QUEUE.RECOVERY_RESET,
          `reset:${checkData.id}`,
          recoveryResetMessage,
        )
        break
      }
      case CheckStatusChange.UP_TO_DOWN:
      case CheckStatusChange.DOWN_TO_UP: {
        this.logger.log(StatusChangeMessages[howStatusChange], this.CONTEXT)
        this.requestVerifyCheck(checkData.id, howStatusChange)
        break
      }
      default:
        new StatusChangeError(howStatusChange)
    }
  }

  async requestVerifyCheck(
    checkId: string,
    howStatusChange: CheckStatusChange,
  ): Promise<void> {
    const verifyRequest: VerifyCheckRequest = {
      checkId: checkId,
      workerId: configService.getWorkerConfig().workerId,
      time: Date.now(),
      howStatusChange:
        howStatusChange === CheckStatusChange.UP_TO_DOWN
          ? CheckStatusChange.UP_TO_DOWN
          : CheckStatusChange.DOWN_TO_UP,
    }
    await this.pubsubqueue.publishToQueue(QUEUE.VERIFY, checkId, verifyRequest)
    this.logger.log(`Push ${checkId} to ${QUEUE.VERIFY}`)
    return
  }

  async updateResultForOrchestrator(
    checkData: CheckData,
    workerId: string,
  ): Promise<void> {
    const workerResult = this.isAPIUp ? WorkerResult.SUCCESS : WorkerResult.FAIL
    // const checkStatus = this.isAPIUp ? CheckStatus.UP : CheckStatus.DOWN
    // console.log(this.isAPIUp, workerResult, checkStatus)
    await this.db.redisSetHash(
      CHECK_WORKER_STATE(checkData.id),
      `${workerId}`,
      JSON.stringify(workerResult),
    )
    const response: CheckWorkerResponseBody = {
      code: this.checkResult.statusCode,
      message: this.responseMessage,
      header: this.responseHeader,
      data: this.responseData,
    }

    await this.db.redisSetHash(
      CHECK_WORKER_STATE(checkData.id),
      WORKER_RESPONSE(workerId),
      JSON.stringify(response),
    )
  }

  // =======================
  //      Utils functions
  // =======================

  private resolveStatus(
    isAPIUp: boolean,
    savedApiStatus: CheckStatus,
  ): CheckStatusChange {
    if (this.isUpToDown(isAPIUp, savedApiStatus)) {
      return CheckStatusChange.UP_TO_DOWN
    }
    if (this.isDownToUp(isAPIUp, savedApiStatus)) {
      return CheckStatusChange.DOWN_TO_UP
    }
    if (this.isDown(isAPIUp)) {
      return CheckStatusChange.STILL_DOWN
    }
    return CheckStatusChange.STILL_UP
  }

  private isUpToDown(isAPIUp: boolean, savedApiStatus: CheckStatus) {
    return !isAPIUp && savedApiStatus === CheckStatus.UP
  }

  private isDownToUp(isAPIUp: boolean, savedApiStatus: CheckStatus) {
    return isAPIUp && savedApiStatus === CheckStatus.DOWN
  }

  private isDown(isAPIUp: boolean) {
    return !isAPIUp
  }

  protected isResponseRedirect(
    response: AxiosResponse,
    handleRedirect: boolean,
  ): boolean {
    return (
      response.status >= 300 &&
      response.status < 400 &&
      response.headers.location &&
      handleRedirect
    )
  }
}
