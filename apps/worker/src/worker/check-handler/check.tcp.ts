import { Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'

import * as net from 'net'

import { CheckData, TestCheckResponse } from '../interface/check.interface'

import { <PERSON><PERSON>heckHandler } from './check.base'

@Injectable({ scope: Scope.TRANSIENT })
export class <PERSON><PERSON><PERSON><PERSON><PERSON> extends BaseCheckHandler {
  protected CONTEXT = this.constructor.name

  constructor(moduleRef: ModuleRef) {
    super(moduleRef)
  }

  async performCheck(checkData: CheckData): Promise<void> {
    const startTime = Date.now()
    const client = new net.Socket()
    let connectionEstablished = false

    try {
      // Remove protocol if present
      const hostname = checkData.url.replace(/^https?:\/\//, '')
      const portNum = checkData.port

      // TCP connection establishment metrics
      const connectionStartTime = Date.now()
      let connectionEndTime = 0

      // Set timeout
      const responsePromise = new Promise<string>((resolve, reject) => {
        const timeout = setTimeout(() => {
          client.destroy()
          reject(new Error('TCP connection timeout'))
        }, checkData.timeout * 1000)

        // Connect to TCP port
        client.connect(portNum, hostname, () => {
          connectionEstablished = true
          connectionEndTime = Date.now()
          if (!checkData.requestBody) {
            clearTimeout(timeout)
            client.destroy()
            resolve('TCP port open')
          }
        })

        client.on('data', (data) => {
          clearTimeout(timeout)
          client.destroy()
          resolve(data.toString())
        })

        client.on('error', (err) => {
          clearTimeout(timeout)
          client.destroy()
          console.error(`[TCP Check] Connection error: ${err.message}`)
          console.error(err.stack)
          reject(err)
        })
      })

      // Construct payload if exist
      if (checkData.requestBody) {
        const requestPayload =
          typeof checkData.requestBody === 'string'
            ? checkData.requestBody
            : JSON.stringify(checkData.requestBody)
        client.write(requestPayload)
      }

      // Construct payload if exist
      const response = await responsePromise
      const endTime = Date.now()

      this.checkResult = {
        tcpConnection: connectionEstablished
          ? connectionEndTime - connectionStartTime
          : 0,
        tlsHandshake: 0, // Not applicable for plain TCP
        dataTransfer:
          endTime - (connectionEstablished ? connectionEndTime : startTime),
        totalTime: endTime - startTime,
        statusCode: 200,
      }

      this.isAPIUp = true
      this.responseMessage = 'TCP check successful'
      this.responseData = response
      this.responseHeader = ''
    } catch (error) {
      console.error(`[TCP Check] Check failed: ${error.message}`)
      console.error(error.stack)
      const endTime = Date.now()
      this.checkResult = {
        tcpConnection: 0,
        tlsHandshake: 0,
        dataTransfer: 0,
        totalTime: endTime - startTime,
        statusCode: 500,
      }
      this.isAPIUp = false
      this.responseMessage = `TCP check failed: ${error.message}`
      this.responseData = JSON.stringify({
        error: error.message,
        stack: error.stack,
      })
      this.responseHeader = ''
    }
  }

  async performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse> {
    try {
      await this.performCheck(testData)
      return {
        status: this.isAPIUp ? 'success' : 'fail',
        worker: workerId,
        totalTime: this.checkResult.totalTime,
        statusCode: this.checkResult.statusCode,
        message: this.responseMessage,
      }
    } catch (error) {
      return {
        status: 'fail',
        worker: workerId,
        totalTime: this.checkResult?.totalTime || 0,
        statusCode: this.checkResult?.statusCode || 500,
        message: error.message,
      }
    }
  }
}
