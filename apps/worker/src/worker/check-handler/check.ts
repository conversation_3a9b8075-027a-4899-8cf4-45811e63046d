import { Inject, Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'
import { CheckTypeError } from '@worker/errors/error.checktypes'
import { UnexpectedError } from '@worker/errors/error.general'
import { PubsubQueue } from '@libs/pubsub-queue/lib/pubsub-queue'
import { Database } from '@libs/database/lib/database'
import { QUEUE } from '@libs/shared/constants/queues'
import {
  REDIS_CHECK_PREFIX,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'
import { CheckType } from '@libs/shared/constants/check.enum'
import { Logger } from '@libs/shared/logger'

import { DataHandler } from '../data-handler/data.index'
import { CheckData, TestCheckResponse } from '../interface/check.interface'

import { BaseCheckHandler } from './check.base'
import { <PERSON><PERSON><PERSON><PERSON> } from './check.status'
import { <PERSON><PERSON><PERSON><PERSON> } from './check.ping'
import { Expected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './check.expected-status'
import { Keyword<PERSON>he<PERSON> } from './check.keyword'
import { NoKeywordChecker } from './check.no_keyword'
import { UDPChecker } from './check.udp'
import { TCPChecker } from './check.tcp'

@Injectable({ scope: Scope.TRANSIENT })
export class Check {
  private CONTEXT = this.constructor.name
  private handler: BaseCheckHandler
  private checkData: CheckData
  private workerId: string

  constructor(
    @Inject(Database) protected readonly db: Database,
    @Inject(PubsubQueue) protected readonly pubsubqueue: PubsubQueue,

    @Inject(DataHandler)
    private readonly dataHandler: DataHandler,

    private readonly moduleRef: ModuleRef,
  ) {}

  async initialize(checkId: string, workerId: string): Promise<void> {
    try {
      this.workerId = workerId
      this.checkData = await this.dataHandler.getDataFromFastDB(checkId)
      this.handler = await this.createHandler(this.checkData.type)
    } catch (error) {
      // Delete old checkType handler if it exist
      if (!this.handler) {
        await this.pubsubqueue.deleteRepeatableJob(QUEUE.MAIN, checkId)
        await this.db.redisDelete(checkId, REDIS_CHECK_PREFIX.CHECK_DATA)
        await this.db.redisDelete(checkId, REDIS_CHECK_PREFIX.WORKER_NODE)
      }
      Logger.error(
        JSON.stringify({
          error,
          id: this.checkData.id,
          checkData: this.checkData,
        }),
      )
      throw new UnexpectedError(error.message, error.statusCode)
    }
  }

  async createHandler(checkType: CheckType): Promise<BaseCheckHandler> {
    switch (checkType) {
      case CheckType.STATUS:
        return this.moduleRef.resolve(StatusChecker)
      case CheckType.EXPECTED_STATUS_CODE:
        return this.moduleRef.resolve(ExpectedStatusChecker)
      case CheckType.PING:
        return this.moduleRef.resolve(PingChecker)
      case CheckType.KEYWORD:
        return this.moduleRef.resolve(KeywordChecker)
      case CheckType.NO_KEYWORD:
        return this.moduleRef.resolve(NoKeywordChecker)
      case CheckType.TCP_PORT:
        return this.moduleRef.resolve(TCPChecker)
      case CheckType.UDP_PORT:
        return this.moduleRef.resolve(UDPChecker)
      default:
        throw new CheckTypeError(checkType)
    }
  }

  async performCheck(): Promise<void> {
    try {
      Logger.log(
        `${this.workerId} is checking on ${this.checkData.id}`,
        this.CONTEXT,
      )

      const log = await this.db
        .redisSetHash(
          this.checkData.id,
          REDIS_CHECK_STATUS_KEY.LAST_CHECK_AT,
          new Date().toISOString(),
        )
        .catch((err) => {
          Logger.error(
            `Failed to set Redis hash for check ${this.checkData.id}: ${err.message}`,
            err,
          )
          throw err
        })

      Logger.log(
        JSON.stringify({
          log,
          id: this.checkData.id,
          key: REDIS_CHECK_STATUS_KEY.LAST_CHECK_AT,
          timestamp: new Date().toISOString(),
        }),
      )

      await this.handler.performCheck(this.checkData)
    } catch (err) {
      Logger.error(
        `Error performing check for ${this.checkData.id}: ${err.message}`,
        err,
      )
      throw err
    }
  }

  async handleCheckStatusChange(): Promise<void> {
    await this.handler.handleCheckStatusChange(this.checkData)
  }

  async sendMetric(checkId: string, workerId: string) {
    await this.handler.sendMetric(checkId, workerId)
  }

  async writeDataToDB(): Promise<void> {
    await this.handler.writeToInflux(this.checkData.id, this.workerId)
  }

  async updateResultForOrchestrator(): Promise<void> {
    await this.handler.updateResultForOrchestrator(
      this.checkData,
      this.workerId,
    )
  }

  async performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse> {
    return await this.handler.performTestCheck(testData, workerId)
  }
}
