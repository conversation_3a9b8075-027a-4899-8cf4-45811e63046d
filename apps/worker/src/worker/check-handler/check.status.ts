/* eslint-disable no-unsafe-finally */
import { Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'

import { CheckData, TestCheckResponse } from '../interface/check.interface'

import { BaseCheckHandler } from './check.base'

@Injectable({ scope: Scope.TRANSIENT })
export class <PERSON><PERSON><PERSON><PERSON> extends BaseCheckHandler {
  protected CONTEXT = this.constructor.name

  constructor(moduleRef: ModuleRef) {
    super(moduleRef)
  }

  async performCheck(checkData: CheckData): Promise<void> {
    const response = await this.getResponse(
      checkData.url,
      checkData.method,
      checkData.timeout * 1000,
      checkData.handleRedirect,
      checkData.requestBody,
      checkData.requestHeader,
    )
    this.checkResult = response.timings
    this.isAPIUp = this.checkStatusCode(this.checkResult.statusCode)
    this.responseMessage = response.responseMessage
    this.responseData = response.responseData
    this.responseHeader = response.responseHeader
  }

  private checkStatusCode(statusCode: number): boolean {
    return 300 > statusCode && statusCode >= 200
  }

  async performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse> {
    this.logger.log(`Receive request to check: ${testData.url}`)
    try {
      await this.performCheck(testData)
    } finally {
      return {
        status: this.isAPIUp ? 'success' : 'fail',
        worker: workerId,
        totalTime: this.checkResult.totalTime,
        statusCode: this.checkResult.statusCode,
        message: this.responseMessage,
      }
    }
  }
}
