/* eslint-disable no-unsafe-finally */
import { Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'

import { StatusData } from '../interface/check-status.interface'
import { CheckData, TestCheckResponse } from '../interface/check.interface'

import { <PERSON><PERSON><PERSON><PERSON> } from './check.status'

@Injectable({ scope: Scope.TRANSIENT })
export class ExpectedStatusChecker extends StatusChecker {
  protected CONTEXT = this.constructor.name
  protected checkResult: StatusData
  private expectedStatusCode: number[]
  constructor(moduleRef: ModuleRef) {
    super(moduleRef)
  }

  async performCheck(checkData: CheckData): Promise<void> {
    this.expectedStatusCode = checkData.expectedStatusCodes
    const response = await this.getResponse(
      checkData.url,
      checkData.method,
      checkData.timeout * 1000,
      checkData.handleRedirect,
      checkData.requestBody,
      checkData.requestHeader,
    )
    this.checkResult = response.timings
    this.responseMessage = response.responseMessage
    this.responseData = response.responseData
    this.responseHeader = response.responseHeader
    this.isAPIUp = this.doesStatusCodeMatchRequirement(
      this.checkResult.statusCode,
      checkData.expectedStatusCodes,
    )
    if (!this.isAPIUp) {
      this.responseMessage = `Status return ${this.checkResult.statusCode} not expected in ${checkData.expectedStatusCodes}`
    }
  }

  async performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse> {
    this.logger.log(`Receive request to check: ${testData.url}`)
    try {
      await this.performCheck(testData)
    } finally {
      return {
        status: this.isAPIUp ? 'success' : 'fail',
        worker: workerId,
        totalTime: this.checkResult.totalTime,
        statusCode: this.checkResult.statusCode,
        message: this.responseMessage,
      }
    }
  }

  // =======================
  //      Utils functions
  // =======================
  private doesStatusCodeMatchRequirement(
    statusCode: number,
    expectedStatusCodes: number[],
  ): boolean {
    return expectedStatusCodes.includes(statusCode)
  }
}
