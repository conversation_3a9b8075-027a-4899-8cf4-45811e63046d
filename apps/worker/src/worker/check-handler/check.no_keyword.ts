/* eslint-disable no-unsafe-finally */
import { Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'

import { CheckData, TestCheckResponse } from '../interface/check.interface'

import { BaseCheckHandler } from './check.base'

@Injectable({ scope: Scope.TRANSIENT })
export class No<PERSON>eywordChecker extends BaseCheckHandler {
  protected CONTEXT = this.constructor.name
  private requiredKeyword: string
  constructor(moduleRef: ModuleRef) {
    super(moduleRef)
  }

  async performCheck(checkData: CheckData): Promise<void> {
    this.requiredKeyword = checkData.requiredKeyword
    const response = await this.getResponse(
      checkData.url,
      checkData.method,
      checkData.timeout * 1000,
      checkData.handleRedirect,
      checkData.requestBody,
      checkData.requestHeader,
    )
    this.checkResult = response.timings
    this.isAPIUp = this.isKeywordNotPresent(
      response.responseData,
      checkData.requiredKeyword,
    )
    this.responseData = response.responseData
    this.responseHeader = response.responseHeader
    if (this.checkResult.statusCode < 300) {
      this.responseMessage = this.isAPIUp
        ? `Keyword ${checkData.requiredKeyword} found`
        : `Keyword ${checkData.requiredKeyword} not found`
    } else {
      this.responseMessage = response.responseMessage
    }
  }

  private isKeywordNotPresent(responseBody: string, keyword: string): boolean {
    const dataString =
      typeof responseBody === 'string'
        ? responseBody
        : JSON.stringify(responseBody)
    return !dataString.includes(keyword)
  }

  async performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse> {
    this.logger.log(`Receive request to check: ${testData.url}`)
    try {
      await this.performCheck(testData)
    } finally {
      return {
        status: this.isAPIUp ? 'success' : 'fail',
        worker: workerId,
        statusCode: this.checkResult.statusCode,
        totalTime: this.checkResult.totalTime,
        message: this.responseMessage,
      }
    }
  }
}
