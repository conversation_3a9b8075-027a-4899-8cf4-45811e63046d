import { Injectable, Scope } from '@nestjs/common'
import { ModuleRef } from '@nestjs/core'

import * as dgram from 'dgram'

import { CheckData, TestCheckResponse } from '../interface/check.interface'

import { BaseCheckHandler } from './check.base'

@Injectable({ scope: Scope.TRANSIENT })
export class <PERSON><PERSON><PERSON>he<PERSON> extends BaseCheckHandler {
  protected CONTEXT = this.constructor.name

  constructor(moduleRef: ModuleRef) {
    super(moduleRef)
  }

  async performCheck(checkData: CheckData): Promise<void> {
    const startTime = Date.now()
    const client = dgram.createSocket('udp4')

    try {
      // Bind socket to receive responses
      await new Promise<void>((resolve, reject) => {
        client.once('error', reject)
        client.bind(0, () => {
          client.removeListener('error', reject)
          resolve()
        })
      })

      // creating Promise Object
      const responsePromise = new Promise<string>((resolve, reject) => {
        const timeout = setTimeout(() => {
          client.close()
          reject(new Error('UDP response timeout'))
        }, checkData.timeout * 1000)

        client.on('message', (msg) => {
          clearTimeout(timeout)
          client.close()
          resolve(msg.toString())
        })

        client.on('error', (err) => {
          clearTimeout(timeout)
          client.close()
          reject(new Error(`UDP error: ${err.message}`))
        })
      })

      // Encoding requestBody to Buffer
      // UDP require payload to be Buffer type so user have to encode their payload
      // Different UDP port have a different protocol-specific packet
      // Below is DNS packet, encoded to base64 by user before sending to Buffer and decoded by us

      // const dnsQuery = dnsPacket.encode({
      //   type: 'query',
      //   id: Math.floor(Math.random() * 65535),
      //   flags: dnsPacket.RECURSION_DESIRED,
      //   questions: [
      //     {
      //       name: 'google.com',
      //       type: 'A',
      //       class: 'IN',
      //     },
      //   ],
      // })
      // const base64 = packet.toString('base64');
      // The code above will give my current requestBody, which is AAABAAABAAAAAAAABmdvb2dsZQNjb20AAAEAAQ==

      const payloadBuffer = Buffer.from(
        checkData.requestBody as string,
        'base64',
      )

      // Send request and get response
      await new Promise<void>((resolve, reject) => {
        client.send(payloadBuffer, checkData.port, checkData.url, (err) => {
          if (err) {
            client.close()
            reject(err)
          } else {
            resolve()
          }
        })
      })

      const response = await responsePromise
      const endTime = Date.now()

      this.checkResult = {
        tcpConnection: 0,
        tlsHandshake: 0,
        dataTransfer: 0,
        totalTime: endTime - startTime,
        statusCode: 200,
      }

      this.isAPIUp = true
      this.responseMessage = 'UDP check successful'
      this.responseData = response
      this.responseHeader = ''
    } catch (error) {
      const endTime = Date.now()
      this.checkResult = {
        tcpConnection: 0,
        tlsHandshake: 0,
        dataTransfer: 0,
        totalTime: endTime - startTime,
        statusCode: 500,
      }
      this.isAPIUp = false
      this.responseMessage = error.message
      this.responseData = ''
      this.responseHeader = ''
    }
  }

  async performTestCheck(
    testData: CheckData,
    workerId: string,
  ): Promise<TestCheckResponse> {
    try {
      await this.performCheck(testData)
      return {
        status: this.isAPIUp ? 'success' : 'fail',
        worker: workerId,
        totalTime: this.checkResult.totalTime,
        statusCode: this.checkResult.statusCode,
        message: this.responseMessage,
      }
    } catch (error) {
      return {
        status: 'fail',
        worker: workerId,
        totalTime: this.checkResult?.totalTime || 0,
        statusCode: this.checkResult?.statusCode || 500,
        message: error.message,
      }
    }
  }
}
