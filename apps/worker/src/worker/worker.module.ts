import { Module } from '@nestjs/common'
import { PubsubQueueModule } from '@libs/pubsub-queue/lib/pubsub-queue.module'
import { DatabaseModule } from '@libs/database/lib/database.module'
import { TelemetryModule } from '@worker/cores/metric-collector/telemetry.module'

import { TestCheckWorker } from './workers/test-check.worker'
import { VerifyCheckWorker } from './workers/verify-check.worker'
import { MainCheckWorker } from './workers/check.worker'
import { CheckDataHandler } from './data-handler/check.data'
import { CheckStatusHandler } from './data-handler/check-status.data'
import { Check } from './check-handler/check'
import { StatusChe<PERSON> } from './check-handler/check.status'
import { DataHandler } from './data-handler/data.index'
import { ExpectedStatusChecker } from './check-handler/check.expected-status'
import { <PERSON><PERSON><PERSON><PERSON> } from './check-handler/check.ping'
import { KeywordChecker } from './check-handler/check.keyword'
import { No<PERSON><PERSON>word<PERSON>he<PERSON> } from './check-handler/check.no_keyword'
import { TCPChecker } from './check-handler/check.tcp'
import { UDPChecker } from './check-handler/check.udp'

// Workers: start on module init
const workers = [MainCheckWorker, VerifyCheckWorker, TestCheckWorker]

const checkTypeWorker = [
  StatusChecker,
  ExpectedStatusChecker,
  PingChecker,
  KeywordChecker,
  NoKeywordChecker,
  TCPChecker,
  UDPChecker,
]

const dataHandlers = [CheckDataHandler, CheckStatusHandler, DataHandler]

@Module({
  imports: [TelemetryModule, PubsubQueueModule, DatabaseModule],
  providers: [...workers, ...checkTypeWorker, ...dataHandlers, Check],
})
export class WorkerModule {}
