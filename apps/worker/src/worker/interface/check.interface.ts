import {
  CheckStatusChange,
  CheckType,
  HttpMethod,
} from '@libs/shared/constants/check.enum'

import { StatusData } from './check-status.interface'

export interface CheckData {
  id: string
  url: string
  port: number
  timeout: number
  type: CheckType
  handleRedirect: boolean
  recoverPeriod: number
  confirmPeriod: number
  protocol: string
  method: HttpMethod
  requestHeader: { [key: string]: any }
  requestBody: { [key: string]: any } | string
  requiredKeyword: string
  expectedStatusCodes: number[]
}

export interface GetResponseData {
  timings: StatusData
  responseData: string
  responseHeader: string
  responseMessage: string
}

export interface VerifyCheckRequest {
  checkId: string
  workerId: string
  time: number
  howStatusChange: CheckStatusChange.UP_TO_DOWN | CheckStatusChange.DOWN_TO_UP
}

export interface TestCheckResponse {
  status: 'success' | 'fail'
  message: string
  worker: string
  statusCode?: number
  totalTime?: number
}
