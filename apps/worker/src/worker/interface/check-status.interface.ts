export interface StatusData {
  tcpConnection: number
  tlsHandshake: number
  dataTransfer: number
  totalTime: number
  statusCode: number
}

export const EmptyStatusData: StatusData = {
  tcpConnection: 0,
  tlsHandshake: 0,
  dataTransfer: 0,
  totalTime: 0,
  statusCode: 0,
}

export interface TimingResult {
  tcpConnection: number
  tlsHandshake: number
  dataTransfer: number
  totalTime: number
  statusCode: number
}
