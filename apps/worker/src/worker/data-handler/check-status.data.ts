import { Database } from '@libs/database/lib/database'
import { CheckStatus } from '@libs/shared/constants/check.enum'
import {
  CHECK_WORKER_STATE,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'
import { Logger } from '@libs/shared/logger'
import { Inject, Injectable } from '@nestjs/common'

@Injectable()
export class CheckStatusHandler {
  private CONTEXT = this.constructor.name

  constructor(@Inject(Database) private readonly db: Database) {}

  async isCheckWorkerVerifying(checkId: string): Promise<boolean> {
    const isVerifying = await this.db.redisGetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.IS_VERIFYING,
    )
    return JSON.parse(isVerifying || 'false')
  }

  async getAPIStatus(checkId: string, isAPIUp: boolean): Promise<string> {
    const savedStatus = await this.db.redisGetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.STATUS,
    )
    if (!savedStatus) return await this.fallBackHash(checkId, isAPIUp)
    // if (!savedStatus) return CheckStatus.UP
    return JSON.parse(savedStatus)
  }

  // FALLBACK FOR REDIS HASH FILE: USE CURRENT WORKER'S RESULT
  async fallBackHash(
    checkId: string,
    currentResult: boolean,
  ): Promise<CheckStatus> {
    Logger.warn(
      `No STATUS found in hash, use current worker's result`,
      this.CONTEXT,
    )
    if (currentResult) {
      this.db.redisSetHash(
        CHECK_WORKER_STATE(checkId),
        REDIS_CHECK_STATUS_KEY.STATUS,
        JSON.stringify(CheckStatus.UP),
      )
      return CheckStatus.UP
    } else {
      this.db.redisSetHash(
        CHECK_WORKER_STATE(checkId),
        REDIS_CHECK_STATUS_KEY.STATUS,
        JSON.stringify(CheckStatus.DOWN),
      )
      return CheckStatus.DOWN
    }
  }
}
