import { Injectable } from '@nestjs/common'

import { CheckData } from '../interface/check.interface'

import { CheckStatusHandler } from './check-status.data'
import { CheckDataHandler } from './check.data'

@Injectable()
export class DataHandler {
  constructor(
    private readonly checkStatus: CheckStatus<PERSON>and<PERSON>,
    private readonly checkData: CheckDataHand<PERSON>,
  ) {}

  async isCheckWorkerVerifying(checkId: string): Promise<boolean> {
    return this.checkStatus.isCheckWorkerVerifying(checkId)
  }

  async getAPIStatus(checkId: string, isAPIUp: boolean): Promise<string> {
    return this.checkStatus.getAPIStatus(checkId, isAPIUp)
  }

  async getDataFromFastDB(checkId: string): Promise<CheckData> {
    return this.checkData.getDataFromFastDB(checkId)
  }
}
