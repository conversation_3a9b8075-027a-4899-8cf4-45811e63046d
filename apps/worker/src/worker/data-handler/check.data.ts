import { Inject, Injectable } from '@nestjs/common'
import { REDIS_CHECK_PREFIX } from '@libs/shared/constants/key-prefix'
import { Database } from '@libs/database/lib/database'
import { HttpMethod } from '@libs/shared/constants/check.enum'
import { Logger } from '@libs/shared/logger'

import { CheckData } from '../interface/check.interface'

@Injectable()
export class CheckDataHandler {
  private CONTEXT = this.constructor.name

  constructor(@Inject(Database) private readonly db: Database) {}

  async getDataFromFastDB(checkId: string): Promise<CheckData> {
    const checkData = await this.db.redisGet(
      checkId,
      REDIS_CHECK_PREFIX.CHECK_DATA,
    )
    if (!checkData)
      return this.convertToCheckData(await this.fallBackConfig(checkId))
    return this.convertToCheckData(checkData)
  }

  convertToCheckData(arr: string[]): CheckData {
    if (arr.length < 12) {
      throw new Error('Invalid array length')
    }
    const checkData = {
      id: arr['id'],
      url: arr['url'],
      port: arr['port'],
      timeout: parseInt(arr['timeout'], 10),
      type: arr['type'],
      handleRedirect: arr['handleRedirect'] === true,
      recoverPeriod: parseInt(arr['recoverPeriod'], 10),
      confirmPeriod: parseInt(arr['confirmPeriod'], 10),
      protocol: arr['protocol'],
      method: arr['method'] as HttpMethod,
      requestHeader: arr['requestHeader'],
      requestBody: arr['requestBody'],
      requiredKeyword: arr['requiredKeyword'],
      expectedStatusCodes: arr['expectedStatusCodes'],
    }
    return checkData
  }

  // TODO, well clean up this
  async fallBackConfig(checkId: string): Promise<string[]> {
    Logger.warn(
      `No config found, attempting to get config from db`,
      this.CONTEXT,
    )
    const fallbackKeys =
      'id, url, timeout, type, recover_period, confirm_period, protocol, method, timeout, header, expected_respond_header, expected_respond_body, required_keyword, expected_status_codes'
    const dbData = await this.db.getValuesFromPersistentDB(
      fallbackKeys,
      'checks',
      checkId,
    )
    const newCheckConfig = this.mapDBDataToCheckData(dbData)
    if (newCheckConfig) {
      Logger.log(`Config found`, this.CONTEXT)
      this.db.redisSet(checkId, REDIS_CHECK_PREFIX.CHECK_DATA, newCheckConfig)
      return newCheckConfig
    } else {
      Logger.error(`Check Data not found!`, this.CONTEXT)
      throw Error('Check Data not found!')
    }
  }

  mapDBDataToCheckData(dbData: { [key: string]: any }): any {
    return {
      id: dbData.id,
      url: dbData.url,
      port: dbData.port,
      type: dbData.type,
      handleRedirect: dbData.handle_redirect,
      recoverPeriod: dbData.recover_period,
      confirmPeriod: dbData.confirm_period,
      protocol: dbData.protocol,
      method: dbData.method,
      timeout: dbData.timeout,
      header: dbData.header,
      requiredKeyword: dbData.required_keyword,
      expectedStatusCodes: dbData.expected_status_code,
    }
  }
}
