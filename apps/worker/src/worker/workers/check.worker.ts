// ======================================================
//            MAIN WORKER: START FIRST CHECK
// ======================================================
import { ModuleRef } from '@nestjs/core'
import { configService } from '@worker/cores/config/config.service'
import { CHANNEL } from '@libs/shared/constants/channel'
import { subscriber } from '@libs/shared/service-clients/redis.client'
import { DefaultLogger, ILogger } from '@libs/shared/logger'
import { Injectable } from '@nestjs/common'

import { Check } from '../check-handler/check'

@Injectable()
export class MainCheckWorker {
  private CONTEXT = this.constructor.name
  private moduleRef: ModuleRef
  private logger: ILogger

  constructor(moduleRef: ModuleRef) {
    if (!moduleRef) {
      throw new Error('ModuleRef is required')
    }
    this.moduleRef = moduleRef
    this.logger = DefaultLogger.createLogger(this.CONTEXT)
    this.initializeWorker()
  }

  private initializeWorker() {
    const { workerId } = configService.getWorkerConfig()
    const channel = CHANNEL.WORKER_MAIN(workerId)

    subscriber.subscribe(channel, (err) => {
      if (err) {
        this.logger.error('Failed to subscribe: ', err)
      } else {
        this.logger.log(`Subscribed to ${channel}`)
      }
    })

    subscriber.on('message', async (channel, message) => {
      if (channel === CHANNEL.WORKER_MAIN(workerId)) {
        try {
          const checkId = message
          const newCheck = await this.moduleRef.resolve(Check)
          await newCheck.initialize(checkId, workerId)
          await newCheck.performCheck()
          await newCheck.handleCheckStatusChange()
          await newCheck.writeDataToDB()
          await newCheck.updateResultForOrchestrator()
          if (process.env.METRIC_ENABLE == 'true') {
            await newCheck.sendMetric(checkId, workerId)
          }
        } catch (err) {
          this.logger.error(`Failed to process check ${message}`, err)
        }
      }
    })
  }
}
