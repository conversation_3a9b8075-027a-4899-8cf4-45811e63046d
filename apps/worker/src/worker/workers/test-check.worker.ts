// ===============================================================
//     TEST WORKER: RECEIVE TEST CHECK WHEN CONFIG A CHECK
// ===============================================================
import { Job, Worker } from 'bullmq'
import { ModuleRef } from '@nestjs/core'
import { configService } from '@worker/cores/config/config.service'
import { redisConnection } from '@worker/commons/redis-connection'
import { QUEUE } from '@libs/shared/constants/queues'
import { Injectable } from '@nestjs/common'

import { Check } from '../check-handler/check'
import { CheckData } from '../interface/check.interface'

@Injectable()
export class TestCheckWorker {
  constructor(private readonly moduleRef: ModuleRef) {
    if (!moduleRef) {
      throw new Error('ModuleRef dependency not injected')
    }
    const { workerId } = configService.getWorkerConfig()
    new Worker(
      QUEUE.TESTCHECK(workerId),
      async (job: Job) => {
        try {
          console.log(job.data)
          const testCheck: CheckData = JSON.parse(job.data)
          const newCheck = await this.moduleRef.resolve(Check)
          const testHandler = await newCheck.createHandler(testCheck.type)
          console.log(testHandler)
          const message = await testHandler.performTestCheck(
            testCheck,
            workerId,
          )
          console.log(message)
          return JSON.stringify(message)
        } catch (error) {
          const messageError = {
            status: 'fail',
            message: error.message,
            worker: workerId,
            statusCode: 500,
            totalTime: 0,
          }
          console.log(error)
          return JSON.stringify(messageError)
        }
      },
      {
        connection: redisConnection,
      },
    )
  }
}
