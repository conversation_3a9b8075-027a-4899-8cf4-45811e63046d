// ==============================================================
//    DOUBLE CHECK WORKER: START RE CHECKING FOR CONFIRMATION
// ==============================================================
import { Job, Worker } from 'bullmq'
import { ModuleRef } from '@nestjs/core'
import { configService } from '@worker/cores/config/config.service'
import { redisConnection } from '@worker/commons/redis-connection'
import { WORKER_VERIFY_QUEUE } from '@libs/shared/constants/queues'

import { Check } from '../check-handler/check'

export class VerifyCheckWorker {
  constructor(private readonly moduleRef: ModuleRef) {
    const { workerId } = configService.getWorkerConfig()

    new Worker(
      WORKER_VERIFY_QUEUE(workerId),
      async (job: Job) => {
        const checkId = job.data.checkId
        const newCheck = await this.moduleRef.resolve(Check)
        await newCheck.initialize(checkId, workerId)
        await newCheck.performCheck()
        // /* Verify Check don't care about status change */
        await newCheck.writeDataToDB()
        await newCheck.updateResultForOrchestrator()
      },
      {
        connection: redisConnection,
      },
    )
  }
}
