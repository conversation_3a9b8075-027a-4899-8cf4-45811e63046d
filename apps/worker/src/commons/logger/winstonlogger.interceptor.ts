import {
  CallHand<PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common'
import { Observable, throwError } from 'rxjs'
import { tap, catchError } from 'rxjs/operators'
import { DefaultLogger } from '@libs/shared/logger/default-logger'

import { format } from 'util'

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest()
    const response = context.switchToHttp().getResponse()
    const method = request.method
    const url = request.url
    const userId = 'userID' // TODO: Get userID from auth token
    const payload = format(request.body)
    const requestContext = context.getClass().name
    // const requestContext2 = context.getHandler().name // Method name
    this.logger.log(
      `[HTTP] Incoming request: ${method} ${url} by ${userId}, body: ${payload}`,
      requestContext,
    )

    return next.handle().pipe(
      catchError((error) => {
        this.logger.error(
          `[HTTP] Request ${method} to ${url} by ${userId} FAILED`,
          error,
        )
        return throwError(() => error)
      }),
      tap(() =>
        this.logger.log(
          `[HTTP] Request ${method} to ${url} by ${userId} completed, response: ${response.statusCode}`,
          requestContext,
        ),
      ),
    )
  }

  constructor(private readonly logger: DefaultLogger) {}
}
export const INTERCEPTOR_LOGGER = 'INTERCEPTOR_LOGGER'
