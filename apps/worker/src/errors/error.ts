import { Logger } from '@libs/shared/logger'
import { StatusData } from '@worker/worker/interface/check-status.interface'

export abstract class CheckError extends Error {
  protected statusCode: number
  protected timings: StatusData

  constructor(message: string, statusCode: number) {
    super(message)
    this.statusCode = statusCode
  }
  logError() {
    Logger.error(this.message)
    return this
  }

  getMessage(): string {
    return this.message
  }
}

export abstract class CheckErrorWithTimings extends CheckError {
  protected timings: StatusData

  constructor(message: string, statusCode: number, timings: StatusData) {
    super(message, statusCode)
    this.statusCode = statusCode
    this.timings = timings
  }

  getTimings(): StatusData {
    return this.timings
  }
}

export abstract class CheckErrorWithData extends CheckE<PERSON>rWithTimings {
  protected header: string
  protected data: string

  constructor(
    message: string,
    header: string,
    data: string,
    statusCode: number,
    timings: StatusData,
  ) {
    super(message, statusCode, timings)
    this.header = header
    this.data = data
  }

  getData(): string {
    return this.data
  }

  getHeader(): string {
    return this.header
  }
}
