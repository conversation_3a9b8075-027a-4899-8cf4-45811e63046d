import axios from 'axios'
import httpTimer from '@worker/cores/httpTimer'
import { StatusData } from '@worker/worker/interface/check-status.interface'
import { GetResponseData } from '@worker/worker/interface/check.interface'

import { CheckErrorWithTimings, CheckErrorWithData, CheckError } from './error'

export class AxiosExpectedErrorHandler {
  static createGeneralErrors(
    error: any,
    timeout: number,
  ): CheckErrorWithTimings {
    const timings = httpTimer.parseTiming(error)
    const responseData = error.data || ''
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT')
        return new TimeoutError(timeout.toString(), timings)
      if (error.code === 'ECONNREFUSED')
        return new ConnectionRefusedError(
          'Connection refused by the server',
          timings,
        )
      if (error.code === 'ENOTFOUND' || error.code === 'EAI_AGAIN')
        return new NetworkError('DNS lookup failed', timings)
    }
    if (error.response) {
      const responseHeader = error.response.headers
      if (error.response.status < 200)
        return new _1XXError(error.message, responseHeader, responseData, {
          ...timings,
          statusCode: error.response.status,
        })
      // 2XX won't go into this route
      if (error.response.status < 400)
        return new _3XXError(error.message, responseHeader, responseData, {
          ...timings,
          statusCode: error.response.status,
        })
      if (error.response.status < 500)
        return new _4XXError(error.message, responseHeader, responseData, {
          ...timings,
          statusCode: error.response.status,
        })
      if (error.response.status < 600)
        return new _5XXError(error.message, responseHeader, responseData, {
          ...timings,
          statusCode: error.response.status,
        })
    }
    return new UnexpectedErrorWithTimings(
      error.message,
      error.response?.status,
      timings,
    )
  }

  static getResponseFromError(
    error: CheckErrorWithTimings,
    timeoutInSecond: number,
  ): GetResponseData {
    const generalError = this.createGeneralErrors(error, timeoutInSecond)
    if (generalError instanceof TimeoutError) {
      return {
        timings: {
          tcpConnection: 0,
          tlsHandshake: 0,
          dataTransfer: 0,
          totalTime: timeoutInSecond,
          statusCode: 408,
        },
        // No Data or Header because timeout means no response
        responseData: '',
        responseHeader: '',
        responseMessage: `Timeout error after waiting for ${timeoutInSecond}s`,
      }
    } else if (generalError instanceof CheckErrorWithData) {
      return {
        timings: generalError.getTimings(),
        responseData: generalError.getData(),
        responseHeader: generalError.getHeader(),
        responseMessage: generalError.message,
      }
    } else {
      return {
        // No Data or Header because we caught request error, so no response
        timings: generalError.getTimings(),
        responseData: '',
        responseHeader: '',
        responseMessage: generalError.message,
      }
    }
  }
}

export class _1XXError extends CheckErrorWithData {
  constructor(
    message: string,
    header: string,
    data: string,
    timings: StatusData,
  ) {
    super(message, header, data, 100, timings)
    this.message = `1XX Error: ${this.message}`
  }
}
export class _3XXError extends CheckErrorWithData {
  constructor(
    message: string,
    header: string,
    data: string,
    timings: StatusData,
  ) {
    super(message, header, data, 300, timings)
    this.message = `3XX Error: ${this.message}`
  }
}

export class _4XXError extends CheckErrorWithData {
  constructor(
    message: string,
    header: string,
    data: string,
    timings: StatusData,
  ) {
    super(message, header, data, 400, timings)
    this.message = `4XX Error: ${this.message}`
  }
}

export class _5XXError extends CheckErrorWithData {
  constructor(
    message: string,
    header: string,
    data: string,
    timings: StatusData,
  ) {
    super(message, header, data, 500, timings)
    this.message = `5XX Error: ${this.message}`
  }
}
export class TimeoutError extends CheckErrorWithTimings {
  constructor(message: string, timings: StatusData) {
    super(message, 408, timings)
    this.message = `Request exceeded expected time, cut off at set timeout, which is ${this.message}s`
  }
}

export class UnexpectedErrorWithTimings extends CheckErrorWithTimings {
  constructor(message: string, statusCode: number, timings: StatusData) {
    super(message, statusCode | 500, timings)
    this.message = `Unexpected error: ${this.message}`
  }
}

export class UnexpectedError extends CheckError {
  constructor(message: string, statusCode: number) {
    super(message, statusCode | 500)
    this.message = `Unexpected error: ${this.message}`
  }
}

export class NetworkError extends CheckErrorWithTimings {
  constructor(message: string, timings: StatusData) {
    super(message, 502, timings)
    this.message = `Network error occurred during API check: ${this.message}`
  }
}

export class ConnectionRefusedError extends CheckErrorWithTimings {
  constructor(message: string, timings: StatusData) {
    super(message, 502, timings)
    this.message = `Connection refused by the server`
  }
}
