import { CheckError } from './error'

export class StatusCheckError extends CheckError {
  constructor(message: string) {
    super(message, 500)
    this.message = message
  }
}

export class CheckTypeError extends CheckError {
  constructor(message: string) {
    super(message, 500)
    this.message = `Unknown check type: ${this.message}`
  }
}

export class StatusChangeError extends CheckError {
  constructor(message: string) {
    super(message, 500)
    this.message = `Abnormal status change detected: ${this.message}`
  }
}
