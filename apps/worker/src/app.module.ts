import { APP_INTERCEPTOR } from '@nestjs/core'
import { Module } from '@nestjs/common'
import { RequestContextModule } from 'nestjs-request-context'
import { ConfigModule } from '@nestjs/config'
import { WinstonModule } from 'nest-winston'
import { ContextInterceptor } from '@worker/commons/context/ContextInterceptor'
import { PubsubQueueModule } from '@libs/pubsub-queue/lib/pubsub-queue.module'
import { DatabaseModule } from '@libs/database/lib/database.module'
import { DefaultLogger, loggerConfig } from '@libs/shared/logger'

import {
  LoggingInterceptor,
  INTERCEPTOR_LOGGER,
} from './commons/logger/winstonlogger.interceptor'
import { WorkerModule } from './worker/worker.module'
import { TelemetryModule } from './cores/metric-collector/telemetry.module'
import { HealthModule } from './health/health.module'

const interceptors = [
  {
    provide: APP_INTERCEPTOR,
    useClass: ContextInterceptor,
  },
  DefaultLogger,
  {
    provide: INTERCEPTOR_LOGGER,
    useClass: LoggingInterceptor,
  },
]

@Module({
  imports: [
    TelemetryModule,
    PubsubQueueModule,
    ConfigModule.forRoot(),
    WinstonModule.forRoot(loggerConfig),
    RequestContextModule,
    DatabaseModule,
    WorkerModule,
    HealthModule,
  ],
  controllers: [],
  providers: [...interceptors],
})
export class AppModule {}
