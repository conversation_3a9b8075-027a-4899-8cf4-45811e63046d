{"name": "worker", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/worker/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/worker", "main": "apps/worker/src/main.ts", "tsConfig": "apps/worker/tsconfig.app.json"}, "configurations": {"development": {"webpackConfig": "apps/worker/webpack.hmr.config.js"}, "production": {"webpackConfig": "apps/worker/webpack.config.js", "generatePackageJson": true}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "worker:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "worker:build:development"}, "production": {"buildTarget": "worker:build:production"}}}, "start:multi": {"executor": "nx:run-commands", "outputs": [], "dependsOn": ["build"], "options": {"commands": [{"command": "pm2 kill && pm2 start apps/worker/ecosystem.config.js && pm2 logs"}]}}, "sync-env": {"executor": "nx:run-commands", "outputs": [], "options": {"commands": [{"command": "node scripts/sync-env.mjs --secretName worker-development --fileName apps/worker/.env.development"}]}}}}