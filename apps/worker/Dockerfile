FROM node:20-alpine AS builder

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

COPY package.json pnpm-lock.yaml ./

RUN pnpm install --frozen-lockfile
COPY . .

RUN pnpm exec nx run worker:build --configuration=production

FROM node:20-alpine as runner

# Using for healthcheck
RUN apk update && apk add curl

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy only the necessary files from the builder stage
COPY --from=builder /app/dist/apps/worker ./

RUN pnpm install --frozen-lockfile --prod

# Run with pm2
RUN npm install pm2 -g

COPY ./apps/worker/pm2.config.js ./pm2.config.js

CMD ["pm2-runtime", "start", "pm2.config.js"]
