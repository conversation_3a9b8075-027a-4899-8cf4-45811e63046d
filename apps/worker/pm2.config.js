// WORKER_ID:
// usa_nv
// usa_oh
// usa_nc
// vie_hn
// vie_sg
// vie_dn

const defaultEnvs = {
  AWS_REGION: 'us-east-1',
  OTEL_RESOURCE_ATTRIBUTES:
    'service.namespace=monitoring-dog,service.name=monitoring-worker',
  ENV: 'development',
  OTEL_METRICS_EXPORTER: 'otlp',
  CHECK_METER: 'check_count',
  METRIC_ENABLE: 'true',
  OTEL_EXPORTER_OTLP_ENDPOINT: 'http://aws-otel-collector:4317',
}

module.exports = {
  apps: [
    {
      name: 'check-worker-US-Ohio',
      script: 'main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'usa_oh',
        ...defaultEnvs,
      },
    },
    {
      name: 'check-worker-US-NorthVirginia',
      script: 'main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'usa_nv',
        ...defaultEnvs,
      },
    },
    {
      name: 'check-worker-US-NorthCarolina',
      script: 'main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'usa_nc',
        ...defaultEnvs,
      },
    },
    {
      name: 'check-worker-VN-Hanoi',
      script: 'main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'vie_hn',
        ...defaultEnvs,
      },
    },
    {
      name: 'check-worker-VN-DaNang',
      script: 'main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'vie_dn',
        ...defaultEnvs,
      },
    },
    {
      name: 'check-worker-VN-Saigon',
      script: 'main.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        WORKER_ID: 'vie_sg',
        ...defaultEnvs,
      },
    },
  ],
}
