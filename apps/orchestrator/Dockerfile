FROM node:20-alpine AS builder

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

COPY package.json pnpm-lock.yaml ./

RUN pnpm install --frozen-lockfile
COPY . .

RUN pnpm exec nx run orchestrator:build --configuration=production

FROM node:20-alpine as runner

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy only the necessary files from the builder stage
COPY --from=builder /app/dist/apps/orchestrator ./

RUN pnpm install --frozen-lockfile --prod

EXPOSE 8090

CMD ["node", "main.js"]
