{"name": "orchestrator", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/orchestrator/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/orchestrator", "main": "apps/orchestrator/src/main.ts", "tsConfig": "apps/orchestrator/tsconfig.app.json"}, "configurations": {"development": {"webpackConfig": "apps/orchestrator/webpack.hmr.config.js"}, "production": {"webpackConfig": "apps/orchestrator/webpack.config.js", "generatePackageJson": true}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "orchestrator:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "orchestrator:build:development"}, "production": {"buildTarget": "orchestrator:build:production"}}}, "sync-env": {"executor": "nx:run-commands", "outputs": [], "options": {"commands": [{"command": "node scripts/sync-env.mjs --secretName orchestrator-development --fileName apps/orchestrator/.env.development"}]}}}}