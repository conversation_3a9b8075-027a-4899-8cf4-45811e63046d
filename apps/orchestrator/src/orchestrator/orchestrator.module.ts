import { Module } from '@nestjs/common'
import { PubsubQueueModule } from '@libs/pubsub-queue/lib/pubsub-queue.module'
import { DatabaseModule } from '@libs/database/lib/database.module'
import { QUEUE } from '@libs/shared/constants/queues'
import { QueueModule } from '@libs/pubsub-queue/lib/queue.module'
import { RedisClient } from '@libs/shared/service-clients/redis.client'
import { BullClient } from '@libs/shared/service-clients/bullmq.client'
import { TimeUtils } from '@libs/shared/time/time.utils'
import { EventBridgeClient } from '@aws-sdk/client-eventbridge'
import { configService } from '@orchestrator/cores/config/config.service'
import { AwsSdkModule } from 'aws-sdk-v3-nest'

import { CheckDistributor } from './handlers/check-distributor'
import { CyclingWorker } from './workers/cycling.worker'
import { FinalizeVerifyWorker } from './workers/finalize-verify.worker'
import { WorkerDataHandler } from './data-handlers/worker.data'
import { CheckDataHandler } from './data-handlers/check.data'
import { CheckStatusHandler } from './data-handlers/check-status.data'
import { FlowManager } from './utils/flow-manager'
import { SchedulerQueue } from './utils/scheduler-queue'
import { FinalizeVerifyHandler } from './handlers/finalize-verify.handler'
import { ValidationPeriodWorker } from './workers/validation-period.worker'
import { VerifyHandler } from './handlers/verify.handler'
import { VerifyWorker } from './workers/verify.worker'
import { RecoveryResetWorker } from './workers/recovery-reset.worker'
import { RecoveryResetHandler } from './handlers/recovery-reset.handler'
import { IncidentEventDataHandler } from './data-handlers/incident-event.data'
import { IncidentDataHandler } from './data-handlers/incident.data'
import { DynamoModule } from './dynamo/dynamo.module'
import { IncidentHandler } from './handlers/incident.handler'
import { WebhookHandler } from './handlers/webhook.handler'

export const workers = [
  CyclingWorker,
  VerifyWorker,
  FinalizeVerifyWorker,
  ValidationPeriodWorker,
  RecoveryResetWorker,
]

const dataHandlers = [
  WorkerDataHandler,
  CheckDataHandler,
  CheckStatusHandler,
  IncidentDataHandler,
  IncidentEventDataHandler,
]

const handlers = [
  WebhookHandler,
  IncidentHandler,
  CheckDistributor,
  VerifyHandler,
  FinalizeVerifyHandler,
  RecoveryResetHandler,
]

const utils = [FlowManager, SchedulerQueue, TimeUtils]

@Module({
  imports: [
    AwsSdkModule.register({
      client: new EventBridgeClient({
        credentials: {
          accessKeyId: configService.getAwsConfig().accessKeyId,
          secretAccessKey: configService.getAwsConfig().secretAccessKey,
        },
        region: configService.getAwsConfig().region,
      }),
    }),
    PubsubQueueModule,
    DatabaseModule,
    DynamoModule,
    QueueModule.register({
      queues: [
        QUEUE.MAIN,
        QUEUE.SCHEDULER,
        QUEUE.VERIFY,
        QUEUE.FINALIZE_VERIFY,
      ],
    }),
  ],
  providers: [
    BullClient,
    RedisClient,
    ...workers,
    ...handlers,
    ...dataHandlers,
    ...utils,
  ],
})
export class OrchestratorModule {}
