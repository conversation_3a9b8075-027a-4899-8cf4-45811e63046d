import { Inject, Injectable } from '@nestjs/common'
import { CHANNEL } from '@libs/shared/constants/channel'
import { Database } from '@libs/database/lib/database'
import { Logger } from '@libs/shared/logger'

import { CheckDataHandler } from '../data-handlers/check.data'
import { Worker<PERSON><PERSON>Handler } from '../data-handlers/worker.data'

@Injectable()
export class CheckDistributor {
  private CONTEXT = this.constructor.name
  constructor(
    @Inject(WorkerDataHandler)
    private readonly workerNode: WorkerDataHandler,
    @Inject(CheckDataHandler)
    private readonly checkDataHandler: CheckDataHandler,
    @Inject(Database) private readonly db: Database,
  ) {}

  async distribute(checkId: string): Promise<void> {
    try {
      const checkData = await this.checkDataHandler.getCheckData(checkId)
      if (this.checkDataHandler.isCheckMaintenance(checkData)) {
        await this.checkDataHandler.unpublishCheck(checkId)
        const jobStartDate =
          await this.checkDataHandler.republishCheckAfterMaintenanceEnd(
            checkData,
          )
        console.log(
          `${checkId} in Maintenance, remove repeatableJob and start new one at ${jobStartDate}`,
        )
        return
      }
      const workerNode = await this.workerNode.getWorkerNode(checkId)
      await this.giveJobToNode(workerNode, checkId)
    } catch (error) {
      Logger.error(`Error checking ${checkId}:`, error.message)
    }
  }

  async giveJobToNode(nextWorkerNode: string, checkId: string): Promise<void> {
    Logger.log(
      `Sending ${checkId} to the queue ${CHANNEL.WORKER_MAIN(nextWorkerNode)}`,
      this.CONTEXT,
    )
    await this.workerNode.sendCheckToWorker(checkId, nextWorkerNode)
  }
}
