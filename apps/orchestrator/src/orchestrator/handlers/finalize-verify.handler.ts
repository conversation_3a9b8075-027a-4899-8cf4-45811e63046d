import { Inject, Injectable, Logger } from '@nestjs/common'
import { CheckStatus, WorkerResult } from '@libs/shared/constants/check.enum'
import { VALIDATION_PERIOD_JOB_NAME } from '@libs/shared/constants/queues'
import { Database } from '@libs/database/lib/database'
import {
  CHECK_WORKER_STATE,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'

import { CheckStatusHandler } from '../data-handlers/check-status.data'
import { SchedulerQueue } from '../utils/scheduler-queue'
import { CheckDataHandler } from '../data-handlers/check.data'
import { WorkerDataHandler } from '../data-handlers/worker.data'

import { IncidentHandler } from './incident.handler'

@Injectable()
export class FinalizeVerifyHandler {
  private CONTEXT = this.constructor.name
  constructor(
    @Inject(CheckDataHandler)
    private readonly checkDataHandler: CheckD<PERSON><PERSON>and<PERSON>,

    @Inject(CheckStatusHandler)
    private readonly checkStatusHandler: CheckStatusHandler,

    @Inject(SchedulerQueue)
    private readonly schedulerQueue: SchedulerQueue,

    @Inject(IncidentHandler)
    private readonly incidentHandler: IncidentHandler,

    @Inject(WorkerDataHandler)
    private readonly workerDataHandler: WorkerDataHandler,

    @Inject(Database)
    private readonly db: Database,
  ) {}

  async finalizeVerification(checkId: string): Promise<void> {
    const checkStatus = await this.checkStatusHandler.getCheckStatus(checkId)
    // TODO: Implement this later, comment out as it might cause problem
    // Check is NOT in active state, it could be paused or deleted
    if (
      !this.isStatusInActiveState(checkStatus) ||
      (await this.checkStatusHandler.isCheckPaused(checkId))
    ) {
      await this.stopVerification(checkId)
      return
    }
    const checkData = await this.checkDataHandler.getCheckData(checkId)

    if (await this.checkDataHandler.isCheckMaintenance(checkData)) {
      await this.stopVerification(checkId)
      return
    }

    const workerResponse =
      await this.workerDataHandler.getWorkerResponse(checkId)
    // console.log(`WORKER RESPONSE: ${format(workerResponse)}`)
    switch (checkStatus) {
      case CheckStatus.UP: {
        const expectedStatusToBeDown =
          await this.getExpectedStatusBasedOnWorkerResult({
            expectedStatus: CheckStatus.DOWN,
            checkId,
          })

        if (expectedStatusToBeDown) {
          const confirmationPeriod =
            await this.checkDataHandler.getConfirmationPeriod(checkId)

          if (!confirmationPeriod) {
            return this.handleConfirmationPeriod(checkId)
          }

          // Wait for confirmation period to end before deciding to go to incident flow
          Logger.warn(
            `Start CONFIRMATION period for ${checkId}, result after ${confirmationPeriod}s`,
          )
          const job = await this.schedulerQueue.scheduleDelayJob({
            jobName: `${VALIDATION_PERIOD_JOB_NAME.CONFIRMATION_PERIOD}:${checkId}`,
            delayTimeMs: confirmationPeriod * 1000,
            payload: { checkId },
          })
          await this.db.redisSetHash(
            CHECK_WORKER_STATE(checkId),
            REDIS_CHECK_STATUS_KEY.CONFIRMATION_JOB,
            JSON.stringify(job.id),
          )
          break
        }
        await this.stopVerification(checkId)
        break
      }

      case CheckStatus.DOWN: {
        const expectStatusToBeUp =
          await this.getExpectedStatusBasedOnWorkerResult({
            expectedStatus: CheckStatus.UP,
            checkId,
          })

        if (expectStatusToBeUp) {
          const recoverPeriod =
            await this.checkDataHandler.getRecoveryPeriod(checkId)

          if (!recoverPeriod) return this.handleRecoveryPeriod(checkId)
          Logger.warn(
            `Start RECOVERY period for ${checkId}, result after ${recoverPeriod}s`,
          )
          // Wait for recovery period to end before deciding to go to recovery flow
          const job = await this.schedulerQueue.scheduleDelayJob({
            jobName: `${VALIDATION_PERIOD_JOB_NAME.RECOVERY_PERIOD}:${checkId}`,
            delayTimeMs: recoverPeriod * 1000,
            payload: { checkId },
          })
          await this.db.redisSetHash(
            CHECK_WORKER_STATE(checkId),
            REDIS_CHECK_STATUS_KEY.RECOVERY_JOB,
            JSON.stringify(job.id),
          )
          break
        }
        await this.incidentHandler.incidentReappear(checkId, workerResponse)
        await this.stopVerification(checkId)
        break
      }

      default:
        break
    }
  }

  async handleConfirmationPeriod(checkId: string) {
    await this.stopVerification(checkId)
    const workerResponse =
      await this.workerDataHandler.getWorkerResponse(checkId)
    const expectedStatusToBeDown =
      await this.getExpectedStatusBasedOnWorkerResult({
        expectedStatus: CheckStatus.DOWN,
        checkId,
      })

    // FIRE INCIDENT
    if (expectedStatusToBeDown) {
      await this.checkStatusHandler.setCheckStatus(checkId, CheckStatus.DOWN)
      await this.incidentHandler.startIncident(checkId, workerResponse)
    }
  }

  async handleRecoveryPeriod(checkId: string) {
    await this.stopVerification(checkId)
    const workerResponse =
      await this.workerDataHandler.getWorkerResponse(checkId)
    const expectedStatusToBeUp =
      await this.getExpectedStatusBasedOnWorkerResult({
        expectedStatus: CheckStatus.UP,
        checkId,
      })
    // RESOLVE INCIDENT
    if (expectedStatusToBeUp) {
      this.checkStatusHandler.setCheckStatus(checkId, CheckStatus.UP)
      return this.incidentHandler.resolveIncident(checkId, workerResponse)
    }
  }

  private async getExpectedStatusBasedOnWorkerResult({
    expectedStatus,
    checkId,
  }: {
    expectedStatus: CheckStatus.DOWN | CheckStatus.UP
    checkId: string
  }): Promise<boolean> {
    const workerStatuses =
      await this.workerDataHandler.getWorkerStatuses(checkId)

    // console.log(workerStatuses)
    switch (expectedStatus) {
      case CheckStatus.DOWN: {
        // Down condition: N / 2 + 1 workers should be down
        // TODO: This is not optimal for check that has many workers, we should consider using a threshold of N workers
        const downWorkers = workerStatuses.filter(
          ({ status }) => status === WorkerResult.FAIL,
        )
        if (workerStatuses.length === 1) return downWorkers.length === 1
        return downWorkers.length >= workerStatuses.length / 2 + 1
      }

      case CheckStatus.UP:
        // Up condition: All workers should be up
        return workerStatuses.every(
          ({ status }) => status === WorkerResult.SUCCESS,
        )
    }
  }

  private async isStatusInActiveState(status: CheckStatus): Promise<boolean> {
    return [CheckStatus.UP, CheckStatus.DOWN].includes(status)
  }

  private async stopVerification(checkId: string): Promise<void> {
    await this.checkStatusHandler.setCheckIsVerifying(checkId, false)
  }
}
