import { Inject, Injectable } from '@nestjs/common'
import {
  IncidentEventDetailType,
  IncidentEventType,
  IncidentStatus,
} from '@libs/shared/constants/incident'
import {
  CheckWorkerResponse,
  IncidentInterface,
} from '@libs/shared/constants/shared.interface'
import { Database } from '@libs/database/lib/database'
import { CheckStatus } from '@libs/shared/constants/check.enum'
import { RedisClient } from '@libs/shared/service-clients/redis.client'
import {
  CHECK_WORKER_STATE,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'
import { InjectModel, Model } from 'nestjs-dynamoose'
import { SortOrder } from 'dynamoose/dist/General'
import { Logger } from '@libs/shared/logger'

import { Check, VerifyCheckRequest } from '../interfaces/check.interface'
import { IncidentDataHandler } from '../data-handlers/incident.data'
import { IncidentEventDataHandler } from '../data-handlers/incident-event.data'

import { WebhookHandler } from './webhook.handler'

@Injectable()
export class IncidentHandler {
  private CONTEXT = this.constructor.name

  constructor(
    @Inject(IncidentDataHandler)
    private readonly incidentDataHandler: IncidentDataHandler,
    @Inject(IncidentEventDataHandler)
    private readonly incidentEventDataHandler: IncidentEventDataHandler,
    @Inject(Database) private readonly db: Database,
    @Inject(RedisClient) private readonly redisClient: RedisClient,
    @InjectModel('IncidentSchema')
    private incidentModel: Model<IncidentInterface, string>,
    private readonly webhookHandler: WebhookHandler,
  ) {}

  async issueDetected(verifyRequest: VerifyCheckRequest): Promise<void> {
    // Find ongoing incident
    const incidentId = await this.findOnGoingIncidentIdByDynamo(
      verifyRequest.checkId,
    )
    if (!incidentId) {
      throw new Error('Failed to get incidentId in DynamoDB')
    }
  }

  async recoverDetected(verifyRequest: VerifyCheckRequest): Promise<void> {
    // Find ongoing incident
    const incidentId = await this.findOnGoingIncidentIdByDynamo(
      verifyRequest.checkId,
    )
    if (!incidentId) {
      throw new Error('Failed to get incidentId in DynamoDB')
    }
    // recoverIncident in incident.usecase

    // check ususcase update
    // await this.db
    //   .pgUpdate(checks)
    //   .set({ status: CheckStatus.DOWN })
    //   .where(eq(checks.id, verifyRequest.checkId))

    await this.incidentDataHandler.update(incidentId, {
      status: IncidentStatus.RECOVERED,
    })

    await this.incidentEventDataHandler.create({
      incidentId: incidentId,
      type: IncidentEventType.SYSTEM,
      attribute: {
        type: IncidentEventDetailType.RECOVERED,
        value: {
          location:
            (await this.findWorkerLocationById(verifyRequest.workerId)) || '',
          message: 'Monitor recovered.',
        },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    })

    await this.waitForResolve(verifyRequest.checkId, verifyRequest.workerId)
  }

  async waitForResolve(checkId: string, workerId: string): Promise<void> {
    // Find ongoing incident
    const incidentId = await this.findOnGoingIncidentIdByDynamo(checkId)
    if (!incidentId) {
      throw new Error('Failed to get incidentId in DynamoDB')
    }

    // updateStatus in check.usecase
    // await this.db
    //   .pgUpdate(checks)
    //   .set({ status: CheckStatus.VALIDATING })
    //   .where(eq(checks.id, checkId))

    // create event in incidentEvent.usecase
    await this.incidentEventDataHandler.create({
      incidentId: incidentId,
      type: IncidentEventType.SYSTEM,
      attribute: {
        type: IncidentEventDetailType.START_RECOVERY,
        value: {
          location: (await this.findWorkerLocationById(workerId)) || '',
          waitingTime: 0,
          message:
            'Waiting for x minutes before auto-resolving the incident, ' +
            'and postponing all escalations.',
        },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    })
  }

  async incidentReappear(
    checkId: string,
    workerResponse: CheckWorkerResponse[],
  ): Promise<void> {
    // Find ongoing incident
    const incidentId = await this.findOnGoingIncidentIdByDynamo(checkId)
    if (!incidentId) {
      throw new Error('Failed to get incidentId in DynamoDB')
    }

    // reappearIncident in check.usecase
    // set the status of check to DOWN
    // await this.db
    //   .pgUpdate(checks)
    //   .set({ status: CheckStatus.DOWN })
    //   .where(eq(checks.id, checkId))

    //set the status of incident to ISSUE_REAPPEARD
    const updatedIncident = await this.incidentDataHandler.update(incidentId, {
      status: IncidentStatus.ISSUE_REAPPEARED,
    })

    // create event in incidentEvent.usecase
    await this.incidentEventDataHandler.create({
      incidentId: incidentId,
      type: IncidentEventType.SYSTEM,
      attribute: {
        type: IncidentEventDetailType.REAPPEARED,
        value: {
          location:
            (await this.findWorkerLocationById(workerResponse[0].workerId)) ||
            '',
          message: 'Incident reappeared.',
        },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    })
  }

  async startIncident(
    checkId: string,
    workerResponse: CheckWorkerResponse[],
  ): Promise<void> {
    // No need to find ongoing incident because this start the incident
    // Create incident object in IncidentUsecase
    const check: Check = await this.findCheckById(checkId)
    if (!check?.id) {
      throw new Error(`Not found check ID: ${checkId}`)
    }
    // if (!check?.teamId) {
    //   throw new Error(`Not found teamId in check: ${checkId}`)
    // }
    const rightNow = new Date()
    const incident = await this.incidentDataHandler.create(
      {
        checkId: checkId,
        date: rightNow,
        workerResponse: workerResponse,
      },
      check,
    )
    if (!incident) {
      throw new Error('Failed to create incident in DynamoDB')
    }

    Logger.warn('INCIDENT CONFIRM')
    // "Start Incident" in checkUsecase => Update Check Status to Down

    // Create Incident Event in incidentEventUsecase
    if (incident) {
      await this.incidentEventDataHandler.create({
        incidentId: incident.id,
        type: IncidentEventType.SYSTEM,
        attribute: {
          type: IncidentEventDetailType.INCIDENT_STARTED,
          value: {
            location:
              // TODO: THERE ARE MULTIPLE WORKER HERE
              (await this.findWorkerLocationById(workerResponse[0].workerId)) ||
              '',
            message: 'Incident started.',
          },
        },
        createdAt: rightNow,
        updatedAt: rightNow,
      })
      // No await
      this.webhookHandler.sendWebhook(incident.id, incident.teamId, 'NEW')
      // this.webhookHandler.sendIncidentWebhook('NEW', incident, rightNow)
    }

    // TODO: Add WEBHOOK HERE
  }

  async resolveIncident(
    checkId: string,
    workerResponse: CheckWorkerResponse[],
  ): Promise<void> {
    // Find ongoing incident
    const incidentId = await this.findOnGoingIncidentIdByDynamo(checkId)
    if (!incidentId) {
      throw new Error('Failed to get incidentId in DynamoDB')
    }

    await this.redisClient.setHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.STATUS,
      JSON.stringify(CheckStatus.UP),
    )

    const rightNow = new Date()

    const incident = await this.incidentDataHandler.update(incidentId, {
      resolvedAt: rightNow,
      resolvedBy: 'SYSTEM',
      status: IncidentStatus.RESOLVED,
    })

    Logger.warn('INCIDENT RESOLVED')

    // Update resolvedIncident in incident.usecase
    if (incident) {
      await this.incidentEventDataHandler.create({
        incidentId: incident.id,
        type: IncidentEventType.SYSTEM,
        attribute: {
          type: IncidentEventDetailType.RESOLVED,
          value: {
            // TODO: MULTIPLE WORKERS HERE
            location:
              (await this.findWorkerLocationById(workerResponse[0].workerId)) ||
              '',
            message: 'Incident resolved automatically.',
          },
        },
        createdAt: rightNow,
        updatedAt: rightNow,
      })

      // no await
      this.webhookHandler.sendWebhook(incident.id, incident.teamId, 'RESOLVED')
      // this.webhookHandler.sendIncidentWebhook('RESOLVED', incident, rightNow)
    }
  }

  async findOnGoingIncidentIdByDynamo(
    checkId: string,
  ): Promise<string | null | undefined> {
    const result = await this.incidentModel
      .query('checkId')
      .eq(checkId)
      .using('checkId-createdAt-index')
      .sort(SortOrder.descending)
      .limit(1)
      .exec()

    return result[0]?.id
  }

  async findWorkerLocationById(workerId: string): Promise<string | undefined> {
    const worker = await this.db.pgQuery().workers.findFirst({
      where: (workers, { eq }) => eq(workers.id, workerId),
      columns: {
        location: true,
      },
    })
    return worker?.location
  }

  async findCheckById(checkId: string): Promise<Check> {
    const checkData = await this.db.pgQuery().checks.findFirst({
      where: (checks, { eq }) => eq(checks.id, checkId),
      columns: {
        id: true,
        type: true,
        url: true,
        port: true,
        method: true,
        locations: true,
        teamId: true,
        recoverPeriod: true,
        confirmPeriod: true,
        timeout: true,
        escalationId: true,
      },
    })
    return checkData as Check
  }
}
