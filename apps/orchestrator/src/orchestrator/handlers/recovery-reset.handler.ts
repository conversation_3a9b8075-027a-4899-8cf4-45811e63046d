import { Inject, Injectable } from '@nestjs/common'
import { Database } from '@libs/database/lib/database'
import {
  CHECK_WORKER_STATE,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'
import { VALIDATION_PERIOD_JOB_NAME } from '@libs/shared/constants/queues'
import { Logger } from '@libs/shared/logger'

import { SchedulerQueue } from '../utils/scheduler-queue'

@Injectable()
export class RecoveryResetHandler {
  constructor(
    @Inject(SchedulerQueue)
    private readonly schedulerQueue: SchedulerQueue,

    @Inject(Database)
    private readonly db: Database,
  ) {}

  async resetCheckRecovery(
    checkId: string,
    recoverPeriod: number,
  ): Promise<void> {
    Logger.warn(`Check ${checkId} is still DOWN, resetting recovery period`)
    const jobId = await this.db.redisGetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.RECOVERY_JOB,
    )
    if (!jobId) return
    this.schedulerQueue.removeDelayedJob(JSON.parse(jobId))
    const newJob = await this.schedulerQueue.scheduleDelayJob({
      jobName: `${VALIDATION_PERIOD_JOB_NAME.RECOVERY_PERIOD}:${checkId}`,
      delayTimeMs: recoverPeriod * 1000,
      payload: { checkId },
    })

    await this.db.redisSetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.RECOVERY_JOB,
      JSON.stringify(newJob.id),
    )
    return
  }
}
