import { Inject, Injectable } from '@nestjs/common'
import { QUEUE } from '@libs/shared/constants/queues'
import { Logger } from '@libs/shared/logger'

import { FlowManager } from '../utils/flow-manager'
import { WorkerDataHandler } from '../data-handlers/worker.data'
import { CheckStatusHandler } from '../data-handlers/check-status.data'
import { IncidentDataHandler } from '../data-handlers/incident.data'

import { IncidentHandler } from './incident.handler'

@Injectable()
export class VerifyHandler {
  private CONTEXT = this.constructor.name
  constructor(
    @Inject(IncidentDataHandler)
    private readonly incidentDataHandler: IncidentDataHandler,
    @Inject(IncidentHandler)
    private readonly incidentHandler: IncidentHandler,
    @Inject(FlowManager) private readonly flowManager: FlowManager,
    @Inject(WorkerDataHandler)
    private readonly workerDataHandler: WorkerDataHandler,
    @Inject(CheckStatusHandler)
    private readonly checkStatusHandler: CheckStatus<PERSON>and<PERSON>,
  ) {}

  async sendVerificationToAllNodes(checkId: string): Promise<void> {
    await this.startVerification(checkId)
    Logger.log(`Sending verification to all nodes for ${checkId}`, this.CONTEXT)

    const workerList = await this.workerDataHandler.getWorkerList(checkId)

    Logger.log(`Sending verification to worker: ${workerList}`, this.CONTEXT)

    await this.flowManager.createFlow({
      parentQueueName: QUEUE.FINALIZE_VERIFY,
      childQueueName: QUEUE.WORKER_VERIFY,
      checkId,
      workerList,
    })
  }

  async startVerification(checkId: string): Promise<void> {
    await this.checkStatusHandler.setCheckIsVerifying(checkId, true)
  }
}
