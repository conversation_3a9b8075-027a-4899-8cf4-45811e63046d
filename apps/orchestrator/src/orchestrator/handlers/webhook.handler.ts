import { Inject, Injectable } from '@nestjs/common'
import {
  WebhookEvent,
  WebhookLambdaRequestInterface,
} from '@libs/shared/constants/webhook.interface'
import { InjectAws } from 'aws-sdk-v3-nest'
import {
  EventBridgeClient,
  PutEventsCommand,
} from '@aws-sdk/client-eventbridge'
import { Database } from '@libs/database/lib/database'
import { Logger } from '@libs/shared/logger'

@Injectable()
export class WebhookHandler {
  private readonly defaultEventBusArn =
    'arn:aws:events:us-east-1:851725359289:event-bus/WebhookEventsBus'
  constructor(
    @Inject(Database) private readonly db: Database,
    @InjectAws(EventBridgeClient) private readonly client: EventBridgeClient,
  ) {}

  public async sendWebhook(
    resourceId: string,
    teamId: string,
    event: WebhookEvent,
  ): Promise<void> {
    const requestToLambda = {
      resourcesType: 'INCIDENT',
      resourceId,
      teamId,
      event,
    } as WebhookLambdaRequestInterface
    const params = {
      Entries: [
        {
          Source: 'webhook',
          DetailType: 'Webhook Request',
          Detail: JSON.stringify(requestToLambda),
          EventBusName: this.defaultEventBusArn,
        },
      ],
    }

    try {
      const command = new PutEventsCommand(params)
      await this.client.send(command)
      Logger.log(
        'Event successfully sent to EventBridge',
        JSON.stringify(requestToLambda),
      )
    } catch (error) {
      Logger.error('Error sending event to EventBridge:', error)
    }
  }
}
