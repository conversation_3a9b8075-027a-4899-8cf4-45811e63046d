import { Injectable } from '@nestjs/common'
import { JobsOptions, Queue } from 'bullmq'
import { InjectQueue } from '@nestjs/bullmq'
import { QUEUE } from '@libs/shared/constants/queues'

@Injectable()
export class SchedulerQueue {
  constructor(
    @InjectQueue(QUEUE.SCHEDULER) private readonly schedulerQueue: Queue,
  ) {}

  scheduleDelayJob({
    jobName,
    delayTimeMs,
    payload,
    options = {},
  }: {
    jobName: string
    delayTimeMs: number
    payload: any
    options?: Omit<JobsOptions, 'delay'>
  }) {
    return this.schedulerQueue.add(jobName, payload, {
      delay: delayTimeMs,
      attempts: 5,
      backoff: { type: 'exponential', delay: 1000 },
      removeOnFail: true,
      ...options,
    })
  }

  async removeDelayedJob(jobId: string): Promise<void> {
    await this.schedulerQueue.remove(jobId)
  }
}
