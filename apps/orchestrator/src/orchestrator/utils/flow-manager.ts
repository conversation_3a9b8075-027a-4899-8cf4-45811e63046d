import { Injectable } from '@nestjs/common'
import { redisConnection } from '@orchestrator/commons/redis-connection'
import { FlowJob, FlowProducer } from 'bullmq'

@Injectable()
export class FlowManager {
  private flowProducer = new FlowProducer({ connection: redisConnection })

  async createFlow({
    parentQueueName,
    childQueueName,
    checkId,
    workerList,
  }: {
    parentQueueName: string
    checkId: string
    workerList: string[]
    childQueueName?: string
  }) {
    const flow: FlowJob = {
      name: `${parentQueueName}:${checkId}`,
      queueName: parentQueueName,
      data: { checkId },
      opts: {
        ignoreDependencyOnFailure: true,
        attempts: 5,
        backoff: { type: 'exponential', delay: 1000 },
        removeOnFail: true,
      },
      children: workerList.map((workerId) => ({
        name: `${parentQueueName}:${checkId}:${workerId}`,
        data: { checkId },
        queueName: `${childQueueName}:${workerId}`,
        opts: { attempts: 3, backoff: 1000, removeOnFail: true },
      })),
    }
    return this.flowProducer.add(flow)
  }
}
