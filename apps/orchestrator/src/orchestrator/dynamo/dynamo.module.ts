import { Modu<PERSON> } from '@nestjs/common'
import { DynamooseModule } from 'nestjs-dynamoose'
import {
  INCIDENT_SCHEMA,
  INCIDENT_TABLE,
  IncidentSchema,
} from '@libs/database/lib/dynamo/incident-check.schema'
import {
  INCIDENT_EVENT_SCHEMA,
  INCIDENT_EVENT_TABLE,
  IncidentEventSchema,
} from '@libs/database/lib/dynamo/incident-event.schema'

const schemas = [
  {
    name: INCIDENT_SCHEMA,
    schema: IncidentSchema,
    tableName: INCIDENT_TABLE,
  },
  {
    name: INCIDENT_EVENT_SCHEMA,
    schema: IncidentEventSchema,
    tableName: INCIDENT_EVENT_TABLE,
  },
]

@Module({
  imports: [
    DynamooseModule.forFeature(
      schemas.map((schema) => ({
        name: schema.name,
        schema: schema.schema,
        options: {
          tableName: schema.tableName,
        },
      })),
    ),
  ],
  exports: [DynamooseModule],
})
export class DynamoModule {}
