import { CheckStatusChange } from '@libs/shared/constants/check.enum'

export interface Check {
  id: string
  url: string
  port: number
  type: string
  recoverPeriod: number
  confirmPeriod: number
  method: string
  timeout: number
  locations: string[]
  expectedStatusCodes?: number[]
  requestHeaders?: Record<string, string>
  requestBody?: string
  maintenanceDays: string[]
  maintenanceFrom: string
  maintenanceTo: string
  maintenanceTimeZone: string
  interval: number
  teamId: string
  escalationId: string
}

export interface VerifyCheckRequest {
  checkId: string
  workerId: string
  time: number
  howStatusChange: CheckStatusChange.UP_TO_DOWN | CheckStatusChange.DOWN_TO_UP
}
