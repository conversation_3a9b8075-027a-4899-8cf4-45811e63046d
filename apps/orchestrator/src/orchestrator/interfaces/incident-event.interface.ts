import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'

export interface IncidentEventInterface {
  id: string
  incidentId: string
  type: IncidentEventType
  user?: {
    id: string
    firstName: string
    lastName: string
    avatar: string
  }
  comment?: string
  attachment?: string
  attribute: {
    type: IncidentEventDetailType
    value: {
      location: string
      message: string
      waitingTime?: number | undefined
      receiver?: string | undefined // Doesn't seem like we will send these field to the Model
    }
  }
  createdAt: Date
  updatedAt: Date
  createdBy?: string | undefined
  updatedBy?: string | undefined // These two fields should not need to exist?
}
