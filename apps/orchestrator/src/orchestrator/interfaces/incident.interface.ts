import { IncidentEvent } from '@libs/shared/constants/incident'
import { CheckWorkerResponse } from '@libs/shared/constants/shared.interface'

export interface IssueDetectedMessage {
  event: IncidentEvent.ISSUE_DETECTED
  checkId: string
  time: number
  workerId: string
}

export interface IncidentStartedMessage {
  event: IncidentEvent.STARTED
  checkId: string
  time: number
  workerResponse: CheckWorkerResponse[]
}

export interface IncidentRecoveredMessage {
  event: IncidentEvent.RECOVERED
  checkId: string
  time: number
  workerId: string
}

export interface IncidentWaitResolveMessage {
  event: IncidentEvent.WAIT_FOR_AUTO_RESOLVE
  checkId: string
  time: number
}

export interface IncidentReappearMessage {
  event: IncidentEvent.REAPPEARED
  checkId: string
  time: number
  workerResponse: CheckWorkerResponse[]
}

export interface IncidentResolvedMessage {
  event: IncidentEvent.RESOLVED
  checkId: string
  time: number
  workerResponse: CheckWorkerResponse[]
}
