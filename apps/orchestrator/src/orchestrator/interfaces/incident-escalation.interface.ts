// FIXME: This is a dummy interface for Escalation
export interface IncidentEscalation {
  repeat: number[]
  repeatDelay: number
  escalationSteps: IncidentEscalationStep[]
}

export interface IncidentEscalationStep {
  index: number
  delay: number
  incidentId: string
  recoverPeriod: number
  notifications: IncidentNotification[]
}

export interface IncidentNotification {
  type: 'SMS' | 'Email' | 'Message' | 'Call'
  to: 'on-call' | 'Team lead' | 'slack' | 'discord'
  message: string
}
