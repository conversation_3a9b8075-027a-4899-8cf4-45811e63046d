import { Inject, Injectable } from '@nestjs/common'
import { Database } from '@libs/database/lib/database'
import { PubsubQueue } from '@libs/pubsub-queue/lib/pubsub-queue'
import {
  CHECK_WORKER_STATE,
  REDIS_CHECK_PREFIX,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'
import { WorkerResult } from '@libs/shared/constants/check.enum'
import { CHANNEL } from '@libs/shared/constants/channel'
import { CheckWorkerResponse } from '@libs/shared/constants/shared.interface'
import { Logger } from '@libs/shared/logger'

import { format } from 'util'

import { CheckWorkerStatus } from '../interfaces/check-worker-state.interface'

import { CheckDataHandler } from './check.data'

@Injectable()
export class WorkerDataHandler {
  private CONTEXT = this.constructor.name

  constructor(
    @Inject(Database) private readonly db: Database,
    @Inject(PubsubQueue) private readonly pubsub: PubsubQueue,

    @Inject(CheckDataHandler)
    private readonly checkDataHandler: CheckDataHandler,
  ) {}

  async getWorkerList(checkId: string): Promise<string[]> {
    const workers = await this.db.redisGetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.WORKERS,
    )

    if (!workers) {
      const fallBackData = await this.fallBackWorkerData(checkId)
      return fallBackData.workerList
    }

    return JSON.parse(workers)
  }

  async getWorkerStatuses(checkId: string): Promise<CheckWorkerStatus[]> {
    const allWorkerDataHashes = await this.db.redisGetHashAll(
      CHECK_WORKER_STATE(checkId),
    )
    if (!allWorkerDataHashes) return []
    const workerList = await this.getWorkerList(checkId)
    return Object.entries(allWorkerDataHashes)
      .filter(([worker]) => workerList.includes(worker))
      .map(([worker, status]) => ({
        id: worker,
        status: JSON.parse(status) as WorkerResult, // Assuming status is a string like 'success' or 'fail'
      }))
  }

  async getWorkerResponse(checkId: string): Promise<CheckWorkerResponse[]> {
    const allWorkerDataHashes = await this.db.redisGetHashAll(
      CHECK_WORKER_STATE(checkId),
    )
    if (!allWorkerDataHashes) return []
    const workerList: string[] = await this.getWorkerList(checkId)
    return workerList
      .filter(
        (workerId: string) => `response:${workerId}` in allWorkerDataHashes, // Check if the 'response:workerId' key exists
      )
      .map((workerId: string) => ({
        workerId: workerId,
        response: JSON.parse(allWorkerDataHashes[`response:${workerId}`]), // Parse and typecast the response
      }))
  }

  async getWorkerNode(checkId: string): Promise<string> {
    const nextWorkerNode = await this.db.redisCycleList(
      checkId,
      REDIS_CHECK_PREFIX.WORKER_NODE,
    )

    if (!nextWorkerNode) {
      const fallBackData = await this.fallBackWorkerData(checkId)
      return fallBackData.nextWorkerNode
    }

    return nextWorkerNode
  }

  async sendCheckToWorker(checkId: string, workerNode: string): Promise<void> {
    await this.pubsub.publishToChannel(CHANNEL.WORKER_MAIN(workerNode), checkId)
  }

  private async fallBackWorkerData(checkId: string): Promise<{
    nextWorkerNode: string
    workerList: string[]
  }> {
    Logger.warn(
      `No location found to check on ${format(checkId)} in queue, attempting to get data from database`,
      this.CONTEXT,
    )
    const checkData = await this.checkDataHandler.getCheckData(checkId)
    if (!checkData) throw Error('Locations data not found!')

    const workerList = checkData.locations

    const nextWorkerNode = await this.cyclingWorkerNodes(checkId, workerList)

    if (!nextWorkerNode) {
      Logger.error(
        `Still no location found to check on ${format(checkId)} in queue, skip this command`,
        this.CONTEXT,
      )
      throw Error('Could not update locations data!')
    }

    return {
      nextWorkerNode,
      workerList,
    }
  }

  private async cyclingWorkerNodes(
    checkId: string,
    workerList: string[],
  ): Promise<string | null> {
    this.db.redisDelete(checkId, REDIS_CHECK_PREFIX.WORKER_NODE)

    for (const i of workerList) {
      await this.db.redisLPush(checkId, REDIS_CHECK_PREFIX.WORKER_NODE, i)
    }

    await this.db.redisSetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.WORKERS,
      JSON.stringify(workerList),
    )

    Logger.log(`Data updated, trying again`, this.CONTEXT)

    const nextWorkerNode = await this.db.redisCycleList(
      checkId,
      REDIS_CHECK_PREFIX.WORKER_NODE,
    )

    return nextWorkerNode
  }
}
