import { Database } from '@libs/database/lib/database'
import { CheckStatus } from '@libs/shared/constants/check.enum'
import {
  CHECK_WORKER_STATE,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'
import { Inject, Injectable } from '@nestjs/common'

@Injectable()
export class CheckStatusHandler {
  private CONTEXT = this.constructor.name

  constructor(@Inject(Database) private readonly db: Database) {}

  async isCheckPaused(checkId: string): Promise<boolean> {
    const isPaused = await this.db.redisGetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.IS_PAUSED,
    )
    return JSON.parse(isPaused || 'false') === true
  }

  async setCheckIsVerifying(checkId: string, value: boolean): Promise<void> {
    await this.db.redisSetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.IS_VERIFYING,
      JSON.stringify(value),
    )
  }

  async getCheckIsVerifying(checkId: string): Promise<boolean> {
    const isVerifying = (await this.db.redisGetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.IS_VERIFYING,
    )) as CheckStatus
    if (!isVerifying) return false
    return JSON.parse(isVerifying)
  }

  async setCheckStatus(checkId: string, value: CheckStatus): Promise<void> {
    await this.db.redisSetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.STATUS,
      JSON.stringify(value),
    )
  }

  async getCheckStatus(checkId: string): Promise<CheckStatus> {
    const checkStatus = (await this.db.redisGetHash(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.STATUS,
    )) as CheckStatus
    //TODO: Add logic to check if the check is UP or DOWN from worker statuses
    if (!checkStatus) return CheckStatus.UP

    return JSON.parse(checkStatus)
  }
}
