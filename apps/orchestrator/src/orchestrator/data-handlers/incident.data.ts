import { Inject, Injectable } from '@nestjs/common'
import { omit } from 'lodash'
import { InjectModel, Item, Model } from 'nestjs-dynamoose'
import { CheckType } from '@libs/shared/constants/check.enum'
import { Database } from '@libs/database/lib/database'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { generateId } from '@orchestrator/commons/id'
import {
  EscalationPolicy,
  IncidentInterface,
} from '@libs/shared/constants/shared.interface'

import { CreateIncidentDto } from '../interfaces/create-incident.dto'
import { Check } from '../interfaces/check.interface'

@Injectable()
export class IncidentDataHandler {
  constructor(
    @InjectModel('IncidentSchema')
    private incidentModel: Model<IncidentInterface, string>,
    @Inject(Database) private readonly db: Database,
  ) {}

  async create(
    data: CreateIncidentDto,
    check: Check,
  ): Promise<Item<IncidentInterface>> {
    const incident = await this.newIncident(data, check)
    const resp = await this.incidentModel.create(incident)
    if (!resp) throw new Error('Failed to create incident')

    // TODO Why update here?
    // const updatedIncident = await this.update(
    //   resp.id,
    //   ...resp,
    // )

    return resp
  }

  async update(
    incidentId: string,
    data: Partial<IncidentInterface>,
  ): Promise<Nullable<IncidentInterface>> {
    const updatedIncident = await this.incidentModel.update(
      incidentId,
      this.serializeUpdateData(data),
    )

    return updatedIncident
  }

  private serializeUpdateData(
    data: Partial<IncidentInterface>,
  ): Partial<IncidentInterface> {
    // Avoid to update partition key, sort key, and timestamps
    return omit(data, [
      'id',
      'checkId',
      'teamId',
      'createdAt',
      'updatedAt',
      'startedAt',
    ])
  }

  private async newIncident(
    data: CreateIncidentDto,
    check: Check,
  ): Promise<IncidentInterface> {
    const incidentId = generateId()

    const escalationPolicy = await this.getEscalationPolicy(
      check.escalationId,
      incidentId,
    )

    const incident: IncidentInterface = {
      id: incidentId,
      checkId: check.id,
      checkInfo: {
        type: check.type as CheckType,
        url: check.url,
        method: check.method,
        locations: check.locations,
      },
      // TODO there are multiple workers here?
      cause: data.workerResponse?.[0]?.response?.message || undefined,
      teamId: check.teamId,
      startedAt: data.date,
      escalationPolicy: JSON.stringify(escalationPolicy),
      status: IncidentStatus.STARTED,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    return incident
  }

  private async getEscalationPolicy(
    escalationId: string,
    incidentId: string,
  ): Promise<EscalationPolicy> {
    const escalation = await this.db.pgQuery().escalations.findFirst({
      where: (escalations, { eq }) => eq(escalations.id, escalationId),
      with: {
        escalationSteps: {
          with: {
            escalationContacts: true,
            severity: true,
          },
        },
      },
    })

    if (!escalation) {
      throw new Error('Escalation policy not found')
    }

    return await this.mapEscalationPolicyToDynamo(escalation, incidentId)
  }

  private async mapEscalationPolicyToDynamo(
    escalation: any,
    incidentId: string,
  ): Promise<EscalationPolicy> {
    const { teamId } = escalation

    return {
      repeat: [...new Array(escalation.repeatCount + 1)].map((_, i) => i + 1),
      repeatDelay: escalation.repeatDelay,
      incidentId,
      escalationSteps: escalation.escalationSteps.map((step) => ({
        stepDelay: step.stepDelay,
        severityId: step.severityId,
        incidentId,
        teamId,
        contacts: (step.escalationContacts || []).map((contact) => ({
          contactType: contact.contactType,
          contactId: contact.contactId,
          alerts: step.severity.alerts,
        })),
      })),
    }
  }
}
