import { Inject, Injectable } from '@nestjs/common'
import { Logger } from '@libs/shared/logger'
import { Database } from '@libs/database/lib/database'
import { REDIS_CHECK_PREFIX } from '@libs/shared/constants/key-prefix'
import { TimeUtils } from '@libs/shared/time/time.utils'
import { BullClient } from '@libs/shared/service-clients/bullmq.client'
import { QUEUE } from '@libs/shared/constants/queues'

import { Check } from '../interfaces/check.interface'

@Injectable()
export class CheckDataHandler {
  private CONTEXT = this.constructor.name

  constructor(
    @Inject(TimeUtils) private readonly timeUtils: TimeUtils,
    @Inject(Database) private readonly db: Database,
    @Inject(BullClient)
    private readonly bullClient: BullClient,
  ) {}

  async getCheckData(checkId: string): Promise<Check> {
    const checkData = (await this.db.redisGet(
      checkId,
      REDIS_CHECK_PREFIX.CHECK_DATA,
    )) as Check
    if (!checkData) return await this.fallBackConfig(checkId)
    return checkData
  }

  async getConfirmationPeriod(checkId: string): Promise<number> {
    const checkData = await this.getCheckData(checkId)
    return checkData.confirmPeriod
  }

  async getRecoveryPeriod(checkId: string): Promise<number> {
    const checkData = await this.getCheckData(checkId)
    return checkData.recoverPeriod
  }

  private async fallBackConfig(checkId: string): Promise<Check> {
    Logger.warn(
      `No config found, attempting to get config from db`,
      this.CONTEXT,
    )

    const checkData = await this.db.pgQuery().checks.findFirst({
      where: (checks, { eq }) => eq(checks.id, checkId),
      columns: {
        id: true,
        url: true,
        port: true,
        expectedStatusCodes: true,
        locations: true,
        type: true,
        recoverPeriod: true,
        confirmPeriod: true,
        method: true,
        timeout: true,
        requestHeaders: true,
        requestBody: true,
      },
    })

    if (checkData) {
      Logger.log(`Config found`, this.CONTEXT)
      await this.db.redisSet(checkId, REDIS_CHECK_PREFIX.CHECK_DATA, checkData)
      return checkData as Check
    } else {
      Logger.error(`Check Data not found!`, this.CONTEXT)
      throw Error('Check Data not found!')
    }
  }

  public isCheckMaintenance(checkData: Check) {
    if (
      !checkData.maintenanceTimeZone ||
      !checkData.maintenanceFrom ||
      !checkData.maintenanceTo ||
      !Array.isArray(checkData.maintenanceDays)
    ) {
      return false
    }
    const isTodayMaintenanceDay = this.timeUtils.isTodayMaintenanceDay(
      checkData.maintenanceDays,
      checkData.maintenanceTimeZone,
    )
    const isMaintaining = this.timeUtils.isCurrentTimeInMaintenanceWindow(
      checkData.maintenanceTimeZone,
      checkData.maintenanceFrom,
      checkData.maintenanceTo,
    )
    return isTodayMaintenanceDay && isMaintaining
  }

  public getCheckMaintenanceEnd(checkData: Check): Date {
    const jobStartDate = this.timeUtils.getMaintenanceEnd(
      checkData.maintenanceTimeZone,
      checkData.maintenanceTo,
    )
    return jobStartDate
  }

  public async unpublishCheck(checkId: string): Promise<void> {
    this.bullClient.deleteRepeatableJob(QUEUE.MAIN, checkId)
  }

  public async republishCheckAfterMaintenanceEnd(
    checkData: Check,
  ): Promise<Date> {
    const jobStartDate = this.getCheckMaintenanceEnd(checkData)
    const cronExpression = this.timeUtils.cronExpressionParser(
      checkData.interval,
    )
    await this.bullClient.addRepeatedJobPatternWithStartDate(
      QUEUE.MAIN,
      checkData.id,
      checkData.id,
      cronExpression,
      jobStartDate,
    )
    return jobStartDate
  }
}
