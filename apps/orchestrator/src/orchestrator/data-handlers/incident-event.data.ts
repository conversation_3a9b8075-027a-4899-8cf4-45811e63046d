import { IncidentEventInterface } from '@libs/shared/constants/shared.interface'
import { Injectable } from '@nestjs/common'
import { generateId } from '@orchestrator/commons/id'
import { InjectModel, Item, Model } from 'nestjs-dynamoose'

@Injectable()
export class IncidentEventDataHandler {
  constructor(
    @InjectModel('IncidentEventSchema')
    private incidentEventModel: Model<IncidentEventInterface, string>,
  ) {}

  async create(
    data: Omit<IncidentEventInterface, 'id'>,
  ): Promise<Item<IncidentEventInterface>> {
    if (!data.incidentId) {
      throw new Error(`Missing check ID`)
    }
    const incidentEvent = await this.newIncidentEvent(data)

    const response = await this.incidentEventModel.create(incidentEvent)
    return response
  }

  private async newIncidentEvent(
    data: Omit<IncidentEventInterface, 'id'>,
  ): Promise<IncidentEventInterface> {
    const incidentEvent: IncidentEventInterface = {
      id: generateId(),
      ...data,
    }
    return incidentEvent
  }
}
