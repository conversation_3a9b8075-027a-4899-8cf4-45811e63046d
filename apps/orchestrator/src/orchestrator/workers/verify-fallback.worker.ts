// ==========================================================================================
//  VERIFY: If one node has other status than current check status, verify with other nodes
// ==========================================================================================
import { Injectable } from '@nestjs/common'
import { Job } from 'bullmq'
import { Processor } from '@nestjs/bullmq'
import { QUEUE } from '@libs/shared/constants/queues'

import { BaseWorkerProcessor } from './base.worker'

@Processor(QUEUE.VERIFY)
@Injectable()
export class VerifyFallbackWorker extends BaseWorkerProcessor {
  constructor() {
    super()
  }

  async process(job: Job): Promise<void> {
    return
  }
}
