// ===========================================================================================================
// FINALIZE VERIFY: Verify the status of the check after verifying with other nodes and proceed confirmation/recovery process
// ===========================================================================================================

import { Inject, Injectable } from '@nestjs/common'
import { Job } from 'bullmq'
import { OnWorkerEvent, Processor } from '@nestjs/bullmq'
import { QUEUE } from '@libs/shared/constants/queues'
import { Logger } from '@libs/shared/logger'

import { FinalizeVerifyHandler } from '../handlers/finalize-verify.handler'

import { BaseWorkerProcessor } from './base.worker'
@Processor(QUEUE.FINALIZE_VERIFY)
@Injectable()
export class FinalizeVerifyWorker extends BaseWorkerProcessor {
  constructor(
    @Inject(FinalizeVerifyHandler)
    private readonly finalizeVerifyHandler: FinalizeVerifyHandler,
  ) {
    super()
  }

  process(job: Job): Promise<void> {
    const { checkId } = job.data
    return this.finalizeVerifyHandler.finalizeVerification(checkId)
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job) {
    const { id, name, queueName, failedReason, attemptsMade } = job
    Logger.error(
      `Job id: ${id}, name: ${name} failed in queue ${queueName}. Failed reason: ${failedReason}. Attempts made: ${attemptsMade}`,
    )
    const maxAttempts = 5
    if (attemptsMade >= maxAttempts) {
      Logger.log(
        `Verification failed, name: ${name} has exceeded the maximum retry attempts in queue ${queueName}`,
      )
    }
  }
}
