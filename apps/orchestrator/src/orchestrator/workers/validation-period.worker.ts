// ===========================================================================================================
// VALIDATION PERIOD WORKER: Handle the validation(confirmation/recovery) period for the check
// ===========================================================================================================

import { Inject, Injectable } from '@nestjs/common'
import { Job } from 'bullmq'
import { OnWorkerEvent, Processor } from '@nestjs/bullmq'
import {
  QUEUE,
  VALIDATION_PERIOD_JOB_NAME,
} from '@libs/shared/constants/queues'
import { Logger } from '@libs/shared/logger'

import { FinalizeVerifyHandler } from '../handlers/finalize-verify.handler'
import { CheckStatusHandler } from '../data-handlers/check-status.data'

import { BaseWorkerProcessor } from './base.worker'

@Processor(QUEUE.SCHEDULER)
@Injectable()
export class ValidationPeriodWorker extends BaseWorkerProcessor {
  constructor(
    @Inject(CheckStatusHandler)
    private readonly checkStatusHandler: CheckStatusHandler,
    @Inject(FinalizeVerifyHandler)
    private readonly finalizeVerifyHandler: FinalizeVerifyHandler,
  ) {
    super()
  }

  async process(job: Job): Promise<void> {
    const { checkId } = job.data
    const [period] = job.name.split(':')
    if (await this.checkStatusHandler.isCheckPaused(checkId)) return
    switch (period) {
      case VALIDATION_PERIOD_JOB_NAME.CONFIRMATION_PERIOD:
        return this.finalizeVerifyHandler.handleConfirmationPeriod(checkId)
      // TODO: Reset recovery period if any node is down during recovery period
      case VALIDATION_PERIOD_JOB_NAME.RECOVERY_PERIOD:
        return this.finalizeVerifyHandler.handleRecoveryPeriod(checkId)
      default:
        return Promise.resolve()
    }
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job) {
    const { id, name, queueName, failedReason, attemptsMade } = job
    Logger.error(
      `Job id: ${id}, name: ${name} failed in queue ${queueName}. Failed reason: ${failedReason}. Attempts made: ${attemptsMade}`,
    )
    const maxAttempts = 5
    if (attemptsMade >= maxAttempts) {
      Logger.log(
        `Verification failed, name: ${name} has exceeded the maximum retry attempts in queue ${queueName}`,
      )
    }
  }
}
