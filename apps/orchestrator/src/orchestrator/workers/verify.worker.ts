// ==========================================================================================
//  VERIFY: If one node has other status than current check status, verify with other nodes
// ==========================================================================================
import { Inject, Injectable } from '@nestjs/common'
import { Job } from 'bullmq'
import { Processor } from '@nestjs/bullmq'
import { QUEUE } from '@libs/shared/constants/queues'
import { CheckStatusChange } from '@libs/shared/constants/check.enum'
import { Logger } from '@libs/shared/logger'

import { VerifyHandler } from '../handlers/verify.handler'
import { VerifyCheckRequest } from '../interfaces/check.interface'
import { IncidentHandler } from '../handlers/incident.handler'

import { BaseWorkerProcessor } from './base.worker'

@Processor(QUEUE.VERIFY)
@Injectable()
export class VerifyWorker extends BaseWorkerProcessor {
  constructor(
    @Inject(VerifyHandler)
    private readonly verifyHandler: VerifyHandler,
    @Inject(IncidentHandler)
    private readonly incidentHandler: IncidentHandler,
  ) {
    super()
  }

  process(job: Job): Promise<void> {
    const verifyRequest = job.data as VerifyCheckRequest
    switch (verifyRequest.howStatusChange) {
      case CheckStatusChange.DOWN_TO_UP:
        this.incidentHandler.recoverDetected(verifyRequest)
        break
      // Disable issueDetected for now because we might want to confirm Incident before start calling events
      case CheckStatusChange.UP_TO_DOWN:
        // this.incidentHandler.issueDetected(verifyRequest)
        break
      default:
        Logger.warn(
          `Verify Request contain invalid status change: ${verifyRequest}`,
        )
    }
    return this.verifyHandler.sendVerificationToAllNodes(verifyRequest.checkId)
  }
}
