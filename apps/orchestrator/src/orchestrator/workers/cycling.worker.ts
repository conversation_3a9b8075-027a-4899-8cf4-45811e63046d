// ======================================================
//            MAIN ORCHESTRATOR CYCLING NODES
// ======================================================

import { Inject, Injectable } from '@nestjs/common'
import { Job } from 'bullmq'
import { Processor } from '@nestjs/bullmq'
import { QUEUE } from '@libs/shared/constants/queues'

import { CheckDistributor } from '../handlers/check-distributor'

import { BaseWorkerProcessor } from './base.worker'

@Processor(QUEUE.MAIN)
@Injectable()
export class CyclingWorker extends BaseWorkerProcessor {
  constructor(
    @Inject(CheckDistributor)
    private readonly checkDistributor: CheckDistributor,
  ) {
    super()
  }

  process(job: Job): Promise<void> {
    return this.checkDistributor.distribute(job.data)
  }
}
