// ===========================================================================================================
// FINALIZE VERIFY: Verify the status of the check after verifying with other nodes and proceed confirmation/recovery process
// ===========================================================================================================

import { Inject, Injectable } from '@nestjs/common'
import { Job } from 'bullmq'
import { Processor } from '@nestjs/bullmq'
import { QUEUE } from '@libs/shared/constants/queues'
import { RecoveryResetRequest } from '@libs/shared/constants/shared.interface.js'

import { RecoveryResetHandler } from '../handlers/recovery-reset.handler'

import { BaseWorkerProcessor } from './base.worker'

@Processor(QUEUE.RECOVERY_RESET)
@Injectable()
export class RecoveryResetWorker extends BaseWorkerProcessor {
  constructor(
    @Inject(RecoveryResetHandler)
    private readonly recoveryResetHandler: RecoveryResetHandler,
  ) {
    super()
  }

  process(job: Job): Promise<void> {
    const jobData: RecoveryResetRequest = job.data
    const { checkId, recoverPeriod } = job.data
    return this.recoveryResetHandler.resetCheckRecovery(checkId, recoverPeriod)
  }
}
