import { Logger } from '@libs/shared/logger'
import { OnWorkerEvent, WorkerHost } from '@nestjs/bullmq'
import { Job } from 'bullmq'

export abstract class BaseWorkerProcessor extends WorkerHost {
  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    // const { id, name, queueName, finishedOn, returnvalue } = job
    // const completionTime = finishedOn ? new Date(finishedOn) : ''
    // this.logger.log(
    //   `Job id: ${id} with data: ${JSON.stringify(job.data, null, 1)}, name: ${name} completed in queue ${queueName} on ${completionTime}. Result: ${returnvalue}`,
    // )
  }

  @OnWorkerEvent('progress')
  onProgress(job: Job) {
    // const { id, name, progress } = job
    // this.logger.log(`Job id: ${id}, name: ${name} completes ${progress}%`)
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job) {
    const { id, name, queueName, failedReason, attemptsMade } = job
    Logger.error(
      `Job id: ${id}, name: ${name} failed in queue ${queueName}. Failed reason: ${failedReason}. Attempts made: ${attemptsMade}`,
    )
  }

  @OnWorkerEvent('active')
  onActive(job: Job) {
    // const { id, name, queueName, timestamp } = job
    // const startTime = timestamp ? new Date(timestamp) : ''
    // this.logger.log(
    //   `Job id: ${id}, name: ${name} starts in queue ${queueName} on ${startTime}.`,
    // )
  }
}
