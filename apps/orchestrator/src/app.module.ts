import { APP_INTERCEPTOR } from '@nestjs/core'
import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { WinstonModule } from 'nest-winston'
import { BullModule } from '@nestjs/bullmq'
import { BullBoardModule } from '@bull-board/nestjs'
import { ContextInterceptor } from '@orchestrator/commons/context/ContextInterceptor'
import { ExpressAdapter } from '@bull-board/express'
import { DatabaseModule } from '@libs/database/lib/database.module'
import { PubsubQueueModule } from '@libs/pubsub-queue/lib/pubsub-queue.module'
import { DynamooseModule } from 'nestjs-dynamoose'
import { DefaultLogger, loggerConfig } from '@libs/shared/logger'

import {
  LoggingInterceptor,
  INTERCEPTOR_LOGGER,
} from './commons/logger/winstonlogger.interceptor'
import { OrchestratorModule } from './orchestrator/orchestrator.module'
import { redisConnection } from './commons/redis-connection'
import { configService } from './cores/config/config.service'

const providers = [
  {
    provide: APP_INTERCEPTOR,
    useClass: ContextInterceptor,
  },
  DefaultLogger,
  {
    provide: INTERCEPTOR_LOGGER,
    useClass: LoggingInterceptor,
  },
]

@Module({
  imports: [
    ConfigModule.forRoot(),
    WinstonModule.forRoot(loggerConfig),
    BullModule.forRoot({
      connection: redisConnection,
    }),
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter,
    }),
    DynamooseModule.forRootAsync({
      useFactory: () => configService.getDynamooseOptions(),
    }),
    DatabaseModule,
    PubsubQueueModule,
    OrchestratorModule,
  ],
  controllers: [],
  providers: [...providers],
})
export class AppModule {}
