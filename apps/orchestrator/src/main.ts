import { NestFactory } from '@nestjs/core'
import { Logger } from '@nestjs/common'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import {
  ExpressAdapter,
  NestExpressApplication,
} from '@nestjs/platform-express'

import { AppModule } from './app.module'

declare const module: any

async function bootstrap() {
  // const app = await NestFactory.createApplicationContext(AppModule)

  // TODO: this is for testing only, production should be run with standalone mode for performance
  const app = await NestFactory.create<NestExpressApplication>(
    AppModule,
    new ExpressAdapter(),
  )

  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER))

  await app.listen(8090)

  Logger.log(
    `Orchestrator is running on:  http://localhost:${process.env.PORT || 8090}`,
  )

  if (module.hot) {
    module.hot.accept()
    module.hot.dispose(() => app.close())
  }
}
bootstrap()
