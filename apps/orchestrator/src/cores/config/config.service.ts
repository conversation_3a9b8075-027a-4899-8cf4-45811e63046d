import { DynamooseModuleOptions } from 'nestjs-dynamoose'
import * as dynamoose from 'dynamoose'

/* eslint-disable @typescript-eslint/no-var-requires */
require('dotenv').config({
  path: 'apps/orchestrator/.env',
})

class ConfigService {
  constructor(private env: { [k: string]: string | undefined }) {
    if (
      process.env.SECRETS &&
      Object.keys(JSON.parse(process.env.SECRETS)).length
    ) {
      const secrets = JSON.parse(process.env.SECRETS)
      Object.entries(secrets).forEach(([key, value]) => {
        // @ts-expect-error: we will inject key later on?
        process.env[key] = value
      })
    }
  }

  public getValue(key: string, throwOnMissing = false): string {
    const value = this.env[key]
    if (!value && throwOnMissing) {
      throw new Error(`config error - missing env.${key}`)
    }

    return value as string
  }

  public ensureValues(keys: string[]) {
    keys.forEach((k) => this.getValue(k, true))
    return this
  }

  public isProduction() {
    const mode = this.getValue('NODE_ENV', true) || 'development'
    return mode != 'development'
  }

  public getDataSource() {
    return {
      host: this.getValue('DB_HOST'),
      port: parseInt(this.getValue('DB_PORT')),
      username: this.getValue('DB_USER'),
      password: this.getValue('DB_PASSWORD'),
      database: this.getValue('DB_NAME'),
      poolSize: Number(this.getValue('POOL_SIZE') || 5),
    }
  }

  public getAwsConfig() {
    return {
      accessKeyId: this.getValue('AWS_ACCESS_KEY'),
      secretAccessKey: this.getValue('AWS_SECRET_ACCESS_KEY'),
      region: this.getValue('AWS_REGION'),
      accountId: this.getValue('AWS_ACCOUNT_ID'),
    }
  }

  public getRedisConfig() {
    if (this.isProduction()) {
      // Prod TODO: implement
      return {
        host: this.getValue('REDIS_HOST'),
        port: parseInt(this.getValue('REDIS_PORT')),
        username: this.getValue('REDIS_USERNAME'),
        password: this.getValue('REDIS_PASSWORD'),
      }
    } // Dev
    return {
      host: this.getValue('REDIS_HOST'),
      port: parseInt(this.getValue('REDIS_PORT')),
      username: this.getValue('REDIS_USERNAME'),
      password: this.getValue('REDIS_PASSWORD'),
    }
  }

  public getDynamooseOptions(): DynamooseModuleOptions {
    const isProduction = this.isProduction()
    if (this.getValue('LOCALSTACK_URL', false) && !isProduction) {
      // LocalStack/Development configuration
      console.log('Using LocalStack DynamoDB configuration')

      return {
        aws: {
          region: 'us-east-1',
          accessKeyId: 'test',
          secretAccessKey: 'test',
        },
        local:
          this.getValue('LOCALSTACK_URL', false) || 'http://localhost:4566',
        logger: true,
        table: {
          throughput: 'ON_DEMAND' as const,
        },
      }
    } else {
      // Production configuration
      console.log('Using Production DynamoDB configuration')
      return {
        ddb: new dynamoose.aws.ddb.DynamoDB({
          credentials: {
            accessKeyId: this.getAwsConfig().accessKeyId,
            secretAccessKey: this.getAwsConfig().secretAccessKey,
          },
          region: this.getAwsConfig().region,
        }),
        logger: false, // Disable verbose logging in production
        table: {
          create: false, // Do not auto-create tables in production
          throughput: 'ON_DEMAND' as const,
        },
      }
    }
  }
}

const configService = new ConfigService(process.env).ensureValues([])

export { configService }
