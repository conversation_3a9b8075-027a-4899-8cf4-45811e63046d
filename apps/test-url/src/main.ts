import express from 'express'
import bodyParser from 'body-parser'
import { createMiddleware } from '@mswjs/http-middleware'

import {
  createConfigH<PERSON><PERSON>,
  deleteConfigHandler,
  getAllConfigsHandler,
  getConfigByIdHandler,
  update<PERSON>onfigHand<PERSON>,
  testHandler,
} from './config.controllers'
import { validateData } from './middlewares'
import { inputConfig } from './utils'

import './keep-alive'

const app = express()

app.use(bodyParser.urlencoded({ extended: false }))
app.use(bodyParser.json())

app.get('/ping', (req, res) => {
  res.send('pong')
})

app.use(createMiddleware(...testHandler))

app.post('/test-url', validateData(inputConfig), createConfigHandler)
app.get('/test-url/:id', getConfigByIdHandler)
app.get('/test-url', getAllConfigsHandler)
app.patch('/test-url/:id', validateData(inputConfig), updateConfigHandler)
app.delete('/test-url/:id', deleteConfigHandler)

app.listen(3131, () => {
  console.log('Server is running on http://localhost:3131')
})
