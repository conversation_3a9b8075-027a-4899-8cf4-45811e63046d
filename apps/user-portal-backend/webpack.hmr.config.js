const { composePlugins, withNx } = require('@nx/webpack')
const nodeExternals = require('webpack-node-externals')
const webpack = require('webpack')

// Nx plugins for webpack.
module.exports = composePlugins(
  withNx({
    skipTypeChecking: false,
    assets: ['apps/user-portal-backend/src/resources'],
  }),
  (config) => {
    // Update the webpack config as needed here.
    // e.g. `config.plugins.push(new MyPlugin())`
    return {
      ...config,
      entry: ['webpack/hot/poll?100', ...config.entry.main],
      externals: [
        nodeExternals({
          allowlist: ['webpack/hot/poll?100'],
        }),
      ],
      plugins: [
        ...config.plugins,
        new webpack.HotModuleReplacementPlugin(),
        new webpack.WatchIgnorePlugin({
          paths: [/\.js$/, /\.d\.ts$/],
        }),
      ],
    }
  },
)
