const { NxAppWebpackPlugin } = require('@nx/webpack/app-plugin')

const { join } = require('path')

const isProduction = process.env.NODE_ENV === 'production'

module.exports = {
  output: {
    path: join(__dirname, '../../dist/apps/user-portal-backend'),
  },
  entry: {
    main: './src/main.ts',
    migration: './src/frameworks/database/migration.ts',
  },
  plugins: [
    new NxAppWebpackPlugin({
      target: 'node',
      compiler: 'tsc',
      main: './src/main.ts',
      tsConfig: './tsconfig.app.json',
      assets: ['./src/resources'],
      optimization: false,
      outputHashing: 'none',
      generatePackageJson: isProduction,
      sourceMap: isProduction,
    }),
  ],
}
