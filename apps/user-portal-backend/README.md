# User Portal Backend

## Overview
The User Portal Backend provides core functionality for:
- User authentication and authorization
- Incident management workflows
- Notification system integration
- Team and organization management
- Status page functionality

## Key Modules

### 1. Authentication & Authorization
- JWT-based authentication (`/frameworks/auth`)
- Role-based permissions (`/modules/user`)
- Organization/team management

### 2. Incident Management
- Incident tracking (`/modules/incident`)
- Custom incident handling (`/modules/customIncident`)
- Escalation workflows (`/modules/escalation`)

### 3. Notification System
- Multi-channel notifications (`/frameworks/notification`)
  - Slack integration
  - Email notifications
  - Voice calls (Plivo)
  - Push notifications

### 4. Status Page
- Public status pages (`/modules/status-page`)
- Incident reporting
- Status updates

### 5. Integration Settings
- Webhook configuration (`/modules/integration-setting`)
- Third-party service integrations

## Technical Stack
- **Framework**: NestJS
- **Database**: PostgreSQL + DynamoDB
- **Cache**: Redis
- **Queue**: BullMQ
- **Monitoring**: InfluxDB

## Environment Configuration
Key environment variables (see `.env`):
- Database connections
- JWT secrets
- Notification service credentials
- AWS credentials

## Development Setup
1. Install dependencies:
```bash
pnpm install
```

2. Configure environment:
```bash
cp .env.example .env
```

3. Run migrations:
```bash
pnpm run typeorm-migration-run
```

4. Start development server:
```bash
pnpm run start:dev
```

## Deployment
The service is containerized with Docker and can be deployed using:
```bash
docker build -t user-portal-backend .
docker run -p 8080:8080 user-portal-backend
```

## API Documentation
See Swagger docs at `/api` when running locally
