FROM node:20-alpine AS builder

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

COPY package.json pnpm-lock.yaml ./

RUN pnpm install --frozen-lockfile
COPY . .

RUN pnpm exec nx run user-portal-backend:build --configuration=production

FROM node:20-alpine as runner

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

COPY --from=builder /app/dist/apps/user-portal-backend ./

RUN pnpm install --frozen-lockfile --prod

COPY ./credentials ./credentials

EXPOSE 8080

CMD ["sh", "-c", "node migration.js && node main.js"]
