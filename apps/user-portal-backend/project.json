{"name": "user-portal-backend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/user-portal-backend/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/user-portal-backend", "main": "apps/user-portal-backend/src/main.ts", "tsConfig": "apps/user-portal-backend/tsconfig.app.json"}, "configurations": {"development": {"webpackConfig": "apps/user-portal-backend/webpack.hmr.config.js"}, "production": {"webpackConfig": "apps/user-portal-backend/webpack.config.js", "generatePackageJson": true}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "watch": false, "options": {"buildTarget": "user-portal-backend:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "user-portal-backend:build:development"}, "production": {"buildTarget": "user-portal-backend:build:production"}}}, "typeorm-migration-create": {"executor": "nx:run-commands", "outputs": [], "options": {"commands": [{"command": "node_modules/.bin/typeorm migration:create apps/user-portal-backend/src/migration/$npm_config_name"}]}}, "typeorm-migration-run": {"executor": "nx:run-commands", "outputs": [], "dependsOn": ["build"], "options": {"commands": [{"command": "node dist/apps/user-portal-backend/migration.js"}]}}, "sync-env": {"executor": "nx:run-commands", "outputs": [], "options": {"commands": [{"command": "node scripts/sync-env.mjs --secretName user-portal-backend-development --fileName apps/user-portal-backend/.env.development"}]}}}}