import { z } from 'zod'

import { Id } from '@backend/cores/base/id.type'

import { CheckSchema } from '../entities'

const CheckDataForWorker = CheckSchema.pick({
  url: true,
  port: true,
  type: true,
  interval: true,
  locations: true,
  // workerIds: true,
  handleRedirect: true,
  recoverPeriod: true,
  confirmPeriod: true,
  method: true,
  timeout: true,
  requestHeaders: true,
  requestBody: true,
  requiredKeyword: true,
  expectedStatusCodes: true,
  maintenanceDays: true,
  maintenanceFrom: true,
  maintenanceTo: true,
  maintenanceTimeZone: true,
})

export interface CheckDataForWorker extends z.infer<typeof CheckDataForWorker> {
  id: Id
  subscription: string
}
