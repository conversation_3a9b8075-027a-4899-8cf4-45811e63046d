export interface CheckChartResponseTimeRequest {
  groupTime: string
  timeRange: string
  location: string
  percentile: number
}

export interface CheckChartResponseTime {
  time: Date
  avgTcpConnection: number
  avgTlsHandshake: number
  avgTotalTime: number
}

export interface CheckChartUpDownRequest {
  groupTime: string
  timeRange: string
  minIncidentLength: number
  checkCreatedAt: Date
}

export interface CheckChartUpDown {
  date: Date
  downTimeInSecond: number
}
