import { Module, forwardRef } from '@nestjs/common'
import { TimeUtils } from '@libs/shared/time/time.utils'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { CacheModule } from '@backend/frameworks/cache/cache.module'
import { InfluxModule } from '@backend/frameworks/influx/influx.module'
import { DynamoModule } from '@backend/frameworks/dynamo/dynamo.module'
import { QueueModule } from '@backend/frameworks/queue/queue.module'

import { EscalationModule } from '../escalation/escalation.module'
import { IntegrationSettingModule } from '../integration-setting/integration-setting.module'
import { StatusPageModule } from '../status-page/status-page.module'
import { SubscriptionModule } from '../subscription/subscription.module'

import { CheckUseCase } from './usecases/check.usecase'
import { CheckController } from './controllers/check.controller'
import { CheckInfrastructure } from './infras/check.infrastructure'
import { CHECK_REPOSITORY } from './applications/check.repository'
import { CheckChartUseCase } from './usecases/check-chart.usecase'
import { CheckChartInfrastructure } from './infras/check-chart.infrastructure'
import { CHECK_CHART_REPOSITORY } from './applications/check-chart.repository'

@Module({
  imports: [
    DynamoModule,
    DatabaseModule,
    CacheModule,
    InfluxModule,
    QueueModule,
    EscalationModule,
    IntegrationSettingModule,
    forwardRef(() => SubscriptionModule),
    forwardRef(() => StatusPageModule),
  ],
  controllers: [CheckController],
  providers: [
    TimeUtils,
    { provide: CHECK_REPOSITORY, useClass: CheckInfrastructure },
    { provide: CHECK_CHART_REPOSITORY, useClass: CheckChartInfrastructure },
    CheckUseCase,
    CheckChartUseCase,
  ],
  exports: [CheckUseCase],
})
export class CheckModule {}
