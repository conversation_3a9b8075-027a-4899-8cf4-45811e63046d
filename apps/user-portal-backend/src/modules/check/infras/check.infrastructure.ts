/* eslint-disable unused-imports/no-unused-vars */
import { forwardRef, Inject, Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { SelectQueryBuilder } from 'typeorm'
import { Job, Queue, QueueEvents } from 'bullmq'
import { isEqual } from 'lodash'
import {
  CHECK_WORKER_STATE,
  REDIS_CHECK_PREFIX,
  REDIS_CHECK_STATUS_KEY,
} from '@libs/shared/constants/key-prefix'
import {
  CheckStatus,
  CheckType,
  HttpMethod,
  SubscriptionPlan,
} from '@libs/shared/constants/check.enum'
import { QUEUE } from '@libs/shared/constants/queues'
import { TimeUtils } from '@libs/shared/time/time.utils'
import { InjectModel, Model } from 'nestjs-dynamoose'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { SortOrder } from 'dynamoose/dist/General'
import { IncidentInterface } from '@libs/shared/constants/shared.interface'
import { ResourceType } from '@libs/database/lib/dynamo/status-report.schema'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { CheckModel as CheckModel } from '@backend/frameworks/database/models/check.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { CacheService } from '@backend/frameworks/cache/cache.service'
import { QueueService } from '@backend/frameworks/queue/queue.service'
import { Team } from '@backend/modules/user/entities'
import { configService } from '@backend/cores/config/config.service'
import { WorkerModel } from '@backend/frameworks/database/models/worker.model'
import { timeout } from '@backend/commons/utils/promises'
import { Escalation } from '@backend/modules/escalation/entities'
// import { SubscriptionUseCase } from '@backend/modules/subscription/usecases/subscription.usecase'
// import { OrganizationUseCase } from '@backend/modules/user/usecases/organization.usecase'

import { StatusPageUseCase } from '@backend/modules/status-page/usecases/status-page.usecase'

import { promises as dns } from 'dns'

import CheckRepository from '../applications/check.repository'
import { Check, CheckProps } from '../entities/check.entity'
import { CheckDataForWorker } from '../interfaces/check-worker.interface'
import { TestCheckDto } from '../applications/dto/test-check.dto'

interface CheckWithExtraData extends CheckModel {
  incident?: {
    id: string
    status: string
  }
  lastCheckAt?: Date | null
  lastPauseAt?: Date | null
  ipv4?: string | null
  ipv6?: string | null
}

@Injectable()
export class CheckInfrastructure implements CheckRepository {
  private readonly checkModel: TypeORMDriver<CheckModel>
  private readonly workerModel: TypeORMDriver<WorkerModel>
  // private readonly escalationModel: TypeORMDriver<EscalationModel>
  private redisConnection = {
    host: configService.getRedisConfig().host,
    port: configService.getRedisConfig().port,
  }
  private queueTestEvents: QueueEvents
  private queueTest: Queue
  constructor(
    @Inject(Database) private database: Database,
    @Inject(TimeUtils) private timeUtils: TimeUtils,
    // @Inject(forwardRef(() => SubscriptionUseCase))
    // private readonly subscriptionUseCase: WrapperType<SubscriptionUseCase>,
    // @Inject(forwardRef(() => OrganizationUseCase))
    // private readonly organizationUseCase: WrapperType<OrganizationUseCase>,
    @Inject(forwardRef(() => StatusPageUseCase))
    private readonly statusPageUseCase: WrapperType<StatusPageUseCase>,
    @InjectModel('IncidentSchema')
    private incidentModel: Model<IncidentInterface, string>,

    private eventEmitter: EventEmitter2,
    private readonly cache: CacheService,
    private readonly queue: QueueService<Id>,
  ) {
    this.checkModel = this.database.typeorm<CheckModel>('CheckModel')
    this.workerModel = this.database.typeorm<WorkerModel>('WorkerModel')
    // this.escalationModel =
    //   this.database.typeorm<EscalationModel>('EscalationModel')
  }

  private toDomain(check: CheckWithExtraData): Check {
    const {
      id,
      createdAt,
      updatedAt,
      team,
      teamId,
      escalationId,
      escalation,
      lastCheckAt,
      lastPauseAt,
      ipv4,
      ipv6,
      ...props
    } = check
    const domainCheck = new Check({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        status: props.status as CheckStatus,
        method: props.method as HttpMethod,
        type: props.type as CheckType,
        maintenanceDays: props.maintenanceDays as (
          | 'mon'
          | 'tue'
          | 'wed'
          | 'thu'
          | 'fri'
          | 'sat'
          | 'sun'
        )[],
        escalation: new Escalation({
          id: escalation?.id || escalationId,
        }),
        team: new Team({
          id: team?.id || teamId,
        }),
      },
    }) as any

    // Add additional fields not part of the domain model
    domainCheck.lastPauseAt = lastPauseAt
    domainCheck.lastCheckAt = lastCheckAt
    domainCheck.ipv4 = ipv4
    domainCheck.ipv6 = ipv6
    return domainCheck
  }

  async findOne(query: ITypeOrmFilter<CheckModel>): Promise<Nullable<Check>> {
    const check = await this.checkModel.findOne({
      ...query,
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })

    if (!check) return null

    return this.toDomain(check)
  }

  async findAll(): Promise<Check[]> {
    // Use find() instead
    const checks = await this.checkModel.findAll({
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })
    return checks.map(this.toDomain)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(query: ITypeOrmFilter<CheckModel>): Promise<IPaginate<Check>> {
    const checks = await this.checkModel.find({
      ...query,
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })

    const incidentMap = await this.fetchAndMapIncidents(checks.data)

    const updatedChecks = this.updateChecksWithIncidents(
      checks.data,
      incidentMap,
    )

    return {
      ...checks,
      data: updatedChecks,
    }
  }

  async findById(id: Id): Promise<Nullable<Check>> {
    const check = await this.checkModel.findById(id, {
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })
    if (!check) {
      return null
    }

    const incident = await this.findCurrentIncidentByCheckId(check?.id)

    const [lastPauseAt, lastCheckAt] = await Promise.all([
      this.cache.hget(id, REDIS_CHECK_STATUS_KEY.LAST_PAUSE_AT),
      this.cache.hget(id, REDIS_CHECK_STATUS_KEY.LAST_CHECK_AT),
    ])

    const [ipv4, ipv6] = await Promise.all([
      this.cache.hget(id, REDIS_CHECK_STATUS_KEY.IPV4),
      this.cache.hget(id, REDIS_CHECK_STATUS_KEY.IPV6),
    ])

    const checkWithExtraData = check as CheckModel & {
      lastPauseAt: Date | null
      lastCheckAt: Date | null
      ipv4: string | null
      ipv6: string | null
    }

    checkWithExtraData.lastPauseAt = lastPauseAt ? new Date(lastPauseAt) : null
    checkWithExtraData.lastCheckAt = lastCheckAt ? new Date(lastCheckAt) : null
    checkWithExtraData.ipv4 = ipv4 ? ipv4 : null
    checkWithExtraData.ipv6 = ipv6 ? ipv6 : null
    // Highest priority: PAUSED
    if (check.status == CheckStatus.PAUSED) {
      return this.handleResolvedIncident(checkWithExtraData)
    }
    // Then MAINTENANCE
    if (this.isCheckMaintenance(check)) {
      return this.handleMaintenanceStatus(checkWithExtraData)
    }
    // Then Incident
    if (!incident || incident.status === IncidentStatus.RESOLVED) {
      return this.handleResolvedIncident(checkWithExtraData)
    }
    // Else Check is UP
    return this.handleActiveIncident(checkWithExtraData, incident)
  }

  async create(check: Check): Promise<Check> {
    const checkProps = check.getProps()

    const newCheck = await this.checkModel
      .create({
        ...checkProps,
        escalation: {
          id: checkProps.escalation?.id,
        },
        team: {
          id: checkProps.team?.id,
        },
      })
      .then(this.toDomain)
    check.publishEvents(this.eventEmitter)
    return newCheck
  }

  async publish(check: Check): Promise<void> {
    const checkId = check.getId()
    // Publish QUEUE of worker nodes into worker_node:checkId
    // QUEUE is needed so that Orchestrator can atomically cycle worker nodes
    const locations = check.getLocations()
    for (const location of locations) {
      await this.cache.lpush(checkId, REDIS_CHECK_PREFIX.WORKER_NODE, location)
    }
    // Publish list of worker nodes into worker_node:checkId for other tasks
    // Is it a dupe? CheckData also contain locations
    await this.cache.set(checkId, REDIS_CHECK_PREFIX.WORKER_LIST, locations)

    // Publish relevant data for worker node into check_data:checkId
    const checkProps = check.getProps()
    await this.cache.set<CheckDataForWorker>(
      checkId,
      REDIS_CHECK_PREFIX.CHECK_DATA,
      {
        id: checkId,
        url: checkProps.url,
        port: checkProps.port,
        type: checkProps.type,
        locations: checkProps.locations,
        interval: checkProps.interval,
        handleRedirect: checkProps.handleRedirect,
        recoverPeriod: checkProps.recoverPeriod,
        confirmPeriod: checkProps.confirmPeriod,
        method: checkProps.method,
        timeout: checkProps.timeout,
        requestHeaders: checkProps.requestHeaders,
        requestBody: checkProps.requestBody,
        requiredKeyword: checkProps.requiredKeyword,
        expectedStatusCodes: checkProps.expectedStatusCodes,
        maintenanceDays: checkProps.maintenanceDays as (
          | 'mon'
          | 'tue'
          | 'wed'
          | 'thu'
          | 'fri'
          | 'sat'
          | 'sun'
        )[],
        maintenanceFrom: checkProps.maintenanceFrom,
        maintenanceTo: checkProps.maintenanceTo,
        maintenanceTimeZone: checkProps.maintenanceTimeZone,

        // TODO: remove later when subscription plan is implemented
        // Testing with free subscription plan
        subscription: SubscriptionPlan.FREE,
      },
    )
    await this.resolveAndSaveIPs(checkProps.url, checkId)

    // Make repeatable job for Orchestrator
    if (check.status !== CheckStatus.PAUSED) {
      const cronExpression = this.timeUtils.cronExpressionParser(
        check.getInterval(),
      )
      await this.queue.addRepeatedJobPattern(
        checkId,
        check.getId(),
        cronExpression,
      )
      await this.checkModel.updateById(checkId, {
        status: CheckStatus.UP,
      })
    }

    const [ipv4, ipv6] = await Promise.all([
      this.cache.hget(checkId, REDIS_CHECK_STATUS_KEY.IPV4),
      this.cache.hget(checkId, REDIS_CHECK_STATUS_KEY.IPV6),
    ])

    return
  }

  private async resolveAndSaveIPs(url: string, checkId: string): Promise<void> {
    try {
      const hostname = new URL(url).hostname

      const ipResults = {
        ipv4: [] as string[],
        ipv6: [] as string[],
      }

      try {
        ipResults.ipv4 = await dns.resolve4(hostname)
      } catch (error) {
        console.warn(`Failed to resolve IPv4 for ${hostname}: ${error.message}`)
      }
      try {
        ipResults.ipv6 = await dns.resolve6(hostname)
      } catch (error) {
        console.warn(`Failed to resolve IPv6 for ${hostname}: ${error.message}`)
      }

      await Promise.all([
        this.cache.hset(
          checkId,
          REDIS_CHECK_STATUS_KEY.IPV4,
          JSON.stringify(ipResults.ipv4),
        ),
        this.cache.hset(
          checkId,
          REDIS_CHECK_STATUS_KEY.IPV6,
          JSON.stringify(ipResults.ipv6),
        ),
      ])
    } catch (error) {
      console.error(`Failed to resolve IPs for URL ${url}: ${error.message}`)
    }
  }

  async unpublish(checkId: Id): Promise<void> {
    await this.cache.delete(checkId, REDIS_CHECK_PREFIX.CHECK_DATA)
    await this.cache.delete(checkId, REDIS_CHECK_PREFIX.WORKER_NODE)
    await this.cache.delete(checkId, REDIS_CHECK_PREFIX.WORKER_LIST)
    await this.queue.deleteRepeatableJob(checkId)
    const jobId = await this.cache.hget(
      CHECK_WORKER_STATE(checkId),
      REDIS_CHECK_STATUS_KEY.RECOVERY_JOB,
    )
    if (!jobId) return
    const schedulerQueue = new Queue(QUEUE.SCHEDULER, {
      connection: this.redisConnection,
    })
    await schedulerQueue.remove(JSON.parse(jobId))
    await schedulerQueue.close()
  }

  async createAndPublish(check: Check): Promise<Check> {
    const newCheck = await this.create(check)

    // Offload the publish to the next tick
    setTimeout(async () => {
      await this.publish(newCheck)

      await this.cache.hset(
        CHECK_WORKER_STATE(check.id),
        REDIS_CHECK_STATUS_KEY.IS_VERIFYING,
        'false', // This is stringify bool false
      )
      await this.cache.hset(
        CHECK_WORKER_STATE(check.id),
        REDIS_CHECK_STATUS_KEY.STATUS,
        JSON.stringify(CheckStatus.UP),
      )
    })

    return newCheck
  }

  // This function help to prevent unnecessary republish
  async republishCheck(check: Check): Promise<void> {
    const checkProps = check.getProps()
    const checkDataForWorker = await this.cache.get<CheckDataForWorker>(
      check.id,
      REDIS_CHECK_PREFIX.CHECK_DATA,
    )

    if (!checkDataForWorker) {
      console.log('No check Data found, we will republish check')
      await this.publish(check)
      return
    }

    const checkLocations = check.getLocations()
    const workerList = await this.cache.get<string[]>(
      check.getId(),
      REDIS_CHECK_PREFIX.WORKER_LIST,
    )

    // Check if data changed and need to republish
    const keysToCompare = [
      'url',
      'port',
      'type',
      'locations',
      'interval',
      'handleRedirect',
      'recoverPeriod',
      'confirmPeriod',
      'method',
      'timeout',
      'requestHeaders',
      'requestBody',
      'requiredKeyword',
      'expectedStatusCodes',
      'maintenanceDays',
      'maintenanceFrom',
      'maintenanceTo',
      'maintenanceTimeZone',
    ]

    if (
      keysToCompare.some(
        (key) =>
          JSON.stringify(checkDataForWorker[key]) !==
          JSON.stringify(checkProps[key]),
      ) ||
      !isEqual(checkLocations, workerList)
    ) {
      console.log('>>>> Need to republish check to workers')
      await this.unpublish(check.getId())
      await this.publish(check)
    }
  }

  async updateById(
    id: Id,
    check: Partial<CheckProps>,
  ): Promise<Nullable<Check>> {
    const updatedCheck = await this.checkModel.updateById(id, {
      ...check,
      escalation: {
        id: check.escalation?.id,
      },
      team: {
        id: check.team?.id,
      },
    })
    if (!updatedCheck) return null
    const entityCheck = this.toDomain(updatedCheck)
    await this.republishCheck(entityCheck)
    return entityCheck
  }

  async updateStatus(id: Id, status: CheckStatus): Promise<Nullable<Check>> {
    const updatedCheck = await this.checkModel.updateById(id, {
      status,
    })

    if (!updatedCheck) return null

    return this.toDomain(updatedCheck)
  }

  async delete(id: Id): Promise<boolean> {
    const check = await this.checkModel.findById(id)
    if (!check) return false
    await this.deleteCheckFromStatusPage(check)
    await this.checkModel.softDelete({
      id,
    })
    await this.unpublish(id)
    await this.cache.delete(id, REDIS_CHECK_PREFIX.CHECK_WORKER_STATE)
    return true
  }

  async deleteCheckFromStatusPage(check: CheckModel): Promise<void> {
    const statusPages = await this.statusPageUseCase.findAll(check.teamId, {
      limit: 100,
      page: 1,
    })
    for (const statusPage of statusPages.data) {
      const props = statusPage
      let modified = false

      const updatedSections = props.statusSections?.map((section) => {
        const originalCount = section.resources.length
        const updatedResources = section.resources.filter(
          (resource) =>
            !(
              resource.resourceType === ResourceType.CHECK &&
              resource.resourceId === check.id
            ),
        )

        if (updatedResources.length !== originalCount) {
          modified = true
        }

        return {
          ...section,
          resources: updatedResources,
        }
      })
      if (modified) {
        await this.statusPageUseCase.update(statusPage.id, {
          statusSections: updatedSections,
        })
      }
      console.log('update', modified ? updatedSections : '')
    }

    return
  }

  async pauseCheck(id: Id): Promise<Check | void> {
    // await this.unpublish(id)
    await this.queue.deleteRepeatableJob(id)
    const returnCheck = await this.checkModel.updateById(id, {
      status: CheckStatus.PAUSED,
    })
    await this.cache.hset(
      CHECK_WORKER_STATE(id),
      REDIS_CHECK_STATUS_KEY.IS_PAUSED,
      'true',
    )
    await this.cache.hset(
      id,
      REDIS_CHECK_STATUS_KEY.LAST_PAUSE_AT,
      new Date().toISOString(),
    )
    if (!returnCheck) return
    const entityCheck = this.toDomain(returnCheck)
    return entityCheck
  }

  async resumeCheck(id: Id): Promise<Check | void> {
    // TODO : Check here? Does this need relation query?
    const checkData = await this.checkModel.findById(id, {
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })
    if (!checkData) return
    const entityCheck = this.toDomain(checkData)
    await this.unpublish(id)
    await this.publish(entityCheck)
    const cronExpression = this.timeUtils.cronExpressionParser(
      checkData.interval,
    )
    await this.queue.addRepeatedJobPattern(id, id, cronExpression)
    await this.cache.hset(
      CHECK_WORKER_STATE(id),
      REDIS_CHECK_STATUS_KEY.IS_VERIFYING,
      'false',
    )
    await this.cache.hset(
      CHECK_WORKER_STATE(id),
      REDIS_CHECK_STATUS_KEY.IS_PAUSED,
      'false',
    )
    const jobId = await this.cache.hget(
      CHECK_WORKER_STATE(id),
      REDIS_CHECK_STATUS_KEY.RECOVERY_JOB,
    )
    if (jobId) {
      const schedulerQueue = new Queue(QUEUE.SCHEDULER, {
        connection: this.redisConnection,
      })
      await schedulerQueue.remove(JSON.parse(jobId))
      await schedulerQueue.close()
    }
    const returnCheck = await this.checkModel.updateById(id, {
      status: CheckStatus.UP,
    })
    if (!returnCheck) return
    return entityCheck
  }

  async resolveIncident(id: Id): Promise<void> {
    // Reset state, and set status as UP
    await this.resumeCheck(id)

    await this.cache.hset(
      CHECK_WORKER_STATE(id),
      REDIS_CHECK_STATUS_KEY.STATUS,
      JSON.stringify(CheckStatus.UP),
    )

    await this.checkModel.updateById(id, {
      status: CheckStatus.UP,
    })

    // TODO: tell Incident Manager to update status here
  }

  async findAllWorkers(): Promise<WorkerModel[]> {
    const workerNodeQuery = await this.workerModel.findAll()
    return workerNodeQuery
  }

  async testCheckWhenConfig(testCheckRequest: TestCheckDto): Promise<string> {
    const worker = testCheckRequest?.locations?.[0]
    if (!worker) throw new Error('Worker not found')

    this.queueTest = new Queue(QUEUE.TESTCHECK(worker), {
      connection: this.redisConnection,
    })
    await this.queueTest.add(
      `${Date.now()}`,
      JSON.stringify(testCheckRequest),
      {
        attempts: 1,
      },
    )
    const resultPromise = new Promise<string>((resolve, reject) => {
      this.queueTestEvents = new QueueEvents(QUEUE.TESTCHECK(worker), {
        connection: this.redisConnection,
      })
      this.queueTestEvents.on('completed', async ({ jobId }) => {
        const job = await Job.fromId(this.queueTest, jobId)
        // console.log(job)
        if (job) {
          const result = job.returnvalue
          resolve(result)
        } else {
          reject(new Error('Job not found'))
        }
      })
      this.queueTestEvents.on('failed', async ({ failedReason }) => {
        reject(new Error(failedReason))
      })
    })

    const testResult = await Promise.race([
      resultPromise,
      timeout(5000).then(() => 'timeout'),
    ])

    await this.queueTest.close()
    await this.queueTestEvents.close()

    return testResult
  }

  async findWorkerById(id: string): Promise<Nullable<WorkerModel>> {
    return this.workerModel.findById(id)
  }

  private async findCurrentIncidentByCheckId(
    checkId: string,
  ): Promise<IncidentInterface | null | undefined> {
    const result = await this.incidentModel
      .query('checkId')
      .eq(checkId)
      .using('checkId-createdAt-index')
      .sort(SortOrder.descending)
      .limit(1)
      .exec()

    return result[0]
  }

  public isCheckMaintenance(checkData: CheckModel) {
    if (
      !checkData.maintenanceTimeZone ||
      !checkData.maintenanceFrom ||
      !checkData.maintenanceTo ||
      !Array.isArray(checkData.maintenanceDays)
    ) {
      return false
    }
    const isTodayMaintenanceDay = this.timeUtils.isTodayMaintenanceDay(
      checkData.maintenanceDays,
      checkData.maintenanceTimeZone,
    )
    const isMaintaining = this.timeUtils.isCurrentTimeInMaintenanceWindow(
      checkData.maintenanceTimeZone,
      checkData.maintenanceFrom,
      checkData.maintenanceTo,
    )
    return isTodayMaintenanceDay && isMaintaining
  }

  private handleResolvedIncident(check: CheckWithExtraData): Check {
    const incidentObject = null
    return this.toDomain({
      ...check,
      incident: incidentObject,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any)
  }

  private handleActiveIncident(
    check: CheckWithExtraData,
    incident: IncidentInterface,
  ): Check {
    const result = this.setStatusAndIncidentToCheck(
      incident,
      check.status,
      null,
    )
    return this.toDomain({
      ...check,
      status: result.status,
      incident: result.incidentObject,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any)
  }

  private handleMaintenanceStatus(check: CheckWithExtraData): Check {
    const status = CheckStatus.MAINTENANCE
    const incidentObject = null
    return this.toDomain({
      ...check,
      status,
      incident: incidentObject,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any)
  }

  private setStatusAndIncidentToCheck(
    incident: IncidentInterface,
    status: string,
    incidentObject: object | null,
  ): { status: string; incidentObject: object | null } {
    switch (incident.status) {
      case IncidentStatus.ACKNOWLEDGED: {
        status = CheckStatus.DOWN
        incidentObject = {
          status: IncidentStatus.ACKNOWLEDGED,
          id: incident.id,
        }
        break
      }
      case IncidentStatus.STARTED: {
        status = CheckStatus.DOWN
        incidentObject = {
          status: IncidentStatus.STARTED,
          id: incident.id,
        }
        break
      }
      case IncidentStatus.ISSUE_REAPPEARED: {
        status = CheckStatus.DOWN
        incidentObject = {
          status: IncidentStatus.ISSUE_REAPPEARED,
          id: incident.id,
        }
        break
      }
      case IncidentStatus.RECOVERED: {
        status = CheckStatus.VALIDATING
        incidentObject = {
          status: IncidentStatus.RECOVERED,
          id: incident.id,
        }
        break
      }
    }
    return { status, incidentObject }
  }

  private async fetchAndMapIncidents(
    checks: CheckModel[],
  ): Promise<Map<string, IncidentInterface | null>> {
    const checkIds = checks.map((check) => check.id)

    const incidents = await Promise.all(
      checkIds.map(async (id) => {
        return await this.findCurrentIncidentByCheckId(id)
      }),
    )

    return this.mapIncidentsToChecks(checkIds, incidents)
  }

  private mapIncidentsToChecks(
    checkIds: string[],
    incidents: (IncidentInterface | null | undefined)[],
  ): Map<string, IncidentInterface | null> {
    const incidentMap = new Map<string, IncidentInterface | null>()
    checkIds.forEach((id, index) => {
      incidentMap.set(id, incidents[index] ?? null)
    })
    return incidentMap
  }

  private updateChecksWithIncidents(
    checks: CheckModel[],
    incidentMap: Map<string, IncidentInterface | null>,
  ): Check[] {
    return checks.map((check) => {
      const incident = incidentMap.get(check.id)

      if (check.status == CheckStatus.PAUSED) {
        return this.handleResolvedIncident(check)
      }
      if (this.isCheckMaintenance(check)) {
        return this.handleMaintenanceStatus(check)
      }
      if (!incident || incident.status === IncidentStatus.RESOLVED) {
        return this.handleResolvedIncident(check)
      }
      return this.handleActiveIncident(check, incident)
    })
  }

  async countChecksByOrganizationId(organizationId: Id): Promise<number> {
    const queryBuilder =
      this.checkModel.getQueryBuilder() as SelectQueryBuilder<CheckModel>

    const count = await queryBuilder
      .leftJoin('teams', 'teams', 'teams.id = checks.team_id')
      .where('teams.organization_id = :organizationId', { organizationId })
      .andWhere('checks.deleted_at IS NULL')
      .getCount()

    return count || 0
  }
}
