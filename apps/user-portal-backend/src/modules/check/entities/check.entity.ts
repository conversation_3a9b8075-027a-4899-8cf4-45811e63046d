import { z } from 'zod'
import {
  CheckStatus,
  CheckType,
  HttpMethod,
  MaintenanceDays,
} from '@libs/shared/constants/check.enum'

import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { Team } from '@backend/modules/user/entities'
import { Escalation } from '@backend/modules/escalation/entities'

import { CheckCreatedDomainEvent } from './events/check-created.event'

export const CheckSchema = z.object({
  title: z.string().optional(),
  type: z.nativeEnum(CheckType).default(CheckType.STATUS),
  tags: z.array(z.string()).optional(),
  url: z
    .string()
    .regex(
      /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,})(\/.*)?$|^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^\[([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\]$/,
      {
        message: 'Invalid URL or domain/IP address',
      },
    ),
  port: z.number().optional(),
  locations: z.array(z.string()),

  status: z.nativeEnum(CheckStatus).default(CheckStatus.PENDING),

  interval: z
    .number()
    .min(15)
    .max(30 * 60)
    .default(30),

  recoverPeriod: z
    .number()
    .min(0)
    .max(60 * 60)
    .default(3 * 60),

  confirmPeriod: z
    .number()
    .min(0)
    .max(5 * 60)
    .default(0),

  method: z.nativeEnum(HttpMethod).default(HttpMethod.GET),
  handleRedirect: z.boolean().default(true),
  requestHeaders: z.record(z.string()).optional(),
  requestBody: z.string().optional(),

  timeout: z.number().min(1).max(60).default(30),
  requiredKeyword: z.string().optional(),
  expectedStatusCodes: z.array(z.number()).optional(),

  lastCheckedAt: z.date().optional(),
  pausedAt: z.date().optional(),

  // Maintenance window
  maintenanceDays: z.enum(MaintenanceDays).array().optional(),
  maintenanceFrom: z.string().optional(),
  maintenanceTo: z.string().optional(),
  maintenanceTimeZone: z.string().optional(),

  // Basic Auth
  authUsername: z.string().optional(),
  authPassword: z.string().optional(),

  // Audit
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),

  incident: z
    .object({
      id: z.string(),
      status: z.string(),
    })
    .optional(),
})

export interface CheckProps extends z.infer<typeof CheckSchema> {
  team?: Team
  escalation?: Escalation
}

export const CheckResponse = CheckSchema.partial().extend({
  id: z.string().optional(),
  updatedAt: z.date().optional(),
  createdAt: z.date().optional(),
  team: z.object({
    id: z.string().optional(),
  }),
  escalation: z.string().optional(),
})

export type CheckResponse = z.infer<typeof CheckResponse>

export class Check extends AggregateRoot<CheckProps, CheckResponse> {
  static create(props: Omit<CheckProps, 'status'>) {
    const checkProps = {
      ...props,
      status: CheckStatus.PENDING,
    }

    const check = new Check({
      id: generateId(),
      props: checkProps,
    })

    check.addEvent(
      new CheckCreatedDomainEvent({
        aggregateId: check.id,
        props: checkProps,
      }),
    )

    return check
  }

  static update({ id, props }: { id: Id; props: CheckProps }) {
    const check = new Check({
      id: id,
      props,
    })

    return check
  }

  public toResponse(): CheckResponse {
    const props = this.getProps()

    return {
      id: props.id,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      title: props.title,
      tags: props.tags,
      url: props.url,
      port: props.port,
      status: props.status,
      locations: props.locations,
      recoverPeriod: props.recoverPeriod,
      confirmPeriod: props.confirmPeriod,
      interval: props.interval,
      type: props.type,
      handleRedirect: props.handleRedirect,
      method: props.method,
      requestHeaders: props.requestHeaders,
      incident: props.incident,
      timeout: props.timeout,
      team: {
        id: props.team?.id,
      },
      escalation: props.escalation?.id,
      maintenanceDays: props.maintenanceDays,
      maintenanceFrom: props.maintenanceFrom,
      maintenanceTo: props.maintenanceTo,
      maintenanceTimeZone: props.maintenanceTimeZone,
    }
  }

  public get status(): CheckStatus {
    return this.getProps().status
  }

  public getInterval(): number {
    return this.getProps().interval
  }

  public getId(): Id {
    return this.getProps().id
  }

  public getTeamId(): Id {
    return this.getProps().team!.id
  }

  public getLocations(): string[] {
    return this.getProps().locations
  }

  public getCreatedAt(): Date {
    return this.getProps().createdAt
  }

  public getUpdatedAt(): Date {
    return this.getProps().updatedAt
  }
}
