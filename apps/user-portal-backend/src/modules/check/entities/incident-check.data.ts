import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'
import { model } from 'dynamoose'
import {
  IncidentItem,
  IncidentSchema,
} from '@libs/database/lib/dynamo/incident-check.schema'

const IncidentModel = model<IncidentItem>(
  DYNAMO_DB_TABLE.INCIDENTS,
  IncidentSchema,
  {
    create: false,
    waitForActive: false,
  },
)

export default class IncidentData {
  static async getIncidentByCheckIdInTimeRange(
    checkId: string,
    startTime: Date,
  ): Promise<IncidentItem[]> {
    const startTimeISO = startTime.toISOString()
    console.log(startTime, startTimeISO)
    const results = await IncidentModel.query('checkId')
      .eq(checkId)
      .using('checkId-createdAt-index')
      .where('updatedAt')
      .gt(startTimeISO)
      .sort('descending')
      .exec()
    const incidentItems = results.map((item: any) =>
      item.toJSON(),
    ) as IncidentItem[]
    return incidentItems
  }
}
