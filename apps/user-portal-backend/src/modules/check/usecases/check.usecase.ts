import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'
import { CheckStatus } from '@libs/shared/constants/check.enum'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { Team } from '@backend/modules/user/entities'
import { WorkerModel } from '@backend/frameworks/database/models/worker.model'
import { Escalation } from '@backend/modules/escalation/entities'
import { EscalationUseCase } from '@backend/modules/escalation/usecases/escalation.usecase'
import { WebhookUseCase } from '@backend/modules/integration-setting/usecases/webhook.usecase'
import { ResourceBillingService } from '@backend/modules/subscription/services/resource-billing.service'

import { CheckResponseDto } from '../applications/dto/check.response.dto'
import CheckRepository, {
  CHECK_REPOSITORY,
} from '../applications/check.repository'
import { Check } from '../entities/check.entity'
import {
  CreateCheckDto,
  UpdateCheckDto,
} from '../applications/dto/check.request.dto'
import { TestCheckDto } from '../applications/dto/test-check.dto'

@Injectable()
export class CheckUseCase {
  constructor(
    @Inject(CHECK_REPOSITORY) private readonly repo: CheckRepository,
    @Inject(EscalationUseCase)
    private readonly escalationUseCase: EscalationUseCase,
    @Inject(WebhookUseCase)
    private readonly webhookUseCase: WebhookUseCase,
    private readonly resourceBillingService: ResourceBillingService,
  ) {}

  async create(
    createCheckDto: CreateCheckDto,
    user: JwtPayload,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<CheckResponseDto | string> {
    try {
      let escalationId =
        typeof createCheckDto.escalation === 'string'
          ? createCheckDto.escalation
          : null

      if (
        typeof createCheckDto.escalation !== 'string' &&
        createCheckDto.escalation
      ) {
        const newEscalation = await this.escalationUseCase.create(
          createCheckDto.escalation,
          user,
          orgTeamParams,
        )

        if (!newEscalation?.id)
          throw new HttpException(
            'Error creating escalation',
            HttpStatus.INTERNAL_SERVER_ERROR,
          )

        escalationId = newEscalation.id
      }

      const newCheck = await this.repo.createAndPublish(
        Check.create({
          ...createCheckDto,
          createdBy: user.userId,
          updatedBy: user.userId,
          team: new Team({ id: orgTeamParams.teamId }),
          escalation: new Escalation({ id: escalationId as string }),
        }),
      )

      this.webhookUseCase.sendWebhook(
        newCheck.getId(),
        orgTeamParams.teamId,
        'CREATED',
      )

      // Track check creation for billing purposes
      await this.resourceBillingService.trackCheckChange(
        orgTeamParams.organizationId,
        'CREATE',
        newCheck.id,
        { checkType: createCheckDto.type, teamId: orgTeamParams.teamId },
      )

      return newCheck.toResponse()
    } catch (error) {
      console.error(error)
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  async find(
    orgTeamParams: OrgTeamHeadersDto,
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<CheckResponseDto>> {
    try {
      const checks = await this.repo.find({
        filter: {
          team: {
            id: orgTeamParams.teamId,
          },
        },
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })
      return {
        data: checks.data.map((check) => check.toResponse()),
        total: checks.total,
        totalPage: checks.totalPage,
        limit: checks.limit,
        page: checks.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<CheckResponseDto>> {
    const entity = await this.repo.findById(id)

    if (!entity) return null
    const EntiyWithExtraData = {
      ...entity.toResponse(),
      lastPauseAt: (entity as any).lastPauseAt,
      lastCheckAt: (entity as any).lastCheckAt,
      ipv4: (entity as any).ipv4,
      ipv6: (entity as any).ipv6,
    }
    return EntiyWithExtraData
  }

  async update(id: Id, updateCheckDto: UpdateCheckDto) {
    try {
      const check = await this.repo.findById(id)

      if (!check) return null

      const returnedCheck = await this.repo.updateById(id, {
        ...check.getProps(),
        ...updateCheckDto,
        escalation: new Escalation({ id: updateCheckDto.escalation }),
      })

      // if (check.status === CheckStatus.PAUSED) {
      //   await this.repo.resumeCheck(id)
      // }

      if (!returnedCheck) return null

      this.webhookUseCase.sendWebhook(
        returnedCheck.getId(),
        returnedCheck.getTeamId(),
        'UPDATED',
      )
      return returnedCheck.toResponse()
    } catch (error) {
      console.error(error)
      return (error as Error).message
    }
  }

  async remove(id: Id) {
    const check = await this.repo.findById(id, {
      relations: {
        team: {
          organization: true,
        },
      },
    })
    if (!check) return null

    const result = await this.repo.delete(id)

    this.webhookUseCase.sendWebhook(
      check.getProps().id,
      check.getProps().team!.id,
      'DELETED',
    )

    // Track check deletion for billing purposes
    const team = check.getProps().team
    if (team?.organization) {
      await this.resourceBillingService.trackCheckChange(
        team.organization.id,
        'DELETE',
        id,
        { teamId: team.id },
      )
    }

    return result
  }

  async pauseCheck(id: Id) {
    // const updatedCheck = await this.updateStatus(id, CheckStatus.PAUSED)
    const updatedCheck = await this.repo.pauseCheck(id)
    if (!updatedCheck) return
    this.webhookUseCase.sendWebhook(
      updatedCheck.getProps().id,
      updatedCheck.getProps().team!.id,
      'PAUSED',
    )
    return updatedCheck
  }

  async resumeCheck(id: Id) {
    // const updatedCheck = await this.updateStatus(id, CheckStatus.UP)
    const updatedCheck = await this.repo.resumeCheck(id)
    if (!updatedCheck) return
    this.webhookUseCase.sendWebhook(
      updatedCheck.getProps().id,
      updatedCheck.getProps().team!.id,
      'RESUMED',
    )
    return updatedCheck
  }

  async findAllWorkers(): Promise<WorkerModel[]> {
    try {
      const allWorkers = await this.repo.findAllWorkers()
      return allWorkers
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findWorkerById(
    workerId: string | undefined,
  ): Promise<Nullable<WorkerModel>> {
    if (!workerId) return null

    try {
      return await this.repo.findWorkerById(workerId)
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async testWhenConfig(testCheckRequest: TestCheckDto): Promise<string | null> {
    const checkResult = await this.repo.testCheckWhenConfig(testCheckRequest)
    if (!checkResult) return null
    return checkResult
  }

  async updateStatus(id: Id, status: CheckStatus) {
    const updatedCheck = await this.repo.updateStatus(id, status)
    return updatedCheck
  }

  // async startIncident(checkId: Id, incidentId: Id) {
  //   const check = await this.repo.findById(checkId)

  //   if (!check) return null

  //   const updatedCheck = await this.repo.updateById(checkId, {
  //     ...check.getProps(),
  //     status: CheckStatus.DOWN,
  //   })

  //   return updatedCheck
  // }

  async resolveIncident(id: Id) {
    const check = await this.repo.findById(id)

    if (!check) return null

    const resolveResult = await this.repo.resolveIncident(id)

    return resolveResult
  }
}
