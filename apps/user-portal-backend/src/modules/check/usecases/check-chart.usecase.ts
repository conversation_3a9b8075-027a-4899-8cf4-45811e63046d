import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'

import {
  ResponseTimeRequestDto,
  UpDownRequestDto,
} from '../applications/dto/check-chart.request.dto'
import {
  ResponseTimeResponseDto,
  UpDownResponseDto,
} from '../applications/dto/check-chart.response.dto'
import CheckChartRepository, {
  CHECK_CHART_REPOSITORY,
} from '../applications/check-chart.repository'

@Injectable()
export class CheckChartUseCase {
  constructor(
    @Inject(CHECK_CHART_REPOSITORY) private readonly repo: CheckChartRepository,
  ) {}

  async queryResponseTime(
    id: Id,
    responseTimeRequest: ResponseTimeRequestDto,
  ): Promise<ResponseTimeResponseDto[]> {
    return this.repo.getResponseTimeData(id, responseTimeRequest)
  }

  async queryUpDownPercentage(
    id: Id,
    upDownRequest: UpDownRequestDto,
  ): Promise<UpDownResponseDto[]> {
    return this.repo.getUpDownData(id, upDownRequest)
  }
}
