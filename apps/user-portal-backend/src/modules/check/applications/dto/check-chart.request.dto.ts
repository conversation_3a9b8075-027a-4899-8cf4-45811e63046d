import { IsString, IsN<PERSON>ber, Matches, IsDate } from 'class-validator'

import {
  CheckChartResponseTimeRequest,
  CheckChartUpDownRequest,
} from '../../interfaces/check-chart.interface'

export class ResponseTimeRequestDto implements CheckChartResponseTimeRequest {
  @IsString()
  @Matches(/^\d+ (minute|hour|day)s?$/, {
    message:
      'groupTime must be a number followed by minute(s), hour(s), or day(s)',
  })
  groupTime: string

  @IsString()
  @Matches(/^\d+ (minute|hour|day)s?$/, {
    message:
      'timeRange must be a number followed by minute(s), hour(s), or day(s)',
  })
  timeRange: string

  @IsString()
  location: string

  @IsNumber()
  percentile: number
}

export class UpDownRequestDto implements CheckChartUpDownRequest {
  @IsString()
  @Matches(/^\d+ (day)s?$/, {
    message: 'groupTime must be a number followed by day(s)',
  })
  groupTime: string

  @IsString()
  @Matches(/^\d+ (day)s?$/, {
    message: 'timeRange must be a number followed by day(s)',
  })
  timeRange: string

  @IsNumber()
  minIncidentLength: number

  @IsDate()
  checkCreatedAt: Date
}
