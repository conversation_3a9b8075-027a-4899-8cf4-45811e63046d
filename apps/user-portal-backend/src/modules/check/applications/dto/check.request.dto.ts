import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'
import { z } from 'zod'

import { EscalationSchema } from '@backend/modules/escalation/entities'

import { CheckSchema } from '../../entities'

export class CreateCheckDto extends createZodDto(
  extendApi(
    CheckSchema.omit({
      status: true,
      pausedAt: true,
      createdBy: true,
      updatedBy: true,
      lastCheckedAt: true,
    }).extend({
      escalation: EscalationSchema.omit({
        createdBy: true,
        updatedBy: true,
      }).or(z.string()),
    }),
  ),
) {}

export class UpdateCheckDto extends createZodDto(
  extendApi(
    CheckSchema.omit({
      status: true,
      pausedAt: true,
      createdBy: true,
      updatedBy: true,
      lastCheckedAt: true,
    })
      .extend({
        escalation: z.string(),
      })
      .partial(),
  ),
) {}
