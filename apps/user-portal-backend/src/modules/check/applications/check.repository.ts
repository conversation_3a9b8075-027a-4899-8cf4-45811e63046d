import { CheckStatus } from '@libs/shared/constants/check.enum'

import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { CheckModel as CheckModel } from '@backend/frameworks/database/models/check.model'
import { WorkerModel } from '@backend/frameworks/database/models/worker.model'
import { Id } from '@backend/cores/base/id.type'

import { Check, CheckProps } from '../entities'

import { TestCheckDto } from './dto/test-check.dto'
interface CheckRepository
  extends CrudRepository<Check, CheckProps, ITypeOrmFilter<CheckModel>> {
  createAndPublish(check: Check): Promise<Check>
  testCheckWhenConfig(testCheckRequest: TestCheckDto): Promise<string>
  findAllWorkers(): Promise<WorkerModel[]>
  pauseCheck(id: Id): Promise<Check | void>
  resumeCheck(id: Id): Promise<Check | void>
  updateStatus(id: Id, status: CheckStatus): Promise<Nullable<Check>>
  resolveIncident(id: Id): Promise<void>
  findWorkerById(id: string): Promise<Nullable<WorkerModel>>

  /**
   * Count checks by organization ID
   * @param organizationId The organization ID
   * @returns The number of checks in the organization
   */
  countChecksByOrganizationId(organizationId: Id): Promise<number>
}

export default CheckRepository
export const CHECK_REPOSITORY = 'CHECK_REPOSITORY'
