import { Id } from '@backend/cores/base/id.type'

import {
  Check<PERSON>hartResponseTime,
  CheckChartResponseTimeRequest,
  Check<PERSON>hartUpDown,
  Check<PERSON>hartUpDownRequest,
} from '../interfaces/check-chart.interface'

abstract class CheckChartRepository {
  abstract getResponseTimeData(
    checkId: Id,
    request: CheckChartResponseTimeRequest,
  ): Promise<CheckChartResponseTime[]>
  abstract getUpDownData(
    checkId: Id,
    request: CheckChartUpDownRequest,
  ): Promise<CheckChartUpDown[]>
}

export default CheckChartRepository
export const CHECK_CHART_REPOSITORY = 'CHECK_CHART_REPOSITORY'
