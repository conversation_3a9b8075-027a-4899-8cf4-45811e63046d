import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { ResourceType } from '@libs/shared/constants/subscription'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { IntegrationSettingUseCase } from '@backend/modules/integration-setting/usecases/integration-setting.usecase'
import {
  CreateIntegrationSettingDto,
  UpdateIntegrationSettingDto,
} from '@backend/modules/integration-setting/applications/dto/integration-setting.request.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { CheckSubscriptionLimit } from '@backend/commons/decorators/check-subscription-limit.decorator'

import { SlackIntegrationUseCase } from '../usecases/slack-integration.usecase'
import { CreateSlackIntegrationDto } from '../applications/dto/slack-integration.request.dto'
import { WebhookUseCase } from '../usecases/webhook.usecase'

@ApiTags('Integration Setting')
@Controller('integration-setting')
export class IntegrationSettingController {
  constructor(
    private readonly integrationSettingUseCase: IntegrationSettingUseCase,
    private readonly slackIntegrationUseCase: SlackIntegrationUseCase,
    private readonly webhookUseCase: WebhookUseCase,
  ) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.INTEGRATIONS,
      type: PermissionType.TEAM,
    },
  ])
  findIntegrationSettings(@OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto) {
    return this.integrationSettingUseCase.findAll(orgTeamParams.teamId)
  }

  // For internal testing
  @Get('webhook/:id/logs')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.INTEGRATIONS,
      type: PermissionType.TEAM,
    },
  ])
  getWebhookLogs(
    @Query() paginateOption: PaginateOptionsDto,
    @Param('id') webhookId: string,
  ) {
    return this.webhookUseCase.getWebhookEventLogs(webhookId, paginateOption)
  }

  @Post('webhook/:id/trigger')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.INTEGRATIONS,
      type: PermissionType.TEAM,
    },
  ])
  testTriggerWebhook(@Param('id') integrationSettingId: string) {
    return this.webhookUseCase.testTriggerWebhook(integrationSettingId)
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.INTEGRATIONS,
      type: PermissionType.TEAM,
    },
  ])
  getIntegrationSettings(@Param('id') notificationSettingId: string) {
    return this.integrationSettingUseCase.findOne(notificationSettingId)
  }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.INTEGRATIONS,
      type: PermissionType.TEAM,
    },
  ])
  @CheckSubscriptionLimit(ResourceType.INTEGRATION)
  createIntegrationSettings(
    @Body() createIntegrationSettingDto: CreateIntegrationSettingDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.integrationSettingUseCase.create(
      createIntegrationSettingDto,
      user,
      orgTeamParams.teamId,
    )
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.INTEGRATIONS,
      type: PermissionType.TEAM,
    },
  ])
  updateIntegrationSettings(
    @Param('id') integrationSettingId: string,
    @Body() updateIntegrationSettingDto: UpdateIntegrationSettingDto,
    @OrgTeamHeaders() _orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.integrationSettingUseCase.update(
      integrationSettingId,
      updateIntegrationSettingDto,
    )
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.INTEGRATIONS,
      type: PermissionType.TEAM,
    },
  ])
  deleteIntegrationSettings(@Param('id') integrationSettingId: string) {
    return this.integrationSettingUseCase.remove(integrationSettingId)
  }

  @Post('slack')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @CheckSubscriptionLimit(ResourceType.INTEGRATION)
  createSlackIntegration(
    @Body() payload: CreateSlackIntegrationDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.slackIntegrationUseCase.create(
      payload?.teamId || orgTeamParams?.teamId,
      user,
      payload,
    )
  }
}
