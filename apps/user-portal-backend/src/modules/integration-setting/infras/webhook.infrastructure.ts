import { Injectable } from '@nestjs/common'
import {
  EventBridgeClient,
  PutEventsCommand,
} from '@aws-sdk/client-eventbridge'
import { InjectAws } from 'aws-sdk-v3-nest'
import {
  WebhookEvent,
  WebhookLambdaRequestInterface,
} from '@libs/shared/constants/webhook.interface'
import { WebhookLogItem } from '@libs/database/lib/dynamo/webhook-log.schema'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

import WebhookLogData from '../entities/webhook-log.data'
import WebhookRepository from '../applications/webhook.repository'

@Injectable()
export class WebhookInfrastructure implements WebhookRepository {
  private readonly defaultEventBusArn =
    'arn:aws:events:us-east-1:851725359289:event-bus/WebhookEventsBus'

  constructor(
    @InjectAws(EventBridgeClient) private readonly client: EventBridgeClient,
  ) {}

  public async sendWebhook(
    resourceId: string,
    teamId: string,
    event: WebhookEvent,
  ): Promise<void> {
    let requestToLambda: WebhookLambdaRequestInterface

    // TEST only need webhookId and event
    if (event == 'TEST') {
      requestToLambda = {
        resourcesType: 'CHECK',
        resourceId: resourceId,
        teamId: resourceId,
        event: 'TEST',
        webhookId: resourceId,
      }
    } else {
      requestToLambda = {
        resourcesType: 'CHECK',
        resourceId,
        teamId,
        event,
      }
    }
    const params = {
      Entries: [
        {
          Source: 'webhook',
          DetailType: 'Webhook Request',
          Detail: JSON.stringify(requestToLambda),
          EventBusName: this.defaultEventBusArn,
        },
      ],
    }

    try {
      const command = new PutEventsCommand(params)
      await this.client.send(command)
      console.log(
        'Event successfully sent to EventBridge',
        JSON.stringify(requestToLambda),
      )
    } catch (error) {
      console.error('Error sending event to EventBridge:', error)
    }
  }

  public async testTriggerWebhook(webhookId: string): Promise<void> {
    await this.sendWebhook(webhookId, webhookId, 'TEST')
  }

  async getWebhookEventLogs(
    webhookId: string,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<WebhookLogItem>> {
    return WebhookLogData.queryLogsByWebhookId(webhookId, paginateOption)
  }
}
