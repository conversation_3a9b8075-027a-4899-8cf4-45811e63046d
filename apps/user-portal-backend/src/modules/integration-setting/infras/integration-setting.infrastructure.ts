import { Inject, Injectable } from '@nestjs/common'
import { SelectQueryBuilder } from 'typeorm'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { Team } from '@backend/modules/user/entities'
import { NotificationFactory } from '@backend/frameworks/notification/notification.factory'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import IntegrationSettingRepository from '@backend/modules/integration-setting/applications/integration-setting.repository'
import { IntegrationSettingModel } from '@backend/frameworks/database/models/integration-setting.model'
import {
  IntegrationSetting,
  IntegrationSettingProps,
} from '@backend/modules/integration-setting/entities/integration-setting.entity'

@Injectable()
export class IntegrationSettingInfrastructure
  implements IntegrationSettingRepository
{
  private readonly integrationSettingModel: TypeORMDriver<IntegrationSettingModel>
  constructor(
    @Inject(Database) private database: Database,
    private notificationFactory: NotificationFactory,
  ) {
    this.integrationSettingModel =
      this.database.typeorm<IntegrationSettingModel>('IntegrationSettingModel')
  }

  toDomain = (
    integrationSettingModel: IntegrationSettingModel,
  ): IntegrationSetting => {
    const { id, team, createdAt, updatedAt, ...props } = integrationSettingModel

    return new IntegrationSetting({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        team: new Team({ id: team.id }),
      },
    })
  }

  async findAll(
    query?: ITypeOrmFilter<IntegrationSettingModel>,
  ): Promise<IntegrationSetting[]> {
    const integrationSettingModels = await this.integrationSettingModel.findAll(
      {
        ...query,
        relations: {
          team: true,
        },
      },
    )
    return integrationSettingModels.map(this.toDomain)
  }

  find(
    query: ITypeOrmFilter<IntegrationSettingModel>,
  ): Promise<IPaginate<IntegrationSetting>> {
    return this.integrationSettingModel
      .find(query)
      .then((integrationSetting) => {
        return {
          ...integrationSetting,
          data: integrationSetting.data.map(this.toDomain),
        }
      })
  }

  findOne(
    query: ITypeOrmFilter<IntegrationSettingModel>,
  ): Promise<Nullable<IntegrationSetting>> {
    return this.integrationSettingModel
      .findOne(query)
      .then((integrationSetting) => {
        if (!integrationSetting) {
          return null
        }

        return this.toDomain(integrationSetting)
      })
  }

  async findById(
    id: Id,
    query?: ITypeOrmFilter<IntegrationSettingModel>,
  ): Promise<IntegrationSetting | null> {
    return await this.integrationSettingModel
      .findById(id, query)
      .then((integrationSetting) => {
        if (!integrationSetting) {
          return null
        }

        return this.toDomain(integrationSetting)
      })
  }

  async create(
    notificationSetting: IntegrationSetting,
  ): Promise<IntegrationSetting> {
    const integrationSettingProps = notificationSetting.getProps()

    return await this.integrationSettingModel
      .create({
        ...integrationSettingProps,
        team: {
          id: integrationSettingProps.team?.id,
        },
      })
      .then(this.toDomain)
  }

  updateById(
    id: Id,
    integrationSettingProps: Partial<IntegrationSettingProps>,
  ): Promise<IntegrationSetting> {
    return this.integrationSettingModel
      .updateById(id, {
        ...integrationSettingProps,
        team: {
          id: integrationSettingProps.team?.id,
        },
      })
      .then(this.toDomain)
  }

  async delete(id: Id): Promise<boolean> {
    await this.integrationSettingModel.softDelete({
      id,
    })

    return Promise.resolve(true)
  }

  async countIntegrationsByOrganizationId(organizationId: Id): Promise<number> {
    const queryBuilder =
      this.integrationSettingModel.getQueryBuilder() as SelectQueryBuilder<IntegrationSettingModel>

    // Join with teams to get organization_id
    const count = await queryBuilder
      .withDeleted()
      .leftJoin('teams', 'teams', 'teams.id = integration-settings.team_id')
      .where('teams.organization_id = :organizationId', { organizationId })
      .andWhere('integration-settings.deleted_at IS NULL')
      .getCount()

    return count || 0
  }
}
