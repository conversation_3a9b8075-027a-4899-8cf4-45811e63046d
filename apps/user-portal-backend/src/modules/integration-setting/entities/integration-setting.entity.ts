import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { Team, TeamResponse } from '@backend/modules/user/entities'

export interface IntegrationSettingProps {
  name?: string
  type?: string
  config?: object
  identity?: string
  team?: Team
  createdBy?: string
  updatedBy?: string
}

export interface IntegrationSettingResponse {
  id: Id
  name?: string
  type?: string
  identity?: string
  config?: object
  teamId?: Id
  team?: TeamResponse
  createdAt?: Date
  createdBy?: string
  updatedBy?: string
  updatedAt?: Date
  deletedAt?: Date
}

export class IntegrationSetting extends AggregateRoot<
  IntegrationSettingProps,
  IntegrationSettingResponse
> {
  static create(props: IntegrationSettingProps) {
    const integrationSetting = new IntegrationSetting({
      id: generateId(),
      props,
    })

    return integrationSetting
  }

  static update({ id, props }: { id: Id; props: IntegrationSettingProps }) {
    const integrationSetting = new IntegrationSetting({
      id: id,
      props,
    })

    return integrationSetting
  }

  public toResponse(): IntegrationSettingResponse {
    const integrationSettingProps = this.getProps()

    return {
      id: integrationSettingProps.id,
      name: integrationSettingProps.name,
      type: integrationSettingProps.type,
      identity: integrationSettingProps.identity,
      config: integrationSettingProps.config,
      team: integrationSettingProps.team?.toResponse(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      createdBy: integrationSettingProps.createdBy,
      updatedBy: integrationSettingProps.updatedBy,
    }
  }
}
