import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { Team } from '@backend/modules/user/entities'
import IntegrationSettingRepository, {
  INTEGRATION_SETTING_REPOSITORY,
} from '@backend/modules/integration-setting/applications/integration-setting.repository'
import {
  CreateIntegrationSettingDto,
  UpdateIntegrationSettingDto,
} from '@backend/modules/integration-setting/applications/dto/integration-setting.request.dto'
import { IntegrationSettingResponseDto } from '@backend/modules/integration-setting/applications/dto/integration-setting.response.dto'
import { IntegrationSetting } from '@backend/modules/integration-setting/entities/integration-setting.entity'

@Injectable()
export class IntegrationSettingUseCase {
  constructor(
    @Inject(INTEGRATION_SETTING_REPOSITORY)
    private readonly repo: IntegrationSettingRepository,
  ) {}

  async create(
    createIntegrationSettingDto: CreateIntegrationSettingDto,
    user: JwtPayload,
    teamId: Id,
  ): Promise<IntegrationSettingResponseDto | null> {
    const integrationSettingData = IntegrationSetting.create({
      ...createIntegrationSettingDto,
      createdBy: user?.userId,
      updatedBy: user?.userId,
      team: new Team({ id: teamId }),
    })

    const integrationSetting = await this.repo.create(integrationSettingData)
    return await this.findOne(integrationSetting.id)
  }

  async findAll(teamId: Id) {
    const integrationSettings = await this.repo.findAll({
      filter: {
        teamId: teamId,
      },
      relations: {
        team: true,
      },
    })
    return integrationSettings.map((integrationSetting) => {
      return integrationSetting.toResponse()
    })
  }

  async findOne(id: Id) {
    const integrationSetting = await this.repo.findById(id, {
      relations: {
        team: true,
      },
    })

    if (!integrationSetting) return null

    return integrationSetting?.toResponse()
  }

  async update(
    id: Id,
    updateIntegrationSettingDto: UpdateIntegrationSettingDto,
  ) {
    // End current notification setting
    const currentIntegrationSetting = await this.repo.findById(id, {
      relations: {
        team: true,
      },
    })

    await this.repo.updateById(id, {
      ...currentIntegrationSetting?.getProps(),
      ...updateIntegrationSettingDto,
    })

    return await this.findOne(id)
  }

  remove(id: Id) {
    return this.repo.delete(id)
  }
}
