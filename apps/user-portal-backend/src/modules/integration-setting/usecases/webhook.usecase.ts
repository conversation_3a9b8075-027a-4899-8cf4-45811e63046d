import { Inject, Injectable } from '@nestjs/common'
import { WebhookLogItem } from '@libs/database/lib/dynamo/webhook-log.schema'
import { WebhookEvent } from '@libs/shared/constants/webhook.interface'

import { Id } from '@backend/cores/base/id.type'
import IntegrationSettingRepository, {
  INTEGRATION_SETTING_REPOSITORY,
} from '@backend/modules/integration-setting/applications/integration-setting.repository'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

import WebhookRepository, {
  WEBHOOK_REPOSITORY,
} from '../applications/webhook.repository'

@Injectable()
export class WebhookUseCase {
  constructor(
    @Inject(INTEGRATION_SETTING_REPOSITORY)
    private readonly repo: IntegrationSettingRepository,

    @Inject(WEBHOOK_REPOSITORY)
    private readonly webhookRepo: WebhookRepository,
  ) {}

  async testTriggerWebhook(id: Id) {
    await this.webhookRepo.testTriggerWebhook(id)
    return
  }

  async sendWebhook(
    checkId: string,
    teamId: string,
    event: WebhookEvent,
  ): Promise<void> {
    return this.webhookRepo.sendWebhook(checkId, teamId, event)
  }

  async getWebhookEventLogs(
    webhookId: string,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<WebhookLogItem>> {
    return this.webhookRepo.getWebhookEventLogs(webhookId, paginateOption)
  }
}
