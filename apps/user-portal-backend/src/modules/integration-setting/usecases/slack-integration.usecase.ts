import { IntegrationType } from '@libs/shared/constants/integration'
import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'
import { startsWith } from 'lodash'

import { Id } from '@backend/cores/base/id.type'
import { NotificationFactory } from '@backend/frameworks/notification/notification.factory'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { Team } from '@backend/modules/user/entities'

import { CreateSlackIntegrationDto } from '../applications/dto/slack-integration.request.dto'
import {
  IntegrationSetting,
  IntegrationSettingResponse,
} from '../entities/integration-setting.entity'
import IntegrationSettingRepository, {
  INTEGRATION_SETTING_REPOSITORY,
} from '../applications/integration-setting.repository'
function removeHashPrefix(str) {
  return startsWith(str, '#') ? str.slice(1) : str
}

@Injectable()
export class SlackIntegrationUseCase {
  constructor(
    @Inject(INTEGRATION_SETTING_REPOSITORY)
    private readonly repo: IntegrationSettingRepository,
    private readonly notificationFactory: NotificationFactory,
  ) {}

  async create(
    teamId: Id,
    user: JwtPayload,
    payload: CreateSlackIntegrationDto,
  ): Promise<IntegrationSettingResponse | null> {
    let config
    let channelId
    try {
      config = await this.notificationFactory
        .getNotificationService('slack')
        .getAccess(payload.code, payload.redirectUrl)
      channelId = config?.incoming_webhook?.channel_id
      if (!config?.ok || !channelId)
        throw new HttpException(
          'Failed to get slack access',
          HttpStatus.BAD_REQUEST,
        )
    } catch (e) {
      throw new HttpException(e, HttpStatus.BAD_REQUEST)
    }

    const existedIntegration = await this.repo.findOne({
      filter: {
        identity: channelId,
      },
      relations: {
        team: true,
      },
    })
    if (existedIntegration) return existedIntegration?.toResponse()
    const integrationSettingData = IntegrationSetting.create({
      name: removeHashPrefix(config.incoming_webhook?.channel),
      identity: channelId,
      createdBy: user?.userId,
      updatedBy: user?.userId,
      team: new Team({ id: teamId }),
      config,
      type: IntegrationType.SLACK,
    })
    const integrationSetting = await this.repo.create(integrationSettingData)
    const response = await this.repo.findById(integrationSetting.id, {
      relations: {
        team: true,
      },
    })

    if (!response)
      throw new HttpException(
        'Failed to get integration from db',
        HttpStatus.NOT_FOUND,
      )

    return response?.toResponse()
  }
}
