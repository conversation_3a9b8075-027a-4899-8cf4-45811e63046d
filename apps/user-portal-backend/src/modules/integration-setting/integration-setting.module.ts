import { Module, forwardRef } from '@nestjs/common'
import { AwsSdkModule } from 'aws-sdk-v3-nest'
import { EventBridgeClient } from '@aws-sdk/client-eventbridge'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { NotificationsModule } from '@backend/frameworks/notification/notification.module'
import { configService } from '@backend/cores/config/config.service'

import { SubscriptionModule } from '../subscription/subscription.module'

import { SlackIntegrationUseCase } from './usecases/slack-integration.usecase'
import { IntegrationSettingController } from './controllers/integration-setting.controller'
import { INTEGRATION_SETTING_REPOSITORY } from './applications/integration-setting.repository'
import { IntegrationSettingInfrastructure } from './infras/integration-setting.infrastructure'
import { IntegrationSettingUseCase } from './usecases/integration-setting.usecase'
import { WebhookUseCase } from './usecases/webhook.usecase'
import { WEBHO<PERSON>_REPOSITORY } from './applications/webhook.repository'
import { WebhookInfrastructure } from './infras/webhook.infrastructure'

@Module({
  imports: [
    AwsSdkModule.register({
      client: new EventBridgeClient({
        credentials: {
          accessKeyId: configService.getAwsConfig().accessKeyId,
          secretAccessKey: configService.getAwsConfig().secretAccessKey,
        },
        region: configService.getAwsConfig().region,
      }),
    }),
    DatabaseModule,
    NotificationsModule,
    forwardRef(() => SubscriptionModule),
  ],
  controllers: [IntegrationSettingController],
  providers: [
    {
      provide: INTEGRATION_SETTING_REPOSITORY,
      useClass: IntegrationSettingInfrastructure,
    },
    {
      provide: WEBHOOK_REPOSITORY,
      useClass: WebhookInfrastructure,
    },
    IntegrationSettingUseCase,
    SlackIntegrationUseCase,
    WebhookUseCase,
  ],
  exports: [WebhookUseCase, IntegrationSettingUseCase],
})
export class IntegrationSettingModule {}
