import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import CrudRepository from '@backend/cores/base/crud-repository'
import { IntegrationSettingModel } from '@backend/frameworks/database/models/integration-setting.model'
import {
  IntegrationSetting,
  IntegrationSettingProps,
} from '@backend/modules/integration-setting/entities/integration-setting.entity'
import { Id } from '@backend/cores/base/id.type'

interface IntegrationSettingRepository
  extends CrudRepository<
    IntegrationSetting,
    Partial<IntegrationSettingProps>,
    ITypeOrmFilter<IntegrationSettingModel>
  > {
  /**
   * Count integrations by organization ID
   * @param organizationId The organization ID
   * @returns The number of integrations in the organization
   */
  countIntegrationsByOrganizationId(organizationId: Id): Promise<number>
}

export default IntegrationSettingRepository
export const INTEGRATION_SETTING_REPOSITORY = 'INTEGRATION_SETTING_REPOSITORY'
