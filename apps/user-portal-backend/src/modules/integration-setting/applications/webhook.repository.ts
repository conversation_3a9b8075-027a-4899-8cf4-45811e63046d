import { WebhookLogItem } from '@libs/database/lib/dynamo/webhook-log.schema'
import { WebhookEvent } from '@libs/shared/constants/webhook.interface'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

interface WebhookRepository {
  testTriggerWebhook(webhookId: string): Promise<void>
  sendWebhook(
    resourceId: string,
    teamId: string,
    event: WebhookEvent,
  ): Promise<void>
  getWebhookEventLogs(
    webhookId: string,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<WebhookLogItem>>
}

export default WebhookRepository
export const WEBHOOK_REPOSITORY = 'WEBHOOK_REPOSITORY'
