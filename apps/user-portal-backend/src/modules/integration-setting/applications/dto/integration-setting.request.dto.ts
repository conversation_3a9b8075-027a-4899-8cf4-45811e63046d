import { IsObject, IsString } from 'class-validator'

import { IntegrationSettingProps } from '@backend/modules/integration-setting/entities/integration-setting.entity'

export class CreateIntegrationSettingDto
  implements Partial<IntegrationSettingProps>
{
  @IsString()
  name: string

  @IsString()
  type: string

  @IsString()
  identity: string

  @IsObject()
  config: object
}

export class UpdateIntegrationSettingDto
  implements Partial<IntegrationSettingProps>
{
  @IsString()
  name: string

  @IsString()
  type: string

  @IsString()
  identity: string

  @IsObject()
  config: object
}
