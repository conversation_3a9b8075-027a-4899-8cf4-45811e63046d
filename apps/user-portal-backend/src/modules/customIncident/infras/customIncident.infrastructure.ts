/* eslint-disable unused-imports/no-unused-vars */
import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { CustomIncidentStatus } from '@libs/shared/constants/customIncident.enum'
import { InjectModel, Model } from 'nestjs-dynamoose'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { SortOrder } from 'dynamoose/dist/General'
import { IncidentInterface } from '@libs/shared/constants/shared.interface'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { CustomIncidentModel as CustomIncidentModel } from '@backend/frameworks/database/models/customIncident.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { Team } from '@backend/modules/user/entities'
import { Escalation } from '@backend/modules/escalation/entities'
import { IntegrationSettingModel } from '@backend/frameworks/database/models/integration-setting.model'

import CustomIncidentRepository from '../applications/customIncident.repository'
import {
  CustomIncident,
  CustomIncidentProps,
} from '../entities/customIncident.entity'
import { TestCustomIncidentDto } from '../applications/dto/test-customIncident.dto'

interface CustomIncidentWithIncident extends CustomIncidentModel {
  incident?: {
    id: string
    status: string
  }
}

@Injectable()
export class CustomIncidentInfrastructure implements CustomIncidentRepository {
  private readonly customIncidentModel: TypeORMDriver<CustomIncidentModel>
  private readonly integrationSettingModel: TypeORMDriver<IntegrationSettingModel>

  constructor(
    @Inject(Database) private database: Database,
    @InjectModel('IncidentSchema')
    private incidentModel: Model<IncidentInterface, string>,

    private eventEmitter: EventEmitter2,
  ) {
    this.customIncidentModel = this.database.typeorm<CustomIncidentModel>(
      'CustomIncidentModel',
    )
    this.integrationSettingModel =
      this.database.typeorm<IntegrationSettingModel>('IntegrationSettingModel')
    // this.eventBridge = new EventBridge()
  }

  toDomain = (customIncident: CustomIncidentWithIncident): CustomIncident => {
    const {
      id,
      createdAt,
      updatedAt,
      team,
      teamId,
      escalationId,
      escalation,
      ...props
    } = customIncident
    return new CustomIncident({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        status: props.status as CustomIncidentStatus,
        escalation: new Escalation({
          id: escalation?.id || escalationId,
        }),
        team: new Team({
          id: team?.id || teamId,
        }),
      },
    })
  }

  async findOne(
    query: ITypeOrmFilter<CustomIncidentModel>,
  ): Promise<Nullable<CustomIncident>> {
    const customIncident = await this.customIncidentModel.findOne({
      ...query,
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })

    if (!customIncident) return null

    return this.toDomain(customIncident)
  }

  async findAll(): Promise<CustomIncident[]> {
    // Use find() instead
    const customIncidents = await this.customIncidentModel.findAll({
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })
    return customIncidents.map(this.toDomain)
  }

  async find(
    query: ITypeOrmFilter<CustomIncidentModel>,
  ): Promise<IPaginate<CustomIncident>> {
    const customIncidents = await this.customIncidentModel.find({
      ...query,
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })

    const incidentMap = await this.fetchAndMapIncidents(customIncidents.data)

    const updatedCustomIncidents = this.updateCustomIncidentsWithIncidents(
      customIncidents.data,
      incidentMap,
    )

    return {
      ...customIncidents,
      data: updatedCustomIncidents,
    }
  }

  async findById(id: Id): Promise<Nullable<CustomIncident>> {
    const customIncident = await this.customIncidentModel.findById(id, {
      relations: {
        escalation: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      },
    })
    if (!customIncident) {
      return null
    }

    const incident = await this.findCurrentIncidentByCustomIncidentId(
      customIncident?.id,
    )

    // DOWN if here is incident
    if (!incident || incident.status === IncidentStatus.RESOLVED) {
      return this.handleResolvedIncident(customIncident)
    }
    // Else CustomIncident is UP
    return this.handleActiveIncident(customIncident, incident)
  }

  async create(customIncident: CustomIncident): Promise<CustomIncident> {
    const customIncidentProps = customIncident.getProps()

    const newCustomIncident = await this.customIncidentModel
      .create({
        ...customIncidentProps,
        escalation: {
          id: customIncidentProps.escalation?.id,
        },
        team: {
          id: customIncidentProps.team?.id,
        },
      })
      .then(this.toDomain)
    console.log(newCustomIncident)
    customIncident.publishEvents(this.eventEmitter)
    return newCustomIncident
  }

  async updateById(
    id: Id,
    customIncident: Partial<CustomIncidentProps>,
  ): Promise<Nullable<CustomIncident>> {
    const updatedCustomIncident = await this.customIncidentModel.updateById(
      id,
      {
        ...customIncident,
        escalation: {
          id: customIncident.escalation?.id,
        },
        team: {
          id: customIncident.team?.id,
        },
      },
    )

    if (!updatedCustomIncident) return null

    const entityCustomIncident = this.toDomain(updatedCustomIncident)
    return entityCustomIncident
  }

  async updateStatus(
    id: Id,
    status: CustomIncidentStatus,
  ): Promise<Nullable<CustomIncident>> {
    const updatedCustomIncident = await this.customIncidentModel.updateById(
      id,
      {
        status,
      },
    )

    if (!updatedCustomIncident) return null

    return this.toDomain(updatedCustomIncident)
  }

  async delete(id: Id): Promise<boolean> {
    const customIncident = await this.customIncidentModel.findById(id)
    if (!customIncident) return false
    const entityCustomIncident = this.toDomain(customIncident)

    await this.customIncidentModel.softDelete({
      id,
    })
    return true
  }

  async resolveIncident(id: Id): Promise<void> {
    // Reset state, and set status as UP
    await this.customIncidentModel.updateById(id, {
      status: CustomIncidentStatus.UP,
    })
    // TODO: tell Incident Manager to update status here
  }

  async testCustomIncidentWhenConfig(
    testCustomIncidentRequest: TestCustomIncidentDto,
  ): Promise<string> {
    return 'TODO'
  }

  private async findCurrentIncidentByCustomIncidentId(
    customIncidentId: string,
  ): Promise<IncidentInterface | null | undefined> {
    return
    // TODO
    const result = await this.incidentModel
      .query('customIncidentId')
      .eq(customIncidentId)
      .using('customIncidentId-createdAt-index')
      .sort(SortOrder.descending)
      .limit(1)
      .exec()

    return result[0]
  }

  private handleResolvedIncident(
    customIncident: CustomIncidentModel,
  ): CustomIncident {
    const incidentObject = null
    return this.toDomain({
      ...customIncident,
      incident: incidentObject,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any)
  }

  private handleActiveIncident(
    customIncident: CustomIncidentModel,
    incident: IncidentInterface,
  ): CustomIncident {
    const result = this.setStatusAndIncidentToCustomIncident(
      incident,
      customIncident.status,
      null,
    )
    return this.toDomain({
      ...customIncident,
      status: result.status,
      incident: result.incidentObject,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } as any)
  }

  private setStatusAndIncidentToCustomIncident(
    incident: IncidentInterface,
    status: string,
    incidentObject: object | null,
  ): { status: string; incidentObject: object | null } {
    switch (incident.status) {
      case IncidentStatus.ACKNOWLEDGED: {
        status = CustomIncidentStatus.DOWN
        incidentObject = {
          status: IncidentStatus.ACKNOWLEDGED,
          id: incident.id,
        }
        break
      }
      case IncidentStatus.STARTED: {
        status = CustomIncidentStatus.DOWN
        incidentObject = {
          status: IncidentStatus.STARTED,
          id: incident.id,
        }
        break
      }
      case IncidentStatus.ISSUE_REAPPEARED: {
        status = CustomIncidentStatus.DOWN
        incidentObject = {
          status: IncidentStatus.ISSUE_REAPPEARED,
          id: incident.id,
        }
        break
      }
    }
    return { status, incidentObject }
  }

  private async fetchAndMapIncidents(
    customIncidents: CustomIncidentModel[],
  ): Promise<Map<string, IncidentInterface | null>> {
    const customIncidentIds = customIncidents.map(
      (customIncident) => customIncident.id,
    )

    const incidents = await Promise.all(
      customIncidentIds.map(async (id) => {
        return await this.findCurrentIncidentByCustomIncidentId(id)
      }),
    )

    return this.mapIncidentsToCustomIncidents(customIncidentIds, incidents)
  }

  private mapIncidentsToCustomIncidents(
    customIncidentIds: string[],
    incidents: (IncidentInterface | null | undefined)[],
  ): Map<string, IncidentInterface | null> {
    const incidentMap = new Map<string, IncidentInterface | null>()
    customIncidentIds.forEach((id, index) => {
      incidentMap.set(id, incidents[index] ?? null)
    })
    return incidentMap
  }

  private updateCustomIncidentsWithIncidents(
    customIncidents: CustomIncidentModel[],
    incidentMap: Map<string, IncidentInterface | null>,
  ): CustomIncident[] {
    return customIncidents.map((customIncident) => {
      const incident = incidentMap.get(customIncident.id)
      if (!incident || incident.status === IncidentStatus.RESOLVED) {
        return this.handleResolvedIncident(customIncident)
      }
      return this.handleActiveIncident(customIncident, incident)
    })
  }
}
