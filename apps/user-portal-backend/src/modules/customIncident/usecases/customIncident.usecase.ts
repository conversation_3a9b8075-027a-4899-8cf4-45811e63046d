import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { Team } from '@backend/modules/user/entities'
import { Escalation } from '@backend/modules/escalation/entities'
import { EscalationUseCase } from '@backend/modules/escalation/usecases/escalation.usecase'

import { CustomIncidentResponseDto } from '../applications/dto/customIncident.response.dto'
import CustomIncidentRepository, {
  CUSTOMINCIDENT_REPOSITORY,
} from '../applications/customIncident.repository'
import { CustomIncident } from '../entities/customIncident.entity'
import {
  CreateCustomIncidentDto,
  UpdateCustomIncidentDto,
} from '../applications/dto/customIncident.request.dto'
import { TestCustomIncidentDto } from '../applications/dto/test-customIncident.dto'

@Injectable()
export class CustomIncidentUseCase {
  constructor(
    @Inject(CUSTOMINCIDENT_REPOSITORY)
    private readonly repo: CustomIncidentRepository,
    @Inject(EscalationUseCase)
    private readonly escalationUseCase: EscalationUseCase,
  ) {}

  async create(
    createCustomIncidentDto: CreateCustomIncidentDto,
    user: JwtPayload,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<CustomIncidentResponseDto | string> {
    try {
      let escalationId =
        typeof createCustomIncidentDto.escalation === 'string'
          ? createCustomIncidentDto.escalation
          : null

      if (
        typeof createCustomIncidentDto.escalation !== 'string' &&
        createCustomIncidentDto.escalation
      ) {
        const newEscalation = await this.escalationUseCase.create(
          createCustomIncidentDto.escalation,
          user,
          orgTeamParams,
        )

        if (!newEscalation?.id)
          throw new HttpException(
            'Error creating escalation',
            HttpStatus.INTERNAL_SERVER_ERROR,
          )

        escalationId = newEscalation.id
      }

      const newCustomIncident = await this.repo.create(
        CustomIncident.create({
          ...createCustomIncidentDto,
          createdBy: user.userId,
          updatedBy: user.userId,
          team: new Team({ id: orgTeamParams.teamId }),
          escalation: new Escalation({ id: escalationId as string }),
        }),
      )

      return newCustomIncident.toResponse()
    } catch (error) {
      console.error(error)
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  async find(
    orgTeamParams: OrgTeamHeadersDto,
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<CustomIncidentResponseDto>> {
    try {
      const customIncidents = await this.repo.find({
        filter: {
          team: {
            id: orgTeamParams.teamId,
          },
        },
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })
      return {
        data: customIncidents.data.map((customIncident) =>
          customIncident.toResponse(),
        ),
        total: customIncidents.total,
        totalPage: customIncidents.totalPage,
        limit: customIncidents.limit,
        page: customIncidents.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<CustomIncidentResponseDto>> {
    const entity = await this.repo.findById(id)

    if (!entity) return null
    return entity.toResponse()
  }

  async update(id: Id, updateCustomIncidentDto: UpdateCustomIncidentDto) {
    try {
      const customIncident = await this.repo.findById(id)

      if (!customIncident) return null

      const returnedCustomIncident = await this.repo.updateById(id, {
        ...customIncident.getProps(),
        ...updateCustomIncidentDto,
        escalation: new Escalation({ id: updateCustomIncidentDto.escalation }),
      })

      // if (customIncident.status === CustomIncidentStatus.PAUSED) {
      //   await this.repo.resumeCustomIncident(id)
      // }

      if (!returnedCustomIncident) return null

      return returnedCustomIncident.toResponse()
    } catch (error) {
      console.error(error)
      return (error as Error).message
    }
  }

  async remove(id: Id) {
    const result = await this.repo.delete(id)
    return result
  }

  async testWhenConfig(
    testCustomIncidentRequest: TestCustomIncidentDto,
  ): Promise<string | null> {
    const customIncidentResult = await this.repo.testCustomIncidentWhenConfig(
      testCustomIncidentRequest,
    )
    if (!customIncidentResult) return null
    return customIncidentResult
  }

  // async startIncident(customIncidentId: Id) {
  //   const customIncident = await this.repo.findById(customIncidentId)

  //   if (!customIncident) return null

  //   const updatedCustomIncident = await this.repo.updateById(customIncidentId, {
  //     ...customIncident.getProps(),
  //     status: CustomIncidentStatus.DOWN,
  //   })

  //   return updatedCustomIncident
  // }

  async resolveIncident(id: Id) {
    const customIncident = await this.repo.findById(id)

    if (!customIncident) return null

    const resolveResult = await this.repo.resolveIncident(id)

    return resolveResult
  }
}
