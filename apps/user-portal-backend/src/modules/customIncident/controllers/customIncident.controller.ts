import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'

import { CustomIncidentUseCase } from '../usecases/customIncident.usecase'
import {
  TestCustomIncidentDto,
  TestCustomIncidentResponseDto,
} from '../applications/dto/test-customIncident.dto'
import {
  CreateCustomIncidentDto,
  UpdateCustomIncidentDto,
} from '../applications/dto/customIncident.request.dto'
import { CustomIncidentResponseDto } from '../applications/dto/customIncident.response.dto'

@ApiTags('Custom Incident')
@Controller('custom-incident')
export class CustomIncidentController {
  constructor(private readonly customIncidentUseCase: CustomIncidentUseCase) {}

  @Post('test')
  @UseGuards(AuthGuard)
  @ApiPaginatedResponse(TestCustomIncidentResponseDto)
  testCustomIncident(@Body() testCustomIncidentRequest: TestCustomIncidentDto) {
    return this.customIncidentUseCase.testWhenConfig(testCustomIncidentRequest)
  }

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.CHECKS,
      type: PermissionType.TEAM,
    },
  ])
  create(
    @Body() createCustomIncidentDto: CreateCustomIncidentDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.customIncidentUseCase.create(
      createCustomIncidentDto,
      user,
      orgTeamParams,
    )
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(CustomIncidentResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.CHECKS,
      type: PermissionType.TEAM,
    },
  ])
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.customIncidentUseCase.find(orgTeamParams, paginateOption)
  }

  @ApiResponse({
    type: CustomIncidentResponseDto,
  })
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.CHECKS,
      type: PermissionType.TEAM,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.customIncidentUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.CHECKS,
    },
  ])
  update(
    @Param('id') id: string,
    @Body() updateCustomIncidentDto: UpdateCustomIncidentDto,
  ) {
    return this.customIncidentUseCase.update(id, updateCustomIncidentDto)
  }

  @Delete(':id')
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.CHECKS,
    },
  ])
  remove(@Param('id') id: string) {
    return this.customIncidentUseCase.remove(id)
  }
}
