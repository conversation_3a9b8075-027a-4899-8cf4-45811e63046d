import { z } from 'zod'
import { CustomIncidentStatus } from '@libs/shared/constants/customIncident.enum'

import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { Team } from '@backend/modules/user/entities'
import { Escalation } from '@backend/modules/escalation/entities'

import { CustomIncidentCreatedDomainEvent } from './events/customIncident-created.event'

export const CustomIncidentSchema = z.object({
  title: z.string().optional(),
  status: z.nativeEnum(CustomIncidentStatus).default(CustomIncidentStatus.UP),
  // url: z.string().url({
  //   message: 'Invalid URL',
  // }),
  // Audit
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),

  incident: z
    .object({
      id: z.string(),
      status: z.string(),
    })
    .optional(),
})

export interface CustomIncidentProps
  extends z.infer<typeof CustomIncidentSchema> {
  team?: Team
  escalation?: Escalation
}

export const CustomIncidentResponse = CustomIncidentSchema.partial().extend({
  id: z.string().optional(),
  updatedAt: z.date().optional(),
  createdAt: z.date().optional(),
  team: z.object({
    id: z.string().optional(),
  }),
  escalation: z.string().optional(),
})

export type CustomIncidentResponse = z.infer<typeof CustomIncidentResponse>

export class CustomIncident extends AggregateRoot<
  CustomIncidentProps,
  CustomIncidentResponse
> {
  static create(props: Omit<CustomIncidentProps, 'status'>) {
    const customIncidentProps = {
      ...props,
      status: CustomIncidentStatus.UP,
    }

    const customIncident = new CustomIncident({
      id: generateId(),
      props: customIncidentProps,
    })

    customIncident.addEvent(
      new CustomIncidentCreatedDomainEvent({
        aggregateId: customIncident.id,
        props: customIncidentProps,
      }),
    )

    return customIncident
  }

  static update({ id, props }: { id: Id; props: CustomIncidentProps }) {
    const customIncident = new CustomIncident({
      id: id,
      props,
    })

    return customIncident
  }

  public toResponse(): CustomIncidentResponse {
    const props = this.getProps()

    return {
      id: props.id,
      updatedAt: props.updatedAt,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      title: props.title,
      // url: props.url,
      status: props.status,
      team: {
        id: props.team?.id,
      },
      escalation: props.escalation?.id,
    }
  }

  public get status(): CustomIncidentStatus {
    return this.getProps().status
  }

  public getId(): Id {
    return this.getProps().id
  }

  public getCreatedAt(): Date {
    return this.getProps().createdAt
  }

  public getUpdatedAt(): Date {
    return this.getProps().updatedAt
  }
}
