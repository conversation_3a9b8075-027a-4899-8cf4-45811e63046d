import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { CustomIncidentModel as CustomIncidentModel } from '@backend/frameworks/database/models/customIncident.model'
import { Id } from '@backend/cores/base/id.type'

import { CustomIncident, CustomIncidentProps } from '../entities'

import { TestCustomIncidentDto } from './dto/test-customIncident.dto'
interface CustomIncidentRepository
  extends CrudRepository<
    CustomIncident,
    CustomIncidentProps,
    ITypeOrmFilter<CustomIncidentModel>
  > {
  testCustomIncidentWhenConfig(
    testCustomIncidentRequest: TestCustomIncidentDto,
  ): Promise<string>
  resolveIncident(id: Id): Promise<void>
}

export default CustomIncidentRepository
export const CUSTOMINCIDENT_REPOSITORY = 'CUSTOMINCIDENT_REPOSITORY'
