import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'
import { z } from 'zod'

import { EscalationSchema } from '@backend/modules/escalation/entities'

import { CustomIncidentSchema } from '../../entities'

export class CreateCustomIncidentDto extends createZodDto(
  extendApi(
    CustomIncidentSchema.omit({
      status: true,
      createdBy: true,
      updatedBy: true,
    }).extend({
      escalation: EscalationSchema.omit({
        createdBy: true,
        updatedBy: true,
      }).or(z.string()),
    }),
  ),
) {}

export class UpdateCustomIncidentDto extends createZodDto(
  extendApi(
    CustomIncidentSchema.omit({
      status: true,
      createdBy: true,
      updatedBy: true,
    })
      .extend({
        escalation: z.string(),
      })
      .partial(),
  ),
) {}
