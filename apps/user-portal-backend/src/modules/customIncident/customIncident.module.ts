import { Module } from '@nestjs/common'
import { TimeUtils } from '@libs/shared/time/time.utils'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { DynamoModule } from '@backend/frameworks/dynamo/dynamo.module'

import { EscalationModule } from '../escalation/escalation.module'

import { CustomIncidentUseCase } from './usecases/customIncident.usecase'
import { CustomIncidentController } from './controllers/customIncident.controller'
import { CustomIncidentInfrastructure } from './infras/customIncident.infrastructure'
import { CUSTOMINCIDENT_REPOSITORY } from './applications/customIncident.repository'
@Module({
  imports: [DynamoModule, DatabaseModule, EscalationModule],
  controllers: [CustomIncidentController],
  providers: [
    TimeUtils,
    {
      provide: CUSTOMINCIDENT_REPOSITORY,
      useClass: CustomIncidentInfrastructure,
    },
    CustomIncidentUseCase,
  ],
  exports: [CustomIncidentUseCase],
})
export class CustomIncidentModule {}
