/* eslint-disable @typescript-eslint/no-empty-interface */
import { z } from 'zod'

import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { Team } from '@backend/modules/user/entities'

import { EscalationContactType } from '../constants/escalation'

export const EscalationSchema = z.object({
  name: z.string(),
  repeatCount: z.number().default(0),
  repeatDelay: z.number().default(0),
  escalationSteps: z.array(
    z.object({
      contacts: z.array(
        z.object({
          contactType: z.string().default(EscalationContactType.ON_CALL),
          contactId: z.string().nullable().default(null),
        }),
      ),
      stepDelay: z.number().int().min(0).default(0),
      severity: z.string(),
    }),
  ),

  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export interface EscalationProps extends z.infer<typeof EscalationSchema> {
  team?: Team
  createdAt?: Date
  updatedAt?: Date
}

export const EscalationResponse = EscalationSchema.partial().extend({
  id: z.string().optional(),
  updatedAt: z.date().optional(),
  createdAt: z.date().optional(),
  team: z.object({
    id: z.string().optional(),
  }),
})

export type EscalationResponse = z.infer<typeof EscalationResponse>

export class Escalation extends AggregateRoot<
  EscalationProps,
  EscalationResponse
> {
  static create(props: Omit<EscalationProps, 'status'>) {
    const escalationProps = {
      ...props,
    }

    const escalation = new Escalation({
      id: generateId(),
      props: escalationProps,
    })

    return escalation
  }

  static update({ id, props }: { id: Id; props: EscalationProps }) {
    const escalation = new Escalation({
      id: id,
      props,
    })

    return escalation
  }

  public toResponse(): EscalationResponse {
    const props = this.getProps()

    return {
      id: props.id,
      name: props.name,
      updatedAt: props.updatedAt,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      repeatCount: props.repeatCount,
      repeatDelay: props.repeatDelay,
      escalationSteps: props.escalationSteps,
      team: {
        id: props.team?.id,
      },
    }
  }
}
