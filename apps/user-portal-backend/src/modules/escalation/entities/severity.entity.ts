/* eslint-disable @typescript-eslint/no-empty-interface */
import { z } from 'zod'

import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { Team } from '@backend/modules/user/entities'

import { SeverityAlert } from '../constants/severity'

export const SeveritySchema = z.object({
  name: z.string(),
  alerts: z.array(z.nativeEnum(SeverityAlert)),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export interface SeverityProps extends z.infer<typeof SeveritySchema> {
  team?: Team
}

export const SeverityResponse = SeveritySchema.partial().extend({
  id: z.string().optional(),
  updatedAt: z.date().optional(),
  createdAt: z.date().optional(),
  team: z.object({
    id: z.string().optional(),
  }),
})

export type SeverityResponse = z.infer<typeof SeverityResponse>

export class Severity extends AggregateRoot<SeverityProps, SeverityResponse> {
  static create(props: Omit<SeverityProps, 'status'>) {
    const severityProps = {
      ...props,
    }

    const severity = new Severity({
      id: generateId(),
      props: severityProps,
    })

    // severity.addEvent(
    //   new CheckCreatedDomainEvent({
    //     aggregateId: severity.id,
    //     props: severityProps,
    //   }),
    // )

    return severity
  }

  static update({ id, props }: { id: Id; props: SeverityProps }) {
    const severity = new Severity({
      id: id,
      props,
    })

    return severity
  }

  public toResponse(): SeverityResponse {
    const props = this.getProps()

    return {
      id: props.id,
      updatedAt: props.updatedAt,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      name: props.name,
      alerts: props.alerts,
      team: {
        id: props.team?.id,
      },
    }
  }
}
