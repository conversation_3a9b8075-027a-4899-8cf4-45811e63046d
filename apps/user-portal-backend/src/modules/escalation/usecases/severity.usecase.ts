import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { Team } from '@backend/modules/user/entities'

import {
  CreateSeverityDto,
  UpdateSeverityDto,
} from '../applications/dto/severity.request.dto'
import { Severity } from '../entities/severity.entity'
import SeverityRepository, {
  SEVERITY_REPOSITORY,
} from '../applications/severity.repository'
import { SeverityResponseDto } from '../applications/dto/severity.response.dto'
import { DEFAULT_SEVERITIES } from '../constants/severity'

@Injectable()
export class SeverityUseCase {
  constructor(
    @Inject(SEVERITY_REPOSITORY) private readonly repo: SeverityRepository,
  ) {}

  async create(
    createSeverityDto: CreateSeverityDto,
    user: JwtPayload,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<SeverityResponseDto> {
    try {
      const newSeverity = await this.repo.create(
        Severity.create({
          ...createSeverityDto,
          createdBy: user.userId,
          updatedBy: user.userId,
          team: new Team({ id: orgTeamParams.teamId }),
        }),
      )

      return newSeverity.toResponse()
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async createDefaultSeverity(userId: Id, teamId: Id): Promise<void> {
    console.log('CREATING NEW SEVERITIES')

    try {
      const created = await Promise.all(
        Object.keys(DEFAULT_SEVERITIES).map(async (severity) =>
          this.repo.create(
            Severity.create({
              ...DEFAULT_SEVERITIES[severity],
              createdBy: userId,
              updatedBy: userId,
              team: new Team({ id: teamId }),
            }),
          ),
        ),
      )
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findAll(
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<SeverityResponseDto[]> {
    const severities = await this.repo.findAll({
      filter: {
        teamId: {
          $eq: orgTeamParams.teamId,
        },
      },
    })

    return severities.map((severity) => severity.toResponse())
  }

  async find(
    paginateOption: PaginateOptionsDto,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<PaginateDto<SeverityResponseDto>> {
    try {
      const severities = await this.repo.find({
        filter: {
          teamId: {
            $eq: orgTeamParams.teamId,
          },
        },
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })
      return {
        data: severities.data.map((severity) => severity.toResponse()),
        total: severities.total,
        totalPage: severities.totalPage,
        limit: severities.limit,
        page: severities.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<SeverityResponseDto>> {
    const entity = await this.repo.findById(id)

    if (!entity) return null
    return entity.toResponse()
  }

  async update(id: Id, updateSeverityDto: UpdateSeverityDto) {
    const severity = await this.repo.findById(id)

    if (!severity) return null

    const returnedSeverity = await this.repo.updateById(id, {
      ...severity.getProps(),
      ...updateSeverityDto,
    })

    if (!returnedSeverity) return null

    return returnedSeverity.toResponse()
  }

  async remove(id: Id) {
    const result = await this.repo.delete(id)
    return result
  }
}
