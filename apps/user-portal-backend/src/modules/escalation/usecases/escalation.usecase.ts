import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { Team } from '@backend/modules/user/entities'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'

import { EscalationResponseDto } from '../applications/dto/escalation.response.dto'
import EscalationRepository, {
  ESCALATION_REPOSITORY,
} from '../applications/escalation.repository'
import { Escalation } from '../entities/escalation.entity'
import {
  CreateEscalationDto,
  UpdateEscalationDto,
} from '../applications/dto/escalation.request.dto'

@Injectable()
export class EscalationUseCase {
  constructor(
    @Inject(ESCALATION_REPOSITORY) private readonly repo: EscalationRepository,
  ) {}

  async create(
    createEscalationDto: CreateEscalationDto,
    user: JwtPayload,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<EscalationResponseDto> {
    try {
      const newEscalation = await this.repo.create(
        Escalation.create({
          ...createEscalationDto,
          createdBy: user.userId,
          updatedBy: user.userId,
          team: new Team({ id: orgTeamParams.teamId }),
        }),
      )

      return newEscalation.toResponse()
    } catch (error) {
      console.error(error)
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  async find(
    paginateOption: PaginateOptionsDto,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<PaginateDto<EscalationResponseDto>> {
    try {
      const escalations = await this.repo.find({
        filter: {
          team: {
            id: orgTeamParams.teamId,
          },
        },
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })
      return {
        data: escalations.data.map((escalation) => escalation.toResponse()),
        total: escalations.total,
        totalPage: escalations.totalPage,
        limit: escalations.limit,
        page: escalations.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<EscalationResponseDto>> {
    const entity = await this.repo.findById(id)

    if (!entity) return null
    return entity.toResponse()
  }

  async update(id: Id, updateEscalationDto: UpdateEscalationDto) {
    try {
      const escalation = await this.repo.findById(id)

      if (!escalation) return null

      const returnedEscalation = await this.repo.updateById(id, {
        ...escalation.getProps(),
        ...updateEscalationDto,
      })

      if (!returnedEscalation) return null

      return returnedEscalation.toResponse()
    } catch (error) {
      console.error(error)
      return (error as Error).message
    }
  }

  async remove(id: Id) {
    const result = await this.repo.delete(id)
    return result
  }
}
