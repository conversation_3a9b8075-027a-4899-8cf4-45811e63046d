import { Module } from '@nestjs/common'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { CacheModule } from '@backend/frameworks/cache/cache.module'
import { QueueModule } from '@backend/frameworks/queue/queue.module'
import { InfluxModule } from '@backend/frameworks/influx/influx.module'

import { EscalationUseCase } from './usecases/escalation.usecase'
import { EscalationController } from './controllers/escalation.controller'
import { EscalationInfrastructure } from './infras/escalation.infrastructure'
import { ESCALATION_REPOSITORY } from './applications/escalation.repository'
import { SeverityController } from './controllers/severity.controller'
import { SEVERITY_REPOSITORY } from './applications/severity.repository'
import { SeverityInfrastructure } from './infras/severity.infrastructure'
import { SeverityUseCase } from './usecases/severity.usecase'

@Module({
  imports: [DatabaseModule, CacheModule, QueueModule, InfluxModule],
  controllers: [EscalationController, SeverityController],
  providers: [
    { provide: ESCALATION_REPOSITORY, useClass: EscalationInfrastructure },
    { provide: SEVERITY_REPOSITORY, useClass: SeverityInfrastructure },
    EscalationUseCase,
    SeverityUseCase,
  ],
  exports: [EscalationUseCase, SeverityUseCase],
})
export class EscalationModule {}
