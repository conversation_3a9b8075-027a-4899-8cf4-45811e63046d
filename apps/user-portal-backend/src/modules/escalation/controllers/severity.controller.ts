import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'

import { SeverityResponseDto } from '../applications/dto/severity.response.dto'
import {
  CreateSeverityDto,
  UpdateSeverityDto,
} from '../applications/dto/severity.request.dto'
import { SeverityUseCase } from '../usecases/severity.usecase'

@ApiTags('Severity')
@Controller('severity')
export class SeverityController {
  constructor(private readonly severityUseCase: SeverityUseCase) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ESCALATION_POLICY,
      type: PermissionType.TEAM,
    },
  ])
  create(
    @Body() createSeverityDto: CreateSeverityDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.severityUseCase.create(createSeverityDto, user, orgTeamParams)
  }

  @Get('all')
  @ApiBearerAuth()
  @ApiPaginatedResponse(SeverityResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ESCALATION_POLICY,
      type: PermissionType.TEAM,
    },
  ])
  findAll(@OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto) {
    return this.severityUseCase.findAll(orgTeamParams)
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(SeverityResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ESCALATION_POLICY,
      type: PermissionType.TEAM,
    },
  ])
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.severityUseCase.find(paginateOption, orgTeamParams)
  }

  @ApiResponse({
    type: SeverityResponseDto,
  })
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ESCALATION_POLICY,
      type: PermissionType.TEAM,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.severityUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ESCALATION_POLICY,
    },
  ])
  update(
    @Param('id') id: string,
    @Body() updateSeverityDto: UpdateSeverityDto,
  ) {
    return this.severityUseCase.update(id, updateSeverityDto)
  }

  @Delete(':id')
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ESCALATION_POLICY,
    },
  ])
  remove(@Param('id') id: string) {
    return this.severityUseCase.remove(id)
  }
}
