import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'

import { EscalationResponseDto } from '../applications/dto/escalation.response.dto'
import {
  CreateEscalationDto,
  UpdateEscalationDto,
} from '../applications/dto/escalation.request.dto'
import { EscalationUseCase } from '../usecases/escalation.usecase'

@ApiTags('Escalation')
@Controller('escalation')
export class EscalationController {
  constructor(private readonly escalationUseCase: EscalationUseCase) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ESCALATION_POLICY,
      type: PermissionType.TEAM,
    },
  ])
  create(
    @Body() createEscalationDto: CreateEscalationDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.escalationUseCase.create(
      createEscalationDto,
      user,
      orgTeamParams,
    )
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(EscalationResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ESCALATION_POLICY,
      type: PermissionType.TEAM,
    },
  ])
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.escalationUseCase.find(paginateOption, orgTeamParams)
  }

  @ApiResponse({
    type: EscalationResponseDto,
  })
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ESCALATION_POLICY,
      type: PermissionType.TEAM,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.escalationUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ESCALATION_POLICY,
    },
  ])
  update(
    @Param('id') id: string,
    @Body() updateEscalationDto: UpdateEscalationDto,
  ) {
    return this.escalationUseCase.update(id, updateEscalationDto)
  }

  @Delete(':id')
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ESCALATION_POLICY,
    },
  ])
  remove(@Param('id') id: string) {
    return this.escalationUseCase.remove(id)
  }
}
