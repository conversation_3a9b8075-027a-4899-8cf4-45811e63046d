import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'

import { SeveritySchema } from '../../entities'

export class CreateSeverityDto extends createZodDto(
  extendApi(
    SeveritySchema.omit({
      createdBy: true,
      updatedBy: true,
    }),
  ),
) {}

export class UpdateSeverityDto extends createZodDto(
  extendApi(
    SeveritySchema.omit({
      createdBy: true,
      updatedBy: true,
    }).partial(),
  ),
) {}
