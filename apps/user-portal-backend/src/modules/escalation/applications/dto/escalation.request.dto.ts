import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'

import { EscalationSchema } from '../../entities'

export class CreateEscalationDto extends createZodDto(
  extendApi(
    EscalationSchema.omit({
      createdBy: true,
      updatedBy: true,
    }),
  ),
) {}

export class UpdateEscalationDto extends createZodDto(
  extendApi(
    EscalationSchema.omit({
      createdBy: true,
      updatedBy: true,
    }).partial(),
  ),
) {}
