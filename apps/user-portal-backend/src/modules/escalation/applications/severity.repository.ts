import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { SeverityModel as SeverityModel } from '@backend/frameworks/database/models/severity.model'

import { Severity, SeverityProps } from '../entities'

type SeverityRepository = CrudRepository<
  Severity,
  SeverityProps,
  ITypeOrmFilter<SeverityModel>
>

export default SeverityRepository
export const SEVERITY_REPOSITORY = 'SEVERITY_REPOSITORY'
