import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { EscalationModel as EscalationModel } from '@backend/frameworks/database/models/escalation.model'

import { Escalation, EscalationProps } from '../entities'

type EscalationRepository = CrudRepository<
  Escalation,
  EscalationProps,
  ITypeOrmFilter<EscalationModel>
>

export default EscalationRepository
export const ESCALATION_REPOSITORY = 'ESCALATION_REPOSITORY'
