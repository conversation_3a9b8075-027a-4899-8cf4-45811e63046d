import { Inject, Injectable } from '@nestjs/common'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import {
  EscalationContactModel,
  EscalationModel,
  EscalationStepModel,
} from '@backend/frameworks/database/models/escalation.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { SeverityModel } from '@backend/frameworks/database/models/severity.model'
import { generateId } from '@backend/commons/id'
import { Team } from '@backend/modules/user/entities'

import EscalationRepository from '../applications/escalation.repository'
import { Escalation, EscalationProps } from '../entities/escalation.entity'

@Injectable()
export class EscalationInfrastructure implements EscalationRepository {
  private readonly escalationModel: TypeORMDriver<EscalationModel>
  private readonly severityModel: TypeORMDriver<SeverityModel>
  constructor(@Inject(Database) private database: Database) {
    this.escalationModel =
      this.database.typeorm<EscalationModel>('EscalationModel')
    this.severityModel = this.database.typeorm<SeverityModel>('SeverityModel')
  }

  toDomain = (escalation: EscalationModel): Escalation => {
    const { id, createdAt, updatedAt, escalationSteps, ...props } = escalation
    return new Escalation({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        escalationSteps: escalationSteps.map((step) => ({
          contacts: step.contacts.map((contact) => ({
            id: contact.id,
            contactType: contact.contactType,
            contactId: contact.contactId || null,
          })),
          stepDelay: step.stepDelay,
          severity: step.severity.id,
        })),
        team: new Team({ id: escalation.team?.id }),
      },
    })
  }

  async find(
    query: ITypeOrmFilter<EscalationModel>,
  ): Promise<IPaginate<Escalation>> {
    return this.escalationModel
      .find({
        ...query,
        relations: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      })
      .then((escalations) => {
        return {
          ...escalations,
          data: escalations.data.map(this.toDomain),
        }
      })
  }

  async findOne(
    query: ITypeOrmFilter<EscalationModel>,
  ): Promise<Nullable<Escalation>> {
    const escalation = await this.escalationModel.findOne({
      ...query,
      relations: {
        escalationSteps: {
          severity: true,
          contacts: true,
        },
        team: true,
      },
    })

    if (!escalation) return null

    return this.toDomain(escalation)
  }

  async findAll(): Promise<Escalation[]> {
    const escalations = await this.escalationModel.findAll({
      relations: {
        escalationSteps: {
          severity: true,
          contacts: true,
        },
        team: true,
      },
    })
    return escalations.map(this.toDomain)
  }

  async findById(id: Id): Promise<Nullable<Escalation>> {
    return this.escalationModel
      .findById(id, {
        relations: {
          escalationSteps: {
            severity: true,
            contacts: true,
          },
          team: true,
        },
      })
      .then((escalation) => {
        if (!escalation) {
          return null
        }

        return this.toDomain(escalation)
      })
  }

  async create(escalation: Escalation): Promise<Escalation> {
    const escalationProps = escalation.getProps()

    const escalationStepsWithSeverity = await Promise.all(
      escalationProps.escalationSteps.map(async (step) => {
        const queriedSeverity = await this.severityModel.findById(step.severity)
        if (!queriedSeverity) {
          throw new Error(`Invalid Severity ID: ${step.severity}`)
        }
        const contactsWithDetails = step.contacts.map((contact) => ({
          id: generateId(),
          contactType: contact.contactType,
          contactId: contact.contactId,
        }))

        return {
          id: generateId(),
          contacts: contactsWithDetails,
          stepDelay: step.stepDelay,
          severity: {
            id: queriedSeverity.id,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      }),
    )

    console.log(escalationStepsWithSeverity)
    const newEscalation = await this.escalationModel
      .create({
        ...escalationProps,
        escalationSteps: escalationStepsWithSeverity,
        team: {
          id: escalationProps.team?.id,
        },
      })
      .then(this.toDomain)

    // escalation.publishEvents(this.eventEmitter)
    console.log(newEscalation)
    return newEscalation
  }

  async updateById(
    id: Id,
    escalation: Partial<EscalationProps>,
  ): Promise<Nullable<Escalation>> {
    const currentEscalation = await this.escalationModel.findById(id, {
      relations: {
        escalationSteps: {
          contacts: true, // Ensure we fetch the contacts for the steps
          severity: true,
        },
        team: true,
      },
    })
    if (!currentEscalation) return null
    // await this.escalationModel.updateById(id, {
    //   escalationSteps: [],
    // })
    for (const step of currentEscalation.escalationSteps) {
      await this.database
        .typeorm<EscalationContactModel>('EscalationContactModel')
        .delete({ escalationStep: { id: step.id } })
    }
    await this.database
      .typeorm<EscalationStepModel>('EscalationStepModel')
      .delete({ escalation: { id } })

    const newEscalationSteps = await Promise.all(
      escalation.escalationSteps?.map(async (step) => {
        const queriedSeverity = await this.severityModel.findById(step.severity)
        if (!queriedSeverity)
          throw new Error(`Invalid Severity ID: ${step.severity}`)
        const contactsWithDetails = step.contacts.map((contact) => ({
          id: generateId(),
          contactType: contact.contactType,
          contactId: contact.contactId,
        }))
        return {
          id: generateId(),
          stepDelay: step.stepDelay,
          severity: queriedSeverity,
          contacts: contactsWithDetails,
        }
      }) || [],
    )

    const updatedEscalation = await this.escalationModel.updateById(id, {
      ...escalation,
      escalationSteps: newEscalationSteps,
      team: {
        id: escalation.team?.id,
      },
    })

    if (!updatedEscalation) return null

    const entityEscalation = this.toDomain(updatedEscalation)

    return entityEscalation
  }

  async delete(id: Id): Promise<boolean> {
    await this.escalationModel.softDelete({
      id,
    })
    return true
  }
}
