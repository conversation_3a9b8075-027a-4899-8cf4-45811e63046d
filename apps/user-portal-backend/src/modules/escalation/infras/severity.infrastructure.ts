import { Inject, Injectable } from '@nestjs/common'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { SeverityModel } from '@backend/frameworks/database/models/severity.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { Team } from '@backend/modules/user/entities'

import SeverityRepository from '../applications/severity.repository'
import { Severity, SeverityProps } from '../entities/severity.entity'
import { SeverityAlert } from '../constants/severity'

@Injectable()
export class SeverityInfrastructure implements SeverityRepository {
  private readonly severityModel: TypeORMDriver<SeverityModel>
  constructor(@Inject(Database) private database: Database) {
    this.severityModel = this.database.typeorm<SeverityModel>('SeverityModel')
  }

  toDomain = (severity: SeverityModel): Severity => {
    const { id, createdAt, updatedAt, team, ...props } = severity
    return new Severity({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        alerts: props.alerts as SeverityAlert[],
        team: new Team({ id: team?.id }),
      },
    })
  }

  async find(
    query: ITypeOrmFilter<SeverityModel>,
  ): Promise<IPaginate<Severity>> {
    return this.severityModel
      .find({
        ...query,
        relations: {
          team: true,
        },
      })
      .then((severities) => {
        return {
          ...severities,
          data: severities.data.map(this.toDomain),
        }
      })
  }

  async findOne(
    query: ITypeOrmFilter<SeverityModel>,
  ): Promise<Nullable<Severity>> {
    const severity = await this.severityModel.findOne({
      ...query,
      relations: {
        team: true,
      },
    })

    if (!severity) return null

    return this.toDomain(severity)
  }

  async findAll(query: ITypeOrmFilter<SeverityModel>): Promise<Severity[]> {
    const severities = await this.severityModel.findAll({
      ...query,
      relations: {
        team: true,
      },
    })

    return severities.map(this.toDomain)
  }

  async findById(id: Id): Promise<Nullable<Severity>> {
    return this.severityModel
      .findById(id, {
        relations: {
          team: true,
        },
      })
      .then((severity) => {
        if (!severity) {
          return null
        }
        return this.toDomain(severity)
      })
  }

  async create(severity: Severity): Promise<Severity> {
    const severityProps = severity.getProps()
    const newSeverity = await this.severityModel
      .create({
        ...severityProps,
        team: {
          id: severityProps.team?.id,
        },
      })
      .then(this.toDomain)

    return newSeverity
  }

  async updateById(
    id: Id,
    severity: Partial<SeverityProps>,
  ): Promise<Nullable<Severity>> {
    const currentSeverity = await this.severityModel.findById(id, {
      relations: {
        team: true,
      },
    })
    if (!currentSeverity) return null

    const updatedSeverity = await this.severityModel.updateById(id, {
      ...severity,
      team: {
        id: severity.team?.id,
      },
    })

    if (!updatedSeverity) return null

    const entitySeverity = this.toDomain(updatedSeverity)

    return entitySeverity
  }

  async delete(id: Id): Promise<boolean> {
    await this.severityModel.softDelete({
      id,
    })
    return true
  }
}
