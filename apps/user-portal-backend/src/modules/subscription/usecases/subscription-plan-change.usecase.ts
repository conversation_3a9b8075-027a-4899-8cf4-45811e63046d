import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import {
  OveragePolicyType,
  ResourceType,
  SubscriptionStatus,
} from '@libs/shared/constants/subscription'
import {
  OverageCalculationInput as FlexibleOverageCalculationInput,
  ResourceUsageMap,
} from '@libs/shared/interfaces/overage.interface'

import { Id } from '@backend/cores/base/id.type'
import {
  PlanType,
  Subscription,
  SubscriptionItem,
  SubscriptionPlan,
} from '@backend/modules/subscription/entities'
import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import SubscriptionItemRepository, {
  SUBSCRIPTION_ITEM_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-item.repository'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import OrganizationRepository, {
  ORGANIZATION_REPOSITORY,
} from '@backend/modules/user/applications/organization.repository'
import { ChangePlanResponseDto } from '@backend/modules/subscription/applications/dto/change-plan.dto'
import { ChangePlanPreviewResponseDto } from '@backend/modules/subscription/applications/dto/change-plan-preview.dto'
import { PlanOverageService } from '@backend/modules/subscription/services/plan-overage.service'
import { OverageQuantityService } from '@backend/modules/subscription/services/overage-quantity.service'
import { StripeIntegrationHelper } from '@backend/modules/subscription/services/stripe-integration-helper.service'
import { SubscriptionDetailsUseCase } from '@backend/modules/subscription/usecases/subscription-details.usecase'

import { PlanSource } from '../types/subscription-plan.types'

/**
 * SubscriptionPlanChangeUseCase handles plan changes, previews, and cancellations.
 * This is the most complex use case containing sophisticated business logic for
 * plan transitions, overage calculations, and billing adjustments.
 */
@Injectable()
export class SubscriptionPlanChangeUseCase {
  private readonly logger = new Logger(SubscriptionPlanChangeUseCase.name)

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_ITEM_REPOSITORY)
    private readonly subscriptionItemRepository: SubscriptionItemRepository,
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
    @Inject(ORGANIZATION_REPOSITORY)
    private readonly organizationRepository: OrganizationRepository,
    private readonly planOverageService: PlanOverageService,
    private readonly overageQuantityService: OverageQuantityService,
    private readonly stripeIntegrationHelper: StripeIntegrationHelper,
    private readonly subscriptionDetailsUseCase: SubscriptionDetailsUseCase,
  ) {}

  /**
   * Initiates a plan change with immediate overage calculation (PRD FR3.6.3).
   * @param userId - The user initiating the plan change.
   * @param organizationId - The organization's ID.
   * @param newPlanId - The new SubscriptionPlan ID to change to.
   */
  async initiatePlanChange(
    userId: Id,
    organizationId: string,
    newPlanId: string,
  ): Promise<ChangePlanResponseDto> {
    this.logger.log(
      `[SubscriptionPlanChangeUseCase] Initiating plan change for org ${organizationId} to plan ${newPlanId}`,
    )

    // 1. Validate user and organization
    await this.validateUserOrganizationOwnership(userId, organizationId)

    // 2. Get and validate current subscription
    const { currentSubscription, currentBasePlanItem, currentPlan } =
      await this.getCurrentSubscriptionDetails(organizationId)

    // 3. Get and validate new plan
    const newPlan = await this.validateNewPlan(
      newPlanId,
      currentPlan.id.toString(),
    )

    const newPlanProps = newPlan.getProps()
    const subscriptionProps = currentSubscription.getProps()

    // 4. Use unified calculation function for consistency with preview API
    const calculationResult = await this.calculatePlanChangeCharges({
      organizationId,
      currentSubscription,
      currentPlan,
      newPlan,
      currentBasePlanItem,
    })

    const { hasOverage } = calculationResult
    const overageReported: { [key: string]: number } = {}

    try {
      // 5. Handle overage calculation and reporting
      if (hasOverage) {
        const overageResult = await this.calculateAndReportOverages(
          organizationId,
          currentPlan,
          currentBasePlanItem,
          {
            currentPeriodStartDate:
              subscriptionProps.currentPeriodStartDate || new Date(),
            currentPeriodEndDate:
              subscriptionProps.currentPeriodEndDate || new Date(),
          },
          userId,
        )

        // 6. Update metered usage quantities in Stripe (always do this for accurate proration)
        this.logger.log(
          `Updating metered usage quantities in Stripe before plan change for org ${organizationId}`,
        )

        const updateResult =
          await this.overageQuantityService.updateOverageQuantitiesForOrganization(
            organizationId,
            currentSubscription.id,
            subscriptionProps.overagePolicyFixedResources ||
              OveragePolicyType.BLOCK,
          )

        if (updateResult.success) {
          this.logger.log(
            `Successfully updated usage quantities for plan change. Updated ${
              Object.keys(updateResult.updatedQuantities).length
            } resource types`,
          )

          // Store for response - convert quantity map to response format
          for (const [resourceType, quantity] of Object.entries(
            updateResult.updatedQuantities,
          )) {
            const resourceName = resourceType.toLowerCase()
            overageReported[resourceName] = quantity
          }
        } else {
          this.logger.warn(
            `Plan change usage quantity update had errors: ${updateResult.errors.length} errors`,
            { errors: updateResult.errors },
          )

          // For plan changes, we should still report individual overages even if Stripe update failed
          for (const resourceOverage of overageResult.getAllOverages()) {
            const resourceName = resourceOverage.resourceType.toLowerCase()
            overageReported[resourceName] = resourceOverage.overageAmount
          }
        }
      }

      // 7. Update Stripe subscription with only base plan change
      await this.stripeIntegrationHelper.changeSubscriptionPlan(
        subscriptionProps.stripeSubscriptionId,
        currentBasePlanItem.getProps().stripeSubscriptionItemId as string,
        newPlanProps.stripePriceId || '',
      )

      // 8. Create new subscription with PENDING status (correct flow per diagram)
      const newSubscription = await this.createNewSubscription(
        userId,
        {
          organizationId: subscriptionProps.organizationId,
          stripeSubscriptionId: subscriptionProps.stripeSubscriptionId,
          stripeCustomerId: subscriptionProps.stripeCustomerId,
        },
        newPlan,
      )

      // 9. Update current subscription to SUPERSEDED status
      const updatedSubscriptionProps = {
        ...subscriptionProps,
        status: SubscriptionStatus.SUPERSEDED,
        updatedBy: userId.toString(),
      }

      await this.subscriptionRepository.updateById(
        currentSubscription.id,
        updatedSubscriptionProps,
      )

      // 10. Store plan change metadata for webhook processing
      await this.storePlanChangeMetadata(
        newSubscription.id.toString(),
        newPlan.id.toString(),
        userId.toString(),
      )

      this.logger.log(
        `Successfully initiated plan change for org ${organizationId} from plan ${currentPlan.id} to ${newPlanId}. Created new subscription ${newSubscription.id} with PENDING status and marked old subscription ${currentSubscription.id} as SUPERSEDED.`,
      )

      return {
        subscriptionId: newSubscription.id.toString(), // Return new subscription ID
        newPlanId: newPlanId,
        previousPlanId: currentPlan.id.toString(),
        overageCharges: {
          // Pure dynamic overage charges for all resource types
          ...overageReported,
        },
        message: hasOverage
          ? 'Plan change initiated. Overage usage updated in Stripe. Awaiting payment confirmation.'
          : 'Plan change initiated. Awaiting payment confirmation.',
        changedAt: new Date().toISOString(),
      }
    } catch (error) {
      this.logger.error(
        `Failed to initiate plan change for org ${organizationId}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        'Failed to initiate plan change.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Previews a plan change without making any modifications.
   * Calculates overage charges and proration amounts.
   * @param userId - The user requesting the preview.
   * @param organizationId - The organization's ID.
   * @param newPlanId - The new SubscriptionPlan ID to preview change to.
   * @returns Preview details including charges and proration
   */
  async changePlanPreview(
    userId: Id,
    organizationId: string,
    newPlanId: string,
  ): Promise<ChangePlanPreviewResponseDto> {
    this.logger.log(
      `[SubscriptionPlanChangeUseCase] Previewing plan change for org ${organizationId} to plan ${newPlanId}`,
    )

    // 1. Validate user and organization (same as initiatePlanChange)
    await this.validateUserOrganizationOwnership(userId, organizationId)

    // 2. Get and validate current subscription
    const { currentSubscription, currentBasePlanItem, currentPlan } =
      await this.getCurrentSubscriptionDetails(organizationId)

    // 3. Get and validate new plan
    const newPlan = await this.validateNewPlan(
      newPlanId,
      currentPlan.id.toString(),
    )

    const currentPlanProps = currentPlan.getProps()
    const newPlanProps = newPlan.getProps()

    // 4. Use unified calculation function
    const calculationResult = await this.calculatePlanChangeCharges({
      organizationId,
      currentSubscription,
      currentPlan,
      newPlan,
      currentBasePlanItem,
    })

    const {
      overageCharges,
      totalOverageAmount,
      proratedCredit,
      newPlanCharge,
      nextInvoiceDate,
      nextInvoiceTotal,
      currency,
    } = calculationResult

    // 5. Calculate total immediate charges for billing cycle reset
    const totalAmountDue = Math.max(
      0,
      -proratedCredit + newPlanCharge + totalOverageAmount,
    )

    // 6. Create summary message for billing cycle reset
    // TODO: Fetch actual pricing from Stripe using stripePriceId
    const summaryMessage = `Switching from ${currentPlanProps.name} to ${newPlanProps.name}. You will be charged $${(totalAmountDue / 100).toFixed(2)} today with billing cycle reset.`

    // 7. Return the preview response
    return {
      immediateCharges: {
        proratedCredit,
        newPlanCharge,
        overageCharges,
        totalAmountDue,
        currency,
      },
      nextInvoice: {
        date: nextInvoiceDate,
        amount: nextInvoiceTotal,
        currency,
      },
      summaryMessage,
    }
  }

  /**
   * Cancels a subscription at period end (PRD FR2.2.3).
   * @param userId - The user initiating the cancellation.
   * @param organizationId - The organization's ID.
   */
  async cancelSubscription(
    userId: Id,
    organizationId: string,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(
      `[SubscriptionPlanChangeUseCase] Canceling subscription for org ${organizationId}`,
    )

    // 1. Validate user and organization
    await this.validateUserOrganizationOwnership(userId, organizationId)

    // 2. Retrieve current subscription
    const subscription = await this.subscriptionRepository.findOne({
      filter: {
        organizationId,
        status: SubscriptionStatus.ACTIVE,
      },
    })

    if (!subscription) {
      throw new NotFoundException(
        `No active subscription found for organization ${organizationId}.`,
      )
    }

    const subscriptionProps = subscription.getProps()
    if (!subscriptionProps.stripeSubscriptionId) {
      throw new NotFoundException(
        'No Stripe subscription ID found for this subscription.',
      )
    }

    try {
      // 3. Cancel Stripe subscription at period end
      await this.stripeIntegrationHelper.cancelSubscriptionAtPeriodEnd(
        subscriptionProps.stripeSubscriptionId,
      )

      this.logger.log(
        `Successfully initiated cancellation for subscription ${subscription.id} (Stripe: ${subscriptionProps.stripeSubscriptionId})`,
      )

      return {
        success: true,
        message:
          'Subscription will be canceled at the end of the current billing period.',
      }
    } catch (error) {
      this.logger.error(
        `Failed to cancel subscription for org ${organizationId}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        'Failed to cancel subscription.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Unified plan change calculation logic used by both preview and actual plan change APIs
   * @param params - Parameters for plan change calculation
   * @returns Calculated charges including overages and proration
   */
  async calculatePlanChangeCharges(params: {
    organizationId: string
    currentSubscription: Subscription
    currentPlan: SubscriptionPlan
    newPlan: SubscriptionPlan
    currentBasePlanItem: SubscriptionItem
  }): Promise<{
    overageCharges: { [key: string]: number }
    totalOverageAmount: number
    hasOverage: boolean
    proratedCredit: number
    newPlanCharge: number
    nextInvoiceDate: string
    nextInvoiceTotal: number
    currency: string
  }> {
    this.logger.log(
      `[CalculatePlanChangeCharges] Starting calculation for org ${params.organizationId}`,
    )

    // 1. Calculate current usage
    const currentUsageRaw =
      await this.subscriptionDetailsUseCase.getCurrentUsageByOrganization(
        params.organizationId,
      )

    // Convert to ResourceUsageMap format
    const currentUsage: ResourceUsageMap = {
      [ResourceType.MEMBER]: currentUsageRaw.members,
      [ResourceType.TEAM]: currentUsageRaw.teams,
      [ResourceType.CHECK]: currentUsageRaw.checks,
      [ResourceType.INTEGRATION]: 0, // TODO: Get actual integration count
      [ResourceType.STATUS_PAGE]: 0, // TODO: Get actual status page count
    }

    // 2. Calculate overages using existing service
    const subscriptionProps = params.currentSubscription.getProps()
    const dynamicOverageInput: FlexibleOverageCalculationInput = {
      plan: params.currentPlan,
      currentUsage,
      billingPeriod: {
        startDate: subscriptionProps.currentPeriodStartDate || new Date(),
        endDate: subscriptionProps.currentPeriodEndDate || new Date(),
      },
      organizationId: params.organizationId,
      subscriptionItemId: params.currentBasePlanItem.id,
    }

    const overageResult =
      this.planOverageService.calculateFlexibleOverageForPlan(
        dynamicOverageInput,
      )

    // 3. Calculate monetary overage amounts using actual Stripe prices
    const overageCharges: { [key: string]: number } = {}
    let totalOverageAmount = 0
    let hasOverage = false

    if (overageResult.hasOverage) {
      hasOverage = true

      // Collect all overage Stripe price IDs
      const stripePriceIds = overageResult
        .getAllOverages()
        .map((o) => o.stripePriceId)
        .filter((id): id is string => !!id)

      // Fetch actual Stripe prices
      const stripePrices =
        await this.stripeIntegrationHelper.getStripePrices(stripePriceIds)

      // Calculate monetary amounts using actual Stripe price data
      for (const resourceOverage of overageResult.getAllOverages()) {
        const resourceName = resourceOverage.resourceType.toLowerCase()
        const overageQuantity = resourceOverage.overageAmount
        const stripePriceId = resourceOverage.stripePriceId

        if (!stripePriceId) continue

        const stripePrice = stripePrices.get(stripePriceId)

        if (stripePrice && stripePrice.unit_amount) {
          // Calculate actual monetary amount (Stripe amounts are in cents)
          const monetaryAmount =
            (overageQuantity * stripePrice.unit_amount) / 100
          overageCharges[resourceName] = monetaryAmount
          totalOverageAmount += monetaryAmount

          this.logger.log(
            `Calculated overage for ${resourceName}: ${overageQuantity} units × $${
              stripePrice.unit_amount / 100
            } = $${monetaryAmount}`,
          )
        } else {
          this.logger.warn(
            `No valid Stripe price found for ${resourceName} overage (price ID: ${resourceOverage.stripePriceId})`,
          )
        }
      }
    }

    // 4. Get proration preview with billing cycle reset
    let proratedCredit = 0
    let newPlanCharge = 0
    let nextInvoiceDate = new Date().toISOString()
    let nextInvoiceTotal = 0
    let currency = 'usd' // Default fallback currency

    try {
      const stripePreview =
        await this.stripeIntegrationHelper.previewPlanChange(
          params.currentSubscription,
          params.newPlan.getProps().stripePriceId || '',
        )

      proratedCredit = stripePreview.proratedCredit
      newPlanCharge = stripePreview.newPlanCharge
      nextInvoiceDate = stripePreview.nextInvoiceDate
      nextInvoiceTotal = stripePreview.nextInvoiceTotal
      currency = stripePreview.currency
    } catch (error) {
      this.logger.error(
        `Failed to get Stripe preview: ${error.message}`,
        error.stack,
      )
      // Continue with estimated values if Stripe preview fails
      // TODO: Fetch actual pricing from Stripe using stripePriceId
      newPlanCharge = 0 // Will be calculated by manual preview
      nextInvoiceTotal = newPlanCharge
      // currency remains 'usd' as fallback
    }

    this.logger.log(
      `[CalculatePlanChangeCharges] Completed calculation: hasOverage=${hasOverage}, totalOverage=$${totalOverageAmount}, proratedCredit=$${proratedCredit}, newPlanCharge=$${newPlanCharge}`,
    )

    return {
      overageCharges,
      totalOverageAmount,
      hasOverage,
      proratedCredit,
      newPlanCharge,
      nextInvoiceDate,
      nextInvoiceTotal,
      currency,
    }
  }

  /**
   * Validates that the user is the owner of the organization
   */
  private async validateUserOrganizationOwnership(
    userId: Id,
    organizationId: string,
  ): Promise<void> {
    const organization =
      await this.organizationRepository.findById(organizationId)
    if (!organization || organization.getProps().ownerId !== userId) {
      throw new HttpException(
        'User is not an owner of Organization.',
        HttpStatus.BAD_REQUEST,
      )
    }
  }

  /**
   * Gets current subscription details with validation
   */
  private async getCurrentSubscriptionDetails(organizationId: string): Promise<{
    currentSubscription: Subscription
    currentBasePlanItem: SubscriptionItem
    currentPlan: SubscriptionPlan
  }> {
    const currentSubscription = await this.subscriptionRepository.findOne({
      filter: {
        organizationId,
        status: SubscriptionStatus.ACTIVE,
      },
    })

    if (!currentSubscription) {
      throw new NotFoundException(
        `No active subscription found for organization ${organizationId}.`,
      )
    }

    // Validate subscription is in a valid state for plan changes
    const subscriptionStatus = currentSubscription.getProps().status
    if (
      subscriptionStatus === SubscriptionStatus.CANCELED ||
      subscriptionStatus === SubscriptionStatus.UNPAID ||
      subscriptionStatus === SubscriptionStatus.INCOMPLETE_EXPIRED
    ) {
      throw new HttpException(
        `Cannot change plan. Subscription is in ${subscriptionStatus} status.`,
        HttpStatus.BAD_REQUEST,
      )
    }

    // Get current subscription items
    const currentSubscriptionItemsResult =
      await this.subscriptionItemRepository.find({
        filter: {
          subscriptionId: currentSubscription.id,
          status: SubscriptionStatus.ACTIVE,
        },
      })
    const currentSubscriptionItems = currentSubscriptionItemsResult.data

    const currentBasePlanItem = currentSubscriptionItems.find(
      (item) =>
        item.getProps().itemType === PlanType.BASE_PLAN &&
        item.getProps().stripeSubscriptionItemId,
    )

    if (!currentBasePlanItem) {
      throw new NotFoundException(
        'No active base plan found for current subscription.',
      )
    }

    const currentPlan =
      await this.subscriptionPlanRepository.findByIdWithLimits(
        currentBasePlanItem.getProps().subscriptionPlanId,
      )

    if (!currentPlan) {
      throw new NotFoundException('Current subscription plan not found.')
    }

    return {
      currentSubscription,
      currentBasePlanItem,
      currentPlan,
    }
  }

  /**
   * Validates the new plan for plan change
   */
  private async validateNewPlan(
    newPlanId: string,
    currentPlanId: string,
  ): Promise<SubscriptionPlan> {
    const newPlan =
      await this.subscriptionPlanRepository.findByIdWithLimits(newPlanId)
    if (!newPlan || !newPlan.getProps().isActive) {
      throw new NotFoundException(
        'New subscription plan not found or inactive.',
      )
    }

    // Check if trying to change to the same plan
    if (currentPlanId === newPlanId) {
      throw new HttpException(
        'Cannot change to the same plan. Organization is already on this plan.',
        HttpStatus.BAD_REQUEST,
      )
    }

    return newPlan
  }

  /**
   * Calculates and reports overages for plan change
   */
  private async calculateAndReportOverages(
    organizationId: string,
    currentPlan: SubscriptionPlan,
    currentBasePlanItem: SubscriptionItem,
    subscriptionProps: {
      currentPeriodStartDate: Date
      currentPeriodEndDate: Date
    },
    userId: Id,
  ) {
    // Calculate current usage vs limits using dynamic resource limits
    const currentUsageRaw =
      await this.subscriptionDetailsUseCase.getCurrentUsageByOrganization(
        organizationId,
      )

    // Convert to ResourceUsageMap format for dynamic calculation
    const currentUsage: ResourceUsageMap = {
      [ResourceType.MEMBER]: currentUsageRaw.members,
      [ResourceType.TEAM]: currentUsageRaw.teams,
      [ResourceType.CHECK]: currentUsageRaw.checks,
      [ResourceType.INTEGRATION]: 0, // TODO: Get actual integration count
      [ResourceType.STATUS_PAGE]: 0, // TODO: Get actual status page count
    }

    // Use the dynamic overage service for calculation and logging
    const dynamicOverageInput: FlexibleOverageCalculationInput = {
      plan: currentPlan,
      currentUsage,
      billingPeriod: {
        startDate: subscriptionProps.currentPeriodStartDate,
        endDate: subscriptionProps.currentPeriodEndDate,
      },
      organizationId,
      subscriptionItemId: currentBasePlanItem.id,
    }

    const overageResult =
      await this.planOverageService.calculateAndLogFlexibleOverageEvents(
        dynamicOverageInput,
        {
          startDate: subscriptionProps.currentPeriodStartDate,
          endDate: subscriptionProps.currentPeriodEndDate,
        },
        currentBasePlanItem.id,
        userId,
      )

    this.logger.log(
      `[DYNAMIC] Detected overage for org ${organizationId}: ${overageResult.getTotalOverageCount()} total across ${
        overageResult.getResourceTypesWithOverage().length
      } resource types`,
    )

    return overageResult
  }

  /**
   * Creates new subscription with PENDING status for plan change
   */
  private async createNewSubscription(
    userId: Id,
    subscriptionProps: {
      organizationId: string
      stripeSubscriptionId: string
      stripeCustomerId: string
    },
    newPlan: SubscriptionPlan,
  ): Promise<Subscription> {
    this.logger.log(
      `Creating new subscription with PENDING status for plan ${newPlan.id}`,
    )

    // Get new plan properties for subscription configuration
    const now = new Date()

    // Calculate new billing period (inherit from current subscription periods)
    const currentPeriodStartDate = now
    const currentPeriodEndDate = new Date(now)
    currentPeriodEndDate.setMonth(currentPeriodEndDate.getMonth() + 1)

    // Create new subscription with PENDING status - everything based on new plan
    const newSubscription = Subscription.create({
      organizationId: subscriptionProps.organizationId,
      stripeSubscriptionId: subscriptionProps.stripeSubscriptionId, // Same Stripe subscription ID
      stripeCustomerId: subscriptionProps.stripeCustomerId,
      status: SubscriptionStatus.PENDING,
      currentPeriodStartDate,
      currentPeriodEndDate,
      gracePeriodEndsAt: null, // New subscription starts fresh without grace period
      usageBillingEnabled: true, // Enable usage billing for new plan
      overagePolicyFixedResources: OveragePolicyType.BLOCK, // Default policy for new plan
      canceledAt: null, // New subscription is not canceled
      createdBy: userId.toString(),
      updatedBy: userId.toString(),
      source: PlanSource.STRIPE,
    })

    const createdSubscription =
      await this.subscriptionRepository.create(newSubscription)

    // DO NOT create subscription items during initiation
    // Resource limits (metered items) will be created in webhook after payment confirmation
    // This ensures we don't create resources until payment succeeds

    this.logger.log(
      `Successfully created subscription ${createdSubscription.id} with PENDING status. Resource limit items will be created after payment confirmation in webhook.`,
    )

    return createdSubscription
  }

  /**
   * Stores plan change metadata for webhook processing
   * This can be implemented as a cache entry or database record
   * to help webhooks identify and process plan changes correctly
   */
  private async storePlanChangeMetadata(
    subscriptionId: string,
    newPlanId: string,
    userId: string,
  ): Promise<void> {
    // TODO: Store plan change metadata in Redis or a dedicated table
    // This metadata will be used by webhook handlers to:
    // 1. Identify plan change events
    // 2. Apply new resource limits
    // 3. Handle subscription item transitions

    this.logger.log(
      `Storing plan change metadata for subscription ${subscriptionId}: newPlanId=${newPlanId}, userId=${userId}`,
    )

    // For now, we'll use logging. In a full implementation, this would store:
    // {
    //   subscriptionId,
    //   newPlanId,
    //   userId,
    //   timestamp: new Date(),
    //   status: 'pending'
    // }
    // in Redis with an appropriate TTL or in a dedicated database table
  }
}
