import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import {
  SubscriptionStatus,
  OveragePolicyType,
} from '@libs/shared/constants/subscription'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import SubscriptionItemRepository, {
  SUBSCRIPTION_ITEM_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-item.repository'
import { CreateSubscriptionDto } from '@backend/modules/subscription/applications/dto/subscription.request.dto'
import { SubscriptionResponseDto } from '@backend/modules/subscription/applications/dto/subscription.response.dto'
import { CreateManualSubscriptionRequest } from '@backend/modules/subscription/applications/dto/create-manual-subscription.dto'
import {
  Subscription,
  SubscriptionPlan,
  SubscriptionItem,
} from '@backend/modules/subscription/entities'
import { PlanSource } from '@backend/modules/subscription/types/subscription-plan.types'
import { PlanType } from '@backend/modules/subscription/types/subscription-plan.types'

/**
 * SubscriptionManagementUseCase handles basic CRUD operations for subscriptions.
 * This is a focused use case that only deals with simple subscription management
 * without complex business logic like billing, plan changes, or Stripe integration.
 */
@Injectable()
export class SubscriptionManagementUseCase {
  private readonly logger = new Logger(SubscriptionManagementUseCase.name)

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
    @Inject(SUBSCRIPTION_ITEM_REPOSITORY)
    private readonly subscriptionItemRepository: SubscriptionItemRepository,
  ) {}

  /**
   * Finds subscriptions with pagination for an organization
   */
  async find(
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<SubscriptionResponseDto>> {
    const subscriptions = await this.subscriptionRepository.find({
      page: paginateOption.page,
      limit: paginateOption.limit,
      orderBy: {
        createdAt: 'DESC',
      },
    })

    return {
      data: subscriptions.data.map((sub) => sub.toResponse()),
      total: subscriptions.total,
      totalPage: subscriptions.totalPage,
      limit: subscriptions.limit,
      page: subscriptions.page,
    }
  }

  /**
   * Finds a single subscription by ID
   */
  async findOne(id: Id): Promise<SubscriptionResponseDto | null> {
    try {
      this.logger.log(`Finding subscription by ID: ${id}`)

      const entity = await this.subscriptionRepository.findById(id)

      if (!entity) {
        this.logger.log(`Subscription not found: ${id}`)
        return null
      }

      this.logger.log(`Found subscription: ${id}`)
      return entity.toResponse()
    } catch (error) {
      this.logger.error(
        `Failed to find subscription ${id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  async update(
    id: Id,
    updateSubscriptionDto: Partial<CreateSubscriptionDto>,
  ): Promise<SubscriptionResponseDto> {
    try {
      this.logger.log(`Updating subscription: ${id}`)

      const subscription = await this.subscriptionRepository.findById(id)

      if (!subscription) {
        throw new NotFoundException(`Subscription with ID ${id} not found.`)
      }

      // Perform the update
      const updatedSubscription = await this.subscriptionRepository.updateById(
        id,
        {
          ...subscription.getProps(),
          ...updateSubscriptionDto,
        },
      )

      if (!updatedSubscription) {
        throw new HttpException(
          'Failed to update subscription.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        )
      }

      this.logger.log(`Successfully updated subscription: ${id}`)
      return updatedSubscription.toResponse()
    } catch (error) {
      this.logger.error(
        `Failed to update subscription ${id}: ${error.message}`,
        error.stack,
      )
      if (error instanceof HttpException) throw error
      throw new HttpException(
        error.message || 'Failed to update subscription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  async findSubscriptionsByOrganizationId(
    organizationId: string,
  ): Promise<SubscriptionResponseDto[]> {
    try {
      const subscriptionsResult = await this.subscriptionRepository.find({
        filter: {
          organizationId,
        },
        relations: {
          subscriptionItems: true,
        },
      })
      const subscriptions = subscriptionsResult.data

      return (
        subscriptions?.map((subscription) => subscription.toResponse()) || []
      )
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to find subscriptions',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  async getFreePlan(): Promise<SubscriptionPlan | null> {
    try {
      const freePlan = await this.subscriptionPlanRepository.findOne({
        filter: {
          source: PlanSource.FREE,
        },
      })

      if (!freePlan) {
        this.logger.warn('Free plan not found in database')
        return null
      }

      const freePlanWithLimits =
        await this.subscriptionPlanRepository.findByIdWithLimits(freePlan.id)

      if (!freePlanWithLimits) {
        return freePlan
      }

      return freePlanWithLimits
    } catch (error) {
      this.logger.error(
        `Failed to retrieve free plan: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async assignFreePlanToOrganization(
    organizationId: string,
    userId: string,
  ): Promise<SubscriptionResponseDto | null> {
    try {
      const freePlan = await this.getFreePlan()
      if (!freePlan) {
        throw new Error('Free plan not available')
      }

      console.log('freePlan', freePlan)
      // Create free subscription directly
      const subscriptionEntity = Subscription.create({
        organizationId,
        stripeSubscriptionId: `${PlanSource.FREE}_${organizationId}_${Date.now()}`,
        stripeCustomerId: `${PlanSource.FREE}_customer_${organizationId}`,
        status: SubscriptionStatus.ACTIVE,
        currentPeriodStartDate: null,
        currentPeriodEndDate: null,
        usageBillingEnabled: false,
        overagePolicyFixedResources: OveragePolicyType.BLOCK,
        createdBy: userId,
        updatedBy: userId,
        source: PlanSource.FREE,
      })

      const createdSubscription =
        await this.subscriptionRepository.create(subscriptionEntity)

      if (!createdSubscription) {
        throw new HttpException(
          'Failed to create free subscription',
          HttpStatus.INTERNAL_SERVER_ERROR,
        )
      }

      await this.createSubscriptionItemsForPlan(
        createdSubscription,
        freePlan,
        userId,
        undefined,
        undefined,
      )

      return createdSubscription.toResponse()
    } catch (error) {
      return null
    }
  }

  async createManualSubscription(
    request: CreateManualSubscriptionRequest,
    userId: string,
  ): Promise<SubscriptionResponseDto> {
    try {
      const plan = await this.subscriptionPlanRepository.findByIdWithLimits(
        request.planId,
      )
      console.log('plan', plan)
      if (!plan) {
        throw new NotFoundException(
          `Subscription plan with ID ${request.planId} not found`,
        )
      }

      await this.validateAndHandleExistingSubscriptionForManual(
        request.organizationId || '',
        userId,
      )

      const now = new Date()
      const endDate = new Date()
      endDate.setFullYear(endDate.getFullYear() + 1)

      const subscriptionEntity = Subscription.create({
        organizationId: request.organizationId || '',
        stripeSubscriptionId: `${PlanSource.MANUAL}_${request.organizationId}_${Date.now()}`,
        stripeCustomerId: `${PlanSource.MANUAL}_customer_${request.organizationId}`,
        status: SubscriptionStatus.ACTIVE,
        currentPeriodStartDate: now,
        currentPeriodEndDate: endDate,
        usageBillingEnabled: false,
        overagePolicyFixedResources: OveragePolicyType.CHARGE,
        createdBy: userId,
        updatedBy: userId,
        source: PlanSource.MANUAL,
      })

      const createdSubscription =
        await this.subscriptionRepository.create(subscriptionEntity)

      if (!createdSubscription) {
        throw new HttpException(
          'Failed to create manual subscription',
          HttpStatus.INTERNAL_SERVER_ERROR,
        )
      }

      await this.createSubscriptionItemsForPlan(
        createdSubscription,
        plan,
        userId,
        now,
        endDate,
      )

      return createdSubscription.toResponse()
    } catch (error) {
      if (error instanceof HttpException) throw error
      throw new HttpException(
        error.message || 'Failed to create manual subscription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  private async validateAndHandleExistingSubscriptionForManual(
    organizationId: string,
    userId: string,
  ): Promise<void> {
    const existingSubscription = await this.subscriptionRepository.findOne({
      filter: {
        organizationId,
        status: SubscriptionStatus.ACTIVE,
      },
    })

    if (existingSubscription) {
      const canUpgrade = await this.canUpgradeFromFreeplan(existingSubscription)

      if (canUpgrade) {
        await this.cancelExistingSubscription(existingSubscription, userId)
        return
      }

      throw new HttpException(
        'Organization already has an active manual subscription',
        HttpStatus.CONFLICT,
      )
    }
  }

  private async canUpgradeFromFreeplan(
    subscription: Subscription,
  ): Promise<boolean> {
    try {
      const subscriptionProps = subscription.getProps()

      return subscriptionProps.source === PlanSource.FREE
    } catch (error) {
      throw new HttpException(
        'Failed to check if subscription can be upgraded',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  private async cancelExistingSubscription(
    subscription: Subscription,
    userId: string,
  ): Promise<void> {
    try {
      const subscriptionProps = subscription.getProps()
      await this.subscriptionRepository.updateById(subscription.id, {
        ...subscriptionProps,
        status: SubscriptionStatus.CANCELED,
        canceledAt: new Date(),
        updatedBy: userId,
      })
    } catch (error) {
      throw new HttpException(
        'Failed to cancel existing subscription for upgrade',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  private async createSubscriptionItemsForPlan(
    subscription: Subscription,
    plan: SubscriptionPlan,
    userId: string,
    startDate?: Date | undefined,
    endDate?: Date | undefined,
  ): Promise<void> {
    try {
      const planProps = plan.getProps()

      // Use current date for free plans if no dates provided
      const effectiveStartDate = startDate || new Date()
      const effectiveEndDate = endDate || new Date()

      // 1. Create base plan subscription item
      const subscriptionPrefix =
        planProps.source === PlanSource.FREE ? 'free' : 'manual'
      const basePlanItem = SubscriptionItem.create({
        subscriptionId: subscription.id,
        stripeSubscriptionItemId: `${subscriptionPrefix}_base_${subscription.id}`,
        stripePriceId:
          planProps.stripePriceId || `${subscriptionPrefix}-base-price`,
        subscriptionPlanId: plan.id,
        itemType: PlanType.BASE_PLAN,
        quantity: 1,
        status: SubscriptionStatus.ACTIVE,
        startDate: effectiveStartDate,
        endDate: effectiveEndDate,
        resourceType: null, // Base plan doesn't have specific resource type
        createdBy: userId,
        updatedBy: userId,
      })

      await this.subscriptionItemRepository.create(basePlanItem)

      // 2. Create overage items for each resource limit
      const resourceLimits = plan.getAllResourceLimits()
      console.log('resourceLimits', resourceLimits)

      console.log(
        `Creating subscription items for plan ${plan.id} with ${resourceLimits.length} resource limits`,
      )

      for (const resourceLimit of resourceLimits) {
        const limitProps = resourceLimit.getProps()

        console.log(
          `Creating overage item for resource type: ${limitProps.resourceType}`,
        )

        const subscriptionItem = SubscriptionItem.create({
          subscriptionId: subscription.id,
          stripeSubscriptionItemId: `${subscriptionPrefix}_overage_${subscription.id}_${limitProps.resourceType}`,
          stripePriceId:
            limitProps.overageStripePriceId ||
            `${subscriptionPrefix}-overage-${limitProps.resourceType.toLowerCase()}`,
          subscriptionPlanId: plan.id,
          itemType: PlanType.ADDON_METERED_USAGE,
          quantity: 0, // Overage items start with 0 quantity
          status: SubscriptionStatus.ACTIVE,
          startDate: effectiveStartDate,
          endDate: effectiveEndDate,
          resourceType: limitProps.resourceType,
          createdBy: userId,
          updatedBy: userId,
        })

        await this.subscriptionItemRepository.create(subscriptionItem)

        console.log(
          `Successfully created overage item for ${limitProps.resourceType}`,
        )
      }

      console.log(
        `Successfully created ${resourceLimits.length + 1} subscription items for plan ${plan.id}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to create subscription items for plan ${plan.id}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        error.message || 'Failed to create subscription items for plan',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }
}
