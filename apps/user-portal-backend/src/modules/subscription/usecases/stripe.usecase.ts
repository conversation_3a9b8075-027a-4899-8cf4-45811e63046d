import { Inject, Injectable, Logger } from '@nestjs/common'
import Stripe from 'stripe'
import {
  SubscriptionStatus,
  PlanType,
  ResourceType,
} from '@libs/shared/constants/subscription'
import { OveragePolicyType } from '@libs/shared/constants/subscription'

import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import {
  Subscription,
  SubscriptionItem,
  SubscriptionPlan,
} from '@backend/modules/subscription/entities'
import { Id } from '@backend/cores/base/id.type'
import { configService } from '@backend/cores/config/config.service'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import SubscriptionUsageRepository, {
  SUBSCRIPTION_USAGE_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-usage.repository'
import SubscriptionItemRepository, {
  SUBSCRIPTION_ITEM_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-item.repository'
import { Operator } from '@backend/frameworks/database/constants/operators'

import { CreateCustomerSessionDto } from '../applications/dto/create-customer-session.dto'
import { PlanOverageService } from '../services/plan-overage.service'
import { PlanSource } from '../types/subscription-plan.types'

export interface PlanChangeEvent {
  subscriptionId: string
  organizationId: string
  previousPlanIds: string[]
  currentPlanIds: string[]
  hasItemChanges: boolean
  changedItems: Array<{
    itemId: string
    priceId: string
    change: 'added' | 'removed' | 'modified'
    previousQuantity?: number
    currentQuantity?: number
  }>
}

export interface PlanChangePreviewResult {
  proratedCredit: number // amount in cents (refund for unused current plan time)
  newPlanCharge: number // amount in cents (full new plan price)
  nextInvoiceDate: string // ISO 8601 string (new billing cycle date)
  nextInvoiceTotal: number // amount in cents (regular monthly price)
  currency: string // ISO 4217 currency code from Stripe (e.g., 'usd', 'eur')
}

@Injectable()
export class StripeUseCase {
  private readonly logger = new Logger(StripeUseCase.name)
  private readonly stripe: Stripe

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_ITEM_REPOSITORY)
    private readonly subscriptionItemRepository: SubscriptionItemRepository,
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
    @Inject(SUBSCRIPTION_USAGE_REPOSITORY)
    private readonly subscriptionUsageRepository: SubscriptionUsageRepository,
    private readonly planOverageService: PlanOverageService,
  ) {
    // Initialize Stripe client
    this.stripe = new Stripe(configService.getValue('STRIPE_SECRET_KEY'), {
      apiVersion: '2025-02-24.acacia',
      typescript: true,
    })
    this.logger.log('StripeService initialized')
  }

  constructWebhookEvent(payload: Buffer, signature: string): Stripe.Event {
    const secret = configService.getValue('STRIPE_WEBHOOK_SECRET')
    try {
      return this.stripe.webhooks.constructEvent(payload, signature, secret)
    } catch (err) {
      this.logger.error(`Error constructing webhook event: ${err.message}`)
      throw err
    }
  }

  /**
   * Handles invoice.paid webhook - Update subscription status and clear grace period (PRD FR2.7.5).
   */
  async handleInvoicePaid(invoice: Stripe.Invoice): Promise<void> {
    this.logger.log(`[StripeService] Handling invoice.paid: ${invoice.id}`)

    // 1. Validate invoice data
    const { stripeSubscriptionId, customerId } =
      this.validateInvoiceData(invoice)
    if (!stripeSubscriptionId || !customerId) return

    this.logger.log(
      `Processing invoice.paid for Subscription ID: ${stripeSubscriptionId}, Customer ID: ${customerId}`,
    )

    // 2. Find subscription
    const subscription = await this.findSubscriptionByStripeId(
      stripeSubscriptionId,
      customerId,
    )

    if (!subscription) {
      this.logger.warn(
        `Received invoice.paid for unknown subscription ${stripeSubscriptionId}. Might be processed later by checkout.session.completed.`,
      )
      return
    }

    // 3. Route to appropriate handler based on subscription status
    const subscriptionProps = subscription.getProps()
    if (subscriptionProps.status === SubscriptionStatus.PENDING) {
      await this.processPlanChangePayment(subscription, stripeSubscriptionId)
    } else {
      await this.processRegularPayment(subscription, invoice.id)
    }
  }

  /**
   * Handles invoice.payment_failed webhook - Set past_due status and grace period (PRD FR2.2.7, FR2.7.5).
   */
  async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    this.logger.log(
      `[StripeService] Handling invoice.payment_failed: ${invoice.id}`,
    )

    // 1. Validate invoice data
    const { stripeSubscriptionId, customerId } =
      this.validateInvoiceData(invoice)
    if (!stripeSubscriptionId || !customerId) return

    this.logger.log(
      `Processing invoice.payment_failed for Subscription ID: ${stripeSubscriptionId}, Customer ID: ${customerId}`,
    )

    // 2. Find subscription
    const subscription = await this.findSubscriptionByStripeId(
      stripeSubscriptionId,
      customerId,
    )

    if (!subscription) {
      this.logger.warn(
        `Received invoice.payment_failed for unknown subscription ${stripeSubscriptionId}`,
      )
      return
    }

    // 3. Route to appropriate handler based on subscription status
    const subscriptionProps = subscription.getProps()
    if (subscriptionProps.status === SubscriptionStatus.PENDING) {
      await this.processPlanChangeRollback(subscription, subscriptionProps)
    } else {
      await this.processRegularPaymentFailure(
        subscription,
        subscriptionProps,
        invoice.id,
      )
    }
  }

  /**
   * Handles customer.subscription.updated webhook - Sync all entities, handle plan changes (PRD FR2.7.5).
   */
  async handleCustomerSubscriptionUpdated(
    stripeSubscription: Stripe.Subscription,
    previousAttributes?: Partial<Stripe.Subscription>,
  ): Promise<void> {
    this.logger.log(
      `[StripeService] Handling customer.subscription.updated: ${stripeSubscription.id}`,
    )
    const customerId = stripeSubscription.customer

    if (typeof customerId !== 'string') {
      this.logger.error(
        `Updated Subscription ${stripeSubscription.id} is missing customer ID.`,
      )
      return
    }

    // 1. Find the latest active subscription in DB using stripeSubscriptionId
    const subscription = await this.findSubscriptionByStripeId(
      stripeSubscription.id,
      customerId,
    )

    if (!subscription) {
      this.logger.warn(
        `Received customer.subscription.updated for unknown subscription ${stripeSubscription.id}`,
      )
      return
    }

    try {
      // 2. Detect plan changes before updating subscription
      const planChangeEvent = this.detectPlanChange(
        stripeSubscription,
        previousAttributes,
      )

      if (planChangeEvent) {
        this.logger.log(
          `Plan change detected for subscription ${subscription.id}. ` +
            `Previous plans: [${planChangeEvent.previousPlanIds.join(', ')}], ` +
            `Current plans: [${planChangeEvent.currentPlanIds.join(', ')}]. ` +
            `Overage calculations were already handled during plan change initiation.`,
        )

        // Note: Overage calculations are already handled in SubscriptionPlanChangeUseCase.initiatePlanChange()
        // before the plan change is sent to Stripe. This webhook is just confirming the change.
        // No additional overage calculation needed here to avoid duplication.
      }

      // 3. Handle subscription status update and syncing
      await this.handleSubscriptionStatusUpdate(
        subscription,
        stripeSubscription,
      )
    } catch (error) {
      this.logger.error(
        `Failed to update subscription ${subscription.id} for customer.subscription.updated event: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Handles subscription status update and syncing
   */
  private async handleSubscriptionStatusUpdate(
    subscription: Subscription,
    stripeSubscription: Stripe.Subscription,
  ): Promise<void> {
    const currentProps = subscription.getProps()
    const status = stripeSubscription.status

    this.logger.log(
      `Handling subscription update for ${subscription.id}. Current DB status: ${currentProps.status}, Stripe status: ${status}`,
    )

    // All subscription updates follow the same pattern: update properties + sync items
    await this.processSubscriptionUpdate(subscription, stripeSubscription)
  }

  /**
   * Creates updated subscription properties from Stripe subscription data
   */
  private buildUpdatedSubscriptionProps(
    currentProps: any,
    stripeSubscription: Stripe.Subscription,
  ): any {
    return {
      ...currentProps,
      status: this.mapStripeStatusToLocal(stripeSubscription.status),
      currentPeriodStartDate: new Date(
        stripeSubscription.current_period_start * 1000,
      ),
      currentPeriodEndDate: new Date(
        stripeSubscription.current_period_end * 1000,
      ),
      canceledAt: stripeSubscription.canceled_at
        ? new Date(stripeSubscription.canceled_at * 1000)
        : null,
      updatedBy: 'stripe_webhook',
    }
  }

  /**
   * Handles all subscription updates with item syncing
   */
  private async processSubscriptionUpdate(
    subscription: Subscription,
    stripeSubscription: Stripe.Subscription,
  ): Promise<void> {
    const currentProps = subscription.getProps()
    const currentStatus = currentProps.status

    // Context-specific logging
    if (currentStatus === SubscriptionStatus.PENDING) {
      this.logger.log(
        `Confirming plan change for subscription ${subscription.id} (PENDING -> ACTIVE)`,
      )
    } else if (currentStatus === SubscriptionStatus.ACTIVE) {
      this.logger.log(
        `Handling regular subscription update for ${subscription.id}`,
      )
    } else {
      this.logger.log(
        `Handling subscription update for ${subscription.id} with status ${currentStatus}`,
      )
    }

    // Update subscription properties (same for all contexts)
    const updatedProps = this.buildUpdatedSubscriptionProps(
      currentProps,
      stripeSubscription,
    )

    await this.subscriptionRepository.updateById(subscription.id, updatedProps)

    // Sync subscription items (always needed to keep database in sync with Stripe)
    await this.syncSubscriptionItems(
      subscription.id.toString(),
      stripeSubscription.items.data,
    )

    // Context-specific success logging
    if (currentStatus === SubscriptionStatus.PENDING) {
      this.logger.log(
        `Successfully confirmed plan change for subscription ${subscription.id}`,
      )
    } else {
      this.logger.log(
        `Successfully updated subscription ${subscription.id} status to '${stripeSubscription.status}'`,
      )
    }
  }

  /**
   * Maps Stripe subscription status to local SubscriptionStatus enum.
   */
  private mapStripeStatusToLocal(stripeStatus: string): SubscriptionStatus {
    switch (stripeStatus) {
      case 'trialing':
        return SubscriptionStatus.TRIALING
      case 'active':
        return SubscriptionStatus.ACTIVE
      case 'past_due':
        return SubscriptionStatus.PAST_DUE
      case 'unpaid':
        return SubscriptionStatus.UNPAID
      case 'canceled':
        return SubscriptionStatus.CANCELED
      case 'incomplete':
        return SubscriptionStatus.INCOMPLETE
      case 'incomplete_expired':
        return SubscriptionStatus.INCOMPLETE_EXPIRED
      default:
        this.logger.warn(
          `Unknown Stripe status: ${stripeStatus}, defaulting to INCOMPLETE`,
        )
        return SubscriptionStatus.INCOMPLETE
    }
  }

  /**
   * Syncs subscription items with Stripe data.
   */
  private async syncSubscriptionItems(
    subscriptionId: string,
    stripeItems: Stripe.SubscriptionItem[],
  ): Promise<void> {
    // Get existing subscription items
    const existingItemsResult = await this.subscriptionItemRepository.find({
      filter: { subscriptionId },
    })
    const existingItems = existingItemsResult.data

    for (const stripeItem of stripeItems) {
      // First try to find by existing stripeSubscriptionItemId
      let existingItem = existingItems.find(
        (item) => item.getProps().stripeSubscriptionItemId === stripeItem.id,
      )

      // If not found by ID, try to find by stripePriceId for items with empty stripeSubscriptionItemId
      // This handles the case where items were created during plan change but don't have Stripe IDs yet
      if (!existingItem) {
        existingItem = existingItems.find((item) => {
          const props = item.getProps()
          return (
            props.stripePriceId === stripeItem.price.id &&
            (props.stripeSubscriptionItemId === '' ||
              !props.stripeSubscriptionItemId)
          )
        })
      }

      if (existingItem) {
        // Update existing item with Stripe data
        const currentProps = existingItem.getProps()
        const updatedProps = {
          ...currentProps,
          stripeSubscriptionItemId: stripeItem.id, // Update with actual Stripe ID
          quantity: stripeItem.quantity || 1,
          status: SubscriptionStatus.ACTIVE, // Assuming active if in Stripe response
          updatedBy: 'stripe_webhook',
        }

        await this.subscriptionItemRepository.updateById(
          existingItem.id,
          updatedProps,
        )

        this.logger.log(
          `Updated subscription item ${existingItem.id} with Stripe ID ${stripeItem.id}`,
        )
      } else {
        // Create new item (this shouldn't happen often in normal flow)
        this.logger.log(
          `Creating new subscription item for Stripe item ${stripeItem.id}`,
        )
        // Note: We'd need more context to properly create a new item
        // This would typically be handled by checkout.session.completed
      }
    }

    // Mark removed items as canceled
    for (const existingItem of existingItems) {
      const stillExists = stripeItems.some(
        (stripeItem) =>
          stripeItem.id === existingItem.getProps().stripeSubscriptionItemId,
      )

      if (!stillExists) {
        const currentProps = existingItem.getProps()
        const updatedProps = {
          ...currentProps,
          status: SubscriptionStatus.CANCELED,
          updatedBy: 'stripe_webhook',
        }

        await this.subscriptionItemRepository.updateById(
          existingItem.id,
          updatedProps,
        )
      }
    }
  }

  /**
   * Handles customer.subscription.deleted webhook - Update status to canceled, set canceledAt (PRD FR2.7.5).
   */
  async handleCustomerSubscriptionDeleted(
    stripeSubscription: Stripe.Subscription,
  ): Promise<void> {
    this.logger.log(
      `[StripeService] Handling customer.subscription.deleted: ${stripeSubscription.id}`,
    )
    const customerId = stripeSubscription.customer

    if (typeof customerId !== 'string') {
      this.logger.error(
        `Deleted Subscription ${stripeSubscription.id} is missing customer ID.`,
      )
      return
    }

    const subscription = await this.findSubscriptionByStripeId(
      stripeSubscription.id,
      customerId,
    )

    if (!subscription) {
      this.logger.error(
        `Received customer.subscription.deleted for unknown subscription ${stripeSubscription.id}.`,
      )
      return
    }

    try {
      // 1. Update subscription status to canceled and set canceledAt
      const currentProps = subscription.getProps()
      const canceledAt = stripeSubscription.canceled_at
        ? new Date(stripeSubscription.canceled_at * 1000)
        : new Date()

      const updatedProps = {
        ...currentProps,
        status: SubscriptionStatus.CANCELED,
        canceledAt,
        updatedBy: 'stripe_webhook',
      }

      await this.subscriptionRepository.updateById(
        subscription.id,
        updatedProps,
      )

      // 2. Update all subscription items to canceled status
      const subscriptionItemsResult =
        await this.subscriptionItemRepository.find({
          filter: {
            subscriptionId: subscription.id,
          },
        })
      const subscriptionItems = subscriptionItemsResult.data

      for (const subscriptionItem of subscriptionItems) {
        const itemProps = subscriptionItem.getProps()
        const updatedItemProps = {
          ...itemProps,
          status: SubscriptionStatus.CANCELED,
          updatedBy: 'stripe_webhook',
        }

        await this.subscriptionItemRepository.updateById(
          subscriptionItem.id,
          updatedItemProps,
        )
      }

      this.logger.log(
        `Subscription ${subscription.id} and ${subscriptionItems.length} items marked as canceled at ${canceledAt.toISOString()}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to handle subscription deletion for ${subscription.id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Handles checkout.session.completed webhook - Create Subscription + SubscriptionItems with new schema (PRD FR2.7.5).
   */
  async handleCheckoutSessionCompleted(
    session: Stripe.Checkout.Session,
  ): Promise<void> {
    this.logger.log(
      `[StripeService] Handling checkout.session.completed: ${session.id}`,
    )
    const stripeSubscriptionId = session.subscription
    const customerId = session.customer
    const clientReferenceId = session.client_reference_id
    const metadata = session.metadata

    if (
      session.mode === 'subscription' &&
      typeof stripeSubscriptionId === 'string' &&
      typeof customerId === 'string'
    ) {
      this.logger.log(
        `Processing checkout.session.completed for Subscription ID: ${stripeSubscriptionId}, Customer ID: ${customerId}`,
      )

      // 1. Validate clientReferenceId
      if (!clientReferenceId) {
        this.logger.error(
          `Missing client_reference_id in checkout session ${session.id}. Cannot link to internal user.`,
        )
        return
      }
      const userId = clientReferenceId as Id

      // 2. Check if subscription already exists in DB (idempotency)
      const existingSubscription = await this.findSubscriptionByStripeId(
        stripeSubscriptionId,
        customerId,
      )

      if (existingSubscription) {
        this.logger.log(
          `Subscription ${stripeSubscriptionId} already exists in DB (ID: ${existingSubscription.id}). Skipping creation.`,
        )
        return
      }

      // 3. Validate metadata
      const organizationId = metadata?.organizationId
      const subscriptionPlanId = metadata?.subscriptionPlanId

      if (!organizationId || !subscriptionPlanId) {
        this.logger.error(
          `Missing required metadata in checkout session ${session.id}: organizationId=${organizationId}, subscriptionPlanId=${subscriptionPlanId}`,
        )
        return
      }

      // 4. Retrieve the full subscription details from Stripe
      let stripeSubscription: Stripe.Subscription
      try {
        stripeSubscription = await this.stripe.subscriptions.retrieve(
          stripeSubscriptionId,
          {
            expand: ['items.data.price'],
          },
        )
      } catch (error) {
        this.logger.error(
          `Failed to retrieve Stripe subscription ${stripeSubscriptionId}: ${error.message}`,
        )
        return
      }

      if (
        !stripeSubscription.items ||
        stripeSubscription.items.data.length === 0
      ) {
        this.logger.error(
          `Stripe subscription ${stripeSubscriptionId} has no items.`,
        )
        return
      }

      // 5. Get the subscription plan
      const subscriptionPlan =
        await this.subscriptionPlanRepository.findById(subscriptionPlanId)
      if (!subscriptionPlan) {
        this.logger.error(`SubscriptionPlan ${subscriptionPlanId} not found`)
        return
      }

      try {
        // 6. Create new subscription record with new schema
        this.logger.log(
          `Creating new subscription record for organization ${organizationId}`,
        )

        const subscription = await this.subscriptionRepository.create(
          Subscription.create({
            organizationId,
            stripeSubscriptionId,
            stripeCustomerId: customerId,
            status: this.mapStripeStatusToLocal(stripeSubscription.status),
            currentPeriodStartDate: new Date(
              stripeSubscription.current_period_start * 1000,
            ),
            currentPeriodEndDate: new Date(
              stripeSubscription.current_period_end * 1000,
            ),
            usageBillingEnabled: true, // Enable usage billing by default
            overagePolicyFixedResources: OveragePolicyType.BLOCK, // Default policy
            createdBy: userId.toString(),
            updatedBy: userId.toString(),
            source: PlanSource.STRIPE,
          }),
        )

        // 7. Create subscription items with new relational schema
        const planProps = subscriptionPlan.getProps()

        // Build a mapping of price IDs to item types using metadata
        const priceToItemTypeMapping = new Map<
          string,
          { type: PlanType; resourceType?: string }
        >()

        // Base plan mapping
        if (planProps.stripePriceId) {
          priceToItemTypeMapping.set(planProps.stripePriceId, {
            type: PlanType.BASE_PLAN,
          })
        }

        // Overage price mappings from metadata
        const overagePriceEntries = Object.entries(metadata || {})
          .filter(([key]) => key.endsWith('OverageStripePriceId'))
          .map(([key, priceId]) => ({
            key,
            priceId: priceId as string,
            resourceType: key.replace('OverageStripePriceId', '').toUpperCase(),
          }))

        overagePriceEntries.forEach(({ priceId, resourceType }) => {
          priceToItemTypeMapping.set(priceId, {
            type: PlanType.ADDON_METERED_USAGE,
            resourceType,
          })
        })

        this.logger.log(
          `Created price mapping for ${priceToItemTypeMapping.size} items: ${Array.from(
            priceToItemTypeMapping.entries(),
          )
            .map(
              ([priceId, mapping]) =>
                `${priceId}=${mapping.type}${mapping.resourceType ? `(${mapping.resourceType})` : ''}`,
            )
            .join(', ')}`,
        )

        for (const stripeItem of stripeSubscription.items.data) {
          // Determine item type using metadata-driven mapping
          let itemMapping = priceToItemTypeMapping.get(stripeItem.price.id)

          if (!itemMapping) {
            this.logger.warn(
              `Unknown price ID ${stripeItem.price.id} in subscription ${stripeSubscriptionId}. This might be a new resource type or configuration issue.`,
            )
            // Default to metered usage for unknown items
            itemMapping = { type: PlanType.ADDON_METERED_USAGE }
          }

          const itemType = itemMapping.type
          const subscriptionPlanItemId = subscriptionPlanId

          // Check if this subscription item already exists (additional idempotency)
          const existingItem = await this.subscriptionItemRepository.findOne({
            filter: {
              subscriptionId: subscription.id,
              stripeSubscriptionItemId: stripeItem.id,
            },
          })

          if (existingItem) {
            this.logger.log(
              `Subscription item for Stripe item ${stripeItem.id} already exists, skipping`,
            )
            continue
          }

          // Create subscription item with new schema
          const newSubscriptionItem =
            await this.subscriptionItemRepository.create(
              SubscriptionItem.create({
                subscriptionId: subscription.id,
                stripeSubscriptionItemId: stripeItem.id,
                stripePriceId: stripeItem.price.id,
                subscriptionPlanId: subscriptionPlanItemId,
                itemType,
                quantity: stripeItem.quantity || 1,
                status: SubscriptionStatus.ACTIVE,
                startDate: new Date(
                  stripeSubscription.current_period_start * 1000,
                ),
                endDate: new Date(stripeSubscription.current_period_end * 1000),
                resourceType:
                  (itemMapping.resourceType as ResourceType) || null,
                createdBy: userId.toString(),
                updatedBy: userId.toString(),
              }),
            )

          this.logger.log(
            `Created subscription item ${newSubscriptionItem.id} for Stripe item ${stripeItem.id} (${itemType}${itemMapping.resourceType ? ` - ${itemMapping.resourceType}` : ''})`,
          )
        }

        this.logger.log(
          `Successfully created subscription ${subscription.id} with ${stripeSubscription.items.data.length} items`,
        )

        // Add overage items to the subscription (moved from checkout session)
        await this.addOverageItemsToSubscription(stripeSubscriptionId, metadata)
      } catch (error) {
        this.logger.error(
          `Failed to create subscription for checkout session ${session.id}: ${error.message}`,
          error.stack,
        )
        throw error
      }
    } else {
      this.logger.log(
        `Checkout session ${session.id} completed, but not a subscription mode or missing IDs.`,
      )
    }
  }

  /**
   * Adds overage items to a subscription after checkout completion
   * This method is called from the webhook handler to add metered usage items
   * Creates both Stripe subscription items and database records
   */
  private async addOverageItemsToSubscription(
    stripeSubscriptionId: string,
    metadata: Record<string, string> | null,
  ): Promise<void> {
    if (!metadata) {
      this.logger.log(
        `No metadata provided for subscription ${stripeSubscriptionId}`,
      )
      return
    }

    // Extract overage prices from metadata
    const overagePriceEntries = Object.entries(metadata).filter(([key]) =>
      key.endsWith('OverageStripePriceId'),
    )

    if (overagePriceEntries.length === 0) {
      this.logger.log(
        `No overage prices found for subscription ${stripeSubscriptionId}`,
      )
      return
    }

    try {
      // Get current subscription to check existing items
      const subscription = await this.stripe.subscriptions.retrieve(
        stripeSubscriptionId,
        {
          expand: ['items'],
        },
      )

      const existingPriceIds = new Set(
        subscription.items.data.map((item) => item.price.id),
      )

      // Only add new overage items (idempotency)
      const newOverageItems = overagePriceEntries
        .map(([, priceId]) => priceId as string)
        .filter((priceId) => !existingPriceIds.has(priceId))
        .map((priceId) => ({ price: priceId }))

      if (newOverageItems.length > 0) {
        const existingItems = subscription.items.data.map((item) => ({
          id: item.id,
        }))

        // Add overage items to Stripe subscription
        const updatedSubscription = await this.stripe.subscriptions.update(
          stripeSubscriptionId,
          {
            items: [...existingItems, ...newOverageItems],
            proration_behavior: 'none', // Avoids creating immediate invoices
          },
        )

        this.logger.log(
          `Added ${newOverageItems.length} overage items to subscription ${stripeSubscriptionId}`,
        )

        // Create database records for the newly added overage items
        await this.createDatabaseRecordsForOverageItems(
          stripeSubscriptionId,
          updatedSubscription,
          metadata,
        )
      } else {
        this.logger.log(
          `No new overage items needed for subscription ${stripeSubscriptionId} (all items already exist)`,
        )
      }
    } catch (error) {
      this.logger.error(
        `Failed to add overage items to subscription ${stripeSubscriptionId}: ${error.message}`,
        error.stack,
      )
      // Don't throw error - subscription is already created, overage items can be added later
    }
  }

  /**
   * Creates database records for overage items that were added to Stripe subscription
   * This ensures consistency between Stripe and database
   */
  private async createDatabaseRecordsForOverageItems(
    stripeSubscriptionId: string,
    updatedStripeSubscription: Stripe.Subscription,
    metadata: Record<string, string>,
  ): Promise<void> {
    try {
      // Find the database subscription record
      const dbSubscription =
        await this.findSubscriptionByStripeId(stripeSubscriptionId)

      if (!dbSubscription) {
        this.logger.error(
          `Database subscription not found for Stripe subscription ${stripeSubscriptionId}`,
        )
        return
      }

      // Get subscription plan ID from metadata
      const subscriptionPlanId = metadata.subscriptionPlanId
      if (!subscriptionPlanId) {
        this.logger.error(
          `No subscription plan ID in metadata for subscription ${stripeSubscriptionId}`,
        )
        return
      }

      // Build price to resource type mapping from metadata
      const priceToResourceTypeMapping = new Map<string, string>()
      const overagePriceEntries = Object.entries(metadata)
        .filter(([key]) => key.endsWith('OverageStripePriceId'))
        .map(([key, priceId]) => ({
          key,
          priceId: priceId as string,
          resourceType: key.replace('OverageStripePriceId', '').toUpperCase(),
        }))

      overagePriceEntries.forEach(({ priceId, resourceType }) => {
        priceToResourceTypeMapping.set(priceId, resourceType)
      })

      // Get existing database subscription items to avoid duplicates
      const existingDbItemsResult = await this.subscriptionItemRepository.find({
        filter: { subscriptionId: dbSubscription.id },
      })
      const existingDbItems = existingDbItemsResult.data

      const existingDbItemStripeIds = new Set(
        existingDbItems.map((item) => item.getProps().stripeSubscriptionItemId),
      )

      this.logger.log(
        `Found ${existingDbItems.length} existing database items for subscription ${dbSubscription.id}: [${Array.from(existingDbItemStripeIds).join(', ')}]`,
      )

      // Log all Stripe items being processed
      const stripeItemIds = updatedStripeSubscription.items.data.map(
        (item) => item.id,
      )
      this.logger.log(
        `Processing ${updatedStripeSubscription.items.data.length} Stripe items: [${stripeItemIds.join(', ')}]`,
      )

      // Create database records for new overage items
      let createdItemsCount = 0
      for (const stripeItem of updatedStripeSubscription.items.data) {
        // Only process items that have overage prices and aren't already in database
        if (
          priceToResourceTypeMapping.has(stripeItem.price.id) &&
          !existingDbItemStripeIds.has(stripeItem.id)
        ) {
          const resourceType = priceToResourceTypeMapping.get(
            stripeItem.price.id,
          )

          this.logger.log(
            `Creating new subscription item for Stripe item ${stripeItem.id}`,
          )

          try {
            const newSubscriptionItem =
              await this.subscriptionItemRepository.create(
                SubscriptionItem.create({
                  subscriptionId: dbSubscription.id,
                  stripeSubscriptionItemId: stripeItem.id,
                  stripePriceId: stripeItem.price.id,
                  subscriptionPlanId,
                  itemType: PlanType.ADDON_METERED_USAGE,
                  quantity: stripeItem.quantity || 1,
                  status: SubscriptionStatus.ACTIVE,
                  startDate: new Date(
                    updatedStripeSubscription.current_period_start * 1000,
                  ),
                  endDate: new Date(
                    updatedStripeSubscription.current_period_end * 1000,
                  ),
                  resourceType: resourceType as ResourceType,
                  createdBy: 'stripe_webhook',
                  updatedBy: 'stripe_webhook',
                }),
              )

            this.logger.log(
              `Created database record for overage item ${newSubscriptionItem.id} ` +
                `(Stripe item: ${stripeItem.id}, Resource: ${resourceType})`,
            )
            createdItemsCount++
          } catch (itemError) {
            if (itemError.message.includes('duplicate key')) {
              this.logger.warn(
                `Subscription item ${stripeItem.id} already exists (race condition), skipping creation`,
              )
              continue
            } else if (
              itemError.message.includes('violates unique constraint')
            ) {
              this.logger.error(
                `Database constraint violation for subscription item ${stripeItem.id}: ${itemError.message}`,
              )
              throw new Error(
                `Database constraint violation: ${itemError.message}`,
              )
            }
            this.logger.error(
              `Unexpected error creating subscription item ${stripeItem.id}: ${itemError.message}`,
            )
            throw itemError
          }
        }
      }

      this.logger.log(
        `Created ${createdItemsCount} database records for overage items in subscription ${dbSubscription.id}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to create database records for overage items in subscription ${stripeSubscriptionId}: ${error.message}`,
        error.stack,
      )
      // Don't throw error - Stripe items are already created, database sync can be retried
    }
  }

  /**
   * Updates a Stripe subscription plan - for plan changes (supports new schema).
   */
  async updateSubscriptionItem(
    _stripeSubscriptionId: string,
    stripeSubscriptionItemId: string,
    newPriceId: string,
    quantity = 1,
  ): Promise<Stripe.SubscriptionItem> {
    this.logger.log(
      `[StripeService] Updating subscription item ${stripeSubscriptionItemId} to price ${newPriceId}`,
    )
    try {
      const updatedItem = await this.stripe.subscriptionItems.update(
        stripeSubscriptionItemId,
        {
          price: newPriceId,
          quantity,
          proration_behavior: 'create_prorations',
        },
      )

      this.logger.log(
        `Successfully updated subscription item ${stripeSubscriptionItemId}`,
      )
      return updatedItem
    } catch (error) {
      this.logger.error(
        `Error updating subscription item ${stripeSubscriptionItemId}: ${error.message}`,
      )
      throw error
    }
  }

  /**
   * Cancels a Stripe subscription at period end (PRD FR2.2.3).
   */
  async cancelStripeSubscriptionAtPeriodEnd(
    stripeSubscriptionId: string,
  ): Promise<Stripe.Subscription> {
    this.logger.log(
      `[StripeService] Canceling subscription ${stripeSubscriptionId} at period end`,
    )
    try {
      const canceledSubscription = await this.stripe.subscriptions.update(
        stripeSubscriptionId,
        {
          cancel_at_period_end: true,
        },
      )

      this.logger.log(
        `Successfully scheduled cancellation for subscription ${stripeSubscriptionId}`,
      )
      return canceledSubscription
    } catch (error) {
      this.logger.error(
        `Error canceling subscription ${stripeSubscriptionId}: ${error.message}`,
      )
      throw error
    }
  }

  /**
   * Creates a Stripe Customer. Idempotency is handled by Stripe based on email.
   * It's recommended to store the returned customer ID in your user database.
   */
  async findOrCreateCustomer(
    email: string,
    name?: string,
    metadata?: Stripe.MetadataParam,
  ): Promise<Stripe.Customer> {
    this.logger.log(
      `[StripeService] Finding or creating customer for email: ${email}`,
    )
    // Check if the customer exists first (optional, Stripe might handle this)
    const existingCustomers = await this.stripe.customers.list({
      email: email,
      limit: 1,
    })
    if (existingCustomers.data.length > 0) {
      this.logger.log(
        `   Found existing customer: ${existingCustomers.data[0].id}`,
      )
      return existingCustomers.data[0]
    }

    // Create a new customer
    try {
      const customer = await this.stripe.customers.create({
        email: email,
        name: name,
        metadata: metadata, // e.g., { internalUserId: 'your-user-id' }
      })
      this.logger.log(`   Created new customer: ${customer.id}`)
      return customer
    } catch (error) {
      this.logger.error(
        `   Error creating Stripe customer for ${email}: ${error.message}`,
      )
      throw error // Re-throw to be handled by the caller
    }
  }

  /**
   * Creates a Stripe Checkout Session for subscription with base plan and dynamic overage prices.
   * Supports dynamic resource types via metadata-driven line item creation.
   */
  async createCheckoutSessionForSubscription(
    customerId: string,
    userId: string,
    basePriceId: string | undefined,
    quantity: number,
    metadata: Record<string, string>,
    successUrl: string,
    cancelUrl: string,
  ): Promise<Stripe.Checkout.Session> {
    this.logger.log(
      `[StripeService] Creating checkout session for customer ${customerId}, user ${userId}, base price ${basePriceId}`,
    )
    try {
      // Create line items starting with base plan
      const lineItems: Stripe.Checkout.SessionCreateParams.LineItem[] = []

      // Add base plan if provided
      if (basePriceId) {
        lineItems.push({
          price: basePriceId,
          quantity: quantity,
        })
      }

      // Extract overage prices from metadata for logging (overage items will be added via webhook)
      const overagePrices = Object.entries(metadata)
        .filter(([key]) => key.endsWith('OverageStripePriceId'))
        .map(([key, priceId]) => ({ key, priceId }))
        .filter(({ priceId }) => priceId && priceId.trim() !== '')

      this.logger.log(
        `Found ${overagePrices.length} overage prices that will be added via webhook`,
      )

      // NOTE: Overage prices are NOT added to checkout session (Stripe rejects quantity: 0)
      // They will be added as subscription items via the checkout.session.completed webhook

      // Build session metadata including all overage price mappings
      const sessionMetadata = {
        subscriptionPlanId: metadata.subscriptionPlanId,
        organizationId: metadata.organizationId,
        type: 'subscription',
        // Include overage price mappings for webhook processing
        ...Object.fromEntries(
          Object.entries(metadata).filter(([key]) =>
            key.endsWith('OverageStripePriceId'),
          ),
        ),
      }

      const session = await this.stripe.checkout.sessions.create({
        customer: customerId,
        client_reference_id: userId,
        line_items: lineItems,
        mode: 'subscription',
        metadata: sessionMetadata,
        success_url: successUrl,
        cancel_url: cancelUrl,
        automatic_tax: { enabled: false }, // Configure based on requirements
      })

      this.logger.log(
        `Created checkout session: ${session.id} with ${lineItems.length} line items (base plan only, ${overagePrices.length} overage prices will be added via webhook)`,
      )
      return session
    } catch (error) {
      this.logger.error(
        `Error creating checkout session for customer ${customerId}: ${error.message}`,
      )
      throw error
    }
  }

  /**
   * Creates a new subscription for a customer with a specific tier price and a metered usage price.
   * Assumes you have Price IDs for your tiers and usage component configured in Stripe.
   */
  async createSubscription(
    customerId: string,
    tierPriceId: string,
    usagePriceId: string, // Price ID for the metered component
    metadata?: Stripe.MetadataParam,
    // paymentBehavior: Stripe.SubscriptionCreateParams.PaymentBehavior = 'default_incomplete', // Often start incomplete, let Checkout/Elements handle payment
  ): Promise<Stripe.Subscription> {
    this.logger.log(
      `[StripeService] Creating subscription for customer ${customerId} with tier ${tierPriceId} and usage ${usagePriceId}`,
    )
    try {
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [
          { price: tierPriceId }, // The base tier price
          { price: usagePriceId }, // The metered usage price
        ],
        // payment_behavior: paymentBehavior, // Use 'error_if_incomplete' if payment method is already attached
        // expand: ['latest_invoice.payment_intent'], // Useful if using Payment Intents directly
        metadata: metadata,
        // Add trial period days if needed: trial_period_days: 30
      })
      this.logger.log(`   Created subscription: ${subscription.id}`)
      // TODO: Store subscription ID, customer ID, tierPriceId, usagePriceId, and status in your database.
      return subscription
    } catch (error) {
      this.logger.error(
        `   Error creating subscription for customer ${customerId}: ${error.message}`,
      )
      throw error
    }
  }

  /**
   * Updates an existing subscription to a new tier.
   * Handles proration automatically by default.
   */
  async updateSubscriptionTier(
    subscriptionId: string,
    newTierPriceId: string,
    oldTierPriceItemId?: string, // Optional: ID of the specific subscription item to replace
  ): Promise<Stripe.Subscription> {
    this.logger.log(
      `[StripeService] Updating subscription ${subscriptionId} to tier ${newTierPriceId}`,
    )
    try {
      // 1. Find the existing subscription to get the current items
      const subscription = await this.stripe.subscriptions.retrieve(
        subscriptionId,
        { expand: ['items'] },
      )

      // 2. Identify the item associated with the old tier price.
      //    If not provided, try to find it (assuming it's the first recurring, non-metered price).
      let itemToReplaceId = oldTierPriceItemId
      if (!itemToReplaceId) {
        const tierItem = subscription.items.data.find(
          (item) =>
            item.price.recurring &&
            item.price.recurring.usage_type !== 'metered',
        )
        if (!tierItem) {
          throw new Error(
            `Could not find the old tier price item on subscription ${subscriptionId}`,
          )
        }
        itemToReplaceId = tierItem.id
        this.logger.log(`   Found old tier item to replace: ${itemToReplaceId}`)
      }

      // 3. Update the subscription, swapping the old tier item for the new one.
      //    Proration is enabled by default. Disable with proration_behavior: 'none'.
      const updatedSubscription = await this.stripe.subscriptions.update(
        subscriptionId,
        {
          items: [
            {
              id: itemToReplaceId, // ID of the item to update
              price: newTierPriceId, // The new tier price ID
              // quantity: 1, // Ensure quantity is set if applicable
            },
          ],
          proration_behavior: 'create_prorations', // Explicitly state default behavior
          // cancel_at_period_end: false, // Ensure it doesn't cancel if previously scheduled
        },
      )

      this.logger.log(
        `   Updated subscription ${updatedSubscription.id} successfully.`,
      )
      // TODO: Update the stored tierPriceId in your database for this subscription.
      // The handleSubscriptionUpdated webhook should also fire and update the status/details.
      return updatedSubscription
    } catch (error) {
      this.logger.error(
        `   Error updating subscription ${subscriptionId} tier: ${error.message}`,
      )
      throw error
    }
  }

  /**
   * Reports usage using the Stripe Billing Meters API.
   * Requires a configured Meter event name in Stripe.
   * Should be called periodically or based on usage events.
   */
  async reportUsage(
    eventName: string, // The name of the meter event configured in Stripe (e.g., 'api_calls', 'data_processed')
    customerId: string, // The Stripe Customer ID associated with the usage
    value: number, // The quantity of usage to report for this event
    timestamp?: number, // Optional: Unix timestamp in seconds. Defaults to Stripe's processing time.
    identifier?: string, // Optional: Unique identifier for idempotency control
  ): Promise<Stripe.Billing.MeterEvent | null> {
    // Allow null return (Ensuring this is applied)
    this.logger.log(
      `[StripeService] Reporting usage event '${eventName}' for customer ${customerId}: value ${value}`,
    )
    if (value <= 0) {
      this.logger.warn(
        `   Skipping usage report for event '${eventName}', customer ${customerId}: value is zero or negative.`,
      )
      return null // Or throw, depending on desired behavior
    }
    try {
      // Assert the type to MeterEvent to access properties like 'id'
      const meterEvent = (await this.stripe.billing.meterEvents.create({
        event_name: eventName,
        payload: {
          // Required properties depend on your Meter configuration in Stripe
          value: value.toString(), // Value often needs to be a string
          // Include customer_id OR subscription_item_id based on Meter config
          customer_id: customerId,
          // You might include other properties relevant to the event
        },
        timestamp: timestamp, // Optional timestamp
        identifier: identifier, // Optional idempotency key
      })) as Stripe.Billing.MeterEvent // Type assertion here
      // Assuming meterEvent itself is the event object based on typical SDK patterns
      this.logger.log(
        `   Usage event reported successfully for event '${eventName}', customer ${customerId}. Full event object:`,
        meterEvent,
      ) // Log the whole object
      // TODO: Optionally store usage locally for auditing or display purposes.
      // Now meterEvent is treated as Stripe.Billing.MeterEvent
      return meterEvent
    } catch (error) {
      this.logger.error(
        `   Error reporting usage event '${eventName}' for customer ${customerId}: ${error.message}`,
      )
      throw error
    }
  }

  /**
   * Retrieves a subscription by its ID.
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    this.logger.log(`[StripeService] Retrieving subscription ${subscriptionId}`)
    try {
      const subscription =
        await this.stripe.subscriptions.retrieve(subscriptionId)
      return subscription
    } catch (error) {
      this.logger.error(
        `   Error retrieving subscription ${subscriptionId}: ${error.message}`,
      )
      throw error
    }
  }

  /**
   * Retrieves active products and their active prices from Stripe.
   * Prices are nested within their corresponding products.
   */
  async getProductsAndPrices(type: string): Promise<Stripe.Product[]> {
    this.logger.log('[StripeService] Fetching active products and prices')
    try {
      // Fetch active prices and expand the product data for each price
      const prices = await this.stripe.prices.list({
        active: true,
        expand: ['data.product'],
        limit: 100,
      })

      // Filter out prices whose products are not active or are not Stripe.Product objects
      const activeProductPrices = prices.data.filter(
        (price) =>
          price.product &&
          typeof price.product === 'object' &&
          (price.product as Stripe.Product).active,
      )

      // Group prices by product ID
      const productsMap = new Map<string, any & { prices: Stripe.Price[] }>()

      for (const price of activeProductPrices) {
        const product = price.product as Stripe.Product
        // remove object product in price
        delete (price as { product?: Stripe.Product }).product
        if (!productsMap.has(product.id)) {
          // If product not yet in map, add it with an empty prices array
          productsMap.set(product.id, {
            ...product,
            prices: [],
          })
        }
        // Add the current price to the product's prices array
        productsMap.get(product.id)?.prices.push(price)
      }

      // Convert map values to an array
      const productsWithPrices = Array.from(productsMap.values())

      // Filter products based on metadata.planId
      const filteredProducts = productsWithPrices.filter(
        (product) => product.metadata && product.metadata.type === type,
      )

      for (const product of filteredProducts) {
        if (product.metadata && product.metadata.planId) {
          // Include plan detail for the product
          const plan = await this.subscriptionPlanRepository.findById(
            product.metadata.planId,
          )
          if (plan) {
            product.plan = plan.getProps() // Assuming you want to add plan detail to the product metadata
          }
        }
      }

      this.logger.log(
        `   Fetched ${productsWithPrices.length} active products, filtered down to ${filteredProducts.length} with planId metadata.`,
      )
      return filteredProducts // Return the filtered list
    } catch (error) {
      this.logger.error(
        `   Error fetching products and prices from Stripe: ${error.message}`,
        error.stack,
      )
      throw error // Re-throw for the caller to handle
    }
  }

  async createCustomerPortalSession(data: CreateCustomerSessionDto) {
    return await this.stripe.billingPortal.sessions.create({
      customer: data.customerId,
      return_url: data.returnUrl,
    })
  }

  /**
   * Generates a unique meter name for a specific resource type
   * Format: {resourceType}_usage_meter (e.g., "member_usage_meter", "check_usage_meter")
   */
  private generateMeterName(resourceType: string): string {
    return `${resourceType.toLowerCase()}_usage_meter`
  }

  /**
   * Creates a Stripe meter with count aggregation for usage tracking
   */
  async createMeter(
    resourceType: string,
    displayName?: string,
  ): Promise<Stripe.Billing.Meter> {
    const meterName = this.generateMeterName(resourceType)
    const eventName = `${resourceType.toLowerCase()}_usage`

    this.logger.log(
      `[StripeService] Creating meter: ${meterName} for resource type: ${resourceType}`,
    )

    try {
      const meter = await this.stripe.billing.meters.create({
        display_name: displayName || `${resourceType} Usage Meter`,
        event_name: eventName,
        customer_mapping: {
          event_payload_key: 'customer_id',
          type: 'by_id',
        },
        default_aggregation: {
          formula: 'count',
        },
        value_settings: {
          event_payload_key: 'value',
        },
      })

      this.logger.log(`[StripeService] Successfully created meter: ${meter.id}`)
      return meter
    } catch (error) {
      this.logger.error(
        `[StripeService] Error creating meter for ${resourceType}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Creates a new Stripe price with the given parameters.
   * Used for creating subscription plan prices automatically.
   */
  async createPrice(params: Stripe.PriceCreateParams): Promise<Stripe.Price> {
    this.logger.log(
      `[StripeService] Creating price: ${params.product_data?.name || 'Unknown'}`,
    )
    try {
      const price = await this.stripe.prices.create(params)
      this.logger.log(`[StripeService] Successfully created price: ${price.id}`)
      return price
    } catch (error) {
      this.logger.error(
        `[StripeService] Error creating price: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Updates a subscription item quantity with optional proration control
   */
  async updateSubscriptionItemQuantity(
    stripeSubscriptionItemId: string,
    newQuantity: number,
    prorationBehavior: 'create_prorations' | 'none' = 'create_prorations',
  ): Promise<Stripe.SubscriptionItem> {
    this.logger.log(
      `[StripeService] Updating subscription item ${stripeSubscriptionItemId} quantity to ${newQuantity} with proration: ${prorationBehavior}`,
    )

    try {
      const updatedItem = await this.stripe.subscriptionItems.update(
        stripeSubscriptionItemId,
        {
          quantity: newQuantity,
          proration_behavior: prorationBehavior,
        },
      )

      this.logger.log(
        `Successfully updated subscription item ${stripeSubscriptionItemId} quantity to ${newQuantity}`,
      )
      return updatedItem
    } catch (error) {
      this.logger.error(
        `Error updating subscription item ${stripeSubscriptionItemId} quantity: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Updates multiple subscription item quantities in a single request for efficiency
   */
  async updateMultipleSubscriptionItemQuantities(
    stripeSubscriptionId: string,
    quantityUpdates: Array<{ itemId: string; quantity: number }>,
    prorationBehavior: 'create_prorations' | 'none' = 'create_prorations',
  ): Promise<Stripe.Subscription> {
    this.logger.log(
      `[StripeService] Updating ${quantityUpdates.length} subscription items for subscription ${stripeSubscriptionId} with proration: ${prorationBehavior}`,
    )

    try {
      const items = quantityUpdates.map((update) => ({
        id: update.itemId,
        quantity: update.quantity,
      }))

      const updatedSubscription = await this.stripe.subscriptions.update(
        stripeSubscriptionId,
        {
          items,
          proration_behavior: prorationBehavior,
        },
      )

      this.logger.log(
        `Successfully updated ${quantityUpdates.length} subscription items for subscription ${stripeSubscriptionId}`,
      )
      return updatedSubscription
    } catch (error) {
      this.logger.error(
        `Error updating multiple subscription items for subscription ${stripeSubscriptionId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Finds a subscription item by price ID within a subscription
   */
  async getSubscriptionItemByPriceId(
    stripeSubscriptionId: string,
    stripePriceId: string,
  ): Promise<Stripe.SubscriptionItem | null> {
    this.logger.log(
      `[StripeService] Finding subscription item with price ${stripePriceId} in subscription ${stripeSubscriptionId}`,
    )

    try {
      const subscription = await this.stripe.subscriptions.retrieve(
        stripeSubscriptionId,
        { expand: ['items.data.price'] },
      )

      const subscriptionItem = subscription.items.data.find(
        (item) => item.price.id === stripePriceId,
      )

      if (subscriptionItem) {
        this.logger.log(
          `Found subscription item ${subscriptionItem.id} for price ${stripePriceId}`,
        )
        return subscriptionItem
      }

      this.logger.log(
        `No subscription item found for price ${stripePriceId} in subscription ${stripeSubscriptionId}`,
      )
      return null
    } catch (error) {
      this.logger.error(
        `Error finding subscription item by price ${stripePriceId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Gets current usage by organization (extracted from SubscriptionUseCase)
   */
  private async getCurrentUsageByOrganization(organizationId: string): Promise<{
    members: number
    teams: number
    checks: number
    integrations: number
  }> {
    try {
      const usage =
        await this.subscriptionUsageRepository.getCurrentUsageByOrganizationId(
          organizationId,
        )
      return {
        members: usage.totalUsers,
        teams: usage.totalTeams,
        checks: usage.totalChecks,
        integrations: usage.totalIntegrations || 0, // Add integrations if available
      }
    } catch (error) {
      this.logger.error(
        `Failed to get current usage for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      // Return zero values on error to prevent blocking other operations
      return {
        members: 0,
        teams: 0,
        checks: 0,
        integrations: 0,
      }
    }
  }

  /**
   * Detects if a subscription update contains plan changes based on previous_attributes
   * @param subscription Current Stripe subscription data
   * @param previousAttributes Previous attributes from webhook event
   * @returns PlanChangeEvent if plan change detected, null otherwise
   */
  private detectPlanChange(
    subscription: Stripe.Subscription,
    previousAttributes?: Partial<Stripe.Subscription>,
  ): PlanChangeEvent | null {
    if (!previousAttributes?.items?.data) {
      this.logger.debug(
        `No previous items data for subscription ${subscription.id}`,
      )
      return null
    }

    const currentItems = subscription.items.data
    const previousItems = previousAttributes.items.data

    // Extract price IDs from items
    const currentPlanIds = currentItems.map((item) => item.price.id)
    const previousPlanIds = previousItems.map((item) => item.price.id)

    // Check if there are any differences in plan IDs
    const hasItemChanges = this.hasItemChanges(currentItems, previousItems)

    if (!hasItemChanges && this.arrayEquals(currentPlanIds, previousPlanIds)) {
      this.logger.debug(
        `No plan changes detected for subscription ${subscription.id}`,
      )
      return null
    }

    // Detect specific changes
    const changedItems = this.detectItemChanges(currentItems, previousItems)

    this.logger.log(
      `Plan change detected for subscription ${subscription.id}: ` +
        `previous plans: [${previousPlanIds.join(', ')}], ` +
        `current plans: [${currentPlanIds.join(', ')}], ` +
        `changed items: ${changedItems.length}`,
    )

    return {
      subscriptionId: subscription.id,
      organizationId: subscription.metadata.organizationId || '',
      previousPlanIds,
      currentPlanIds,
      hasItemChanges,
      changedItems,
    }
  }

  /**
   * Checks if there are changes in subscription items (quantity or price)
   */
  private hasItemChanges(
    currentItems: Stripe.SubscriptionItem[],
    previousItems: Stripe.SubscriptionItem[],
  ): boolean {
    if (currentItems.length !== previousItems.length) {
      return true
    }

    // Create maps for easier comparison
    const currentMap = new Map(
      currentItems.map((item) => [
        item.id,
        { priceId: item.price.id, quantity: item.quantity || 1 },
      ]),
    )

    const previousMap = new Map(
      previousItems.map((item) => [
        item.id,
        { priceId: item.price.id, quantity: item.quantity || 1 },
      ]),
    )

    // Check if any item has changed
    for (const [itemId, currentData] of currentMap) {
      const previousData = previousMap.get(itemId)
      if (!previousData) {
        return true // New item added
      }

      if (
        currentData.priceId !== previousData.priceId ||
        currentData.quantity !== previousData.quantity
      ) {
        return true // Item modified
      }
    }

    // Check for removed items
    for (const itemId of previousMap.keys()) {
      if (!currentMap.has(itemId)) {
        return true // Item removed
      }
    }

    return false
  }

  /**
   * Detects specific changes between current and previous items
   */
  private detectItemChanges(
    currentItems: Stripe.SubscriptionItem[],
    previousItems: Stripe.SubscriptionItem[],
  ): PlanChangeEvent['changedItems'] {
    const changes: PlanChangeEvent['changedItems'] = []

    // Create maps for easier comparison
    const currentMap = new Map(
      currentItems.map((item) => [
        item.id,
        { priceId: item.price.id, quantity: item.quantity || 1 },
      ]),
    )

    const previousMap = new Map(
      previousItems.map((item) => [
        item.id,
        { priceId: item.price.id, quantity: item.quantity || 1 },
      ]),
    )

    // Check for new and modified items
    for (const [itemId, currentData] of currentMap) {
      const previousData = previousMap.get(itemId)

      if (!previousData) {
        // New item added
        changes.push({
          itemId,
          priceId: currentData.priceId,
          change: 'added',
          currentQuantity: currentData.quantity,
        })
      } else if (
        currentData.priceId !== previousData.priceId ||
        currentData.quantity !== previousData.quantity
      ) {
        // Item modified
        changes.push({
          itemId,
          priceId: currentData.priceId,
          change: 'modified',
          previousQuantity: previousData.quantity,
          currentQuantity: currentData.quantity,
        })
      }
    }

    // Check for removed items
    for (const [itemId, previousData] of previousMap) {
      if (!currentMap.has(itemId)) {
        changes.push({
          itemId,
          priceId: previousData.priceId,
          change: 'removed',
          previousQuantity: previousData.quantity,
        })
      }
    }

    return changes
  }

  /**
   * Utility method to compare arrays
   */
  private arrayEquals(a: string[], b: string[]): boolean {
    if (a.length !== b.length) return false
    const sortedA = [...a].sort()
    const sortedB = [...b].sort()
    return sortedA.every((val, index) => val === sortedB[index])
  }

  /**
   * Previews a plan change by calculating proration without making any changes.
   * Uses Stripe's invoice preview API to determine costs.
   * @param subscription - The current subscription entity
   * @param newPlanStripePriceId - The Stripe price ID of the new plan
   * @returns Proration details and next invoice information
   */
  async previewPlanChange(
    subscription: Subscription,
    newPlanStripePriceId: string,
  ): Promise<PlanChangePreviewResult> {
    this.logger.log(
      `Previewing plan change for subscription ${subscription.id} to price ${newPlanStripePriceId}`,
    )

    try {
      const subscriptionProps = subscription.getProps()

      // Find the existing subscription item for the base plan
      const currentSubscriptionItemsResult =
        await this.subscriptionItemRepository.find({
          filter: {
            subscriptionId: subscription.id,
            itemType: PlanType.BASE_PLAN,
            status: SubscriptionStatus.ACTIVE,
          },
        })
      const currentSubscriptionItems = currentSubscriptionItemsResult.data

      const currentBasePlanItem = currentSubscriptionItems.find(
        (item) => item.getProps().itemType === PlanType.BASE_PLAN,
      )

      if (!currentBasePlanItem) {
        throw new Error(
          `No active base plan found for subscription ${subscription.id}`,
        )
      }

      // Use Stripe's invoice preview with billing cycle reset
      const invoicePreview = await this.stripe.invoices.createPreview({
        customer: subscriptionProps.stripeCustomerId,
        subscription: subscriptionProps.stripeSubscriptionId,
        subscription_details: {
          items: [
            {
              id: currentBasePlanItem.getProps().stripeSubscriptionItemId,
              price: newPlanStripePriceId,
            },
          ],
          billing_cycle_anchor: 'now', // Reset billing cycle to now
          proration_behavior: 'create_prorations',
        },
      })

      // Parse the invoice preview to extract charges
      let proratedCredit = 0
      let newPlanCharge = 0

      // Analyze line items to separate proration and new plan charges
      for (const lineItem of invoicePreview.lines.data) {
        if (lineItem.proration && lineItem.amount < 0) {
          // Negative proration amounts are credits for unused time
          proratedCredit += Math.abs(lineItem.amount)
        } else if (!lineItem.proration && lineItem.amount > 0) {
          // Non-proration positive amounts are new plan charges
          newPlanCharge += lineItem.amount
        }
      }

      // Calculate next invoice date (1 month from billing cycle reset date)
      const nextInvoiceDate = new Date()
      nextInvoiceDate.setMonth(nextInvoiceDate.getMonth() + 1)

      const result: PlanChangePreviewResult = {
        proratedCredit,
        newPlanCharge,
        nextInvoiceDate: nextInvoiceDate.toISOString(),
        nextInvoiceTotal: newPlanCharge, // Regular monthly charge for future cycles
        currency: invoicePreview.currency, // Extract currency from Stripe invoice preview
      }

      // Debug logging
      this.logger.log('Invoice preview line items for billing cycle reset:')
      for (const lineItem of invoicePreview.lines.data) {
        this.logger.log(
          `- ${lineItem.description}: ${lineItem.amount}¢ (proration: ${lineItem.proration})`,
        )
      }

      this.logger.log(
        `Plan change preview with billing cycle reset: proratedCredit=${result.proratedCredit}¢, newPlanCharge=${result.newPlanCharge}¢, totalDue=${invoicePreview.amount_due}¢`,
      )

      return result
    } catch (error) {
      this.logger.error(
        `Failed to preview plan change for subscription ${subscription.id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Changes subscription plan with billing cycle reset for immediate proration
   */
  async changeSubscriptionPlan(
    stripeSubscriptionId: string,
    stripeSubscriptionItemId: string,
    newPlanPriceId: string,
  ): Promise<any> {
    this.logger.log(
      `Initiating plan change in Stripe for subscription ${stripeSubscriptionId}. Changing base plan to ${newPlanPriceId}. Resource limits will be added after payment confirmation.`,
    )

    try {
      // Only update the base plan price during initiation
      // Stripe will calculate proration between old and new base plan
      // Resource limits (metered items) will be added later in webhook after payment success
      const items: Array<{
        id?: string
        price?: string
        quantity?: number
        deleted?: boolean
      }> = [
        {
          id: stripeSubscriptionItemId,
          price: newPlanPriceId, // Update base plan to new plan price
        },
      ]

      // Keep existing metered items for now - don't delete until payment is confirmed
      // This ensures we don't lose resource tracking during the payment process

      // Update subscription with proper proration (no billing cycle reset)
      const updatedSubscription = await this.stripe.subscriptions.update(
        stripeSubscriptionId,
        {
          items,
          proration_behavior: 'create_prorations',
          billing_cycle_anchor: 'now',
        },
      )

      this.logger.log(
        `Successfully initiated plan change in Stripe for subscription ${stripeSubscriptionId}. Updated base plan to ${newPlanPriceId}. Stripe will handle payment calculation.`,
      )

      return updatedSubscription
    } catch (error) {
      this.logger.error(
        `Failed to initiate plan change in Stripe for subscription ${stripeSubscriptionId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Retrieves Stripe prices for the given price IDs
   * @param priceIds - Array of Stripe price IDs to retrieve
   * @returns Map of price ID to Stripe.Price object
   */
  async getStripePrices(
    priceIds: string[],
  ): Promise<Map<string, Stripe.Price>> {
    this.logger.log(`Fetching Stripe prices for ${priceIds.length} price IDs`)

    const priceMap = new Map<string, Stripe.Price>()

    for (const priceId of priceIds) {
      if (!priceId) continue

      try {
        const price = await this.stripe.prices.retrieve(priceId)
        priceMap.set(priceId, price)
        this.logger.log(
          `Retrieved price ${priceId}: ${price.unit_amount} ${price.currency}`,
        )
      } catch (error) {
        this.logger.warn(
          `Failed to retrieve price ${priceId}: ${error.message}`,
        )
      }
    }

    this.logger.log(
      `Successfully retrieved ${priceMap.size} out of ${priceIds.length} prices`,
    )
    return priceMap
  }

  /**
   * Retrieves a single Stripe price by ID.
   * Used for real-time pricing calculations in usage billing.
   */
  async getPrice(priceId: string): Promise<Stripe.Price> {
    this.logger.log(`Fetching Stripe price: ${priceId}`)

    try {
      const price = await this.stripe.prices.retrieve(priceId)
      this.logger.log(
        `Retrieved price ${priceId}: ${price.unit_amount} ${price.currency}`,
      )
      return price
    } catch (error) {
      this.logger.error(
        `Failed to retrieve Stripe price ${priceId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Finds active subscription by Stripe ID (prioritizes PENDING for plan changes)
   */
  private async findSubscriptionByStripeId(
    stripeSubscriptionId: string,
    stripeCustomerId?: string,
  ): Promise<Subscription | null> {
    const filter: any = {
      stripeSubscriptionId,
      status: {
        [Operator.$not]: {
          [Operator.$in]: [SubscriptionStatus.CANCELED],
        },
      }, // Exclude only canceled subscriptions
    }

    if (stripeCustomerId) {
      filter.stripeCustomerId = stripeCustomerId
    }

    // Get all matching subscriptions and prioritize PENDING (new plan change subscription)
    const subscriptions = await this.subscriptionRepository.find({
      filter,
      orderBy: { createdAt: 'DESC' }, // Most recent first
    })

    if (!subscriptions.data || subscriptions.data.length === 0) {
      return null
    }

    // Prioritize PENDING status (new subscription for plan change)
    const pendingSubscription = subscriptions.data.find(
      (sub) => sub.getProps().status === SubscriptionStatus.PENDING,
    )

    if (pendingSubscription) {
      return pendingSubscription
    }

    // Fall back to ACTIVE subscription
    const activeSubscription = subscriptions.data.find(
      (sub) => sub.getProps().status === SubscriptionStatus.ACTIVE,
    )

    if (activeSubscription) {
      return activeSubscription
    }

    // Return the most recent subscription if no PENDING or ACTIVE found
    return subscriptions.data[0]
  }

  /**
   * Handles plan change confirmation after successful payment
   * Now we can safely add new resource limits to Stripe and remove old ones
   */
  private async handlePlanChangeConfirmation(
    subscription: Subscription,
    stripeSubscriptionId: string,
  ): Promise<void> {
    this.logger.log(
      `Handling plan change confirmation for subscription ${subscription.id}. Adding new resource limits to Stripe and removing old ones.`,
    )

    try {
      // 1. Get the current Stripe subscription to determine the new plan
      const subscriptionProps = subscription.getProps()

      const currentStripeSubscription =
        await this.stripe.subscriptions.retrieve(stripeSubscriptionId, {
          expand: ['items'],
        })

      // Find the base plan item (non-metered) to determine the new plan
      const basePlanItem = currentStripeSubscription.items.data.find(
        (item) => item.price.recurring?.usage_type !== 'metered',
      )

      if (!basePlanItem) {
        this.logger.error(
          `Cannot find base plan item in Stripe subscription ${stripeSubscriptionId} for plan change confirmation`,
        )
        return
      }

      // Find the plan by the Stripe price ID
      const newPlanResult = await this.subscriptionPlanRepository.find({
        filter: { stripePriceId: basePlanItem.price.id },
      })

      if (!newPlanResult.data || newPlanResult.data.length === 0) {
        this.logger.error(
          `Cannot find subscription plan for Stripe price ID ${basePlanItem.price.id}`,
        )
        return
      }

      const newPlanByPrice = newPlanResult.data[0]

      // Get the plan with resource limits
      const newPlan = await this.subscriptionPlanRepository.findByIdWithLimits(
        newPlanByPrice.id.toString(),
      )

      if (!newPlan) {
        this.logger.error(
          `Cannot find new plan ${newPlanByPrice.id} with resource limits`,
        )
        return
      }

      this.logger.log(
        `Determined new plan ${newPlan.id} from Stripe base plan price ${basePlanItem.price.id} for subscription ${subscription.id}`,
      )

      const resourceLimits = newPlan.getAllResourceLimits()

      // 3. Build new items array: keep base plan, remove old metered items, add new metered items
      const items: Array<{
        id?: string
        price?: string
        // quantity?: number
        deleted?: boolean
        clear_usage?: boolean
      }> = []

      // Keep the base plan item (already updated during initiation)
      if (basePlanItem) {
        items.push({ id: basePlanItem.id }) // Keep existing base plan
      }

      // Mark all existing metered items for deletion
      const meteredItems = currentStripeSubscription.items.data.filter(
        (item) => item.price.recurring?.usage_type === 'metered',
      )
      for (const meteredItem of meteredItems) {
        items.push({
          id: meteredItem.id,
          deleted: true, // Remove old metered items
          clear_usage: true,
        })
      }

      // Add new resource limit metered items
      for (const resourceLimit of resourceLimits) {
        const limitProps = resourceLimit.getProps()
        if (limitProps.overageStripePriceId) {
          items.push({
            price: limitProps.overageStripePriceId,
            // quantity: 0, // Start with 0 usage
          })
        }
      }

      // 4. Update Stripe subscription with new resource limits
      if (items.length > 1) {
        // More than just base plan
        await this.stripe.subscriptions.update(stripeSubscriptionId, {
          items,
          proration_behavior: 'none', // No additional proration needed - payment already processed
        })

        this.logger.log(
          `Updated Stripe subscription ${stripeSubscriptionId} with ${resourceLimits.length} new resource limits and removed old metered items`,
        )
      }

      // 5. Create subscription items in our database for the new plan
      const updatedStripeSubscription =
        await this.stripe.subscriptions.retrieve(stripeSubscriptionId, {
          expand: ['items'],
        })

      // Create subscription items for the new plan
      await this.createSubscriptionItemsForPlanChange(
        subscription.id.toString(),
        newPlan,
        updatedStripeSubscription,
      )

      this.logger.log(
        `Successfully handled plan change confirmation for subscription ${subscription.id}. Added ${resourceLimits.length} new resource limits to Stripe and created corresponding database records.`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to handle plan change confirmation for subscription ${subscription.id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Creates subscription items in database for plan change confirmation
   */
  private async createSubscriptionItemsForPlanChange(
    subscriptionId: string,
    newPlan: SubscriptionPlan,
    stripeSubscription: Stripe.Subscription,
  ): Promise<void> {
    const stripeItems = stripeSubscription.items.data
    this.logger.log(
      `Creating subscription items for plan change confirmation. Plan: ${newPlan.id}, Items: ${stripeItems.length}`,
    )

    const resourceLimits = newPlan.getAllResourceLimits()
    const subscriptionEndDate = new Date(
      stripeSubscription.current_period_end * 1000,
    )

    try {
      for (const stripeItem of stripeItems) {
        const isBasePlan = stripeItem.price.recurring?.usage_type !== 'metered'

        if (isBasePlan) {
          // Create base plan subscription item
          const basePlanItem = SubscriptionItem.create({
            subscriptionId,
            subscriptionPlanId: newPlan.id.toString(),
            stripeSubscriptionItemId: stripeItem.id,
            stripePriceId: stripeItem.price.id,
            itemType: PlanType.BASE_PLAN,
            quantity: stripeItem.quantity || 1,
            status: SubscriptionStatus.ACTIVE,
            startDate: new Date(), // Current date as start date
            endDate: subscriptionEndDate, // Subscription period end date
            resourceType: null, // Base plan doesn't have resource type
            createdBy: 'stripe_webhook',
            updatedBy: 'stripe_webhook',
          })

          await this.subscriptionItemRepository.create(basePlanItem)

          this.logger.log(
            `Created base plan subscription item for plan ${newPlan.id}`,
          )
        } else {
          // Find matching resource limit for this metered item
          const matchingLimit = resourceLimits.find(
            (limit) =>
              limit.getProps().overageStripePriceId === stripeItem.price.id,
          )

          if (matchingLimit) {
            const limitProps = matchingLimit.getProps()
            const resourceLimitItem = SubscriptionItem.create({
              subscriptionId,
              subscriptionPlanId: newPlan.id.toString(),
              stripeSubscriptionItemId: stripeItem.id,
              stripePriceId: stripeItem.price.id,
              itemType: PlanType.ADDON_METERED_USAGE,
              quantity: stripeItem.quantity || 0,
              status: SubscriptionStatus.ACTIVE,
              startDate: new Date(), // Current date as start date
              endDate: subscriptionEndDate, // Subscription period end date
              resourceType: limitProps.resourceType, // Set the specific resource type
              createdBy: 'stripe_webhook',
              updatedBy: 'stripe_webhook',
            })

            await this.subscriptionItemRepository.create(resourceLimitItem)

            this.logger.log(
              `Created resource limit subscription item for ${matchingLimit.getProps().resourceType}`,
            )
          } else {
            this.logger.warn(
              `Could not find matching resource limit for Stripe price ${stripeItem.price.id}`,
            )
          }
        }
      }

      this.logger.log(
        `Successfully created ${stripeItems.length} subscription items for plan change`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to create subscription items for plan change: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Validates invoice data and extracts required fields
   */
  private validateInvoiceData(invoice: Stripe.Invoice): {
    stripeSubscriptionId: string | null
    customerId: string | null
  } {
    const stripeSubscriptionId = invoice['subscription'] as string | null
    const customerId = invoice.customer

    if (
      typeof stripeSubscriptionId !== 'string' ||
      typeof customerId !== 'string'
    ) {
      this.logger.error(
        `Invoice ${invoice.id} is missing subscription or customer ID.`,
      )
      return { stripeSubscriptionId: null, customerId: null }
    }

    return { stripeSubscriptionId, customerId }
  }

  /**
   * Processes payment for plan changes (PENDING -> ACTIVE)
   */
  private async processPlanChangePayment(
    subscription: Subscription,
    stripeSubscriptionId: string,
  ): Promise<void> {
    this.logger.log(
      `Confirming plan change for subscription ${subscription.id} after successful payment`,
    )

    try {
      // Update subscription status to ACTIVE
      const subscriptionProps = subscription.getProps()
      const updatedProps = {
        ...subscriptionProps,
        status: SubscriptionStatus.ACTIVE,
        gracePeriodEndsAt: null,
        updatedBy: 'stripe_webhook',
      }

      await this.subscriptionRepository.updateById(
        subscription.id,
        updatedProps,
      )

      // Add new resource limits to Stripe and sync items
      await this.handlePlanChangeConfirmation(
        subscription,
        stripeSubscriptionId,
      )

      this.logger.log(
        `Successfully confirmed plan change for subscription ${subscription.id} after payment confirmation`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to confirm plan change for subscription ${subscription.id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Processes regular payments (non-plan-change)
   */
  private async processRegularPayment(
    subscription: Subscription,
    invoiceId: string,
  ): Promise<void> {
    try {
      const subscriptionProps = subscription.getProps()
      const updatedProps = {
        ...subscriptionProps,
        status: SubscriptionStatus.ACTIVE,
        gracePeriodEndsAt: null, // Clear grace period on successful payment
        updatedBy: 'stripe_webhook',
      }

      await this.subscriptionRepository.updateById(
        subscription.id,
        updatedProps,
      )

      this.logger.log(
        `Updated subscription ${subscription.id} status to 'active' and cleared grace period for invoice ${invoiceId}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to update subscription ${subscription.id} for regular payment: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Handles plan change rollback for failed payments
   */
  private async processPlanChangeRollback(
    subscription: Subscription,
    subscriptionProps: any,
  ): Promise<void> {
    try {
      this.logger.log(
        `Rolling back plan change for subscription ${subscription.id} due to payment failure`,
      )

      // 1. Delete PENDING subscription (the failed plan change)
      await this.subscriptionRepository.delete(subscription.id)
      this.logger.log(`Deleted PENDING subscription ${subscription.id}`)

      // 2. Find and restore SUPERSEDED subscription to ACTIVE
      const supersededSubscription = await this.subscriptionRepository.findOne({
        filter: {
          organizationId: subscriptionProps.organizationId,
          stripeSubscriptionId: subscriptionProps.stripeSubscriptionId,
          status: SubscriptionStatus.SUPERSEDED,
        },
      })

      if (supersededSubscription) {
        const supersededProps = supersededSubscription.getProps()
        const restoredProps = {
          ...supersededProps,
          status: SubscriptionStatus.ACTIVE,
          updatedBy: 'stripe_webhook_rollback',
        }

        await this.subscriptionRepository.updateById(
          supersededSubscription.id,
          restoredProps,
        )

        this.logger.log(
          `Restored superseded subscription ${supersededSubscription.id} to ACTIVE status`,
        )
      } else {
        this.logger.warn(
          `No superseded subscription found to restore for organization ${subscriptionProps.organizationId}`,
        )
      }

      this.logger.log(
        `Successfully rolled back plan change for subscription ${subscription.id}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to rollback plan change for subscription ${subscription.id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Handles regular payment failure processing
   */
  private async processRegularPaymentFailure(
    subscription: Subscription,
    subscriptionProps: any,
    invoiceId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing regular payment failure for subscription ${subscription.id}, invoice ${invoiceId}`,
      )

      // Set grace period for payment retry (e.g., 7 days)
      const gracePeriodEndsAt = new Date()
      gracePeriodEndsAt.setDate(gracePeriodEndsAt.getDate() + 7)

      const updatedProps = {
        ...subscriptionProps,
        status: SubscriptionStatus.PAST_DUE,
        gracePeriodEndsAt,
        updatedBy: 'stripe_webhook',
      }

      await this.subscriptionRepository.updateById(
        subscription.id,
        updatedProps,
      )

      this.logger.log(
        `Updated subscription ${subscription.id} to PAST_DUE status with grace period ending at ${gracePeriodEndsAt.toISOString()}`,
      )

      // TODO: Trigger notification to user about payment failure
      // This could include email notification, dashboard alerts, etc.
      this.logger.log(
        `TODO: Notify user about payment failure for subscription ${subscription.id}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to process regular payment failure for subscription ${subscription.id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }
}
