import { z } from 'zod'
import { ResourceType } from '@libs/shared/constants/subscription'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

import { Period, PlanSource, PlanType } from '../types/subscription-plan.types'

import { PlanResourceLimit } from './plan-resource-limit.entity'

export const SubscriptionPlanSchema = z.object({
  name: z.string(),
  planType: z.nativeEnum(PlanType),
  source: z.nativeEnum(PlanSource),
  stripePriceId: z.string().optional(), // Base plan price only
  period: z.nativeEnum(Period).default(Period.MONTH),
  isActive: z.boolean().default(true),

  // Audit fields
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export type SubscriptionPlanProps = z.infer<typeof SubscriptionPlanSchema>

export const SubscriptionPlanResponse = SubscriptionPlanSchema.partial().extend(
  {
    id: z.string().optional(),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
    resourceLimits: z.array(z.any()).optional(),
    price: z
      .object({
        id: z.string(),
        amount: z.number().nullable(), // Amount in cents
        currency: z.string(),
        recurring: z.any().nullable(),
      })
      .optional(),
  },
)

export type SubscriptionPlanResponse = z.infer<typeof SubscriptionPlanResponse>

export class SubscriptionPlan extends AggregateRoot<
  SubscriptionPlanProps,
  SubscriptionPlanResponse
> {
  private _resourceLimits: PlanResourceLimit[] = []

  static create(props: SubscriptionPlanProps) {
    return new SubscriptionPlan({
      id: generateId(),
      props: {
        ...props,
        createdBy: props.createdBy,
        updatedBy: props.updatedBy,
      },
    })
  }

  static update({ id, props }: { id: Id; props: SubscriptionPlanProps }) {
    return new SubscriptionPlan({
      id: id,
      props: props,
    })
  }

  /**
   * Domain method to set the resource limits for this plan
   */
  public setResourceLimits(limits: PlanResourceLimit[]): void {
    this._resourceLimits = limits
  }

  /**
   * Domain method to get the resource limit for a specific resource type
   */
  public getResourceLimitFor(
    resourceType: ResourceType,
  ): PlanResourceLimit | undefined {
    return this._resourceLimits.find((limit) =>
      limit.appliesToResourceType(resourceType),
    )
  }

  /**
   * Domain method to get all resource limits for this plan
   */
  public getAllResourceLimits(): PlanResourceLimit[] {
    return this._resourceLimits
  }

  /**
   * Domain method to check if this plan has a specific resource limit
   */
  public hasResourceLimit(resourceType: ResourceType): boolean {
    return this.getResourceLimitFor(resourceType) !== undefined
  }

  /**
   * Domain method to get the included quantity for a specific resource type
   */
  public getIncludedQuantityFor(resourceType: ResourceType): number {
    const limit = this.getResourceLimitFor(resourceType)
    return limit ? limit.getProps().includedQuantity : 0
  }

  /**
   * Domain method to get the overage price ID for a specific resource type
   */
  public getOveragePriceIdFor(resourceType: ResourceType): string | null {
    const limit = this.getResourceLimitFor(resourceType)
    return limit ? limit.getOverageStripePriceId() : null
  }

  /**
   * Domain method to calculate overage for a specific resource type
   */
  public calculateOverageFor(
    resourceType: ResourceType,
    actualUsage: number,
  ): number {
    const limit = this.getResourceLimitFor(resourceType)
    return limit ? limit.calculateOverage(actualUsage) : 0
  }

  /**
   * Domain method to validate that all resource limits are properly configured
   */
  public validateResourceLimits(): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    for (const limit of this._resourceLimits) {
      const validation = limit.validate()
      if (!validation.isValid) {
        errors.push(
          `Resource limit for ${limit.getProps().resourceType}: ${validation.errors.join(', ')}`,
        )
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  public toResponse(): SubscriptionPlanResponse {
    const props = this.getProps()

    return {
      id: props.id,
      name: props.name,
      planType: props.planType,
      source: props.source,
      stripePriceId: props.stripePriceId,
      period: props.period,
      isActive: props.isActive,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
      resourceLimits: this._resourceLimits.map((limit) => limit.toResponse()),
    }
  }
}
