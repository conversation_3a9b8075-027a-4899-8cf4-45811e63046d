import { z } from 'zod'
import { UsageType } from '@libs/shared/constants/subscription'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

export const SubscriptionUsageSchema = z.object({
  subscriptionItemId: z.string().nullable().optional(), // FK to SubscriptionItem - links to the item against which usage is recorded
  organizationId: z.string(), // FK to Organization - denormalized for easier querying
  usageType: z.nativeEnum(UsageType), // Type of usage event - enum enforced for type safety
  usageValue: z.number().int(), // e.g., 1 for an SMS, number of overage members (integer only)
  usageTimestamp: z.coerce.date(), // When the usage occurred or was snapshotted
  billingCycleStartDate: z.coerce.date().nullable().optional(), // For overages, the cycle it pertains to
  billingCycleEndDate: z.coerce.date().nullable().optional(), // For overages, the cycle it pertains to
  reportedToStripe: z.boolean().nullable().optional(), // Indicates if successfully sent to Stripe
  stripePriceId: z.string().nullable().optional(), // FK to StripePrice - CRITICAL: specific Stripe Price ID used for reporting
  stripeUsageRecordId: z.string().nullable().optional(), // FK to StripeUsageRecord - Stripe's ID if reported
  isBillable: z.boolean().nullable().optional(), // true if billable, false if not billable, null if not applicable
  notes: z.string().nullable().optional(), // e.g., reason for not billable, error message

  // Audit fields
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export type SubscriptionUsageProps = z.infer<typeof SubscriptionUsageSchema>

export const SubscriptionUsageResponse =
  SubscriptionUsageSchema.partial().extend({
    id: z.string().optional(),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
  })

export type SubscriptionUsageResponse = z.infer<
  typeof SubscriptionUsageResponse
>

export class SubscriptionUsage extends AggregateRoot<
  SubscriptionUsageProps,
  SubscriptionUsageResponse
> {
  static create(props: SubscriptionUsageProps) {
    return new SubscriptionUsage({
      id: generateId(),
      props: {
        ...props,
        createdBy: props.createdBy,
        updatedBy: props.updatedBy,
      },
    })
  }

  static update({ id, props }: { id: Id; props: SubscriptionUsageProps }) {
    return new SubscriptionUsage({
      id: id,
      props: props,
    })
  }

  public toResponse(): SubscriptionUsageResponse {
    const props = this.getProps()

    return {
      id: props.id,
      subscriptionItemId: props.subscriptionItemId,
      organizationId: props.organizationId,
      usageType: props.usageType,
      usageValue: props.usageValue,
      usageTimestamp: props.usageTimestamp,
      billingCycleStartDate: props.billingCycleStartDate,
      billingCycleEndDate: props.billingCycleEndDate,
      reportedToStripe: props.reportedToStripe,
      stripePriceId: props.stripePriceId,
      stripeUsageRecordId: props.stripeUsageRecordId,
      isBillable: props.isBillable,
      notes: props.notes,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    }
  }
}
