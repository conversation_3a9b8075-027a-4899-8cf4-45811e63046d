import { z } from 'zod'
import {
  PlanType,
  SubscriptionStatus,
} from '@libs/shared/constants/subscription'
import { ResourceType } from '@libs/shared/constants/subscription/resource-mapping'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

export const SubscriptionItemSchema = z.object({
  subscriptionId: z.string(), // FK to Subscription
  stripeSubscriptionItemId: z.string().optional(), // FK to StripeSubscriptionItem - unique constraint
  stripePriceId: z.string(), // FK to StripePrice - the actual price of this item instance
  subscriptionPlanId: z.string(), // FK to SubscriptionPlan - denormalizes the plan choice
  itemType: z.nativeEnum(PlanType), // Denormalized from SubscriptionPlan for efficiency
  quantity: z.number().int().default(1), // Usually 1 for BASE_PLAN
  status: z.nativeEnum(SubscriptionStatus), // Mirrors Stripe item status

  // Required date fields for the database schema
  startDate: z.date().nullable(), // Start date of the subscription item
  endDate: z.date().nullable(), // End date of the subscription item

  // Resource type for overage items (null for base plan items)
  resourceType: z.nativeEnum(ResourceType).nullable().optional(),

  // Audit fields
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
  createdAt: z.date().optional().nullable(),
  updatedAt: z.date().optional().nullable(),
})

export type SubscriptionItemProps = z.infer<typeof SubscriptionItemSchema>

export const SubscriptionItemResponse = SubscriptionItemSchema.partial().extend(
  {
    id: z.string().optional(),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
  },
)

export type SubscriptionItemResponse = z.infer<typeof SubscriptionItemResponse>

export class SubscriptionItem extends AggregateRoot<
  SubscriptionItemProps,
  SubscriptionItemResponse
> {
  static create(props: SubscriptionItemProps) {
    return new SubscriptionItem({
      id: generateId(),
      props: {
        ...props, // Use provided props directly
        createdBy: props.createdBy,
        updatedBy: props.updatedBy,
      },
    })
  }

  static update({ id, props }: { id: Id; props: SubscriptionItemProps }) {
    return new SubscriptionItem({
      id: id,
      props: props,
    })
  }

  public toResponse(): SubscriptionItemResponse {
    const props = this.getProps()

    return {
      id: props.id,
      subscriptionId: props.subscriptionId,
      stripeSubscriptionItemId: props.stripeSubscriptionItemId,
      stripePriceId: props.stripePriceId,
      subscriptionPlanId: props.subscriptionPlanId,
      itemType: props.itemType,
      quantity: props.quantity,
      status: props.status,
      startDate: props.startDate,
      endDate: props.endDate,
      resourceType: props.resourceType,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    }
  }
}
