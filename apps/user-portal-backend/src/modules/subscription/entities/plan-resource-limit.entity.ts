import { z } from 'zod'
import { ResourceType } from '@libs/shared/constants/subscription'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

export const PlanResourceLimitSchema = z.object({
  subscriptionPlanId: z.string(), // FK to SubscriptionPlan
  resourceType: z.nativeEnum(ResourceType), // e.g., 'MEMBER', 'TEAM', 'CHECK', 'INTEGRATION', 'STATUS_PAGE'
  includedQuantity: z.number().int().min(0), // Number of units included in the plan
  overagePrice: z.number(), // Price per unit for overage (in cents)
  overageStripePriceId: z.string(), // FK to StripePrice - metered price for overage of this specific resource

  // Audit fields
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export type PlanResourceLimitProps = z.infer<typeof PlanResourceLimitSchema>

export const PlanResourceLimitResponse =
  PlanResourceLimitSchema.partial().extend({
    id: z.string().optional(),
    createdAt: z.date().optional(),
    updatedAt: z.date().optional(),
    overagePrice: z
      .object({
        id: z.string(),
        amount: z.number().nullable(), // Amount in cents
        currency: z.string(),
        recurring: z.any().nullable(),
      })
      .optional(),
  })

export type PlanResourceLimitResponse = z.infer<
  typeof PlanResourceLimitResponse
>

export class PlanResourceLimit extends AggregateRoot<
  PlanResourceLimitProps,
  PlanResourceLimitResponse
> {
  static create(props: PlanResourceLimitProps) {
    const limit = new PlanResourceLimit({
      id: generateId(),
      props: {
        ...props,
        createdBy: props.createdBy,
        updatedBy: props.updatedBy,
      },
    })

    return limit
  }

  static update({ id, props }: { id: Id; props: PlanResourceLimitProps }) {
    return new PlanResourceLimit({
      id: id,
      props: props,
    })
  }

  /**
   * Domain method to check if this limit allows a specific quantity
   */
  public allowsQuantity(requestedQuantity: number): boolean {
    const props = this.getProps()
    return requestedQuantity <= props.includedQuantity
  }

  /**
   * Domain method to calculate overage amount for a given usage
   */
  public calculateOverage(actualUsage: number): number {
    const props = this.getProps()
    return Math.max(0, actualUsage - props.includedQuantity)
  }

  /**
   * Domain method to check if this limit applies to a specific resource type
   */
  public appliesToResourceType(resourceType: ResourceType): boolean {
    const props = this.getProps()
    return props.resourceType === resourceType
  }

  /**
   * Domain method to get the overage stripe price ID for billing
   */
  public getOverageStripePriceId(): string {
    const props = this.getProps()
    return props.overageStripePriceId
  }

  /**
   * Domain method to validate the resource limit configuration
   */
  public validate(): { isValid: boolean; errors: string[] } {
    const props = this.getProps()
    const errors: string[] = []

    if (!props.subscriptionPlanId) {
      errors.push('Subscription plan ID is required')
    }

    if (!props.resourceType) {
      errors.push('Resource type is required')
    }

    if (props.includedQuantity < 0) {
      errors.push('Included quantity cannot be negative')
    }

    if (!props.overageStripePriceId) {
      errors.push('Overage Stripe price ID is required')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  public toResponse(): PlanResourceLimitResponse {
    const props = this.getProps()

    return {
      id: props.id,
      subscriptionPlanId: props.subscriptionPlanId,
      resourceType: props.resourceType,
      includedQuantity: props.includedQuantity,
      overagePrice: {
        id: props.overageStripePriceId,
        amount: props.overagePrice,
        currency: 'usd',
        recurring: {
          interval: 'month',
          usage_type: 'metered',
        },
      },
      overageStripePriceId: props.overageStripePriceId,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    }
  }
}
