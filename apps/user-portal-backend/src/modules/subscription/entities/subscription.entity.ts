import { z } from 'zod'
import {
  SubscriptionStatus,
  OveragePolicyType,
} from '@libs/shared/constants/subscription'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

import { PlanSource } from '../types/subscription-plan.types'

export const SubscriptionSchema = z.object({
  organizationId: z.string(), // FK to Organization
  stripeSubscriptionId: z.string(), // FK to StripeSubscription - unique constraint
  stripeCustomerId: z.string(), // FK to StripeCustomer
  status: z.nativeEnum(SubscriptionStatus),
  currentPeriodStartDate: z.coerce.date().nullable(),
  currentPeriodEndDate: z.coerce.date().nullable(),
  gracePeriodEndsAt: z.coerce.date().nullable().optional(), // For payment delinquency
  usageBillingEnabled: z.boolean().default(true),
  overagePolicyFixedResources: z
    .nativeEnum(OveragePolicyType)
    .default(OveragePolicyType.CHARGE),
  canceledAt: z.coerce.date().nullable().optional(),

  // Audit fields
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
  source: z.nativeEnum(PlanSource),
})

export type SubscriptionProps = z.infer<typeof SubscriptionSchema>

export const SubscriptionResponse = SubscriptionSchema.partial().extend({
  id: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
})

export type SubscriptionResponse = z.infer<typeof SubscriptionResponse>

export class Subscription extends AggregateRoot<
  SubscriptionProps,
  SubscriptionResponse
> {
  static create(props: SubscriptionProps) {
    return new Subscription({
      id: generateId(),
      props: {
        ...props,
        createdBy: props.createdBy,
        updatedBy: props.updatedBy,
      },
    })
  }

  static update({ id, props }: { id: Id; props: SubscriptionProps }) {
    return new Subscription({
      id: id,
      props: props,
    })
  }

  public toResponse(): SubscriptionResponse {
    const props = this.getProps()

    return {
      id: props.id,
      organizationId: props.organizationId,
      stripeSubscriptionId: props.stripeSubscriptionId,
      stripeCustomerId: props.stripeCustomerId,
      status: props.status,
      currentPeriodStartDate: props.currentPeriodStartDate,
      currentPeriodEndDate: props.currentPeriodEndDate,
      gracePeriodEndsAt: props.gracePeriodEndsAt,
      usageBillingEnabled: props.usageBillingEnabled,
      overagePolicyFixedResources: props.overagePolicyFixedResources,
      canceledAt: props.canceledAt,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
      source: props.source,
    }
  }

  /**
   * Helper methods for plan change status management
   */
  public isPendingPlanChange(): boolean {
    return this.getProps().status === SubscriptionStatus.PENDING
  }

  public isSuperseded(): boolean {
    return this.getProps().status === SubscriptionStatus.SUPERSEDED
  }

  public canInitiatePlanChange(): boolean {
    const status = this.getProps().status
    return (
      status === SubscriptionStatus.ACTIVE ||
      status === SubscriptionStatus.TRIALING
    )
  }

  public isValidForWebhookProcessing(): boolean {
    const status = this.getProps().status
    return ![
      SubscriptionStatus.CANCELED,
      SubscriptionStatus.SUPERSEDED,
      SubscriptionStatus.INCOMPLETE_EXPIRED,
    ].includes(status)
  }
}
