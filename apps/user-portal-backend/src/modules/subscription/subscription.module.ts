import { Module, forwardRef } from '@nestjs/common'
import { TimeUtils } from '@libs/shared/time/time.utils'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { DynamoModule } from '@backend/frameworks/dynamo/dynamo.module'
import { NotificationsModule } from '@backend/frameworks/notification/notification.module'
import { QueueModule } from '@backend/frameworks/queue/queue.module'
import { QueueService } from '@backend/frameworks/queue/queue.service'
import { SUBSCRIPTION_PLAN_REPOSITORY } from '@backend/modules/subscription/applications/subscription-plan.repository'
import { SubscriptionPlanInfrastructure } from '@backend/modules/subscription/infras/subscription-plan.infrastructure'
import { SUBSCRIPTION_REPOSITORY } from '@backend/modules/subscription/applications/subscription.repository'
import { SubscriptionInfrastructure } from '@backend/modules/subscription/infras/subscription.infrastructure'
import { SubscriptionPlanController } from '@backend/modules/subscription/controllers/subscription-plan.controller'
import { SubscriptionPlanUseCase } from '@backend/modules/subscription/usecases/subscription-plan.usecase'
import { InfluxModule } from '@backend/frameworks/influx/influx.module'
import { BullClient } from '@backend/frameworks/queue/bull/bull.client'
import { SubscriptionController } from '@backend/modules/subscription/controllers/subscription.controller'
import { StripeWebhookController } from '@backend/modules/subscription/controllers/stripe-webhook.controller'
import { SUBSCRIPTION_USAGE_REPOSITORY } from '@backend/modules/subscription/applications/subscription-usage.repository'
import { SubscriptionUsageInfrastructure } from '@backend/modules/subscription/infras/subscription-usage.infrastructure'
import { USER_REPOSITORY } from '@backend/modules/user/applications/users.repository'
import { UserInfrastructure } from '@backend/modules/user/infras/user.infrastructure'
import { ORGANIZATION_REPOSITORY } from '@backend/modules/user/applications/organization.repository'
import { OrganizationInfrastructure } from '@backend/modules/user/infras/organization.infrastructure'
import { UserModule } from '@backend/modules/user/user.module'
import { CheckModule } from '@backend/modules/check/check.module'
import { IntegrationSettingModule } from '@backend/modules/integration-setting/integration-setting.module'
import { TEAM_REPOSITORY } from '@backend/modules/user/applications/team.repository'
import { TeamInfrastructure } from '@backend/modules/user/infras/team.infrastructure'
import { CHECK_REPOSITORY } from '@backend/modules/check/applications/check.repository'
import { CheckInfrastructure } from '@backend/modules/check/infras/check.infrastructure'
import { INTEGRATION_SETTING_REPOSITORY } from '@backend/modules/integration-setting/applications/integration-setting.repository'
import { IntegrationSettingInfrastructure } from '@backend/modules/integration-setting/infras/integration-setting.infrastructure'
import { SubscriptionLimitGuard } from '@backend/commons/guard/subscription-limit.guard'
import { SUBSCRIPTION_ITEM_REPOSITORY } from '@backend/modules/subscription/applications/subscription-item.repository'
import { SubscriptionItemInfrastructure } from '@backend/modules/subscription/infras/subscription-item.infrastructure'
import { PLAN_RESOURCE_LIMIT_REPOSITORY } from '@backend/modules/subscription/applications/plan-resource-limit.repository'
import { PlanResourceLimitInfrastructure } from '@backend/modules/subscription/infras/plan-resource-limit.infrastructure'

import { StatusPageModule } from '../status-page/status-page.module'

import { StripeUseCase } from './usecases/stripe.usecase'
import { SubscriptionLimitService } from './services/subscription-limit.service'
import { ResourceManipulationQueueService } from './infras/sqs/resource-manipulation-queue.service'
import { ResourceBillingService } from './services/resource-billing.service'
import { PlanOverageService } from './services/plan-overage.service'
import { OverageQuantityService } from './services/overage-quantity.service'
import { UsageCalculationService } from './services/usage-calculation.service'
import { StripeIntegrationHelper } from './services/stripe-integration-helper.service'
import { SubscriptionManagementUseCase } from './usecases/subscription-management.usecase'
import { SubscriptionCheckoutUseCase } from './usecases/subscription-checkout.usecase'
import { SubscriptionDetailsUseCase } from './usecases/subscription-details.usecase'
import { SubscriptionPlanChangeUseCase } from './usecases/subscription-plan-change.usecase'

@Module({
  imports: [
    DatabaseModule,
    DynamoModule,
    NotificationsModule,
    InfluxModule,
    QueueModule.register({ queues: [] }), // Register to access ResourceManipulationQueueService
    forwardRef(() => UserModule),
    CheckModule,
    IntegrationSettingModule,
    forwardRef(() => StatusPageModule),
  ],
  controllers: [
    SubscriptionPlanController,
    SubscriptionController,
    StripeWebhookController,
  ],
  providers: [
    {
      provide: SUBSCRIPTION_PLAN_REPOSITORY,
      useClass: SubscriptionPlanInfrastructure,
    },
    {
      provide: SUBSCRIPTION_REPOSITORY,
      useClass: SubscriptionInfrastructure,
    },
    {
      provide: SUBSCRIPTION_ITEM_REPOSITORY,
      useClass: SubscriptionItemInfrastructure,
    },
    {
      provide: SUBSCRIPTION_USAGE_REPOSITORY,
      useClass: SubscriptionUsageInfrastructure,
    },
    {
      provide: USER_REPOSITORY,
      useClass: UserInfrastructure,
    },
    {
      provide: ORGANIZATION_REPOSITORY,
      useClass: OrganizationInfrastructure,
    },
    {
      provide: TEAM_REPOSITORY,
      useClass: TeamInfrastructure,
    },
    {
      provide: CHECK_REPOSITORY,
      useClass: CheckInfrastructure,
    },
    {
      provide: INTEGRATION_SETTING_REPOSITORY,
      useClass: IntegrationSettingInfrastructure,
    },
    {
      provide: PLAN_RESOURCE_LIMIT_REPOSITORY,
      useClass: PlanResourceLimitInfrastructure,
    },
    QueueService,
    TimeUtils,
    SubscriptionPlanUseCase,
    BullClient,
    StripeUseCase,
    SubscriptionLimitService,
    SubscriptionLimitGuard,
    ResourceManipulationQueueService,
    ResourceBillingService,
    PlanOverageService,
    OverageQuantityService,
    // New services and use cases
    UsageCalculationService,
    StripeIntegrationHelper,
    SubscriptionManagementUseCase,
    SubscriptionCheckoutUseCase,
    SubscriptionDetailsUseCase,
    SubscriptionPlanChangeUseCase,
  ],
  exports: [
    SubscriptionLimitService,
    SubscriptionLimitGuard,
    ResourceBillingService,
    // Export new use cases for controller usage
    SubscriptionManagementUseCase,
    SubscriptionCheckoutUseCase,
    SubscriptionDetailsUseCase,
    SubscriptionPlanChangeUseCase,
  ],
})
export class SubscriptionModule {}
