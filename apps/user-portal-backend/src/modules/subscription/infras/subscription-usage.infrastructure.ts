import { Inject, Injectable } from '@nestjs/common'
import { DeepPartial } from 'typeorm'
import {
  ResourceType,
  SubscriptionStatus,
  UsageType,
} from '@libs/shared/constants/subscription'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { SubscriptionUsageModel } from '@backend/frameworks/database/models/subscription-usage.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import {
  SubscriptionUsage,
  SubscriptionUsageProps,
} from '@backend/modules/subscription/entities'
import SubscriptionUsageRepository from '@backend/modules/subscription/applications/subscription-usage.repository'
import OrganizationRepository, {
  ORGANIZATION_REPOSITORY,
} from '@backend/modules/user/applications/organization.repository'
import TeamRepository, {
  TEAM_REPOSITORY,
} from '@backend/modules/user/applications/team.repository'
import CheckRepository, {
  CHECK_REPOSITORY,
} from '@backend/modules/check/applications/check.repository'
import IntegrationSettingRepository, {
  INTEGRATION_SETTING_REPOSITORY,
} from '@backend/modules/integration-setting/applications/integration-setting.repository'
import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import SubscriptionItemRepository, {
  SUBSCRIPTION_ITEM_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-item.repository'

@Injectable()
export class SubscriptionUsageInfrastructure
  implements SubscriptionUsageRepository
{
  private readonly subscriptionUsageModel: TypeORMDriver<SubscriptionUsageModel>

  constructor(
    @Inject(Database) private database: Database,
    @Inject(ORGANIZATION_REPOSITORY)
    private organizationRepository: OrganizationRepository,
    @Inject(TEAM_REPOSITORY)
    private teamRepository: TeamRepository,
    @Inject(CHECK_REPOSITORY)
    private checkRepository: CheckRepository,
    @Inject(INTEGRATION_SETTING_REPOSITORY)
    private integrationSettingRepository: IntegrationSettingRepository,
    @Inject(SUBSCRIPTION_REPOSITORY)
    private subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private subscriptionPlanRepository: SubscriptionPlanRepository,
    @Inject(SUBSCRIPTION_ITEM_REPOSITORY)
    private subscriptionItemRepository: SubscriptionItemRepository,
  ) {
    this.subscriptionUsageModel = this.database.typeorm<SubscriptionUsageModel>(
      'SubscriptionUsageModel',
    )
  }

  async getCurrentUsageByOrganizationId(
    organizationId: Id,
  ): Promise<Record<string, number>> {
    // Get the organization's current plan to discover resource types dynamically
    const subscription =
      await this.getActiveSubscriptionForOrganization(organizationId)
    if (!subscription) {
      return this.getDefaultUsageCounts()
    }

    // Get the first subscription plan from the subscription items
    const subscriptionPlanId =
      subscription.subscriptionItems?.[0]?.subscriptionPlanId
    if (!subscriptionPlanId) {
      return this.getDefaultUsageCounts()
    }

    const plan = await this.getSubscriptionPlanWithLimits(subscriptionPlanId)
    if (!plan) {
      return this.getDefaultUsageCounts()
    }

    // Dynamically build usage queries based on plan resource limits
    const usagePromises: Promise<{ type: string; count: number }>[] = []
    const usage: Record<string, number> = {}

    // Process each resource limit defined in the plan
    for (const resourceLimit of plan.getAllResourceLimits()) {
      const resourceType = resourceLimit.getProps().resourceType

      // Map resource type to the appropriate counting method
      const countPromise = this.getResourceCount(
        organizationId,
        resourceType as ResourceType,
      )
      usagePromises.push(
        countPromise.then((count) => ({ type: resourceType, count })),
      )
    }

    // Also get transactional usage types (SMS, PHONE_CALL)
    const transactionalTypes = ['SMS', 'PHONE_CALL']
    for (const usageType of transactionalTypes) {
      usagePromises.push(
        this.getTransactionalUsage(organizationId, usageType).then((count) => ({
          type: usageType.toLowerCase().replace('_', ''),
          count,
        })),
      )
    }

    // Execute all counting queries in parallel
    const results = await Promise.all(usagePromises)

    // Build the usage object dynamically
    for (const result of results) {
      const usageKey = this.getUsageKey(result.type)
      usage[usageKey] = result.count
    }

    return usage
  }

  toDomain = (
    subscriptionUsageModel: SubscriptionUsageModel,
  ): SubscriptionUsage => {
    const { id, createdAt, updatedAt, ...props } = subscriptionUsageModel
    return new SubscriptionUsage({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        usageType: props.usageType as UsageType, // Cast to avoid enum mismatch
      },
    })
  }

  async create(
    subscriptionUsage: SubscriptionUsage,
  ): Promise<SubscriptionUsage> {
    const subscriptionUsageRawProps = subscriptionUsage.getProps()
    const payloadForCreate = {
      ...subscriptionUsageRawProps,
      usageType: subscriptionUsageRawProps.usageType as string,
      billingCycleStartDate:
        subscriptionUsageRawProps.billingCycleStartDate === null
          ? undefined
          : subscriptionUsageRawProps.billingCycleStartDate,
      billingCycleEndDate:
        subscriptionUsageRawProps.billingCycleEndDate === null
          ? undefined
          : subscriptionUsageRawProps.billingCycleEndDate,
    }

    const newSubscriptionUsage = await this.subscriptionUsageModel
      .create(payloadForCreate as DeepPartial<SubscriptionUsageModel>)
      .then(this.toDomain)

    return newSubscriptionUsage
  }

  async delete(id: Id): Promise<boolean> {
    await this.subscriptionUsageModel.softDelete({
      id,
    })
    return true
  }

  async findById(id: Id): Promise<Nullable<SubscriptionUsage>> {
    return this.subscriptionUsageModel.findById(id, {}).then((usage) => {
      if (!usage) {
        return null
      }

      return this.toDomain(usage)
    })
  }

  async update(
    id: Id,
    subscriptionUsageUpdateProps: Partial<SubscriptionUsageProps>,
  ): Promise<Nullable<SubscriptionUsage>> {
    const currentSubscriptionUsage =
      await this.subscriptionUsageModel.findById(id)
    if (!currentSubscriptionUsage) return null

    const payloadForUpdate = {
      ...subscriptionUsageUpdateProps,
      usageType: subscriptionUsageUpdateProps.usageType
        ? (subscriptionUsageUpdateProps.usageType as string)
        : undefined,
      billingCycleStartDate:
        subscriptionUsageUpdateProps.billingCycleStartDate === null
          ? undefined
          : subscriptionUsageUpdateProps.billingCycleStartDate,
      billingCycleEndDate:
        subscriptionUsageUpdateProps.billingCycleEndDate === null
          ? undefined
          : subscriptionUsageUpdateProps.billingCycleEndDate,
    }

    const updatedSubscriptionUsage =
      await this.subscriptionUsageModel.updateById(
        id,
        payloadForUpdate as DeepPartial<SubscriptionUsageModel>,
      )

    if (!updatedSubscriptionUsage) return null

    return this.toDomain(updatedSubscriptionUsage)
  }

  async findByOrganizationId(
    organizationId: Id,
  ): Promise<Nullable<SubscriptionUsage>> {
    const usage = await this.subscriptionUsageModel.findOne({
      filter: {
        organizationId,
      },
      orderBy: {
        createdAt: 'DESC',
      },
    })

    if (!usage) return null

    return this.toDomain(usage)
  }

  async findAll(options?: {
    filter?: Record<string, unknown>
    limit?: number
    offset?: number
    orderBy?: Record<string, 'ASC' | 'DESC'>
  }): Promise<SubscriptionUsage[]> {
    const queryOptions: ITypeOrmFilter<SubscriptionUsageModel> = {
      filter: options?.filter,
      orderBy: options?.orderBy || { usageTimestamp: 'DESC' },
      limit: options?.limit,
      page: options?.offset
        ? Math.floor(options.offset / (options.limit || 10)) + 1
        : 1,
    }

    if (options?.limit) {
      const result = await this.subscriptionUsageModel.find(queryOptions)
      return result.data.map(this.toDomain)
    } else {
      const result = await this.subscriptionUsageModel.findAll(queryOptions)
      return result.map(this.toDomain)
    }
  }

  async count(options?: { filter?: Record<string, unknown> }): Promise<number> {
    return this.subscriptionUsageModel.count(options?.filter || {})
  }

  /**
   * Create a subscription usage event log record (for overages, SMS, phone calls)
   * Following the event-based logging pattern as per PRD requirements
   */
  async createUsageEventRecord(params: {
    organizationId: string
    subscriptionItemId?: string
    usageType: string
    usageValue: number
    usageTimestamp?: Date
    billingCycleStartDate?: Date
    billingCycleEndDate?: Date
    stripePriceId?: string
    stripeUsageRecordId?: string
    reportedToStripe?: boolean
    isBillable?: boolean
    notes?: string
    createdBy?: string
  }): Promise<SubscriptionUsage> {
    const usage = SubscriptionUsage.create({
      organizationId: params.organizationId,
      subscriptionItemId: params.subscriptionItemId,
      usageType: params.usageType as UsageType,
      usageValue: params.usageValue,
      usageTimestamp: params.usageTimestamp || new Date(),
      billingCycleStartDate: params.billingCycleStartDate,
      billingCycleEndDate: params.billingCycleEndDate,
      stripePriceId: params.stripePriceId,
      stripeUsageRecordId: params.stripeUsageRecordId,
      reportedToStripe: params.reportedToStripe,
      isBillable: params.isBillable,
      notes: params.notes,
      createdBy: params.createdBy,
      updatedBy: params.createdBy,
    })

    const result = await this.create(usage)
    return result
  }

  /**
   * Get usage records for a specific billing period
   */
  async getUsageByBillingPeriod(
    organizationId: string,
    startDate: Date,
    endDate: Date,
    usageType?: string,
  ): Promise<SubscriptionUsage[]> {
    const filter: Record<string, unknown> = {
      organizationId,
      usageTimestamp: {
        $gte: startDate,
        $lte: endDate,
      },
    }

    if (usageType) {
      filter.usageType = usageType
    }

    return this.findAll({ filter })
  }

  /**
   * Get active subscription for an organization
   */
  private async getActiveSubscriptionForOrganization(
    organizationId: string,
  ): Promise<{
    id: string
    subscriptionItems: { subscriptionPlanId: string }[]
  } | null> {
    try {
      const subscription = await this.subscriptionRepository.findOne({
        filter: {
          organizationId,
          status: SubscriptionStatus.ACTIVE,
        },
      })

      if (!subscription) {
        return null
      }

      const subscriptionItemsResult =
        await this.subscriptionItemRepository.find({
          filter: {
            subscriptionId: subscription.id,
          },
        })
      const subscriptionItems = subscriptionItemsResult.data

      return {
        id: subscription.id,
        subscriptionItems: subscriptionItems.map((item) => ({
          subscriptionPlanId: item.getProps().subscriptionPlanId,
        })),
      }
    } catch (error) {
      console.error(
        `Failed to get active subscription for organization ${organizationId}:`,
        error,
      )
      return null
    }
  }

  /**
   * Get subscription plan with limits
   */
  private async getSubscriptionPlanWithLimits(planId: string): Promise<{
    getAllResourceLimits: () => { getProps: () => { resourceType: string } }[]
  } | null> {
    try {
      const plan =
        await this.subscriptionPlanRepository.findByIdWithLimits(planId)
      return plan
    } catch (error) {
      console.error(
        `Failed to get subscription plan with limits for plan ${planId}:`,
        error,
      )
      return null
    }
  }

  /**
   * Get default usage counts when no plan is found
   */
  private getDefaultUsageCounts(): Record<string, number> {
    return {
      totalUsers: 0,
      totalTeams: 0,
      totalChecks: 0,
      totalIntegrations: 0,
      integrations: 0,
      sms: 0,
      phoneCalls: 0,
    }
  }

  /**
   * Get resource count for a specific resource type dynamically
   */
  private async getResourceCount(
    organizationId: string,
    resourceType: ResourceType,
  ): Promise<number> {
    try {
      // Dynamic repository selection based on resource type
      switch (resourceType) {
        case ResourceType.MEMBER:
          return this.organizationRepository.countActiveUsersByOrganizationId(
            organizationId,
          )
        case ResourceType.TEAM:
          return this.teamRepository.countTeamsByOrganizationId(organizationId)
        case ResourceType.CHECK:
          return this.checkRepository.countChecksByOrganizationId(
            organizationId,
          )
        case ResourceType.INTEGRATION:
          return this.integrationSettingRepository.countIntegrationsByOrganizationId(
            organizationId,
          )
        case ResourceType.STATUS_PAGE:
          // TODO: Implement status page counting when repository is available
          return 0
        default:
          // For any new resource types, return 0 until repository method is implemented
          console.warn(
            `No counting method implemented for resource type: ${resourceType}`,
          )
          return 0
      }
    } catch (error) {
      console.error(`Failed to get resource count for ${resourceType}:`, error)
      return 0
    }
  }

  /**
   * Get transactional usage (SMS, PHONE_CALL) from event log
   */
  private async getTransactionalUsage(
    organizationId: string,
    usageType: string,
  ): Promise<number> {
    try {
      const usageRecords = await this.subscriptionUsageModel.findAll({
        filter: {
          organizationId,
          usageType,
        },
      })

      return usageRecords.reduce(
        (total, record) => total + record.usageValue,
        0,
      )
    } catch (error) {
      console.error(
        `Failed to get transactional usage for ${usageType}:`,
        error,
      )
      return 0
    }
  }

  /**
   * Convert resource type to usage key format
   */
  private getUsageKey(resourceType: string): string {
    const keyMap: Record<string, string> = {
      [ResourceType.MEMBER]: 'totalUsers',
      [ResourceType.TEAM]: 'totalTeams',
      [ResourceType.CHECK]: 'totalChecks',
      [ResourceType.INTEGRATION]: 'totalIntegrations',
      [ResourceType.STATUS_PAGE]: 'totalStatusPages',
      sms: 'sms',
      phoneCall: 'phoneCalls',
    }

    return keyMap[resourceType] || resourceType.toLowerCase()
  }
}
