import { Injectable, Logger } from '@nestjs/common'
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs'

import { configService } from '@backend/cores/config/config.service'

export interface ResourceManipulationEvent {
  organizationId: string
  resourceType: 'MEMBER' | 'TEAM' | 'CHECK' | 'INTEGRATION' | 'STATUS_PAGE'
  eventTimestamp: string // ISO 8601 format
  action?: 'CREATE' | 'DELETE' | 'UPDATE' // Optional: for future extensibility
  resourceId?: string // Optional: for tracking specific resources

  // Plan context to prevent race conditions during overage calculation
  planContext?: {
    subscriptionPlanId: string
    resourceLimits: Array<{
      resourceType: string
      includedQuantity: number
      overageStripePriceId: string
    }>
    overagePolicyFixedResources: string // 'BLOCK' | 'CHARGE' | 'GRACE'
  }
}

@Injectable()
export class ResourceManipulationQueueService {
  private readonly logger = new Logger(ResourceManipulationQueueService.name)
  private readonly sqsClient: SQSClient
  private readonly queueUrl: string

  constructor() {
    try {
      this.sqsClient = configService.getSqsClient()
      this.queueUrl = this.getResourceManipulationQueueUrl()
      this.logger.log(
        `Initialized ResourceManipulationQueue with URL: ${this.queueUrl}`,
      )
    } catch (error) {
      this.logger.error(
        'Failed to initialize ResourceManipulationQueueService',
        {
          error: error.message,
          stack: error.stack,
        },
      )
      throw error
    }
  }

  /**
   * Publishes a resource manipulation event to the ResourceManipulationQueue
   * Events are used for tracking resource usage for billing purposes
   */
  async publishResourceEvent(event: ResourceManipulationEvent): Promise<void> {
    try {
      const messageBody = JSON.stringify({
        ...event,
        timestamp: new Date().toISOString(), // Add internal timestamp for processing
      })

      const command = new SendMessageCommand({
        QueueUrl: this.queueUrl,
        MessageBody: messageBody,
        MessageAttributes: {
          organizationId: {
            DataType: 'String',
            StringValue: event.organizationId,
          },
          resourceType: {
            DataType: 'String',
            StringValue: event.resourceType,
          },
          eventTimestamp: {
            DataType: 'String',
            StringValue: event.eventTimestamp,
          },
        },
      })

      await this.sqsClient.send(command)

      this.logger.debug(
        `Published resource manipulation event: ${event.resourceType} for organization ${event.organizationId}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to publish resource manipulation event: ${error.message}`,
        {
          event,
          error: error.stack,
        },
      )
      // Don't throw error to avoid blocking primary operations
      // The billing system should be resilient and handle missing events
    }
  }

  /**
   * Convenience method for publishing member events
   */
  async publishMemberEvent(
    organizationId: string,
    action: 'CREATE' | 'DELETE' | 'UPDATE' = 'CREATE',
    resourceId?: string,
    planContext?: ResourceManipulationEvent['planContext'],
  ): Promise<void> {
    await this.publishResourceEvent({
      organizationId,
      resourceType: 'MEMBER',
      eventTimestamp: new Date().toISOString(),
      action,
      resourceId,
      planContext,
    })
  }

  /**
   * Convenience method for publishing team events
   */
  async publishTeamEvent(
    organizationId: string,
    action: 'CREATE' | 'DELETE' | 'UPDATE' = 'CREATE',
    resourceId?: string,
    planContext?: ResourceManipulationEvent['planContext'],
  ): Promise<void> {
    await this.publishResourceEvent({
      organizationId,
      resourceType: 'TEAM',
      eventTimestamp: new Date().toISOString(),
      action,
      resourceId,
      planContext,
    })
  }

  /**
   * Convenience method for publishing check events
   */
  async publishCheckEvent(
    organizationId: string,
    action: 'CREATE' | 'DELETE' | 'UPDATE' = 'CREATE',
    resourceId?: string,
    planContext?: ResourceManipulationEvent['planContext'],
  ): Promise<void> {
    await this.publishResourceEvent({
      organizationId,
      resourceType: 'CHECK',
      eventTimestamp: new Date().toISOString(),
      action,
      resourceId,
      planContext,
    })
  }

  /**
   * Convenience method for publishing integration events
   */
  async publishIntegrationEvent(
    organizationId: string,
    action: 'CREATE' | 'DELETE' | 'UPDATE' = 'CREATE',
    resourceId?: string,
    planContext?: ResourceManipulationEvent['planContext'],
  ): Promise<void> {
    await this.publishResourceEvent({
      organizationId,
      resourceType: 'INTEGRATION',
      eventTimestamp: new Date().toISOString(),
      action,
      resourceId,
      planContext,
    })
  }

  /**
   * Convenience method for publishing status page events
   */
  async publishStatusPageEvent(
    organizationId: string,
    action: 'CREATE' | 'DELETE' | 'UPDATE' = 'CREATE',
    resourceId?: string,
    planContext?: ResourceManipulationEvent['planContext'],
  ): Promise<void> {
    await this.publishResourceEvent({
      organizationId,
      resourceType: 'STATUS_PAGE',
      eventTimestamp: new Date().toISOString(),
      action,
      resourceId,
      planContext,
    })
  }

  /**
   * Gets the ResourceManipulationQueue URL from configuration
   */
  private getResourceManipulationQueueUrl(): string {
    const env = configService.getValue('ENV', false) || 'development'
    const region = configService.getValue('AWS_REGION')
    const accountId = configService.getValue('AWS_ACCOUNT_ID')

    if (!region || !accountId) {
      throw new Error(
        'AWS_REGION and AWS_ACCOUNT_ID must be configured for ResourceManipulationQueue',
      )
    }

    // For local development, use LocalStack URL if available
    if (configService.getValue('LOCALSTACK_URL', false)) {
      return `${configService.getValue('LOCALSTACK_URL')}/************/resource-manipulation-queue-${env}`
    }

    // For AWS, construct the standard SQS URL
    return `https://sqs.${region}.amazonaws.com/${accountId}/resource-manipulation-queue-${env}`
  }
}
