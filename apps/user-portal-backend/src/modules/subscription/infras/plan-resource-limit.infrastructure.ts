import { Injectable, Inject, Logger } from '@nestjs/common'
import { DeepPartial } from 'typeorm'
import { ResourceType } from '@libs/shared/constants/subscription'

import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { PlanResourceLimitModel } from '@backend/frameworks/database/models/plan-resource-limit.model'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'

import {
  PlanResourceLimit,
  PlanResourceLimitProps,
} from '../entities/plan-resource-limit.entity'
import PlanResourceLimitRepository from '../applications/plan-resource-limit.repository'

@Injectable()
export class PlanResourceLimitInfrastructure
  implements PlanResourceLimitRepository
{
  private readonly logger = new Logger(PlanResourceLimitInfrastructure.name)
  private readonly model: TypeORMDriver<PlanResourceLimitModel>

  constructor(@Inject(Database) private database: Database) {
    this.model = this.database.typeorm<PlanResourceLimitModel>(
      'PlanResourceLimitModel',
    )
  }

  toDomain = (model: PlanResourceLimitModel): PlanResourceLimit => {
    const { id, createdAt, updatedAt, ...props } = model
    return new PlanResourceLimit({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  async create(entity: PlanResourceLimit): Promise<PlanResourceLimit> {
    try {
      const props = entity.getProps()

      // Use the same pattern as SubscriptionPlan infrastructure
      const created = await this.model.create({
        ...props,
      })

      if (!created) {
        throw new Error('Failed to create plan resource limit')
      }

      return this.toDomain(created)
    } catch (error) {
      this.logger.error(
        `Failed to create plan resource limit: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  async findById(id: Id): Promise<PlanResourceLimit | null> {
    try {
      const found = await this.model.findById(id)
      if (!found) return null

      return this.toDomain(found)
    } catch (error) {
      this.logger.error(
        `Failed to find plan resource limit by ID ${id}: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async findAll(
    query?: ITypeOrmFilter<PlanResourceLimitModel>,
  ): Promise<PlanResourceLimit[]> {
    try {
      const results = await this.model.findAll(query || {})
      return results.map(this.toDomain)
    } catch (error) {
      this.logger.error(
        `Failed to find plan resource limits: ${error.message}`,
        error.stack,
      )
      return []
    }
  }

  async updateById(
    id: Id,
    props: Partial<PlanResourceLimitProps>,
  ): Promise<PlanResourceLimit | null> {
    try {
      const updated = await this.model.updateById(
        id,
        props as DeepPartial<PlanResourceLimitModel>,
      )

      if (!updated) return null

      return this.toDomain(updated)
    } catch (error) {
      this.logger.error(
        `Failed to update plan resource limit ${id}: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async deleteById(id: Id): Promise<boolean> {
    try {
      await this.model.softDelete({ id })
      return true
    } catch (error) {
      this.logger.error(
        `Failed to delete plan resource limit ${id}: ${error.message}`,
        error.stack,
      )
      return false
    }
  }

  async findByPlanId(subscriptionPlanId: Id): Promise<PlanResourceLimit[]> {
    try {
      const results = await this.model.findAll({
        filter: { subscriptionPlanId },
        orderBy: { resourceType: 'ASC' },
      })

      return results.map(this.toDomain)
    } catch (error) {
      this.logger.error(
        `Failed to find plan resource limits for plan ${subscriptionPlanId}: ${error.message}`,
        error.stack,
      )
      return []
    }
  }

  async findByPlanAndResourceType(
    subscriptionPlanId: Id,
    resourceType: ResourceType,
  ): Promise<PlanResourceLimit | null> {
    try {
      const found = await this.model.findOne({
        filter: {
          subscriptionPlanId,
          resourceType,
        },
      })

      if (!found) return null

      return this.toDomain(found)
    } catch (error) {
      this.logger.error(
        `Failed to find plan resource limit for plan ${subscriptionPlanId} and type ${resourceType}: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async bulkCreateForPlan(
    subscriptionPlanId: Id,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<PlanResourceLimit[]> {
    try {
      const entities = limits.map((limit) => {
        const entity = PlanResourceLimit.create({
          ...limit,
          subscriptionPlanId,
        })
        return entity
      })

      const created: PlanResourceLimit[] = []

      // Create all entities sequentially
      for (const entity of entities) {
        const result = await this.create(entity)
        if (result) {
          created.push(result)
        }
      }

      return created
    } catch (error) {
      this.logger.error(
        `Failed to bulk create plan resource limits for plan ${subscriptionPlanId}: ${error.message}`,
        error.stack,
      )
      return []
    }
  }

  async replaceAllForPlan(
    subscriptionPlanId: Id,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<PlanResourceLimit[]> {
    try {
      let created: PlanResourceLimit[] = []

      // First delete all existing limits for this plan
      await this.deleteByPlanId(subscriptionPlanId)

      // Then create new limits
      created = await this.bulkCreateForPlan(subscriptionPlanId, limits)

      return created
    } catch (error) {
      this.logger.error(
        `Failed to replace plan resource limits for plan ${subscriptionPlanId}: ${error.message}`,
        error.stack,
      )
      return []
    }
  }

  async deleteByPlanId(subscriptionPlanId: Id): Promise<void> {
    try {
      await this.model.softDelete({
        subscriptionPlanId,
      })
    } catch (error) {
      this.logger.error(
        `Failed to delete plan resource limits for plan ${subscriptionPlanId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  async hasPlanLimits(subscriptionPlanId: Id): Promise<boolean> {
    try {
      const results = await this.model.findAll({
        filter: { subscriptionPlanId },
        limit: 1,
      })
      return results.length > 0
    } catch (error) {
      this.logger.error(
        `Failed to check if plan has limits for plan ${subscriptionPlanId}: ${error.message}`,
        error.stack,
      )
      return false
    }
  }

  async getLimitMapForPlan(
    subscriptionPlanId: Id,
  ): Promise<Map<ResourceType, PlanResourceLimit>> {
    try {
      const limits = await this.findByPlanId(subscriptionPlanId)
      const limitMap = new Map<ResourceType, PlanResourceLimit>()

      for (const limit of limits) {
        const props = limit.getProps()
        limitMap.set(props.resourceType, limit)
      }

      return limitMap
    } catch (error) {
      this.logger.error(
        `Failed to get limit map for plan ${subscriptionPlanId}: ${error.message}`,
        error.stack,
      )
      return new Map()
    }
  }

  // Additional utility methods for clean architecture compliance
  async findOne(
    query: ITypeOrmFilter<PlanResourceLimitModel>,
  ): Promise<PlanResourceLimit | null> {
    try {
      const found = await this.model.findOne(query)
      if (!found) return null

      return this.toDomain(found)
    } catch (error) {
      this.logger.error(
        `Failed to find one plan resource limit: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async count(query?: ITypeOrmFilter<PlanResourceLimitModel>): Promise<number> {
    try {
      const results = await this.model.findAll(query || {})
      return results.length
    } catch (error) {
      this.logger.error(
        `Failed to count plan resource limits: ${error.message}`,
        error.stack,
      )
      return 0
    }
  }

  async update(
    entity: PlanResourceLimit,
    props: Partial<PlanResourceLimitProps>,
  ): Promise<PlanResourceLimit | null> {
    return this.updateById(entity.id, props)
  }

  // Required by CrudRepository base class
  async find(query: ITypeOrmFilter<PlanResourceLimitModel>): Promise<{
    data: PlanResourceLimit[]
    total: number
    totalPage: number
    limit: number
    page: number
  }> {
    try {
      const results = await this.model.findAll(query)
      const total = results.length
      const limit = query.limit || 10
      const page = query.page || 1
      const totalPage = Math.ceil(total / limit)

      return {
        data: results.map(this.toDomain),
        total,
        totalPage,
        limit,
        page,
      }
    } catch (error) {
      this.logger.error(
        `Failed to find plan resource limits with pagination: ${error.message}`,
        error.stack,
      )
      return {
        data: [],
        total: 0,
        totalPage: 0,
        limit: query.limit || 10,
        page: query.page || 1,
      }
    }
  }

  // Required by CrudRepository base class
  async delete(id: Id): Promise<boolean> {
    return this.deleteById(id)
  }
}
