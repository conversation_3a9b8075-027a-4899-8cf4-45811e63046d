import { Inject, Injectable, Logger } from '@nestjs/common'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { SubscriptionPlanModel } from '@backend/frameworks/database/models/subscription-plan.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import {
  SubscriptionPlan,
  SubscriptionPlanProps,
  PlanResourceLimitProps,
} from '@backend/modules/subscription/entities'
import SubscriptionPlanRepository from '@backend/modules/subscription/applications/subscription-plan.repository'
import PlanResourceLimitRepository, {
  PLAN_RESOURCE_LIMIT_REPOSITORY,
} from '@backend/modules/subscription/applications/plan-resource-limit.repository'

@Injectable()
export class SubscriptionPlanInfrastructure
  implements SubscriptionPlanRepository
{
  private readonly logger = new Logger(SubscriptionPlanInfrastructure.name)
  private readonly subscriptionPlanModel: TypeORMDriver<SubscriptionPlanModel>

  constructor(
    @Inject(Database) private database: Database,
    @Inject(PLAN_RESOURCE_LIMIT_REPOSITORY)
    private readonly planResourceLimitRepository: PlanResourceLimitRepository,
  ) {
    this.subscriptionPlanModel = this.database.typeorm<SubscriptionPlanModel>(
      'SubscriptionPlanModel',
    )
  }

  toDomain = (
    subscriptionPlanModel: SubscriptionPlanModel,
  ): SubscriptionPlan => {
    const { id, createdAt, updatedAt, ...props } = subscriptionPlanModel
    return new SubscriptionPlan({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  async find(
    query: ITypeOrmFilter<SubscriptionPlanModel>,
  ): Promise<IPaginate<SubscriptionPlan>> {
    return this.subscriptionPlanModel
      .find({
        ...query,
      })
      .then((escalations) => {
        return {
          ...escalations,
          data: escalations.data.map(this.toDomain),
        }
      })
  }

  async findOne(
    query: ITypeOrmFilter<SubscriptionPlanModel>,
  ): Promise<Nullable<SubscriptionPlan>> {
    const subscriptionPlan = await this.subscriptionPlanModel.findOne({
      ...query,
    })

    if (!subscriptionPlan) return null

    return this.toDomain(subscriptionPlan)
  }

  async findAll(): Promise<SubscriptionPlan[]> {
    const subscriptionPlans = await this.subscriptionPlanModel.findAll({})
    return subscriptionPlans.map(this.toDomain)
  }

  async findById(id: Id): Promise<Nullable<SubscriptionPlan>> {
    return this.subscriptionPlanModel.findById(id, {}).then((escalation) => {
      if (!escalation) {
        return null
      }

      return this.toDomain(escalation)
    })
  }

  async create(escalation: SubscriptionPlan): Promise<SubscriptionPlan> {
    const subscriptionPlanProps = escalation.getProps()
    const newSubscriptionPlan = await this.subscriptionPlanModel
      .create({
        ...subscriptionPlanProps,
      })
      .then(this.toDomain)

    console.log(newSubscriptionPlan)
    return newSubscriptionPlan
  }

  async updateById(
    id: Id,
    escalation: Partial<SubscriptionPlanProps>,
  ): Promise<Nullable<SubscriptionPlan>> {
    const currentSubscriptionPlan = await this.subscriptionPlanModel.findById(
      id,
      {},
    )
    if (!currentSubscriptionPlan) return null

    const updatedSubscriptionPlan = await this.subscriptionPlanModel.updateById(
      id,
      {
        ...escalation,
      },
    )

    if (!updatedSubscriptionPlan) return null

    const entityEscalation = this.toDomain(updatedSubscriptionPlan)
    return entityEscalation
  }

  async delete(id: Id): Promise<boolean> {
    await this.subscriptionPlanModel.softDelete({
      id,
    })
    return true
  }

  async createWithLimits(
    plan: SubscriptionPlan,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<SubscriptionPlan | null> {
    try {
      // First create the subscription plan
      const createdPlan = await this.create(plan)

      if (!createdPlan) {
        throw new Error('Failed to create subscription plan')
      }

      // Then create all resource limits for this plan
      if (limits.length > 0) {
        const createdLimits =
          await this.planResourceLimitRepository.bulkCreateForPlan(
            createdPlan.id,
            limits,
          )

        if (createdLimits.length !== limits.length) {
          throw new Error('Failed to create all resource limits')
        }

        // Set the resource limits on the plan entity
        createdPlan.setResourceLimits(createdLimits)
      }

      this.logger.log(
        `Successfully created subscription plan ${createdPlan.id} with ${limits.length} resource limits`,
      )

      return createdPlan
    } catch (error) {
      this.logger.error(
        `Failed to create subscription plan with limits: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async updateWithLimits(
    planId: Id,
    planProps: Partial<SubscriptionPlanProps>,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<SubscriptionPlan | null> {
    try {
      // First update the subscription plan
      const updatedPlan = await this.updateById(planId, planProps)

      if (!updatedPlan) {
        throw new Error('Failed to update subscription plan')
      }

      // Then replace all resource limits for this plan
      const updatedLimits =
        await this.planResourceLimitRepository.replaceAllForPlan(planId, limits)

      // Set the updated resource limits on the plan entity
      updatedPlan.setResourceLimits(updatedLimits)

      this.logger.log(
        `Successfully updated subscription plan ${planId} with ${limits.length} resource limits`,
      )

      return updatedPlan
    } catch (error) {
      this.logger.error(
        `Failed to update subscription plan ${planId} with limits: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async findByIdWithLimits(planId: Id): Promise<SubscriptionPlan | null> {
    try {
      // Find the subscription plan
      const plan = await this.findById(planId)
      if (!plan) return null

      // Load its resource limits
      const limits = await this.planResourceLimitRepository.findByPlanId(planId)

      // Set the resource limits on the plan entity
      plan.setResourceLimits(limits)

      return plan
    } catch (error) {
      this.logger.error(
        `Failed to find subscription plan ${planId} with limits: ${error.message}`,
        error.stack,
      )
      return null
    }
  }

  async findAllWithLimits(options?: any): Promise<SubscriptionPlan[]> {
    try {
      // Find all subscription plans
      const plans = options
        ? await this.find(options).then((result) => result.data)
        : await this.findAll()

      // Load resource limits for each plan
      const plansWithLimits = await Promise.all(
        plans.map(async (plan) => {
          const limits = await this.planResourceLimitRepository.findByPlanId(
            plan.id,
          )
          plan.setResourceLimits(limits)
          return plan
        }),
      )

      return plansWithLimits
    } catch (error) {
      this.logger.error(
        `Failed to find subscription plans with limits: ${error.message}`,
        error.stack,
      )
      return []
    }
  }

  async deleteWithLimits(planId: Id): Promise<boolean> {
    try {
      // First delete all resource limits for this plan
      await this.planResourceLimitRepository.deleteByPlanId(planId)

      // Then delete the subscription plan
      const deleted = await this.delete(planId)

      if (!deleted) {
        throw new Error('Failed to delete subscription plan')
      }

      this.logger.log(
        `Successfully deleted subscription plan ${planId} with its resource limits`,
      )

      return true
    } catch (error) {
      this.logger.error(
        `Failed to delete subscription plan ${planId} with limits: ${error.message}`,
        error.stack,
      )
      return false
    }
  }
}
