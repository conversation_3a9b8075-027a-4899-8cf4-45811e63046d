import { Inject, Injectable } from '@nestjs/common'
import { DeepPartial } from 'typeorm'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { SubscriptionModel } from '@backend/frameworks/database/models/subscription.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import {
  Subscription,
  SubscriptionProps,
} from '@backend/modules/subscription/entities'
import SubscriptionRepository from '@backend/modules/subscription/applications/subscription.repository'

@Injectable()
export class SubscriptionInfrastructure implements SubscriptionRepository {
  private readonly subscriptionModel: TypeORMDriver<SubscriptionModel>
  constructor(@Inject(Database) private database: Database) {
    this.subscriptionModel =
      this.database.typeorm<SubscriptionModel>('SubscriptionModel')
  }

  toDomain = (subscriptionModel: SubscriptionModel): Subscription => {
    const { id, createdAt, updatedAt, ...props } = subscriptionModel
    return new Subscription({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  async find(
    query: ITypeOrmFilter<SubscriptionModel>,
  ): Promise<IPaginate<Subscription>> {
    return this.subscriptionModel
      .find({
        ...query,
      })
      .then((escalations) => {
        return {
          ...escalations,
          data: escalations.data.map(this.toDomain),
        }
      })
  }

  async findOne(
    query: ITypeOrmFilter<SubscriptionModel>,
  ): Promise<Nullable<Subscription>> {
    const subscription = await this.subscriptionModel.findOne({
      ...query,
    })

    if (!subscription) return null

    return this.toDomain(subscription)
  }

  async findAll(): Promise<Subscription[]> {
    const subscriptionPlans = await this.subscriptionModel.findAll({})
    return subscriptionPlans.map(this.toDomain)
  }

  async findById(id: Id): Promise<Nullable<Subscription>> {
    return this.subscriptionModel.findById(id, {}).then((escalation) => {
      if (!escalation) {
        return null
      }

      return this.toDomain(escalation)
    })
  }

  async create(subscription: Subscription): Promise<Subscription> {
    const subscriptionRawProps = subscription.getProps()
    const payloadForCreate = {
      ...subscriptionRawProps,
      canceledAt:
        subscriptionRawProps.canceledAt === null
          ? undefined
          : subscriptionRawProps.canceledAt,
    }

    const newSubscription = await this.subscriptionModel
      .create(payloadForCreate as DeepPartial<SubscriptionModel>)
      .then(this.toDomain)

    console.log(newSubscription)
    return newSubscription
  }

  async updateById(
    id: Id,
    subscriptionUpdateProps: Partial<SubscriptionProps>,
  ): Promise<Nullable<Subscription>> {
    const currentSubscription = await this.subscriptionModel.findById(id)
    if (!currentSubscription) return null

    const payloadForUpdate = {
      ...subscriptionUpdateProps,
      canceledAt:
        subscriptionUpdateProps.canceledAt === null
          ? undefined
          : subscriptionUpdateProps.canceledAt,
    }

    const updatedSubscription = await this.subscriptionModel.updateById(
      id,
      payloadForUpdate as DeepPartial<SubscriptionModel>,
    )

    if (!updatedSubscription) return null

    return this.toDomain(updatedSubscription)
  }

  async delete(id: Id): Promise<boolean> {
    await this.subscriptionModel.softDelete({
      id,
    })
    return true
  }
}
