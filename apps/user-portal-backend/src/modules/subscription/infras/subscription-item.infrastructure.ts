import { Inject, Injectable } from '@nestjs/common'
import { ResourceType } from '@libs/shared/constants/subscription/resource-mapping'
import { PlanType } from '@libs/shared/constants/subscription'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import SubscriptionItemRepository from '@backend/modules/subscription/applications/subscription-item.repository'
import { SubscriptionItemModel } from '@backend/frameworks/database/models/subscription-item.model'

import { SubscriptionItem, SubscriptionItemProps } from '../entities'

@Injectable()
export class SubscriptionItemInfrastructure
  implements SubscriptionItemRepository
{
  private readonly subscriptionItemModel: TypeORMDriver<SubscriptionItemModel>
  constructor(@Inject(Database) private database: Database) {
    this.subscriptionItemModel = this.database.typeorm<SubscriptionItemModel>(
      'SubscriptionItemModel',
    )
  }

  toDomain = (subscriptionModel: SubscriptionItemModel): SubscriptionItem => {
    const { id, createdAt, updatedAt, ...props } = subscriptionModel
    return new SubscriptionItem({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  async find(
    query: ITypeOrmFilter<SubscriptionItemModel>,
  ): Promise<IPaginate<SubscriptionItem>> {
    return this.subscriptionItemModel
      .find({
        ...query,
      })
      .then((escalations) => {
        return {
          ...escalations,
          data: escalations.data.map(this.toDomain),
        }
      })
  }

  async findOne(
    query: ITypeOrmFilter<SubscriptionItemModel>,
  ): Promise<Nullable<SubscriptionItem>> {
    const subscription = await this.subscriptionItemModel.findOne({
      ...query,
    })

    if (!subscription) return null

    return this.toDomain(subscription)
  }

  async findAll(
    query?: ITypeOrmFilter<SubscriptionItemModel>,
  ): Promise<SubscriptionItem[]> {
    console.log('🚀 ~ query:', query)
    const subscriptionPlans = await this.subscriptionItemModel.findAll(query)
    return subscriptionPlans.map(this.toDomain)
  }

  async findById(id: Id): Promise<Nullable<SubscriptionItem>> {
    return this.subscriptionItemModel.findById(id, {}).then((escalation) => {
      if (!escalation) {
        return null
      }

      return this.toDomain(escalation)
    })
  }

  async create(subscription: SubscriptionItem): Promise<SubscriptionItem> {
    const subscriptionProps = subscription.getProps()
    const newSubscription = await this.subscriptionItemModel
      .create({
        ...subscriptionProps,
        startDate: subscriptionProps.startDate || undefined,
        endDate: subscriptionProps.endDate || undefined,
      })
      .then(this.toDomain)

    console.log(newSubscription)
    return newSubscription
  }

  async updateById(
    id: Id,
    subscription: Partial<SubscriptionItemProps>,
  ): Promise<Nullable<SubscriptionItem>> {
    const currentSubscription = await this.subscriptionItemModel.findById(id)
    if (!currentSubscription) return null

    const { startDate, endDate, createdAt, updatedAt, ...otherProps } =
      subscription
    const updatedSubscription = await this.subscriptionItemModel.updateById(
      id,
      {
        ...otherProps,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      },
    )

    if (!updatedSubscription) return null

    return this.toDomain(updatedSubscription)
  }

  async delete(id: Id): Promise<boolean> {
    await this.subscriptionItemModel.softDelete({
      id,
    })
    return true
  }

  async findBySubscriptionAndResourceType(
    subscriptionId: string,
    resourceType: ResourceType,
  ): Promise<SubscriptionItem | null> {
    const item = await this.subscriptionItemModel.findOne({
      filter: {
        subscriptionId,
        resourceType,
      },
    })

    return item ? this.toDomain(item) : null
  }

  async findOverageItemsBySubscription(
    subscriptionId: string,
  ): Promise<SubscriptionItem[]> {
    const result = await this.subscriptionItemModel.find({
      filter: {
        subscriptionId,
        itemType: PlanType.ADDON_METERED_USAGE,
      },
    })

    return result.data.map(this.toDomain)
  }

  async findByResourceType(
    resourceType: ResourceType,
  ): Promise<SubscriptionItem[]> {
    const result = await this.subscriptionItemModel.find({
      filter: {
        resourceType,
      },
    })

    return result.data.map(this.toDomain)
  }
}
