import { Injectable, Logger } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { StripeUseCase } from '@backend/modules/subscription/usecases/stripe.usecase'
import { Subscription } from '@backend/modules/subscription/entities'

export interface StripeCustomerResult {
  id: string
  email: string
  name: string
}

export interface StripeCheckoutSessionResult {
  sessionId: string
  url: string | null
}

export interface StripePlanPreviewResult {
  proratedCredit: number // amount in cents (refund for unused current plan time)
  newPlanCharge: number // amount in cents (full new plan price)
  nextInvoiceDate: string // ISO 8601 string (new billing cycle date)
  nextInvoiceTotal: number // amount in cents (regular monthly price)
  currency: string // ISO 4217 currency code from Stripe (e.g., 'usd', 'eur')
}

@Injectable()
export class StripeIntegrationHelper {
  private readonly logger = new Logger(StripeIntegrationHelper.name)

  constructor(private readonly stripeService: StripeUseCase) {}

  /**
   * Creates or finds a Stripe customer and updates user record if needed
   */
  async ensureStripeCustomer(
    userId: Id,
    email: string,
    name: string,
    updateUserCallback: (stripeCustomerId: string) => Promise<void>,
  ): Promise<string> {
    try {
      this.logger.log(`Ensuring Stripe customer for user ${userId}`)

      const customer = await this.stripeService.findOrCreateCustomer(
        email,
        name,
        { internalUserId: userId },
      )

      // Update user record with Stripe customer ID if needed
      await updateUserCallback(customer.id)

      this.logger.log(
        `Successfully ensured Stripe customer ${customer.id} for user ${userId}`,
      )

      return customer.id
    } catch (error) {
      this.logger.error(
        `Failed to ensure Stripe customer for user ${userId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Creates a Stripe checkout session for subscription
   */
  async createSubscriptionCheckoutSession(
    stripeCustomerId: string,
    userId: string,
    planPriceId: string,
    metadata: Record<string, string>,
    successUrl: string,
    cancelUrl: string,
  ): Promise<StripeCheckoutSessionResult> {
    try {
      this.logger.log(
        `Creating checkout session for customer ${stripeCustomerId}`,
      )

      const session =
        await this.stripeService.createCheckoutSessionForSubscription(
          stripeCustomerId,
          userId,
          planPriceId,
          1, // quantity
          metadata,
          successUrl,
          cancelUrl,
        )

      this.logger.log(
        `Successfully created checkout session ${session.id} for customer ${stripeCustomerId}`,
      )

      return {
        sessionId: session.id,
        url: session.url,
      }
    } catch (error) {
      this.logger.error(
        `Failed to create checkout session for customer ${stripeCustomerId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Changes a Stripe subscription plan with multiple subscription items
   */
  async changeSubscriptionPlan(
    stripeSubscriptionId: string,
    stripeSubscriptionItemId: string,
    newPlanPriceId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Initiating plan change for subscription ${stripeSubscriptionId} to plan ${newPlanPriceId}. Resource limits will be added after payment confirmation.`,
      )

      // Only update base plan price during initiation
      await this.stripeService.changeSubscriptionPlan(
        stripeSubscriptionId,
        stripeSubscriptionItemId,
        newPlanPriceId,
      )

      this.logger.log(
        `Successfully initiated plan change for subscription ${stripeSubscriptionId} to plan ${newPlanPriceId}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to initiate plan change for ${stripeSubscriptionId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Cancels a Stripe subscription at period end
   */
  async cancelSubscriptionAtPeriodEnd(
    stripeSubscriptionId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Canceling subscription ${stripeSubscriptionId} at period end`,
      )

      await this.stripeService.cancelStripeSubscriptionAtPeriodEnd(
        stripeSubscriptionId,
      )

      this.logger.log(
        `Successfully scheduled cancellation for subscription ${stripeSubscriptionId}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to cancel subscription ${stripeSubscriptionId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Previews plan change charges and proration
   */
  async previewPlanChange(
    currentSubscription: Subscription,
    newPlanPriceId: string,
  ): Promise<StripePlanPreviewResult> {
    try {
      this.logger.log(
        `Previewing plan change for subscription ${currentSubscription.id}`,
      )

      const preview = await this.stripeService.previewPlanChange(
        currentSubscription,
        newPlanPriceId,
      )

      this.logger.log(
        `Successfully generated plan change preview for subscription ${currentSubscription.id}`,
      )

      return preview
    } catch (error) {
      this.logger.error(
        `Failed to preview plan change for subscription ${currentSubscription.id}: ${error.message}`,
        error.stack,
      )
      // Return default values if preview fails
      const fallbackNextInvoiceDate = new Date()
      fallbackNextInvoiceDate.setMonth(fallbackNextInvoiceDate.getMonth() + 1)

      return {
        proratedCredit: 0,
        newPlanCharge: 0, // Will be calculated by Stripe preview
        nextInvoiceDate: fallbackNextInvoiceDate.toISOString(),
        nextInvoiceTotal: 0,
        currency: 'usd', // Default fallback currency
      }
    }
  }

  /**
   * Fetches multiple Stripe prices by their IDs
   */
  async getStripePrices(
    priceIds: string[],
  ): Promise<Map<string, { unit_amount: number | null }>> {
    try {
      this.logger.log(`Fetching ${priceIds.length} Stripe prices`)

      const prices = await this.stripeService.getStripePrices(priceIds)

      this.logger.log(`Successfully fetched ${prices.size} Stripe prices`)

      return prices
    } catch (error) {
      this.logger.error(
        `Failed to fetch Stripe prices: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Builds metadata for checkout session with dynamic overage price IDs
   */
  buildCheckoutMetadata(
    subscriptionPlanId: string,
    organizationId: string,
    resourceLimits: Array<{
      getProps(): {
        resourceType: string
        overageStripePriceId?: string
      }
    }>,
  ): {
    metadata: Record<string, string>
    validOveragePrices: Array<{ resourceType: string; priceId: string }>
    invalidLimits: string[]
  } {
    const metadata: Record<string, string> = {
      subscriptionPlanId,
      organizationId,
    }

    const invalidLimits: string[] = []
    const validOveragePrices: Array<{
      resourceType: string
      priceId: string
    }> = []

    resourceLimits.forEach((limit) => {
      const limitProps = limit.getProps()
      const resourceType = limitProps.resourceType

      if (
        !limitProps.overageStripePriceId ||
        limitProps.overageStripePriceId.trim() === ''
      ) {
        this.logger.warn(
          `Missing overage Stripe price ID for resource type ${resourceType}`,
        )
        invalidLimits.push(resourceType)
        return
      }

      // Generate dynamic metadata field name for each resource type
      const resourceName = resourceType.toLowerCase()
      const metadataKey = `${resourceName}OverageStripePriceId`
      metadata[metadataKey] = limitProps.overageStripePriceId

      validOveragePrices.push({
        resourceType,
        priceId: limitProps.overageStripePriceId,
      })
    })

    this.logger.log(
      `Built checkout metadata with ${validOveragePrices.length} valid overage prices`,
    )

    return {
      metadata,
      validOveragePrices,
      invalidLimits,
    }
  }
}
