import { Injectable, Logger } from '@nestjs/common'
import { OveragePolicyType } from '@libs/shared/constants/subscription'

import {
  ResourceManipulationQueueService,
  ResourceManipulationEvent,
} from '../infras/sqs/resource-manipulation-queue.service'
import { SubscriptionDetailsUseCase } from '../usecases/subscription-details.usecase'

import { OverageQuantityService } from './overage-quantity.service'

// Resource type mapping for queue service methods
type ResourceType = ResourceManipulationEvent['resourceType']
type ActionType = ResourceManipulationEvent['action']

// Resource limit interface for type safety
interface ResourceLimit {
  resourceType: string
  includedQuantity: number
  overageStripePriceId: string
}

// Plan context interface for type safety
interface PlanContext {
  subscriptionPlanId: string
  resourceLimits: Array<ResourceLimit>
  overagePolicyFixedResources: string
}

// Generic context for resource changes
interface ResourceChangeContext {
  // Common properties
  [key: string]: unknown

  // Resource-specific context properties
  isInitialOwner?: boolean
  invitationId?: string
  teamId?: string
  name?: string
  ownerId?: string
  checkType?: string
  url?: string
  integrationType?: string
  companyName?: string
  subDomain?: string
}

/**
 * Service for handling resource billing and usage tracking
 * Provides both generic and specific methods for tracking resource changes that affect billing
 * Uses a generic implementation to eliminate code duplication and ease future extensions
 */
@Injectable()
export class ResourceBillingService {
  private readonly logger = new Logger(ResourceBillingService.name)

  constructor(
    private readonly resourceQueueService: ResourceManipulationQueueService,
    private readonly subscriptionDetailsUseCase: SubscriptionDetailsUseCase,
    private readonly overageQuantityService: OverageQuantityService,
  ) {}

  /**
   * Helper method to get current plan context for an organization
   * This prevents race conditions in Lambda overage calculation
   */
  private async getPlanContext(
    organizationId: string,
  ): Promise<PlanContext | undefined> {
    try {
      const subscriptionDetails =
        await this.subscriptionDetailsUseCase.getSubscriptionDetails(
          organizationId,
        )

      if (
        !subscriptionDetails ||
        !subscriptionDetails.planDetails ||
        !subscriptionDetails.planDetails.id
      ) {
        this.logger.warn(
          `No subscription or plan details found for organization ${organizationId}`,
        )
        return undefined
      }

      // Transform plan details into SQS-compatible format
      const planContext: PlanContext = {
        subscriptionPlanId: subscriptionDetails.planDetails.id,
        resourceLimits: (
          (subscriptionDetails.planDetails.resourceLimits as ResourceLimit[]) ||
          []
        ).map((limit: ResourceLimit) => ({
          resourceType: limit.resourceType,
          includedQuantity: limit.includedQuantity,
          overageStripePriceId: limit.overageStripePriceId,
        })),
        overagePolicyFixedResources:
          subscriptionDetails.overagePolicyFixedResources ||
          OveragePolicyType.BLOCK,
      }

      return planContext
    } catch (error) {
      this.logger.error(
        `Failed to get plan context for organization ${organizationId}: ${error.message}`,
      )
      return undefined // Gracefully degrade without plan context
    }
  }

  /**
   * Generic method for tracking resource changes
   * Eliminates code duplication and provides a consistent interface
   */
  async trackResourceChange(
    resourceType: ResourceType,
    organizationId: string,
    action: ActionType = 'CREATE',
    resourceId?: string,
    context?: ResourceChangeContext,
  ): Promise<void> {
    try {
      this.logger.debug(
        `Tracking ${resourceType} change: organizationId=${organizationId}, action=${action}, resourceId=${resourceId}, context=${JSON.stringify(context)}`,
      )

      // Get current plan context to include in SQS message
      const planContext = await this.getPlanContext(organizationId)

      // Map resource types to their respective queue service methods
      const publishMethod = this.getPublishMethod(resourceType)
      await publishMethod.call(
        this.resourceQueueService,
        organizationId,
        action,
        resourceId,
        planContext,
      )

      this.logger.debug(
        `Successfully tracked ${resourceType} change for organization ${organizationId} with plan context`,
      )

      // For high-value actions (CREATE/DELETE), also trigger immediate overage quantity update
      if (action === 'CREATE' || action === 'DELETE') {
        await this.updateOverageQuantitiesImmediate(organizationId, planContext)
      }
    } catch (error) {
      this.logger.error(
        `Failed to track ${resourceType} change: ${error.message}`,
        {
          organizationId,
          action,
          resourceId,
          context,
          error: error.stack,
        },
      )
      // Note: We don't re-throw the error to maintain the "fire-and-forget" pattern
      // The primary operation should not fail if billing tracking fails
    }
  }

  /**
   * Immediately update overage quantities for an organization
   * This provides synchronous overage billing updates alongside async Lambda processing
   */
  private async updateOverageQuantitiesImmediate(
    organizationId: string,
    _planContext?: PlanContext,
  ): Promise<void> {
    try {
      this.logger.debug(
        `Triggering immediate overage quantity update for organization ${organizationId}`,
      )

      // Get active subscription for this organization
      const subscriptionDetails =
        await this.subscriptionDetailsUseCase.getSubscriptionDetails(
          organizationId,
        )

      if (
        !subscriptionDetails ||
        !subscriptionDetails.id ||
        !subscriptionDetails.stripeSubscriptionId
      ) {
        this.logger.debug(
          `No active subscription found for organization ${organizationId}, skipping immediate overage update`,
        )
        return
      }

      // Use OverageQuantityService to update subscription item quantities
      const updateResult =
        await this.overageQuantityService.updateOverageQuantitiesForOrganization(
          organizationId,
          subscriptionDetails.id,
          subscriptionDetails.overagePolicyFixedResources ||
            OveragePolicyType.BLOCK,
        )

      if (updateResult.success) {
        this.logger.log(
          `Successfully updated overage quantities for organization ${organizationId}. Updated ${Object.keys(updateResult.updatedQuantities).length} resource types`,
        )
      } else {
        this.logger.warn(
          `Overage quantity update had errors for organization ${organizationId}: ${updateResult.errors.length} errors`,
          { errors: updateResult.errors },
        )
      }
    } catch (error) {
      this.logger.error(
        `Failed immediate overage quantity update for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      // Don't re-throw - this is a best-effort immediate update
      // The async Lambda processing will handle as backup
    }
  }

  /**
   * Manually trigger overage quantity updates for an organization
   * Useful for plan changes, manual billing adjustments, or periodic reconciliation
   */
  async triggerOverageQuantityUpdate(organizationId: string): Promise<void> {
    this.logger.log(
      `Manual overage quantity update triggered for organization ${organizationId}`,
    )

    const planContext = await this.getPlanContext(organizationId)
    await this.updateOverageQuantitiesImmediate(organizationId, planContext)
  }

  /**
   * Get the appropriate queue service publish method for a resource type
   */
  private getPublishMethod(resourceType: ResourceType) {
    const methodMap = {
      MEMBER: this.resourceQueueService.publishMemberEvent,
      TEAM: this.resourceQueueService.publishTeamEvent,
      CHECK: this.resourceQueueService.publishCheckEvent,
      INTEGRATION: this.resourceQueueService.publishIntegrationEvent,
      STATUS_PAGE: this.resourceQueueService.publishStatusPageEvent,
    }

    const method = methodMap[resourceType]
    if (!method) {
      throw new Error(`Unsupported resource type: ${resourceType}`)
    }

    return method
  }

  /**
   * Track member changes for billing purposes
   * Called when users are added/removed from organizations
   */
  async trackMemberChange(
    organizationId: string,
    action: ActionType = 'CREATE',
    userId?: string,
    context?: {
      isInitialOwner?: boolean
      invitationId?: string
      teamId?: string
    },
  ): Promise<void> {
    return this.trackResourceChange(
      'MEMBER',
      organizationId,
      action,
      userId,
      context,
    )
  }

  /**
   * Track team changes for billing purposes
   * Called when teams are created/deleted
   */
  async trackTeamChange(
    organizationId: string,
    action: ActionType = 'CREATE',
    teamId?: string,
    context?: { name?: string; ownerId?: string },
  ): Promise<void> {
    return this.trackResourceChange(
      'TEAM',
      organizationId,
      action,
      teamId,
      context,
    )
  }

  /**
   * Track check changes for billing purposes
   * Called when checks are created/deleted/updated
   */
  async trackCheckChange(
    organizationId: string,
    action: ActionType = 'CREATE',
    checkId?: string,
    context?: { checkType?: string; teamId?: string; url?: string },
  ): Promise<void> {
    return this.trackResourceChange(
      'CHECK',
      organizationId,
      action,
      checkId,
      context,
    )
  }

  /**
   * Track integration changes for billing purposes
   * Called when integrations are created/deleted/updated
   */
  async trackIntegrationChange(
    organizationId: string,
    action: ActionType = 'CREATE',
    integrationId?: string,
    context?: { integrationType?: string; teamId?: string; name?: string },
  ): Promise<void> {
    return this.trackResourceChange(
      'INTEGRATION',
      organizationId,
      action,
      integrationId,
      context,
    )
  }

  /**
   * Track status page changes for billing purposes
   * Called when status pages are created/deleted/updated
   */
  async trackStatusPageChange(
    organizationId: string,
    action: ActionType = 'CREATE',
    statusPageId?: string,
    context?: { companyName?: string; teamId?: string; subDomain?: string },
  ): Promise<void> {
    return this.trackResourceChange(
      'STATUS_PAGE',
      organizationId,
      action,
      statusPageId,
      context,
    )
  }
}
