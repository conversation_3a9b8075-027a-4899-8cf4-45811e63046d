import { ResourceType } from '@libs/shared/constants/subscription'
import {
  type BillingPeriod,
  type OverageCalculationInput as FlexibleOverageCalculationInput,
  type OverageResult as FlexibleOverageResult,
  OverageResultImpl,
  type ResourceOverage,
  type ResourceUsageMap,
} from '@libs/shared/interfaces/overage.interface'
import { UsageTypeUtils } from '@libs/shared/utils/usage-type.utils'
import { Inject, Injectable, Logger } from '@nestjs/common'

import { SUBSCRIPTION_USAGE_REPOSITORY } from '../applications/subscription-usage.repository'

import type SubscriptionUsageRepository from '../applications/subscription-usage.repository'
import type { SubscriptionPlan } from '../entities/subscription-plan.entity'

export interface OverageCalculationInput {
  organizationId: string
  subscriptionId: string
  currentPlan: SubscriptionPlan
  currentUsage: {
    members: number
    teams: number
    checks: number
    integrations: number
  }
}

export interface OverageResult {
  hasOverage: boolean
  memberOverage: number
  teamOverage: number
  checkOverage: number
  integrationOverage: number
}

@Injectable()
export class PlanOverageService {
  private readonly logger = new Logger(PlanOverageService.name)

  constructor(
    @Inject(SUBSCRIPTION_USAGE_REPOSITORY)
    private readonly subscriptionUsageRepository: SubscriptionUsageRepository,
  ) {}

  /**
   * Calculates overages for a plan given current usage
   * This method can handle any number of resource types without code changes
   */
  calculateFlexibleOverageForPlan(
    input: FlexibleOverageCalculationInput,
  ): FlexibleOverageResult {
    this.logger.log(
      `Calculating overage for organization ${input.organizationId} with plan ${input.plan}`,
    )

    const overageMap = new Map<ResourceType, number>()
    const resourceOverageDetails = new Map<ResourceType, ResourceOverage>()

    // Dynamic loop through all resource limits in the plan
    for (const resourceLimit of input.plan.getAllResourceLimits()) {
      const limitProps = resourceLimit.getProps()
      const resourceType = limitProps.resourceType
      const planLimit = limitProps.includedQuantity
      const stripePriceId = limitProps.overageStripePriceId

      // Get current usage for this resource type
      const currentUsage = this.getUsageForResourceType(
        input.currentUsage,
        resourceType,
      )

      // Calculate overage using the resource limit's built-in method
      const overageAmount = resourceLimit.calculateOverage(currentUsage)

      if (overageAmount > 0) {
        overageMap.set(resourceType, overageAmount)

        // Store detailed overage information
        resourceOverageDetails.set(resourceType, {
          resourceType,
          currentUsage,
          planLimit,
          overageAmount,
          stripePriceId,
        })

        this.logger.log(
          `Overage detected for ${resourceType}: ${overageAmount} ` +
            `(usage: ${currentUsage}, limit: ${planLimit})`,
        )
      }
    }

    const result = new OverageResultImpl(overageMap, resourceOverageDetails)

    if (result.hasOverage) {
      this.logger.log(
        `Total overage for org ${input.organizationId}: ${result.getTotalOverageCount()} ` +
          `across ${result.getResourceTypesWithOverage().length} resource types`,
      )
    }

    return result
  }

  /**
   * Helper method to get usage amount for a specific resource type from ResourceUsageMap
   */
  private getUsageForResourceType(
    usageMap: ResourceUsageMap,
    resourceType: ResourceType,
  ): number {
    switch (resourceType) {
      case ResourceType.MEMBER:
        return usageMap[ResourceType.MEMBER]
      case ResourceType.TEAM:
        return usageMap[ResourceType.TEAM]
      case ResourceType.CHECK:
        return usageMap[ResourceType.CHECK]
      case ResourceType.INTEGRATION:
        return usageMap[ResourceType.INTEGRATION]
      case ResourceType.STATUS_PAGE:
        return usageMap[ResourceType.STATUS_PAGE]
      default:
        this.logger.warn(
          `Unknown resource type: ${resourceType}, returning 0 usage`,
        )
        return 0
    }
  }

  /**
   * Pure dynamic overage price retrieval - NO hardcoded resource types
   */
  getOveragePriceMap(plan: SubscriptionPlan): Map<ResourceType, string> {
    const priceMap = new Map<ResourceType, string>()

    for (const resourceLimit of plan.getAllResourceLimits()) {
      const resourceType = resourceLimit.getProps().resourceType
      const priceId = resourceLimit.getOverageStripePriceId()
      if (priceId) {
        priceMap.set(resourceType, priceId)
      }
    }

    return priceMap
  }

  /**
   * Log overage events to SubscriptionUsage for audit trail and billing
   * This method can handle any number of resource types without hardcoded if blocks
   */
  async logFlexibleOverageEvents(
    input: FlexibleOverageCalculationInput,
    overageResult: FlexibleOverageResult,
    billingPeriod: BillingPeriod,
    subscriptionItemId?: string,
    userId?: string,
  ): Promise<void> {
    if (!overageResult.hasOverage) {
      this.logger.log(
        `No overage detected for org ${input.organizationId}, skipping event logging`,
      )
      return
    }

    const resourceOverages = overageResult.getAllOverages()

    const usageTimestamp = new Date()

    try {
      // Dynamic loop through all overages - no hardcoded if blocks!
      for (const resourceOverage of resourceOverages) {
        const usageType = UsageTypeUtils.generateOverageUsageType(
          resourceOverage.resourceType,
        )
        const resourceName = resourceOverage.resourceType.toLowerCase()

        await this.subscriptionUsageRepository.createUsageEventRecord({
          organizationId: input.organizationId,
          subscriptionItemId,
          usageType,
          usageValue: resourceOverage.overageAmount,
          usageTimestamp,
          billingCycleStartDate: billingPeriod.startDate,
          billingCycleEndDate: billingPeriod.endDate,
          stripePriceId: resourceOverage.stripePriceId || undefined,
          reportedToStripe: false,
          isBillable: true,
          notes: `Overage calculation: ${resourceOverage.overageAmount} ${resourceName}s over plan limit (${resourceOverage.currentUsage}/${resourceOverage.planLimit})`,
          createdBy: userId,
        })

        this.logger.log(
          `Logged ${usageType} event: ${resourceOverage.overageAmount} ${resourceName}s`,
        )
      }

      this.logger.log(
        `Successfully logged all ${resourceOverages.length} overage events for org ${input.organizationId}`,
      )
    } catch (error) {
      this.logger.error(
        `Failed to log overage events for org ${input.organizationId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Calculate and log overage events in one operation
   * Convenience method that combines calculation and logging for any number of resource types
   */
  async calculateAndLogFlexibleOverageEvents(
    input: FlexibleOverageCalculationInput,
    billingPeriod: BillingPeriod,
    subscriptionItemId?: string,
    userId?: string,
  ): Promise<FlexibleOverageResult> {
    const overageResult = this.calculateFlexibleOverageForPlan(input)

    if (overageResult.hasOverage) {
      await this.logFlexibleOverageEvents(
        input,
        overageResult,
        billingPeriod,
        subscriptionItemId,
        userId,
      )
    }

    return overageResult
  }
}
