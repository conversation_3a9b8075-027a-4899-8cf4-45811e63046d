import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common'
import {
  ResourceType,
  OveragePolicyType,
} from '@libs/shared/constants/subscription'

import { Id } from '@backend/cores/base/id.type'
import { SubscriptionDetailsUseCase } from '@backend/modules/subscription/usecases/subscription-details.usecase'

import { PlanSource } from '../types/subscription-plan.types'

@Injectable()
export class SubscriptionLimitService {
  private readonly logger = new Logger(SubscriptionLimitService.name)

  constructor(
    private readonly subscriptionDetailsUseCase: SubscriptionDetailsUseCase,
  ) {}

  async checkResourceLimitByOrg(
    organizationId: Id,
    resourceType: ResourceType,
    currentOperationCount = 1,
  ): Promise<void> {
    const { source, includedQuantity, currentUsage } =
      await this.subscriptionDetailsUseCase.getPlanSourceAndLimit(
        organizationId,
        resourceType as ResourceType,
      )
    const overagePolicy = this.determineOveragePolicy(
      source,
      resourceType as ResourceType,
    )

    if (
      overagePolicy === OveragePolicyType.CHARGE ||
      overagePolicy === OveragePolicyType.GRACE
    ) {
      return
    }

    if (overagePolicy === OveragePolicyType.BLOCK) {
      const newTotalUsage = currentUsage + currentOperationCount
      if (newTotalUsage > includedQuantity) {
        const errorMessage = `Subscription limit exceeded for ${resourceType}. Current usage: ${currentUsage}/${includedQuantity}`
        throw new HttpException(
          {
            message: errorMessage,
            code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
          },
          HttpStatus.BAD_REQUEST,
        )
      }
    }
  }

  private determineOveragePolicy(
    planSource: PlanSource | undefined,
    resourceType: ResourceType,
  ): OveragePolicyType {
    switch (planSource) {
      case PlanSource.FREE: {
        return OveragePolicyType.BLOCK
      }

      case PlanSource.MANUAL: {
        if (this.isTransactionalResource(resourceType)) {
          return OveragePolicyType.CHARGE // SMS, phone calls: usage-based
        }
        return OveragePolicyType.BLOCK // Fixed resources: restrict
      }

      case PlanSource.STRIPE: {
        if (this.isTransactionalResource(resourceType)) {
          return OveragePolicyType.CHARGE // SMS, phone calls: always usage-based
        }
        // TODO: Implement toggle feature for Stripe subscriptions
        // For now, default to BLOCK (restrict)
        return OveragePolicyType.BLOCK
      }

      default:
        return OveragePolicyType.BLOCK
    }
  }

  private isTransactionalResource(resourceType: ResourceType): boolean {
    return (
      resourceType === ResourceType.SMS ||
      resourceType === ResourceType.PHONE_CALL
    )
  }
}
