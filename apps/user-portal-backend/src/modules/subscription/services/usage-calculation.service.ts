import { Injectable, Logger } from '@nestjs/common'
import { UsageType } from '@libs/shared/constants/subscription'
import { UsageTypeUtils } from '@libs/shared/utils/usage-type.utils'

import { StripeUseCase } from '@backend/modules/subscription/usecases/stripe.usecase'
import { SubscriptionUsage } from '@backend/modules/subscription/entities'

export interface UsageBillingResult {
  startDate: Date
  endDate: Date
  sms: { count: number; cost: number } // cost in cents
  phoneCalls: { count: number; cost: number } // cost in cents
  totalCost: number // cost in cents
}

export interface CurrentUsageResult {
  members: number
  teams: number
  checks: number
}

export interface AggregatedUsageResult {
  events: SubscriptionUsage[]
  total: number
  aggregatedUsage: Record<string, number>
}

@Injectable()
export class UsageCalculationService {
  private readonly logger = new Logger(UsageCalculationService.name)

  // Cache for Stripe prices to avoid repeated API calls during billing period calculation
  private readonly priceCache = new Map<
    string,
    { price: number; timestamp: number }
  >()
  private readonly PRICE_CACHE_TTL = 5 * 60 * 1000 // 5 minutes cache

  constructor(private readonly stripeService: StripeUseCase) {}

  /**
   * Calculates usage billing for a specific period.
   * Fetches SMS and Phone call usage with costs.
   */
  async calculateUsageBillingForPeriod(
    usageEvents: SubscriptionUsage[],
    startDate: Date,
    endDate: Date,
  ): Promise<UsageBillingResult> {
    try {
      let smsCount = 0
      let smsCost = 0
      let phoneCount = 0
      let phoneCost = 0

      // Separate SMS and phone usage events
      const smsUsage = usageEvents.filter(
        (event) => event.getProps().usageType === UsageType.SMS,
      )
      const phoneUsage = usageEvents.filter(
        (event) => event.getProps().usageType === UsageType.PHONE_CALL,
      )

      // Process SMS usage
      for (const usage of smsUsage) {
        const props = usage.getProps()
        smsCount += props.usageValue

        // Calculate cost using Stripe price if available
        if (props.stripePriceId) {
          try {
            const unitCost = await this.getStripePriceAmount(
              props.stripePriceId,
            )
            smsCost += props.usageValue * unitCost
          } catch (error) {
            this.logger.warn(
              `Failed to fetch Stripe price for SMS (${props.stripePriceId}), using fallback rate`,
            )
            // Fallback to default rate if Stripe price fetch fails (1 cent per SMS)
            smsCost += props.usageValue * 1
          }
        } else {
          // No Stripe price ID, use default rate (1 cent per SMS)
          smsCost += props.usageValue * 1
        }
      }

      // Process phone usage
      for (const usage of phoneUsage) {
        const props = usage.getProps()
        phoneCount += props.usageValue

        // Calculate cost using Stripe price if available
        if (props.stripePriceId) {
          try {
            const unitCost = await this.getStripePriceAmount(
              props.stripePriceId,
            )
            phoneCost += props.usageValue * unitCost
          } catch (error) {
            this.logger.warn(
              `Failed to fetch Stripe price for phone calls (${props.stripePriceId}), using fallback rate`,
            )
            // Fallback to default rate if Stripe price fetch fails (10 cents per phone call)
            phoneCost += props.usageValue * 10
          }
        } else {
          // No Stripe price ID, use default rate (10 cents per phone call)
          phoneCost += props.usageValue * 10
        }
      }

      return {
        startDate,
        endDate,
        sms: {
          count: smsCount,
          cost: smsCost,
        },
        phoneCalls: {
          count: phoneCount,
          cost: phoneCost,
        },
        totalCost: smsCost + phoneCost,
      }
    } catch (error) {
      this.logger.error(
        `Failed to calculate usage billing for period ${startDate} - ${endDate}: ${error.message}`,
        error.stack,
      )
      // Return zero values on error
      return {
        startDate,
        endDate,
        sms: { count: 0, cost: 0 },
        phoneCalls: { count: 0, cost: 0 },
        totalCost: 0,
      }
    }
  }

  /**
   * Aggregates usage events by type for billing calculations.
   * Pure dynamic usage aggregation.
   */
  aggregateUsageEvents(
    usageEvents: SubscriptionUsage[],
  ): Record<string, number> {
    const aggregatedUsage: Record<string, number> = {}

    usageEvents.forEach((event) => {
      const eventProps = event.getProps()
      if (eventProps.isBillable !== false) {
        // Include billable and null (unknown)
        const usageType = eventProps.usageType

        // Handle transactional usage types
        if (UsageTypeUtils.isTransactionalUsageType(usageType)) {
          switch (usageType) {
            case UsageType.SMS:
              aggregatedUsage.sms =
                (aggregatedUsage.sms || 0) + eventProps.usageValue
              break
            case UsageType.PHONE_CALL:
              aggregatedUsage.phoneCalls =
                (aggregatedUsage.phoneCalls || 0) + eventProps.usageValue
              break
          }
        }
        // Handle dynamic overage usage types
        else if (UsageTypeUtils.isOverageUsageType(usageType)) {
          const resourceType =
            UsageTypeUtils.extractResourceTypeFromOverageUsage(usageType)
          if (resourceType) {
            const resourceName = resourceType.toLowerCase()

            // Dynamic aggregation for any resource type
            const overageKey = `${resourceName}Overage`
            if (!aggregatedUsage[overageKey]) {
              aggregatedUsage[overageKey] = 0
            }
            aggregatedUsage[overageKey] += eventProps.usageValue
          }
        }
        // Handle unknown usage types
        else {
          this.logger.warn(`Unknown usage type in aggregation: ${usageType}`)
        }
      }
    })

    return aggregatedUsage
  }

  /**
   * Fetches Stripe price amount and converts to decimal dollars.
   * Implements caching to avoid repeated API calls for the same price.
   */
  async getStripePriceAmount(stripePriceId: string): Promise<number> {
    // Check cache first
    const cached = this.priceCache.get(stripePriceId)
    const now = Date.now()

    if (cached && now - cached.timestamp < this.PRICE_CACHE_TTL) {
      this.logger.debug(
        `Using cached price for ${stripePriceId}: ${cached.price}¢`,
      )
      return cached.price
    }

    try {
      // Fetch from Stripe
      const price = await this.stripeService.getPrice(stripePriceId)

      if (
        !price ||
        price.unit_amount === null ||
        price.unit_amount === undefined
      ) {
        throw new Error(`Invalid price data for ${stripePriceId}`)
      }

      // Keep amount in cents for consistency
      const centAmount = price.unit_amount

      // Cache the result
      this.priceCache.set(stripePriceId, {
        price: centAmount,
        timestamp: now,
      })

      this.logger.debug(
        `Fetched and cached price for ${stripePriceId}: ${centAmount}¢`,
      )
      return centAmount
    } catch (error) {
      this.logger.error(
        `Failed to fetch Stripe price ${stripePriceId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Clears the price cache (useful for testing or forced refresh)
   */
  clearPriceCache(): void {
    this.priceCache.clear()
    this.logger.debug('Price cache cleared')
  }

  /**
   * Gets the current size of the price cache
   */
  getPriceCacheSize(): number {
    return this.priceCache.size
  }
}
