import {
  Controller,
  Post,
  Body,
  Req,
  RawBodyRequest,
  Headers,
  Logger,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common'
import { ApiTags, ApiResponse } from '@nestjs/swagger'
import Stripe from 'stripe'

import { StripeUseCase } from '../usecases/stripe.usecase'
import { CreateCustomerSessionDto } from '../applications/dto/create-customer-session.dto'

@ApiTags('Stripe')
@Controller('webhooks/stripe')
export class StripeWebhookController {
  private readonly logger = new Logger(StripeWebhookController.name)

  constructor(private readonly stripeUseCase: StripeUseCase) {}

  @Post()
  @HttpCode(HttpStatus.OK) // Respond 200 OK by default if successful
  async handleStripeWebhook(
    @Headers('stripe-signature') signature: string,
    @Req() request: RawBodyRequest<Request>, // Requires raw body parsing enabled
  ) {
    if (!signature) {
      this.logger.warn('Missing stripe-signature header')
      throw new BadRequestException('Missing stripe-signature header')
    }

    if (!request.rawBody) {
      this.logger.error(
        'Raw body not available. Ensure rawBody: true is configured in main.ts for this route.',
      )
      return
    }

    let event: Stripe.Event

    // Removed for security - don't log signature

    try {
      // Use the service to construct the event and verify signature
      event = this.stripeUseCase.constructWebhookEvent(
        request.rawBody,
        signature,
      )
    } catch (err) {
      this.logger.error(`Webhook signature verification failed: ${err.message}`)
      throw new BadRequestException(`Webhook Error: ${err.message}`)
    }

    this.logger.log(`Received Stripe event: ${event.type} (${event.id})`)

    // Handle the event
    switch (event.type) {
      case 'invoice.paid': {
        const invoicePaid = event.data.object as Stripe.Invoice
        this.logger.log(`Processing invoice.paid for invoice ${invoicePaid.id}`) // Subscription ID available in invoicePaid.subscription if needed
        // TODO: Add logic to handle successful payment (e.g., update subscription status in DB)
        await this.stripeUseCase.handleInvoicePaid(invoicePaid)
        break
      }
      case 'invoice.payment_failed': {
        const invoiceFailed = event.data.object as Stripe.Invoice
        this.logger.log(
          `Processing invoice.payment_failed for invoice ${invoiceFailed.id}`,
        ) // Subscription ID available in invoiceFailed.subscription if needed
        // TODO: Add logic to handle failed payment (e.g., update status, notify user)
        await this.stripeUseCase.handleInvoicePaymentFailed(invoiceFailed)
        break
      }
      case 'customer.subscription.updated': {
        const subscriptionUpdated = event.data.object as Stripe.Subscription
        this.logger.log(
          `Processing customer.subscription.updated for subscription ${subscriptionUpdated.id}`,
        )

        // Pass the complete event (including previous_attributes) to detect plan changes
        await this.stripeUseCase.handleCustomerSubscriptionUpdated(
          subscriptionUpdated,
          event.data.previous_attributes,
        )
        break
      }

      case 'customer.subscription.deleted': {
        const subscriptionDeleted = event.data.object as Stripe.Subscription
        this.logger.log(
          `Processing customer.subscription.deleted for subscription ${subscriptionDeleted.id}`,
        )

        await this.stripeUseCase.handleCustomerSubscriptionDeleted(
          subscriptionDeleted,
        )
        break
      }

      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        this.logger.log(
          `Processing checkout.session.completed for session ${session.id}`,
        )

        // Handle initial subscription creation confirmation
        return this.stripeUseCase.handleCheckoutSessionCompleted(session)
      }

      // ... handle other event types as needed
      default:
        this.logger.log(`Unhandled event type ${event.type}`)
    }

    // Return a 200 response to acknowledge receipt of the event
    // NestJS handles this automatically if no exception is thrown and @HttpCode(HttpStatus.OK) is set.
  }

  @Post('create-customer-portal-session')
  @ApiResponse({
    status: 201,
    description: 'Successfully created customer portal session.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'bps_1...' },
        url: {
          type: 'string',
          example: 'https://billing.stripe.com/session/...',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., missing customerId or returnUrl)',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error creating session',
  })
  async createCustomerPortalSession(@Body() data: CreateCustomerSessionDto) {
    return this.stripeUseCase.createCustomerPortalSession(data)
  }
}
