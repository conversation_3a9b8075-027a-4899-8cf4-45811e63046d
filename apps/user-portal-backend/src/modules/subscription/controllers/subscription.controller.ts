import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import { SubscriptionPlanResponseDto } from '@backend/modules/subscription/applications/dto/subscription-plan.response.dto'
import { SubscriptionDetailResponseDto } from '@backend/modules/subscription/applications/dto/subscription-detail.response.dto'
import { SubscriptionManagementUseCase } from '@backend/modules/subscription/usecases/subscription-management.usecase'
import { SubscriptionCheckoutUseCase } from '@backend/modules/subscription/usecases/subscription-checkout.usecase'
import { SubscriptionDetailsUseCase } from '@backend/modules/subscription/usecases/subscription-details.usecase'
import { SubscriptionPlanChangeUseCase } from '@backend/modules/subscription/usecases/subscription-plan-change.usecase'
import { CreateSubscriptionDto } from '@backend/modules/subscription/applications/dto/subscription.request.dto'
import { CreateCheckoutSessionDto } from '@backend/modules/subscription/applications/dto/create-checkout-session.dto'
import {
  ChangePlanDto,
  ChangePlanResponseDto,
} from '@backend/modules/subscription/applications/dto/change-plan.dto'
import {
  ChangePlanPreviewRequestDto,
  ChangePlanPreviewResponseDto,
} from '@backend/modules/subscription/applications/dto/change-plan-preview.dto'
import { CreateManualSubscriptionDto } from '@backend/modules/subscription/applications/dto/create-manual-subscription.dto'
import { InternalRole } from '@backend/modules/user/constants/user'

@ApiTags('Subscription')
@Controller('subscription')
export class SubscriptionController {
  constructor(
    private readonly subscriptionManagementUseCase: SubscriptionManagementUseCase,
    private readonly subscriptionCheckoutUseCase: SubscriptionCheckoutUseCase,
    private readonly subscriptionDetailsUseCase: SubscriptionDetailsUseCase,
    private readonly subscriptionPlanChangeUseCase: SubscriptionPlanChangeUseCase,
  ) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  async assignManualSubscription(
    @CurrentUser() user: JwtPayload,
    @Body() createManualSubscriptionDto: CreateManualSubscriptionDto,
  ) {
    if (user.internalRole === InternalRole.SUPER_ADMIN) {
      return this.subscriptionManagementUseCase.createManualSubscription(
        {
          planId: createManualSubscriptionDto.planId,
          organizationId: createManualSubscriptionDto.organizationId,
        },
        user.userId,
      )
    }
  }

  @Post('create-checkout-session')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  async createCheckoutSession(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Body() createCheckoutSessionDto: CreateCheckoutSessionDto,
  ): Promise<{ sessionId: string; url: string | null }> {
    return this.subscriptionCheckoutUseCase.createCheckoutSession(
      user.userId,
      createCheckoutSessionDto.planId,
      orgTeamParams.organizationId,
      createCheckoutSessionDto.paymentMethod,
    )
  }

  @Post('plan-change-preview')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  async changePlanPreview(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Body() changePlanPreviewDto: ChangePlanPreviewRequestDto,
  ): Promise<ChangePlanPreviewResponseDto> {
    return this.subscriptionPlanChangeUseCase.changePlanPreview(
      user.userId,
      orgTeamParams.organizationId,
      changePlanPreviewDto.newPlanId,
    )
  }

  @Post('plan-change')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  async changePlan(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Body() changePlanDto: ChangePlanDto,
  ): Promise<ChangePlanResponseDto> {
    return this.subscriptionPlanChangeUseCase.initiatePlanChange(
      user.userId,
      orgTeamParams.organizationId,
      changePlanDto.newPlanId,
    )
  }

  @Post('cancel')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Cancel subscription at period end',
    description:
      'Cancels the organization subscription at the end of the current billing period.',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  async cancelSubscription(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ): Promise<{ success: boolean; message: string }> {
    return this.subscriptionPlanChangeUseCase.cancelSubscription(
      user.userId,
      orgTeamParams.organizationId,
    )
  }

  @Get('/current-subscription')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  getCurrentSubscription(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Query('organizationId') organizationId?: string,
  ): Promise<SubscriptionDetailResponseDto | null> {
    if (user.internalRole === InternalRole.SUPER_ADMIN && organizationId) {
      return this.subscriptionDetailsUseCase.getSubscriptionDetails(
        organizationId,
      )
    }

    return this.subscriptionDetailsUseCase.getSubscriptionDetails(
      orgTeamParams.organizationId,
    )
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(SubscriptionPlanResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  find(@Query() paginateOption: PaginateOptionsDto) {
    return this.subscriptionManagementUseCase.find(paginateOption)
  }

  @Get('organization/:id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  findSubscriptionByOrganizationId(@Param('id') organizationId: string) {
    return this.subscriptionManagementUseCase.findSubscriptionsByOrganizationId(
      organizationId,
    )
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  findOne(@Param('id') id: string) {
    return this.subscriptionManagementUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  update(
    @Param('id') id: string,
    @Body() updateSubscriptionDto: Partial<CreateSubscriptionDto>,
  ) {
    return this.subscriptionManagementUseCase.update(id, updateSubscriptionDto)
  }
}
