import { Id } from '@backend/cores/base/id.type'
import {
  SubscriptionUsage,
  SubscriptionUsageProps,
} from '@backend/modules/subscription/entities'

interface SubscriptionUsageRepository {
  findById(id: Id): Promise<Nullable<SubscriptionUsage>>
  findByOrganizationId(organizationId: Id): Promise<Nullable<SubscriptionUsage>>
  findAll(options?: {
    filter?: any
    limit?: number
    offset?: number
    orderBy?: any
  }): Promise<SubscriptionUsage[]>
  count(options?: { filter?: any }): Promise<number>
  create(data: SubscriptionUsage): Promise<Nullable<SubscriptionUsage>>
  update(
    id: Id,
    data: Partial<SubscriptionUsageProps>,
  ): Promise<Nullable<SubscriptionUsage>>
  delete(id: Id): Promise<boolean>
  getCurrentUsageByOrganizationId(organizationId: Id): Promise<any>

  /**
   * Create a subscription usage event log record (for overages, SMS, phone calls)
   * Following the event-based logging pattern as per PRD requirements
   */
  createUsageEventRecord(params: {
    organizationId: string
    subscriptionItemId?: string
    usageType: string
    usageValue: number
    usageTimestamp?: Date
    billingCycleStartDate?: Date
    billingCycleEndDate?: Date
    stripePriceId?: string
    stripeUsageRecordId?: string
    reportedToStripe?: boolean
    isBillable?: boolean
    notes?: string
    createdBy?: string
  }): Promise<SubscriptionUsage>

  /**
   * Get usage records for a specific billing period
   */
  getUsageByBillingPeriod(
    organizationId: string,
    startDate: Date,
    endDate: Date,
    usageType?: string,
  ): Promise<SubscriptionUsage[]>
}

export default SubscriptionUsageRepository
export const SUBSCRIPTION_USAGE_REPOSITORY = 'SUBSCRIPTION_USAGE_REPOSITORY'
