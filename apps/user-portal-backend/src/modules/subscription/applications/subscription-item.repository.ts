import { ResourceType } from '@libs/shared/constants/subscription/resource-mapping'

import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { SubscriptionItemModel } from '@backend/frameworks/database/models/subscription-item.model'

import { SubscriptionItem, SubscriptionItemProps } from '../entities'

interface SubscriptionItemRepository
  extends CrudRepository<
    SubscriptionItem,
    SubscriptionItemProps,
    ITypeOrmFilter<SubscriptionItemModel>
  > {
  findBySubscriptionAndResourceType(
    subscriptionId: string,
    resourceType: ResourceType,
  ): Promise<SubscriptionItem | null>

  findOverageItemsBySubscription(
    subscriptionId: string,
  ): Promise<SubscriptionItem[]>

  findByResourceType(resourceType: ResourceType): Promise<SubscriptionItem[]>
}

export default SubscriptionItemRepository
export const SUBSCRIPTION_ITEM_REPOSITORY = 'SUBSCRIPTION_ITEM_REPOSITORY'
