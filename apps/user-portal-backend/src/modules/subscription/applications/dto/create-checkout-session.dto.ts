import { createZodDto } from '@anatine/zod-nestjs'
import { extendZodWithOpenApi } from '@anatine/zod-openapi'
import { z } from 'zod'

// Extend Zod with OpenAPI capabilities
extendZodWithOpenApi(z)

const CreateCheckoutSessionZodSchema = z.object({
  planId: z
    .string()
    .min(1)
    .describe('The ID of the subscription plan to create checkout session for.')
    .openapi({ example: 'plan-123' }),
  paymentMethod: z
    .string()
    .describe(
      'The payment method to use for the subscription, also define redirect URL by this method.',
    ),
})

export class CreateCheckoutSessionDto extends createZodDto(
  CreateCheckoutSessionZodSchema,
) {}
