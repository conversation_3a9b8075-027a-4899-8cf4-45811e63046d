import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'

import { SubscriptionSchema } from '../../entities'

export class CreateSubscriptionDto extends createZodDto(
  extendApi(
    SubscriptionSchema.omit({
      createdBy: true,
      updatedBy: true,
    }),
  ),
) {}

export class UpdateSubscriptionDto extends createZodDto(
  extendApi(
    SubscriptionSchema.omit({
      createdBy: true,
      updatedBy: true,
    }).partial(),
  ),
) {}
