import { IsString, IsNotEmpty, IsUUID } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class ChangePlanPreviewRequestDto {
  @ApiProperty({
    description: 'The ID of the new subscription plan to preview change to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  newPlanId: string
}

export class ChangePlanPreviewResponseDto {
  @ApiProperty({
    description: 'Immediate charges that would be applied (amounts in cents)',
    type: Object,
    example: {
      proratedCredit: -500,
      newPlanCharge: 5000,
      overageCharges: { members: 1000, teams: 500 },
      totalAmountDue: 6000,
      currency: 'usd',
    },
  })
  immediateCharges: {
    proratedCredit: number // e.g., -500 (refund for unused current plan time)
    newPlanCharge: number // e.g., 5000 (full new plan price in cents)
    overageCharges: { [key: string]: number } // e.g., { members: 1000 } (in cents)
    totalAmountDue: number // e.g., 6000 (total immediate charge in cents)
    currency: string // ISO 4217 currency code from Stripe (e.g., 'usd', 'eur')
  }

  @ApiProperty({
    description: 'Information about the next invoice (amounts in cents)',
    type: Object,
    example: {
      date: '2025-02-15T00:00:00.000Z',
      amount: 5000,
      currency: 'usd',
    },
  })
  nextInvoice: {
    date: string // ISO 8601 string (new billing cycle date)
    amount: number // amount in cents (regular monthly price)
    currency: string // ISO 4217 currency code from Stripe (e.g., 'usd', 'eur')
  }

  @ApiProperty({
    description: 'Human-readable summary of the plan change',
    example:
      'Switching from Basic ($1.00/mo) to Pro ($50.00/mo). You will be charged $45.00 today and your billing date will change to the 15th of each month.',
  })
  summaryMessage: string
}
