import { createZodDto } from '@anatine/zod-nestjs'
import { extendZodWithOpenApi } from '@anatine/zod-openapi'
import { z } from 'zod'

// Extend Zod with OpenAPI capabilities
extendZodWithOpenApi(z)

const CreateCustomerSessionZodSchema = z.object({
  customerId: z
    .string()
    .min(1) // from IsNotEmpty
    .describe('The Stripe Customer ID for the customer portal session.')
    .openapi({ example: 'cus_1234567890' }), // from ApiProperty
  returnUrl: z
    .string()
    .url() // from IsUrl
    .min(1) // from IsNotEmpty
    .describe(
      'The URL to redirect the customer to after they visit the customer portal.',
    )
    .openapi({ example: 'https://your-website.com/account' }), // from ApiProperty
})

export class CreateCustomerSessionDto extends createZodDto(
  CreateCustomerSessionZodSchema,
) {}
