import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'
import { z } from 'zod'

export const CreateManualSubscriptionSchema = z.object({
  planId: z.string().describe('The ID of the subscription plan'),
  organizationId: z.string().optional().describe('The ID of the organization'),
})

export class CreateManualSubscriptionDto extends createZodDto(
  extendApi(CreateManualSubscriptionSchema),
) {}

export type CreateManualSubscriptionRequest = z.infer<
  typeof CreateManualSubscriptionSchema
>
