import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'
import { z } from 'zod'

import { SubscriptionPlanSchema } from '../../entities'
import { PlanResourceLimitSchema } from '../../entities/plan-resource-limit.entity'

export class SubscriptionPlanDto extends createZodDto(
  extendApi(
    SubscriptionPlanSchema.omit({
      createdBy: true,
      updatedBy: true,
    }),
  ),
) {}

const ResourceLimitForPlanDto = PlanResourceLimitSchema.omit({
  subscriptionPlanId: true,
  overageStripePriceId: true,
  createdBy: true,
  updatedBy: true,
})

export class CreateSubscriptionPlanDto extends createZodDto(
  extendApi(
    SubscriptionPlanSchema.omit({
      stripePriceId: true,
      createdBy: true,
      updatedBy: true,
    }).extend({
      price: z.number(), // Price in cents
      resourceLimits: z.array(ResourceLimitForPlanDto).optional(),
    }),
  ),
) {}

export class UpdateSubscriptionPlanDto extends createZodDto(
  extendApi(
    SubscriptionPlanSchema.omit({
      stripePriceId: true,
      createdBy: true,
      updatedBy: true,
    })
      .partial()
      .extend({
        resourceLimits: z.array(ResourceLimitForPlanDto).optional(),
      }),
  ),
) {}
