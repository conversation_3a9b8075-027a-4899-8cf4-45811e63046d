import { IsString, <PERSON>NotEmpty, IsUUID } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class ChangePlanDto {
  @ApiProperty({
    description: 'The ID of the new subscription plan to change to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  newPlanId: string
}

export class ChangePlanResponseDto {
  @ApiProperty({
    description: 'The ID of the subscription that was updated',
    example: '550e8400-e29b-41d4-a716-************',
  })
  subscriptionId: string

  @ApiProperty({
    description: 'The ID of the new plan',
    example: '550e8400-e29b-41d4-a716-************',
  })
  newPlanId: string

  @ApiProperty({
    description: 'The ID of the previous plan',
    example: '550e8400-e29b-41d4-a716-************',
  })
  previousPlanId: string

  @ApiProperty({
    description:
      'Overage charges applied during the plan change (dynamic resource types)',
    type: Object,
    example: {
      member: 50,
      team: 20,
      check: 10,
      integration: 5,
      statusPage: 2,
    },
  })
  overageCharges: {
    [key: string]: number // Pure dynamic resource types
  }

  @ApiProperty({
    description: 'Message about the plan change',
    example:
      'Plan change successful. Overage charges have been reported to Stripe.',
  })
  message: string

  @ApiProperty({
    description: 'Timestamp of when the plan was changed',
    example: '2024-01-15T12:00:00.000Z',
  })
  changedAt: string
}
