import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { SubscriptionPlanModel } from '@backend/frameworks/database/models/subscription-plan.model'
import { Id } from '@backend/cores/base/id.type'

import {
  SubscriptionPlan,
  SubscriptionPlanProps,
  PlanResourceLimitProps,
} from '../entities'

interface SubscriptionPlanRepository
  extends CrudRepository<
    SubscriptionPlan,
    SubscriptionPlanProps,
    ITypeOrmFilter<SubscriptionPlanModel>
  > {
  /**
   * Create a subscription plan with its resource limits in a single transaction
   */
  createWithLimits(
    plan: SubscriptionPlan,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<SubscriptionPlan | null>

  /**
   * Update a subscription plan and replace all its resource limits in a single transaction
   */
  updateWithLimits(
    planId: Id,
    planProps: Partial<SubscriptionPlanProps>,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<SubscriptionPlan | null>

  /**
   * Find a subscription plan with its resource limits eagerly loaded
   */
  findByIdWithLimits(planId: Id): Promise<SubscriptionPlan | null>

  /**
   * Find all subscription plans with their resource limits eagerly loaded
   */
  findAllWithLimits(options?: any): Promise<SubscriptionPlan[]>

  /**
   * Delete a subscription plan and all its resource limits
   */
  deleteWithLimits(planId: Id): Promise<boolean>
}

export default SubscriptionPlanRepository
export const SUBSCRIPTION_PLAN_REPOSITORY = 'SUBSCRIPTION_PLAN_REPOSITORY'
