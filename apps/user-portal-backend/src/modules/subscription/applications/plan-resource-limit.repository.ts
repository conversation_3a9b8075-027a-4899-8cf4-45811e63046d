import { ResourceType } from '@libs/shared/constants/subscription'

import { Id } from '@backend/cores/base/id.type'
import CrudRepository from '@backend/cores/base/crud-repository'

import {
  PlanResourceLimit,
  PlanResourceLimitProps,
} from '../entities/plan-resource-limit.entity'

export interface PlanResourceLimitRepository
  extends CrudRepository<PlanResourceLimit, PlanResourceLimitProps> {
  /**
   * Find all resource limits for a specific subscription plan
   */
  findByPlanId(subscriptionPlanId: Id): Promise<PlanResourceLimit[]>

  /**
   * Find a specific resource limit for a plan and resource type
   */
  findByPlanAndResourceType(
    subscriptionPlanId: Id,
    resourceType: ResourceType,
  ): Promise<PlanResourceLimit | null>

  /**
   * Create multiple resource limits for a plan in a single transaction
   */
  bulkCreateForPlan(
    subscriptionPlanId: Id,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<PlanResourceLimit[]>

  /**
   * Update all resource limits for a plan (delete existing and create new ones)
   */
  replaceAllForPlan(
    subscriptionPlanId: Id,
    limits: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[],
  ): Promise<PlanResourceLimit[]>

  /**
   * Delete all resource limits for a specific plan
   */
  deleteByPlanId(subscriptionPlanId: Id): Promise<void>

  /**
   * Check if a plan has any resource limits configured
   */
  hasPlanLimits(subscriptionPlanId: Id): Promise<boolean>

  /**
   * Get resource limits mapped by resource type for a plan
   */
  getLimitMapForPlan(
    subscriptionPlanId: Id,
  ): Promise<Map<ResourceType, PlanResourceLimit>>
}

export const PLAN_RESOURCE_LIMIT_REPOSITORY = 'PLAN_RESOURCE_LIMIT_REPOSITORY'

export default PlanResourceLimitRepository
