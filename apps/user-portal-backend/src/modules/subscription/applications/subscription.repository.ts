import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { SubscriptionModel } from '@backend/frameworks/database/models/subscription.model'

import { Subscription, SubscriptionProps } from '../entities'

type SubscriptionRepository = CrudRepository<
  Subscription,
  SubscriptionProps,
  ITypeOrmFilter<SubscriptionModel>
>

export default SubscriptionRepository
export const SUBSCRIPTION_REPOSITORY = 'SUBSCRIPTION_REPOSITORY'
