import { forwardRef, Inject, Injectable } from '@nestjs/common'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { CheckStatus } from '@libs/shared/constants/check.enum'
import moment from 'moment'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import {
  CreateIncidentDto,
  IncidentFilterDto,
} from '@backend/modules/incident/applications/dto/incident-request.dto'
import { Incident, IncidentResponse } from '@backend/modules/incident/entities'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { CheckUseCase } from '@backend/modules/check/usecases/check.usecase'
import { StateMachineService } from '@backend/frameworks/state-machine/state-machine.service'
import { NotificationFactory } from '@backend/frameworks/notification/notification.factory'
import { SlackMessageType } from '@backend/frameworks/notification/slack/slack.constant'

import IncidentRepository, {
  INCIDENT_REPOSITORY,
} from '../applications/incident.repository'

@Injectable()
export class IncidentUseCase {
  constructor(
    @Inject(INCIDENT_REPOSITORY) private readonly repo: IncidentRepository,
    @Inject(forwardRef(() => CheckUseCase))
    private readonly checkUseCase: WrapperType<CheckUseCase>,
    @Inject(StateMachineService)
    private readonly stateMachineService: StateMachineService,
    private notificationFactory: NotificationFactory,
  ) {}

  async findAll(
    incidentFilter: IncidentFilterDto,
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<IncidentResponse>> {
    try {
      const incidents: Nullable<IPaginate<Incident>> = await this.repo.findAll(
        incidentFilter.teamId,
        incidentFilter.checkId,
        null,
        paginateOption,
      )
      if (!incidents)
        return { data: [], total: 0, totalPage: 0, page: 0, limit: 0 }

      return {
        ...incidents,
        data: incidents.data.map((incident) => incident.toResponse()),
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findByCheckId(
    checkId: Id,
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<IncidentResponse>> {
    try {
      const incidents: Nullable<IPaginate<Incident>> =
        await this.repo.findAllByCheckId(checkId, paginateOption)
      if (!incidents)
        return { data: [], total: 0, totalPage: 0, page: 0, limit: 0 }

      return {
        ...incidents,
        data: incidents.data.map((incident) => incident.toResponse()),
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  /**
   * Find ongoing incident by checkId
   *
   * @param checkId Check ID
   */
  // async findOnGoingIncident(checkId: Id): Promise<Nullable<Incident>> {
  //   const check = await this.checkUseCase.findOne(checkId)

  //   if (!check?.incidentId) return null

  //   return this.repo.findById(check.incidentId)
  // }

  async findOne(id: Id): Promise<Nullable<IncidentResponse>> {
    try {
      const entity = await this.repo.findById(id)

      if (!entity) return null
      return entity.toResponse()
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  // async createTestIncident(incident: IncidentRequestDto) {
  //   await this.repo.createTestData(incident)
  // }

  async create(data: CreateIncidentDto): Promise<Nullable<Incident>> {
    if (!data.checkId) return null
    const check = await this.checkUseCase.findOne(data.checkId)

    if (!check?.id) {
      throw new Error(`Not found check ID: ${data.checkId}`)
    }

    if (!check?.team?.id) {
      throw new Error(`Not found teamId in check: ${data.checkId}`)
    }

    const incident = Incident.create({
      checkId: check.id,
      checkInfo: {
        type: check.type,
        url: check.url,
        method: check.method,
        locations: check.locations,
      },
      cause: data.workerResponse?.[0]?.response?.message,
      teamId: check.team.id,
      startedAt: data.date,
    })

    const createdIncident = await this.repo.create(incident)

    if (!createdIncident) throw new Error('Failed to create incident')

    const stepFunctionArn = await this.stateMachineService.startExecution(
      createdIncident.getEscalationPolicy({
        recoverPeriod: check.recoverPeriod,
      }),
    )

    createdIncident.setStepFunctionArn(stepFunctionArn)

    const updatedIncident = await this.repo.update(
      createdIncident.id,
      createdIncident.getProps(),
    )

    await this.notificationFactory.getNotificationService('slack').send({
      type: SlackMessageType.IncidentStarted,
      url: updatedIncident?.getProps().checkInfo?.url,
      method: updatedIncident?.getProps().checkInfo?.method,
      cause: updatedIncident?.getProps().cause,
      startedAt: updatedIncident?.getProps().startedAt,
    })

    return updatedIncident
  }

  async recoverIncident({
    id,
    checkId,
  }: {
    id: Id
    checkId: Id
  }): Promise<Nullable<Incident>> {
    const incident = await this.repo.findById(id)

    if (!incident) return null

    await this.checkUseCase.updateStatus(checkId, CheckStatus.VALIDATING)

    return await this.repo.update(incident.id, {
      status: IncidentStatus.RECOVERED,
    })
  }

  async reappearIncident({
    id,
    checkId,
  }: {
    id: Id
    checkId: Id
  }): Promise<Nullable<Incident>> {
    const incident = await this.repo.findById(id)

    if (!incident) return null

    await this.checkUseCase.updateStatus(checkId, CheckStatus.DOWN)

    return await this.repo.update(incident.id, {
      status: IncidentStatus.ISSUE_REAPPEARED,
    })
  }

  async acknowledgeIncident({
    id,
    userId,
  }: {
    id: Id
    userId?: Id
  }): Promise<Nullable<Incident>> {
    const incident = await this.repo.findById(id)

    if (!incident) return null

    if (incident.stepFunctionArn) {
      await this.stateMachineService.stopExecution(incident.stepFunctionArn)
    }

    return await this.repo.update(incident.id, {
      acknowledgedAt: new Date(),
      // TODO: Acknowledge by could be a user from frontend or integration system
      acknowledgedBy: userId || 'some user',
      status: IncidentStatus.ACKNOWLEDGED,
    })
  }

  async resolveIncident({
    id,
    date,
    userId,
  }: {
    id: Id
    date?: Date
    userId?: Id
  }): Promise<Nullable<Incident>> {
    const incident = await this.repo.findById(id)

    if (!incident) return null

    if (incident.stepFunctionArn) {
      await this.stateMachineService.stopExecution(incident.stepFunctionArn)
    }

    await this.checkUseCase.resolveIncident(incident?.checkId)

    const resolvedIncident = await this.repo.update(incident.id, {
      resolvedAt: date || new Date(),
      // TODO: Resolve by could be a user or system
      resolvedBy: userId || 'SYSTEM',
      status: IncidentStatus.RESOLVED,
    })

    await this.notificationFactory.getNotificationService('slack').send({
      type: SlackMessageType.IncidentAutoResolved,
      url: resolvedIncident?.getProps().checkInfo?.url,
      method: resolvedIncident?.getProps().checkInfo?.method,
      cause: resolvedIncident?.getProps().cause,
      startedAt: resolvedIncident?.getProps().startedAt,
      length: moment(resolvedIncident?.getProps().resolvedAt).from(
        resolvedIncident?.getProps().startedAt,
        true,
      ),
    })

    return resolvedIncident
  }
}
