import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { generateId } from '@backend/commons/id'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import IncidentEventRepository, {
  INCIDENT_EVENT_REPOSITORY,
} from '@backend/modules/incident/applications/incident-event.repository'
import {
  IncidentEvent,
  IncidentEventProps,
  IncidentEventResponse,
} from '@backend/modules/incident/entities/incident-event.entity'

@Injectable()
export class IncidentEventUseCase {
  constructor(
    @Inject(INCIDENT_EVENT_REPOSITORY)
    private readonly repo: IncidentEventRepository,
  ) {}

  async findAll(
    incidentId: Id,
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<IncidentEventResponse>> {
    try {
      const incidentEvents: Nullable<IPaginate<IncidentEvent>> =
        await this.repo.findAll(incidentId, paginateOption)
      if (!incidentEvents)
        return { data: [], total: 0, totalPage: 0, page: 0, limit: 0 }

      return {
        ...incidentEvents,
        data: incidentEvents.data.map((event) => event.toResponse()),
      }
    } catch (error) {
      console.error(error)

      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<IncidentEventResponse>> {
    try {
      const entity = await this.repo.findById(id)

      if (!entity) return null
      return entity.toResponse()
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async create(data: IncidentEventProps): Promise<Nullable<IncidentEvent>> {
    if (!data.incidentId) {
      throw new Error(`Missing check ID`)
    }

    const event: IncidentEvent = new IncidentEvent({
      id: generateId(),
      props: data,
    })

    return await this.repo.create(event)
  }
}
