/* eslint-disable prettier/prettier */
import { Type } from 'class-transformer'
import { IsArray, IsOptional, IsString } from 'class-validator'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { CheckWorkerResponse } from '@libs/shared/constants/shared.interface'

import { Id } from '@backend/cores/base/id.type'

export class CreateIncidentDto {
  date: Date
  checkId: Id
  workerResponse?: CheckWorkerResponse[]
}

export class IncidentFilterDto {
  @Type(() => String)
  @IsString()
  @IsOptional()
  teamId: Id

  @Type(() => String)
  @IsString()
  @IsOptional()
  checkId: Id

  @Type(() => Array<string>)
  @IsArray()
  @IsOptional()
  status: IncidentStatus[]
}
