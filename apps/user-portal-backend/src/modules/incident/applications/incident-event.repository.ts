import { Id } from '@backend/cores/base/id.type'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { IncidentEvent } from '@backend/modules/incident/entities/incident-event.entity'

interface IncidentEventRepository {
  findAll(
    incidentId: Id,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<IncidentEvent>>
  findById(id: Id): Promise<Nullable<IncidentEvent>>
  create(data: IncidentEvent): Promise<Nullable<IncidentEvent>>
  update(id: Id, data: IncidentEvent): Promise<Nullable<IncidentEvent>>
  delete(id: Id): Promise<boolean>
}

export default IncidentEventRepository
export const INCIDENT_EVENT_REPOSITORY = 'INCIDENT_EVENT_REPOSITORY'
