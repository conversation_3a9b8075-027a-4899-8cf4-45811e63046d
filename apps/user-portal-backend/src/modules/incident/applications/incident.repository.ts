import { IncidentStatus } from '@libs/shared/constants/incident'

import { Id } from '@backend/cores/base/id.type'
import { Incident, IncidentProps } from '@backend/modules/incident/entities'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { PaginateOptions } from '@backend/commons/dto/paginateOptions.dto'

interface IncidentRepository {
  // createTestData(data: IncidentRequestDto): Promise<any>
  findAll(
    teamId: Id,
    checkId: Nullable<Id>,
    status: Nullable<IncidentStatus[]>,
    paginateOption: PaginateOptions,
  ): Promise<IPaginate<Incident>>
  findAllByCheckId(
    checkId: Id,
    paginateOption: PaginateOptions,
  ): Promise<IPaginate<Incident>>
  findById(id: Id): Promise<Nullable<Incident>>
  create(data: Incident): Promise<Nullable<Incident>>
  update(id: Id, data: Partial<IncidentProps>): Promise<Nullable<Incident>>
  delete(id: Id): Promise<boolean>

  getOccurringIncidentByCheckId(checkId: Id): Promise<Nullable<Incident>>
}

export default IncidentRepository
export const INCIDENT_REPOSITORY = 'INCIDENT_REPOSITORY'
