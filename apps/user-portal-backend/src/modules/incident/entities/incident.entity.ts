import { z } from 'zod'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { CheckType } from '@libs/shared/constants/check.enum'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

import { IncidentEscalation } from '../interfaces/incident-escalation'

export const IncidentSchema = z.object({
  checkId: z.string(),
  teamId: z.string(),
  status: z
    .nativeEnum(IncidentStatus)
    .optional()
    .default(IncidentStatus.STARTED),
  startedAt: z.date().optional(),
  resolvedAt: z.date().optional(),
  resolvedBy: z.string().optional(),
  acknowledgedAt: z.date().optional(),
  acknowledgedBy: z.string().optional(),
  escalationPolicy: z.record(z.string()).optional(),
  checkInfo: z
    .object({
      type: z.nativeEnum(CheckType).optional(),
      url: z.string().optional(),
      method: z.string().optional(),
      locations: z.array(z.string()).optional(),
    })
    .optional(),
  stepFunctionArn: z.string().optional(),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
  cause: z.string().optional(),

  customIncidentId: z.string().optional(),
  requester_email: z.string().optional(),
  name: z.string().optional(),
  summary: z.string().optional(),
  description: z.string().optional(),
})

export type IncidentProps = z.infer<typeof IncidentSchema>

export const IncidentResponse = IncidentSchema.partial()

export interface IncidentResponse extends z.infer<typeof IncidentResponse> {
  id: Id
  createdAt: Date
  updatedAt: Date
}

export class Incident extends AggregateRoot<IncidentProps, IncidentResponse> {
  static create(props: Omit<IncidentProps, 'status'>) {
    const incidentProps = {
      ...props,
      status: IncidentStatus.STARTED,
    }

    const incident = new Incident({
      id: generateId(),
      props: incidentProps,
    })

    return incident
  }

  static update({ id, props }: { id: Id; props: IncidentProps }) {
    const incident = new Incident({
      id: id,
      props: props,
    })

    return incident
  }

  public toResponse(): IncidentResponse {
    const props = this.getProps()

    return {
      id: props.id,
      checkId: props.checkId,
      teamId: props.teamId,
      status: props.status,
      startedAt: props.startedAt,
      resolvedAt: props.resolvedAt,
      resolvedBy: props.resolvedBy,
      acknowledgedAt: props.acknowledgedAt,
      acknowledgedBy: props.acknowledgedBy,
      checkInfo: props.checkInfo,
      cause: props.cause,
      escalationPolicy: props.escalationPolicy,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,

      customIncidentId: props.customIncidentId,
      requester_email: props.requester_email,
      name: props.name,
      summary: props.summary,
      description: props.description,
    }
  }

  get stepFunctionArn() {
    return this.props.stepFunctionArn
  }

  get checkId() {
    return this.props.checkId
  }

  setStepFunctionArn(stepFunctionArn: string) {
    this.props.stepFunctionArn = stepFunctionArn
  }

  // FIXME: This is a dummy method for Escalation, replace with escalation feature later
  public getEscalationPolicy({
    recoverPeriod = 0,
  }: {
    recoverPeriod?: number
  }): IncidentEscalation {
    return {
      repeat: [1, 2, 3],
      repeatDelay: 30,
      escalationSteps: [
        {
          index: 0,
          delay: 0,
          recoverPeriod,
          incidentId: this.id,
          notifications: [
            {
              type: 'SMS',
              to: 'on-call',
              message: '[SMS] Incident detected!',
            },
            {
              type: 'Email',
              to: 'on-call',
              message: '[Email] Incident detected!',
            },
            {
              type: 'Message',
              to: 'slack',
              message: '[Slack] Incident detected!',
            },
            {
              type: 'Message',
              to: 'discord',
              message: '[Discord] Incident detected!',
            },
          ],
        },
        {
          index: 1,
          delay: 180,
          recoverPeriod,
          incidentId: this.id,
          notifications: [
            {
              type: 'SMS',
              to: 'Team lead',
              message: '[SMS] Incident detected!',
            },
            {
              type: 'Call',
              to: 'Team lead',
              message: '[Slack] Incident detected!',
            },
          ],
        },
      ],
    }
  }
}
