import { Controller, Get, Param, Query } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { IncidentEventResponseDto } from '@backend/modules/incident/applications/dto/incident-event-response.dto'
import { IncidentEventUseCase } from '@backend/modules/incident/usecases/incident-event.usecase'

@ApiTags('IncidentEvent')
@Controller('incident-event')
export class IncidentEventController {
  constructor(private readonly incidentEventUseCase: IncidentEventUseCase) {}

  @Get('/incident/:id')
  @ApiBearerAuth()
  @ApiPaginatedResponse(IncidentEventResponseDto)
  // @UseGuards(AuthGuard, PermissionsGuard)
  // @CheckPermissions([
  //   {
  //     action: PermissionAction.VIEW,
  //     scope: PermissionScope.INCIDENTS,
  //     type: PermissionType.TEAM,
  //   },
  // ])
  findAllByIncidentId(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Param('id') incidentId: string,
  ) {
    return this.incidentEventUseCase.findAll(incidentId, paginateOption)
  }
}
