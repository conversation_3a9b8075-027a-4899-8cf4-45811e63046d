import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { IncidentUseCase } from '@backend/modules/incident/usecases/incident.usecase'
import { IncidentResponseDto } from '@backend/modules/incident/applications/dto/incident-response.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { IncidentFilterDto } from '@backend/modules/incident/applications/dto/incident-request.dto'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'

@ApiTags('Incident')
@Controller('incident')
export class IncidentController {
  constructor(private readonly incidentUseCase: IncidentUseCase) {}

  @Get('/check/:id')
  @ApiBearerAuth()
  @ApiPaginatedResponse(IncidentResponseDto)
  // @UseGuards(AuthGuard, PermissionsGuard)
  // @CheckPermissions([
  //   {
  //     action: PermissionAction.VIEW,
  //     scope: PermissionScope.CHECKS,
  //     type: PermissionType.TEAM,
  //   },
  // ])
  findByCheckId(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Param('id') checkId: string,
  ) {
    return this.incidentUseCase.findByCheckId(checkId, paginateOption)
  }

  // @CheckPermissions([
  //   {
  //     action: PermissionAction.VIEW,
  //     scope: PermissionScope.CHECKS,
  //     type: PermissionType.TEAM,
  //   },
  // ])
  @Get(':id/acknowledge')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  acknowledgeIncident(
    @Param('id') id: string,
    @CurrentUser() payload: JwtPayload,
  ) {
    return this.incidentUseCase.acknowledgeIncident({
      id,
      userId: payload.userId,
    })
  }

  @Get(':id/resolve')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  resolveIncident(@Param('id') id: string, @CurrentUser() payload: JwtPayload) {
    return this.incidentUseCase.resolveIncident({
      id,
      date: new Date(),
      userId: payload.userId,
    })
  }

  @ApiResponse({
    type: IncidentResponseDto,
  })
  @Get(':id')
  // @ApiBearerAuth()
  // @UseGuards(AuthGuard)
  // @CheckPermissions([
  //   {
  //     action: PermissionAction.VIEW,
  //     scope: PermissionScope.CHECKS,
  //     type: PermissionType.TEAM,
  //   },
  // ])
  findOne(@Param('id') id: string) {
    return this.incidentUseCase.findOne(id)
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(IncidentResponseDto)
  // @UseGuards(AuthGuard, PermissionsGuard)
  // @CheckPermissions([
  //   {
  //     action: PermissionAction.VIEW,
  //     scope: PermissionScope.INCIDENTS,
  //     type: PermissionType.TEAM,
  //   },
  // ])
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @Query() incidentFilter: IncidentFilterDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    incidentFilter.teamId = orgTeamParams.teamId
    return this.incidentUseCase.findAll(incidentFilter, paginateOption)
  }

  // @ApiResponse({
  //   type: IncidentResponseDto,
  // })
  // @Post('/test-incident')
  // @ApiBearerAuth()
  // create(@Body() incident: IncidentRequestDto) {
  //   return this.incidentUseCase.createTestIncident(incident)
  // }
}
