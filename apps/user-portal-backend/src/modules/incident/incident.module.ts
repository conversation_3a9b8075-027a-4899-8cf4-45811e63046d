import { forwardRef, Module } from '@nestjs/common'
import { TimeUtils } from '@libs/shared/time/time.utils'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { CacheModule } from '@backend/frameworks/cache/cache.module'
import { IncidentUseCase } from '@backend/modules/incident/usecases/incident.usecase'
import { IncidentController } from '@backend/modules/incident/controllers/incident.controller'
import { INCIDENT_REPOSITORY } from '@backend/modules/incident/applications/incident.repository'
import { IncidentInfrastructure } from '@backend/modules/incident/infras/incident.infrastructure'
import { CHECK_REPOSITORY } from '@backend/modules/check/applications/check.repository'
import { CheckInfrastructure } from '@backend/modules/check/infras/check.infrastructure'
import { INCIDENT_EVENT_REPOSITORY } from '@backend/modules/incident/applications/incident-event.repository'
import { IncidentEventInfrastructure } from '@backend/modules/incident/infras/incident-event.infrastructure'
import { IncidentEventUseCase } from '@backend/modules/incident/usecases/incident-event.usecase'
import { IncidentEventController } from '@backend/modules/incident/controllers/incident-event.controller'
import { StateMachineModule } from '@backend/frameworks/state-machine/state-machine.module'
import { DynamoModule } from '@backend/frameworks/dynamo/dynamo.module'
import { NotificationsModule } from '@backend/frameworks/notification/notification.module'
import { QueueService } from '@backend/frameworks/queue/queue.service'
import { BullClient } from '@backend/frameworks/queue/bull/bull.client'
import { SubscriptionModule } from '@backend/modules/subscription/subscription.module'
import { UserModule } from '@backend/modules/user/user.module'

import { CheckModule } from '../check/check.module'
import { StatusPageModule } from '../status-page/status-page.module'

@Module({
  imports: [
    DatabaseModule,
    CacheModule,
    DynamoModule,
    forwardRef(() => CheckModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => StatusPageModule),
    forwardRef(() => UserModule),
    StateMachineModule,
    NotificationsModule,
  ],
  controllers: [IncidentController, IncidentEventController],
  providers: [
    { provide: INCIDENT_REPOSITORY, useClass: IncidentInfrastructure },
    {
      provide: INCIDENT_EVENT_REPOSITORY,
      useClass: IncidentEventInfrastructure,
    },
    { provide: CHECK_REPOSITORY, useClass: CheckInfrastructure },
    QueueService,
    BullClient,
    TimeUtils,
    IncidentUseCase,
    IncidentEventUseCase,
  ],
})
export class IncidentModule {}
