import { Inject, Injectable } from '@nestjs/common'
import { InjectModel, Item, Model } from 'nestjs-dynamoose'
import { SortOrder } from 'dynamoose/dist/General'
import { omit } from 'lodash'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { INCIDENT_SCHEMA } from '@libs/database/lib/dynamo/incident-check.schema'
import { INCIDENT_CUSTOM_SCHEMA } from '@libs/database/lib/dynamo/incident-customIncident.schema'

import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import IncidentRepository from '@backend/modules/incident/applications/incident.repository'
import { Incident, IncidentProps } from '@backend/modules/incident/entities'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

interface IncidentModel extends IncidentProps {
  id: Id
  checkId: Id
  teamId: Id
  createdAt: Date
  updatedAt: Date
}

@Injectable()
export class IncidentInfrastructure implements IncidentRepository {
  constructor(
    @Inject(Database) private database: Database,
    @InjectModel(INCIDENT_SCHEMA)
    private incidentModel: Model<IncidentModel, Id>,
    @InjectModel(INCIDENT_CUSTOM_SCHEMA)
    private incidentCustomModel: Model<IncidentModel, Id>,
  ) {}

  private toDomain(data: Item<IncidentModel>): Incident {
    const { id, createdAt, updatedAt, ...props } = data

    return new Incident({
      id,
      createdAt,
      updatedAt,
      props,
    })
  }

  private toModel(data: Incident): IncidentModel {
    return {
      ...data.getProps(),
      id: data.id,
      checkId: data.getProps().checkId,
      teamId: data.getProps().teamId,
    }
  }

  private serializeUpdateData(
    data: Partial<IncidentProps>,
  ): Partial<IncidentModel> {
    // Avoid to update partition key, sort key, and timestamps
    return omit(data, [
      'id',
      'checkId',
      'teamId',
      'createdAt',
      'updatedAt',
      'startedAt',
    ])
  }

  async create(data: Incident): Promise<Nullable<Incident>> {
    const resp = await this.incidentModel.create(this.toModel(data))
    return this.toDomain(resp)
  }

  async findById(id: Id): Promise<Nullable<Incident>> {
    const incident = await this.incidentModel.get(id)
    if (!incident) {
      return null
    }

    return this.toDomain(incident)
  }

  findOne(query: ITypeOrmFilter<Incident>): Promise<Nullable<Incident>> {
    return Promise.resolve(null)
  }

  updateById(id: Id, data: IncidentProps): Promise<Nullable<Incident>> {
    return Promise.resolve(null)
  }

  async findAllByCheckId(
    checkId: Id,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<Incident>> {
    const startAt = paginateOption.lastItemId
      ? await this.incidentModel.get(paginateOption.lastItemId || '')
      : null
    const total = await this.incidentModel
      .query('checkId')
      .eq(checkId)
      .count()
      .exec()

    if (!total || total.count === 0) {
      return {
        data: [],
        total: 0,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    }

    const query = await this.incidentModel
      .query('checkId')
      .eq(checkId)
      .using('checkId-index')

    if (startAt) {
      query.startAt(startAt)
    }
    if (paginateOption.limit) {
      query.limit(paginateOption.limit)
    }

    const data = await query.exec()

    return {
      data: data.map((incident) => this.toDomain(incident)),
      total: total.count,
      totalPage: 0,
      page: 0,
      limit: 0,
    }
  }

  async update(
    id: Id,
    data: Partial<IncidentProps>,
  ): Promise<Nullable<Incident>> {
    const incident = await this.findById(id)

    if (!incident) {
      return null
    }

    const updatedIncident = await this.incidentModel.update(
      id,
      this.serializeUpdateData(data),
    )

    return this.toDomain(updatedIncident)
  }

  async findAll(
    teamId: Id,
    checkId: Nullable<Id>,
    status: Nullable<IncidentStatus[]>,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<Incident>> {
    const startAt = paginateOption.lastItemId
      ? await this.incidentModel.get(paginateOption.lastItemId || '')
      : null

    const countQuery = this.incidentModel
      .query('teamId')
      .eq(teamId)
      .using('teamId-index')

    if (checkId) {
      countQuery.where('checkId').eq(checkId).using('checkId-index')
    }

    if (status) {
      countQuery.where('status').in(status)
    }

    const total = await countQuery.count().exec()

    if (!total || total.count === 0) {
      return {
        data: [],
        total: 0,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    }

    const query = await this.incidentModel
      .query({
        teamId,
      })
      .using('teamId-createdAt-index')

    if (checkId) {
      query.where('checkId').eq(checkId).using('checkId-index')
    }
    if (status) {
      query.where('status').eq(status)
    }

    if (startAt) {
      query.startAt({
        id: startAt.id,
        createdAt: new Date(startAt.createdAt).toISOString(),
        teamId: startAt.teamId,
      })
    }

    if (paginateOption.limit) {
      query.limit(Number(paginateOption.limit))
    }

    query.sort(SortOrder.descending)

    const data = await query.exec()

    return Promise.resolve({
      data: data.map((incident) => this.toDomain(incident)),
      total: total.count,
      totalPage: Math.ceil(total.count / paginateOption.limit),
      page: paginateOption.page,
      lastItemId: data.lastKey?.id,
      limit: Number(paginateOption.limit),
    })
  }

  async delete(id: Id): Promise<boolean> {
    await this.incidentModel.delete(id)
    return Promise.resolve(true)
  }

  async getOccurringIncidentByCheckId(
    checkId: Id,
  ): Promise<Nullable<Incident>> {
    // TODO: come up the way to find the occurring incident by checkId
    // Base on status? or the latest created incident?

    // Get latest incident by checkId
    const incidents = await this.incidentModel
      .query('checkId')
      .eq(checkId)
      .using('checkId-index')
      .sort(SortOrder.descending)
      .exec()

    if (!incidents?.length) {
      return null
    }

    return this.toDomain(incidents[0])
  }
}
