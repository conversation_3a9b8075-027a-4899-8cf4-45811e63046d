import { Inject, Injectable } from '@nestjs/common'
import { InjectModel, Model } from 'nestjs-dynamoose'
import { SortOrder } from 'dynamoose/dist/General'

import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import IncidentEventRepository from '@backend/modules/incident/applications/incident-event.repository'
import {
  IncidentEvent,
  IncidentEventProps,
} from '@backend/modules/incident/entities/incident-event.entity'

interface IncidentEventModel extends IncidentEventProps {
  id: Id
  createdAt: Date
  updatedAt: Date
}

@Injectable()
export class IncidentEventInfrastructure implements IncidentEventRepository {
  constructor(
    @Inject(Database) private database: Database,
    @InjectModel('IncidentEventSchema')
    private incidentEventModel: Model<IncidentEventModel, Id>,
  ) {}

  toDomain(data: IncidentEventModel): IncidentEvent {
    const { id, createdAt, updatedAt, ...props } = data

    return new IncidentEvent({
      id,
      createdAt,
      updatedAt,
      props,
    })
  }

  async create(data: IncidentEvent): Promise<Nullable<IncidentEvent>> {
    const response = await this.incidentEventModel.create(data.getProps())
    return this.toDomain(response)
  }

  async delete(id: Id): Promise<boolean> {
    await this.incidentEventModel.delete(id)
    return Promise.resolve(true)
  }

  async findAll(
    incidentId: Id,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<IncidentEvent>> {
    const startAt = paginateOption.lastItemId
      ? await this.incidentEventModel.get(paginateOption.lastItemId || '')
      : null
    const total = await this.incidentEventModel
      .query('incidentId')
      .eq(incidentId)
      .using('incidentId-index')
      .count()
      .exec()

    if (!total || total.count === 0) {
      return {
        data: [],
        total: 0,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    }

    const query = await this.incidentEventModel
      .query({
        incidentId,
      })
      .using('incidentId-createdAt-index')
      .sort(SortOrder.descending)

    if (startAt) {
      query.startAt({
        id: startAt.id,
        incidentId: startAt.incidentId,
        createdAt: new Date(startAt.createdAt).toISOString(),
      })
    }
    if (paginateOption.limit) {
      query.limit(paginateOption.limit)
    }

    const data = await query.exec()

    return Promise.resolve({
      data: data.map((incidentEvent) => this.toDomain(incidentEvent)),
      total: total.count,
      totalPage: Math.ceil(total.count / paginateOption.limit),
      page: paginateOption.page,
      lastItemId: data.lastKey?.id,
      limit: Number(paginateOption.limit),
    })
  }

  async findById(id: Id): Promise<Nullable<IncidentEvent>> {
    const incidentEvent = await this.incidentEventModel.get(id)
    return this.toDomain(incidentEvent)
  }

  update(id: Id, data: IncidentEvent): Promise<Nullable<IncidentEvent>> {
    return Promise.resolve(null)
  }
}
