import { Module } from '@nestjs/common'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { DynamoModule } from '@backend/frameworks/dynamo/dynamo.module'

import { StatusPageController } from './controllers/status-page.controller'
import { StatusPageUseCase } from './usecases/status-page.usecase'
import { StatusReportUseCase } from './usecases/status-report.usecase'
import { STATUS_PAGE_REPOSITORY } from './applications/status-page.repository'
import { StatusPageInfrastructure } from './infras/status-page.infrastructure'
import { STATUS_REPORT_REPOSITORY } from './applications/status-report.repository'
import { StatusReportInfrastructure } from './infras/status-report.infrastructure'
import { StatusReportUpdateUseCase } from './usecases/status-report-update.usecase'
import { StatusReportController } from './controllers/status-report.controller'
import { StatusPageReportController } from './controllers/status-report-update.controller'
import { StatusPagePublicUseCase } from './usecases/status-page-public.usecase'
import { StatusPagePublicController } from './controllers/status-page-public.controller'

@Module({
  imports: [DatabaseModule, DynamoModule],
  controllers: [
    StatusPageController,
    StatusReportController,
    StatusPageReportController,
    StatusPagePublicController,
  ],
  providers: [
    {
      provide: STATUS_PAGE_REPOSITORY,
      useClass: StatusPageInfrastructure,
    },
    {
      provide: STATUS_REPORT_REPOSITORY,
      useClass: StatusReportInfrastructure,
    },
    StatusPageUseCase,
    StatusReportUseCase,
    StatusReportUpdateUseCase,
    StatusPagePublicUseCase,
  ],
  exports: [StatusPageUseCase, StatusReportUpdateUseCase, StatusReportUseCase],
})
export class StatusPageModule {}
