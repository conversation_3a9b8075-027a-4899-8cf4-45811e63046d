import { Injectable } from '@nestjs/common'
import {
  STATUS_REPORT_SCHEMA,
  StatusReportType,
} from '@libs/database/lib/dynamo/status-report.schema'
import { InjectModel, Model } from 'nestjs-dynamoose'
import { omit } from 'lodash'
import { SortOrder } from 'dynamoose/dist/General'

import { Id } from '@backend/cores/base/id.type'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import StatusReportRepository from '@backend/modules/status-page/applications/status-report.repository'
import {
  StatusReport,
  StatusReportProps,
} from '@backend/modules/status-page/entities/status-report.entity'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

interface StatusReportModel extends StatusReportProps {
  id: Id
  reportTypeCreatedAt: string
  createdAt: Date
  updatedAt: Date
}

interface StatusReportKey {
  id: Id
  createdAt?: string
}

@Injectable()
export class StatusReportInfrastructure implements StatusReportRepository {
  constructor(
    @InjectModel(STATUS_REPORT_SCHEMA)
    private statusReportModel: Model<StatusReportModel, StatusReportKey>,
  ) {}

  toDomain = (statusReportModel: StatusReportModel): StatusReport => {
    const { id, createdAt, updatedAt, ...props } = statusReportModel

    return new StatusReport({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  private toModel(data: StatusReport): StatusReportModel {
    return {
      ...data.getProps(),
      id: data.id,
      // This field only exsit in DynamoDB as key
      reportTypeCreatedAt:
        data.getProps().reportType +
        '#' +
        data.getProps().createdAt.toISOString(),
    }
  }

  async findAllReportByStatusPageIdInTimeRange(
    statusPageId: Id,
    startDate: string,
    endDate: string,
    paginateOption: PaginateOptionsDto,
  ) {
    return this.findAllByStatusPageIdInTimeRange(
      StatusReportType.REPORT,
      statusPageId,
      startDate,
      endDate,
      paginateOption,
    )
  }

  async findAllMaintenanceByStatusPageIdInTimeRange(
    statusPageId: Id,
    startDate: string,
    endDate: string,
    paginateOption: PaginateOptionsDto,
  ) {
    return this.findAllByStatusPageIdInTimeRange(
      StatusReportType.MAINTENANCE,
      statusPageId,
      startDate,
      endDate,
      paginateOption,
    )
  }

  private async findAllByStatusPageIdInTimeRange(
    reportType: StatusReportType,
    statusPageId: Id,
    startDate: string,
    endDate: string,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<StatusReport>> {
    const startAt = paginateOption.lastItemId
      ? await this.statusReportModel.get({
          id: paginateOption.lastItemId || '',
        })
      : null
    const total = await this.statusReportModel
      .query('statusPageId')
      .eq(statusPageId)
      .using('statusPageId-reportType-createdAt-index')
      .where('reportTypeCreatedAt')
      .between(`${reportType}#${startDate}`, `${reportType}#${endDate}`)
      .count()
      .exec()

    if (!total || total.count === 0) {
      return {
        data: [],
        total: 0,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    }

    const query = this.statusReportModel
      .query('statusPageId')
      .eq(statusPageId)
      .using('statusPageId-reportType-createdAt-index')
      .where('reportTypeCreatedAt')
      .between(`${reportType}#${startDate}`, `${reportType}#${endDate}`)
      .sort(SortOrder.descending)

    if (startAt) {
      query.startAt(startAt)
    }
    if (paginateOption.limit) {
      query.limit(paginateOption.limit)
    }

    const data = await query.exec()

    return Promise.resolve({
      data: data.map((statusReport) => this.toDomain(statusReport)),
      total: total.count,
      totalPage: Math.ceil(total.count / paginateOption.limit),
      page: paginateOption.page,
      lastItemId: data.lastKey?.id,
      limit: Number(paginateOption.limit),
    })
  }

  async findAllByStatusPageId(
    statusPageId: Id,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<StatusReport>> {
    const startAt = paginateOption.lastItemId
      ? await this.statusReportModel.get({
          id: paginateOption.lastItemId || '',
        })
      : null
    const total = await this.statusReportModel
      .query('statusPageId')
      .eq(statusPageId)
      .count()
      .exec()

    if (!total || total.count === 0) {
      return {
        data: [],
        total: 0,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    }

    const query = this.statusReportModel
      .query('statusPageId')
      .eq(statusPageId)
      .using('statusPageId-createdAt-index')
      .sort(SortOrder.descending)

    if (startAt) {
      query.startAt(startAt)
    }
    if (paginateOption.limit) {
      query.limit(paginateOption.limit)
    }

    const data = await query.exec()

    return Promise.resolve({
      data: data.map((statusReport) => this.toDomain(statusReport)),
      total: total.count,
      totalPage: Math.ceil(total.count / paginateOption.limit),
      page: paginateOption.page,
      lastItemId: data.lastKey?.id,
      limit: Number(paginateOption.limit),
    })
  }

  async create(data: StatusReport): Promise<StatusReport> {
    const resp = await this.statusReportModel.create(this.toModel(data))
    return this.toDomain(resp)
  }

  async findById(id: Id): Promise<Nullable<StatusReport>> {
    const statusReport = await this.statusReportModel.query('id').eq(id).exec()
    if (!statusReport) {
      return null
    }
    const data = statusReport.map((statusReport) => this.toDomain(statusReport))
    return data[0]
  }

  async update(
    id: Id,
    data: Partial<StatusReportProps>,
  ): Promise<Nullable<StatusReport>> {
    const item = await this.findById(id)
    if (!item) return null
    const updatedStatusReport = await this.statusReportModel.update(
      {
        id: id,
        createdAt: item.getProps().createdAt.toISOString(),
      },
      this.serializeUpdateData(data),
    )
    return this.toDomain(updatedStatusReport)
  }

  private serializeUpdateData(
    data: Partial<StatusReportProps>,
  ): Partial<StatusReport> {
    // Avoid to update partition key, sort key, and timestamps
    return omit(data, [
      'id',
      'statusPageId',
      'createdAt',
      'updatedAt',
      'startedAt',
    ])
  }

  async delete(id: Id): Promise<boolean> {
    const item = await this.findById(id)
    if (!item) return false
    this.statusReportModel.delete({
      id: id,
      createdAt: item.getProps().createdAt.toISOString(),
    })
    return Promise.resolve(true)
  }
}
