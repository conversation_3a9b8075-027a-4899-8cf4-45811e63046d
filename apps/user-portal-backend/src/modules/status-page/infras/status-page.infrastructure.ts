import { Injectable } from '@nestjs/common'
import { STATUS_PAGE_SCHEMA } from '@libs/database/lib/dynamo/status-page.schema'
import { InjectModel, Item, Model } from 'nestjs-dynamoose'
import { omit } from 'lodash'
import { SortOrder } from 'dynamoose/dist/General'

import { Id } from '@backend/cores/base/id.type'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import StatusPageRepository from '@backend/modules/status-page/applications/status-page.repository'
import {
  StatusPage,
  StatusPageProps,
} from '@backend/modules/status-page/entities/status-page.entity'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

interface StatusPageModel extends StatusPageProps {
  id: Id
  createdAt: Date
  updatedAt: Date
}

interface StatusPageKey {
  id: Id
  createdAt?: string
}

@Injectable()
export class StatusPageInfrastructure implements StatusPageRepository {
  constructor(
    @InjectModel(STATUS_PAGE_SCHEMA)
    private statusPageModel: Model<StatusPageModel, StatusPageKey>,
  ) {}

  toDomain = (statusPageModel: Item<StatusPageModel>): StatusPage => {
    const { id, createdAt, updatedAt, ...props } = statusPageModel

    return new StatusPage({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  private toModel(data: StatusPage): StatusPageModel {
    return {
      ...data.getProps(),
      id: data.id,
    }
  }

  async findAllByTeamId(
    teamId: Id,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<StatusPage>> {
    const startAt = paginateOption.lastItemId
      ? await this.statusPageModel.get({ id: paginateOption.lastItemId || '' })
      : null
    const total = await this.statusPageModel
      .query('teamId')
      .eq(teamId)
      .count()
      .exec()

    if (!total || total.count === 0) {
      return {
        data: [],
        total: 0,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    }

    const query = await this.statusPageModel
      .query('teamId')
      .eq(teamId)
      .using('teamId-createdAt-index')
      .sort(SortOrder.descending)

    if (startAt) {
      query.startAt(startAt)
    }
    if (paginateOption.limit) {
      query.limit(paginateOption.limit)
    }

    const data = await query.exec()

    return Promise.resolve({
      data: data.map((statusPage) => this.toDomain(statusPage)),
      total: total.count,
      totalPage: Math.ceil(total.count / paginateOption.limit),
      page: paginateOption.page,
      lastItemId: data.lastKey?.id,
      limit: Number(paginateOption.limit),
    })
  }

  async create(data: StatusPage): Promise<StatusPage> {
    const resp = await this.statusPageModel.create(this.toModel(data))
    return this.toDomain(resp)
  }

  async findBySubDomain(subDomain: string): Promise<Nullable<StatusPage>> {
    const statusPage = await this.statusPageModel
      .query('subDomain')
      .eq(subDomain)
      .exec()
    if (!statusPage) {
      return null
    }
    const data = statusPage.map((statusPage) => this.toDomain(statusPage))
    return data[0]
  }

  async findById(id: Id): Promise<Nullable<StatusPage>> {
    const statusPage = await this.statusPageModel.query('id').eq(id).exec()
    if (!statusPage) {
      return null
    }
    const data = statusPage.map((statusPage) => this.toDomain(statusPage))
    return data[0]
  }

  async update(
    id: Id,
    data: Partial<StatusPageProps>,
  ): Promise<Nullable<StatusPage>> {
    const item = await this.findById(id)
    if (!item) return null
    const updatedStatusPage = await this.statusPageModel.update(
      {
        id: id,
        createdAt: item.getProps().createdAt.toISOString(),
      },
      this.serializeUpdateData(data),
    )
    return this.toDomain(updatedStatusPage)
  }

  private serializeUpdateData(
    data: Partial<StatusPageProps>,
  ): Partial<StatusPage> {
    return omit(data, ['id', 'teamId', 'createdAt', 'updatedAt'])
  }

  async delete(id: Id): Promise<boolean> {
    const item = await this.findById(id)
    if (!item) return false
    await this.statusPageModel.delete({
      id: id,
      createdAt: item.getProps().createdAt.toISOString(),
    })
    return Promise.resolve(true)
  }
}
