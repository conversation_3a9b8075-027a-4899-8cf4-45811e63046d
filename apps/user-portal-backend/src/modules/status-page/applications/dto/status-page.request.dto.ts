import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'

import { StatusPageSchema } from '../../entities'

export class CreateStatusPageDto extends createZodDto(
  extendApi(
    StatusPageSchema.omit({
      createdBy: true,
      updatedBy: true,
    }),
  ),
) {}

export class UpdateStatusPageDto extends createZodDto(
  extendApi(
    StatusPageSchema.omit({
      createdBy: true,
      updatedBy: true,
    }).partial(),
  ),
) {}
