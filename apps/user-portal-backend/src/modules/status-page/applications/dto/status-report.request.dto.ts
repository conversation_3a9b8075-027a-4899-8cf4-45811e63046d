import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'

import { StatusReportSchema } from '../../entities'

export class CreateStatusReportDto extends createZodDto(
  extendApi(
    StatusReportSchema.omit({
      createdBy: true,
      updatedBy: true,
    }),
  ),
) {}

export class UpdateStatusReportDto extends createZodDto(
  extendApi(
    StatusReportSchema.omit({
      createdBy: true,
      updatedBy: true,
    }).partial(),
  ),
) {}
