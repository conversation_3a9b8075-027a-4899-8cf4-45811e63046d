import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'

import { StatusReportUpdateSchema } from '../../entities'

export class CreateStatusReportUpdateDto extends createZodDto(
  extendApi(
    StatusReportUpdateSchema.omit({
      id: true,
    }),
  ),
) {}

export class UpdateStatusReportUpdateDto extends createZodDto(
  extendApi(
    StatusReportUpdateSchema.omit({
      id: true,
    }).partial(),
  ),
) {}
