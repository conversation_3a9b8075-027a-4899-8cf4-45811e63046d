import { Id } from '@backend/cores/base/id.type'
import { PaginateOptions } from '@backend/commons/dto/paginateOptions.dto'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

import { StatusPage, StatusPageProps } from '../entities'

interface StatusPageRepository {
  findAllByTeamId(
    teamId: Id,
    paginateOption: PaginateOptions,
  ): Promise<IPaginate<StatusPage>>
  findBySubDomain(subDomain: string): Promise<Nullable<StatusPage>>
  findById(id: Id): Promise<Nullable<StatusPage>>
  create(data: StatusPage): Promise<StatusPage>
  update(id: Id, data: Partial<StatusPageProps>): Promise<Nullable<StatusPage>>
  delete(id: Id): Promise<boolean>
}

export default StatusPageRepository
export const STATUS_PAGE_REPOSITORY = 'STATUS_PAGE_REPOSITORY'
