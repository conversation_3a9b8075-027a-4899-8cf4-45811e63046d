import { Id } from '@backend/cores/base/id.type'
import { PaginateOptions } from '@backend/commons/dto/paginateOptions.dto'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

import { StatusReport, StatusReportProps } from '../entities'

interface StatusReportRepository {
  findAllByStatusPageId(
    statusPageId: Id,
    paginateOption: PaginateOptions,
  ): Promise<IPaginate<StatusReport>>
  findAllMaintenanceByStatusPageIdInTimeRange(
    statusPageId: Id,
    startDate: string,
    endDate: string,
    paginateOption: PaginateOptions,
  ): Promise<IPaginate<StatusReport>>
  findAllReportByStatusPageIdInTimeRange(
    statusPageId: Id,
    startDate: string,
    endDate: string,
    paginateOption: PaginateOptions,
  ): Promise<IPaginate<StatusReport>>
  findById(id: Id): Promise<Nullable<StatusReport>>
  create(data: StatusReport): Promise<StatusReport>
  update(
    id: Id,
    data: Partial<StatusReportProps>,
  ): Promise<Nullable<StatusReport>>
  delete(id: Id): Promise<boolean>
}

export default StatusReportRepository
export const STATUS_REPORT_REPOSITORY = 'STATUS_REPORT_REPOSITORY'
