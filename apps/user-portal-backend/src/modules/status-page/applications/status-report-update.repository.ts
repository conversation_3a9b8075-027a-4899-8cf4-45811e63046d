import { StatusReportUpdate } from '@libs/database/lib/dynamo/status-report.schema'

import { Id } from '@backend/cores/base/id.type'

import { StatusPage } from '../entities'

interface StatusPageRepository {
  create(
    statusReportId: Id,
    data: Omit<StatusReportUpdate, 'id'>,
  ): Promise<StatusPage>
  update(
    statusReportId: Id,
    id: Id,
    data: Partial<Omit<StatusReportUpdate, 'id'>>,
  ): Promise<Nullable<StatusPage>>
  delete(statusReportId: Id, id: Id): Promise<boolean>
}

export default StatusPageRepository
export const STATUS_PAGE_REPOSITORY = 'STATUS_PAGE_REPOSITORY'
