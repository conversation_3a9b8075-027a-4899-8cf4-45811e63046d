import { Controller, Get, Param, Query } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { StatusPagePublicUseCase } from '../usecases/status-page-public.usecase'

@ApiTags('Status Page Public')
@Controller('status-page-public')
export class StatusPagePublicController {
  constructor(
    private readonly statusPagePublicUseCase: StatusPagePublicUseCase,
  ) {}

  @Get(':id')
  getStatusPagePublic(
    @Param('id') statusPageId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.statusPagePublicUseCase.getReportByStatusPageId(
      statusPageId,
      startDate,
      endDate,
    )
  }

  @Get(':id/report/:reportId')
  getStatusPagePublicReport(
    @Param('id') statusPageId: string,
    @Param('reportId') reportId: string,
  ) {
    return this.statusPagePublicUseCase.getReportById(statusPageId, reportId)
  }

  @Get(':id/reports')
  getStatusPagePublicReports(
    @Param('id') statusPageId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.statusPagePublicUseCase.getReportsByStatusPageId(
      statusPageId,
      startDate,
      endDate,
    )
  }
}
