import {
  <PERSON>,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'

import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'

import { StatusReportUpdateUseCase } from '../usecases/status-report-update.usecase'
import {
  CreateStatusReportUpdateDto,
  UpdateStatusReportUpdateDto,
} from '../applications/dto/status-report-update.request.dto'

@ApiTags('Status Report Update')
@Controller('report/:id2/update')
export class StatusPageReportController {
  constructor(
    private readonly statusReportUpdateUseCase: StatusReportUpdateUseCase,
  ) {}

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  createStatusReportUpdates(
    // @Param('id') statusPageId: string,
    @Param('id2') statusReportId: string,
    @Body() createStatusReportUpdateDto: CreateStatusReportUpdateDto,
  ) {
    return this.statusReportUpdateUseCase.create(
      statusReportId,
      createStatusReportUpdateDto,
    )
  }

  @Patch(':id3')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  updateStatusReportUpdates(
    // @Param('id') statusPageId: string,
    @Param('id2') statusReportId: string,
    @Param('id3') id: string,
    @Body() updateStatusReportUpdateDto: UpdateStatusReportUpdateDto,
  ) {
    return this.statusReportUpdateUseCase.update(
      statusReportId,
      id,
      updateStatusReportUpdateDto,
    )
  }

  @Delete(':id3')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  deleteStatusReportUpdates(
    // @Param('id') statusPageId: string,
    @Param('id2') statusReportId: string,
    @Param('id3') id: string,
  ) {
    return this.statusReportUpdateUseCase.remove(statusReportId, id)
  }
}
