import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common'
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { StatusPageUseCase } from '@backend/modules/status-page/usecases/status-page.usecase'
import {
  CreateStatusPageDto,
  UpdateStatusPageDto,
} from '@backend/modules/status-page/applications/dto/status-page.request.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

@ApiTags('Status Page')
@Controller('status-page')
export class StatusPageController {
  constructor(private readonly statusPageUseCase: StatusPageUseCase) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  findStatusPages(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.statusPageUseCase.findAll(orgTeamParams.teamId, paginateOption)
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  getStatusPages(@Param('id') statusPageId: string) {
    return this.statusPageUseCase.findOne(statusPageId)
  }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  createStatusPages(
    @Body() createStatusPageDto: CreateStatusPageDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.statusPageUseCase.create(
      createStatusPageDto,
      user,
      orgTeamParams.teamId,
    )
  }

  @Patch(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  updateStatusPages(
    @Param('id') statusPageId: string,
    @Body() updateStatusPageDto: UpdateStatusPageDto,
  ) {
    return this.statusPageUseCase.update(statusPageId, updateStatusPageDto)
  }

  @Delete(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  deleteStatusPages(@Param('id') statusPageId: string) {
    return this.statusPageUseCase.remove(statusPageId)
  }
}
