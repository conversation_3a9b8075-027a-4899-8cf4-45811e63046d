import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

import { StatusReportUseCase } from '../usecases/status-report.usecase'
import {
  CreateStatusReportDto,
  UpdateStatusReportDto,
} from '../applications/dto/status-report.request.dto'

@ApiTags('Status Report')
@Controller('status-page/:id/report')
export class StatusReportController {
  constructor(private readonly statusReportUseCase: StatusReportUseCase) {}

  @Get('maintenance')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  findAllMaintenanceByStatusPageIdInTimeRange(
    @Param('id') statusPageId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query() paginateOption: PaginateOptionsDto,
  ) {
    return this.statusReportUseCase.findAllMaintenanceByStatusPageIdInTimeRange(
      statusPageId,
      startDate,
      endDate,
      paginateOption,
    )
  }

  @Get('report')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  findAllReportByStatusPageIdInTimeRange(
    @Param('id') statusPageId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query() paginateOption: PaginateOptionsDto,
  ) {
    return this.statusReportUseCase.findAllReportByStatusPageIdInTimeRange(
      statusPageId,
      startDate,
      endDate,
      paginateOption,
    )
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  findStatusReports(
    @Param('id') statusPageId: string,
    @Query() paginateOption: PaginateOptionsDto,
  ) {
    return this.statusReportUseCase.findAll(statusPageId, paginateOption)
  }

  @Get(':id2')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  getStatusReports(
    @Param('id') statusPageId: string,
    @Param('id2') statusReportId: string,
  ) {
    return this.statusReportUseCase.findOne(statusReportId)
  }

  @Post('')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  createStatusReports(
    @Param('id') statusPageId: string,
    @Body() createStatusReportDto: CreateStatusReportDto,
    @CurrentUser() user: JwtPayload,
  ) {
    return this.statusReportUseCase.create(
      createStatusReportDto,
      user,
      statusPageId,
    )
  }

  @Patch(':id2')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  updateStatusReports(
    @Param('id') statusPageId: string,
    @Param('id2') statusReportId: string,
    @Body() updateStatusReportDto: UpdateStatusReportDto,
  ) {
    return this.statusReportUseCase.update(
      statusReportId,
      updateStatusReportDto,
    )
  }

  @Delete(':id2')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.STATUS_PAGE,
      type: PermissionType.TEAM,
    },
  ])
  deleteStatusReports(
    @Param('id') statusPageId: string,
    @Param('id2') statusReportId: string,
  ) {
    return this.statusReportUseCase.remove(statusReportId)
  }
}
