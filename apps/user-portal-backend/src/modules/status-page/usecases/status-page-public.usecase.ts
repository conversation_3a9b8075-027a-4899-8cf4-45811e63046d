import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import StatusReportRepository, {
  STATUS_REPORT_REPOSITORY,
} from '@backend/modules/status-page/applications/status-report.repository'

import StatusPageRepository, {
  STATUS_PAGE_REPOSITORY,
} from '../applications/status-page.repository'

@Injectable()
export class StatusPagePublicUseCase {
  constructor(
    @Inject(STATUS_REPORT_REPOSITORY)
    private readonly statusReportRepository: StatusReportRepository,
    @Inject(STATUS_PAGE_REPOSITORY)
    private readonly statusPageRepository: StatusPageRepository,
  ) {}

  async getReportByStatusPageId(
    subDomain: string,
    startDate: string,
    endDate: string,
  ) {
    const statusPage =
      await this.statusPageRepository.findBySubDomain(subDomain)

    if (!statusPage) return null
    const statusPageResponse = statusPage.toResponse()

    const statusReports =
      await this.statusReportRepository.findAllReportByStatusPageIdInTimeRange(
        statusPage.id,
        startDate,
        endDate,
        {},
      )

    const maintenances =
      await this.statusReportRepository.findAllMaintenanceByStatusPageIdInTimeRange(
        statusPage.id,
        startDate,
        endDate,
        {},
      )

    return {
      ...statusPageResponse,
      statusReports: statusReports.data.map((statusReport) =>
        statusReport.toResponse(),
      ),
      maintenances: maintenances.data.map((statusReport) =>
        statusReport.toResponse(),
      ),
    }
  }

  async getReportById(subDomain: string, reportId: Id) {
    const statusPage =
      await this.statusPageRepository.findBySubDomain(subDomain)
    if (!statusPage) return null

    const statusReport = await this.statusReportRepository.findById(reportId)
    if (!statusReport) return null

    return statusReport.toResponse()
  }

  async getReportsByStatusPageId(
    subDomain: Id,
    startDate: string,
    endDate: string,
  ) {
    const statusPage =
      await this.statusPageRepository.findBySubDomain(subDomain)
    if (!statusPage) return null

    const statusReports =
      await this.statusReportRepository.findAllReportByStatusPageIdInTimeRange(
        statusPage.id,
        startDate,
        endDate,
        {},
      )

    return statusReports.data.map((statusReport) => statusReport.toResponse())
  }
}
