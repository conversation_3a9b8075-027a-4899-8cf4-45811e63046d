import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import StatusPageRepository, {
  STATUS_PAGE_REPOSITORY,
} from '@backend/modules/status-page/applications/status-page.repository'
import {
  CreateStatusPageDto,
  UpdateStatusPageDto,
} from '@backend/modules/status-page/applications/dto/status-page.request.dto'
import { StatusPageResponseDto } from '@backend/modules/status-page/applications/dto/status-page.response.dto'
import { StatusPage } from '@backend/modules/status-page/entities/status-page.entity'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

@Injectable()
export class StatusPageUseCase {
  constructor(
    @Inject(STATUS_PAGE_REPOSITORY)
    private readonly repo: StatusPageRepository,
  ) {}

  async create(
    createStatusPageDto: CreateStatusPageDto,
    user: JwtPayload,
    teamId: Id,
  ): Promise<StatusPageResponseDto | null> {
    const statusPageData = StatusPage.create({
      ...createStatusPageDto,
      createdBy: user?.userId,
      updatedBy: user?.userId,
      teamId: teamId,
    })

    const statusPage = await this.repo.create(statusPageData)
    return await this.findOne(statusPage.id)
  }

  async findAll(teamId: Id, paginateOption: PaginateOptionsDto) {
    try {
      const statusPages = await this.repo.findAllByTeamId(
        teamId,
        paginateOption,
      )
      return {
        data: statusPages.data.map((statusPage) => statusPage.toResponse()),
        total: statusPages.total,
        totalPage: statusPages.totalPage,
        limit: statusPages.limit,
        page: statusPages.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id) {
    // const statusPage = await this.repo.findById(id)
    const statusPage = await this.repo.findById(id)
    if (!statusPage) return null

    return statusPage?.toResponse()
  }

  async update(id: Id, updateStatusPageDto: UpdateStatusPageDto) {
    const currentStatusPage = await this.repo.findById(id)

    await this.repo.update(id, {
      ...currentStatusPage?.getProps(),
      ...updateStatusPageDto,
    })

    return await this.findOne(id)
  }

  remove(id: Id) {
    return this.repo.delete(id)
  }
}
