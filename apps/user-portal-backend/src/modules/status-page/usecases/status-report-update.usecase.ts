import { Inject, Injectable } from '@nestjs/common'
import { StatusReportUpdate } from '@libs/database/lib/dynamo/status-report.schema'

import { Id } from '@backend/cores/base/id.type'

import { StatusReportResponseDto } from '../applications/dto/status-report.response.dto'
import {
  CreateStatusReportUpdateDto,
  UpdateStatusReportUpdateDto,
} from '../applications/dto/status-report-update.request.dto'
import { StatusReport } from '../entities'
import StatusReportRepository, {
  STATUS_REPORT_REPOSITORY,
} from '../applications/status-report.repository'

@Injectable()
export class StatusReportUpdateUseCase {
  constructor(
    @Inject(STATUS_REPORT_REPOSITORY)
    private readonly repo: StatusReportRepository,
  ) {}

  async create(
    statusReportId: Id,
    createStatusReportUpdateDto: CreateStatusReportUpdateDto,
  ): Promise<StatusReportResponseDto | null> {
    const statusReport = await this.repo.findById(statusReportId)
    if (!statusReport) {
      return null
    }
    const updatedReportUpdates = statusReport.getProps().reportUpdates
    if (!updatedReportUpdates) return null
    updatedReportUpdates.push(createStatusReportUpdateDto)
    const updatedStatusReport = StatusReport.update({
      id: statusReportId,
      props: {
        ...statusReport.getProps(),
        reportUpdates: updatedReportUpdates,
      },
    })

    const updatedReport = await this.repo.update(
      statusReportId,
      updatedStatusReport.toResponse(),
    )
    if (!updatedReport) return null
    return updatedReport.toResponse()
  }

  async update(
    statusReportId: Id,
    id: Id,
    updateStatusReportUpdateDto: UpdateStatusReportUpdateDto,
  ) {
    const updatedReportBlock = {
      id: id,
      ...updateStatusReportUpdateDto,
    }
    const statusReport = await this.repo.findById(statusReportId)
    if (!statusReport) {
      return null
    }
    const updatedReportUpdates = statusReport.getProps().reportUpdates
    if (!updatedReportUpdates) return null

    const updatedUpdates = updatedReportUpdates.map((update) =>
      update.id === id ? updatedReportBlock : update,
    ) as StatusReportUpdate[]

    const updatedStatusReport = StatusReport.update({
      id: statusReportId,
      props: {
        ...statusReport.getProps(),
        reportUpdates: updatedUpdates,
      },
    })

    const updatedReport = await this.repo.update(
      statusReportId,
      updatedStatusReport.toResponse(),
    )
    if (!updatedReport) return null
    return updatedReport.toResponse()
  }

  async remove(statusReportId: Id, id: Id) {
    const statusReport = await this.repo.findById(statusReportId)
    if (!statusReport) {
      return null
    }
    const updatedReportUpdates = statusReport.getProps().reportUpdates
    if (!updatedReportUpdates) return null

    const filteredUpdates = updatedReportUpdates.filter(
      (update) => update.id !== id,
    )

    const updatedStatusReport = StatusReport.update({
      id: statusReportId,
      props: {
        ...statusReport.getProps(),
        reportUpdates: filteredUpdates,
      },
    })

    const updatedReport = await this.repo.update(
      statusReportId,
      updatedStatusReport.toResponse(),
    )
    if (!updatedReport) return null
    return updatedReport.toResponse()
  }
}
