import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import StatusReportRepository, {
  STATUS_REPORT_REPOSITORY,
} from '@backend/modules/status-page/applications/status-report.repository'
import {
  CreateStatusReportDto,
  UpdateStatusReportDto,
} from '@backend/modules/status-page/applications/dto/status-report.request.dto'
import { StatusReportResponseDto } from '@backend/modules/status-page/applications/dto/status-report.response.dto'
import { StatusReport } from '@backend/modules/status-page/entities/status-report.entity'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

@Injectable()
export class StatusReportUseCase {
  constructor(
    @Inject(STATUS_REPORT_REPOSITORY)
    private readonly repo: StatusReportRepository,
  ) {}

  async create(
    createStatusReportDto: CreateStatusReportDto,
    user: JwtPayload,
    statusPageId: Id,
  ): Promise<StatusReportResponseDto | null> {
    const statusReportData = StatusReport.create({
      ...createStatusReportDto,
      createdBy: user?.userId,
      updatedBy: user?.userId,
      statusPageId: statusPageId,
    })

    const statusReport = await this.repo.create(statusReportData)
    return await this.findOne(statusReport.id)
  }

  async findAll(statusPageId: Id, paginateOption: PaginateOptionsDto) {
    try {
      const statusReports = await this.repo.findAllByStatusPageId(
        statusPageId,
        paginateOption,
      )
      return {
        data: statusReports.data.map((statusReport) =>
          statusReport.toResponse(),
        ),
        total: statusReports.total,
        totalPage: statusReports.totalPage,
        limit: statusReports.limit,
        page: statusReports.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findAllMaintenanceByStatusPageIdInTimeRange(
    statusPageId: Id,
    startDate: string,
    endDate: string,
    paginateOption: PaginateOptionsDto,
  ) {
    try {
      const statusMaintenances =
        await this.repo.findAllMaintenanceByStatusPageIdInTimeRange(
          statusPageId,
          startDate,
          endDate,
          paginateOption,
        )
      return {
        data: statusMaintenances.data.map((statusReport) =>
          statusReport.toResponse(),
        ),
        total: statusMaintenances.total,
        totalPage: statusMaintenances.totalPage,
        limit: statusMaintenances.limit,
        page: statusMaintenances.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findAllReportByStatusPageIdInTimeRange(
    statusPageId: Id,
    startDate: string,
    endDate: string,
    paginateOption: PaginateOptionsDto,
  ) {
    try {
      const statusReports =
        await this.repo.findAllReportByStatusPageIdInTimeRange(
          statusPageId,
          startDate,
          endDate,
          paginateOption,
        )
      return {
        data: statusReports.data.map((statusReport) =>
          statusReport.toResponse(),
        ),
        total: statusReports.total,
        totalPage: statusReports.totalPage,
        limit: statusReports.limit,
        page: statusReports.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id) {
    // const statusReport = await this.repo.findById(id)
    const statusReport = await this.repo.findById(id)
    if (!statusReport) return null

    return statusReport?.toResponse()
  }

  async update(id: Id, updateStatusReportDto: UpdateStatusReportDto) {
    const currentStatusReport = await this.repo.findById(id)

    await this.repo.update(id, {
      ...currentStatusReport?.getProps(),
      ...updateStatusReportDto,
    })

    return await this.findOne(id)
  }

  remove(id: Id) {
    return this.repo.delete(id)
  }
}
