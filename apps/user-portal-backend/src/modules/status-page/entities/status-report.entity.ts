import { z } from 'zod'
import {
  AffectedResourceStatus,
  ResourceType,
  StatusMaintenanceUpdateStatus,
  StatusReportType,
  StatusReportUpdateStatus,
} from '@libs/database/lib/dynamo/status-report.schema'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

export const StatusReportUpdateAffectedResourceSchema = z.object({
  id: z.string().optional(),
  resourceId: z.string(),
  resourceType: z.nativeEnum(ResourceType),
  resourceName: z.string(),
  status: z.nativeEnum(AffectedResourceStatus),
})

export const StatusReportUpdateSchema = z.object({
  id: z.string().optional(),
  message: z.string(),
  status: z.union([
    z.nativeEnum(StatusReportUpdateStatus),
    z.nativeEnum(StatusMaintenanceUpdateStatus),
  ]),
  affectedResources: z.array(StatusReportUpdateAffectedResourceSchema),
  publishedAt: z.preprocess(
    (val) => (typeof val === 'string' ? new Date(val) : val),
    z.date().optional(),
  ),
})

export const StatusReportSchema = z.object({
  statusPageId: z.string().optional(),
  summary: z.string().optional(),
  reportType: z.nativeEnum(StatusReportType).optional(),

  status: z.string().optional(),
  affectedResources: z
    .array(StatusReportUpdateAffectedResourceSchema)
    .optional(),
  from: z
    .preprocess(
      (val) => (typeof val === 'string' ? new Date(val) : val),
      z.date().optional(),
    )
    .optional(),
  to: z
    .preprocess(
      (val) => (typeof val === 'string' ? new Date(val) : val),
      z.date().optional(),
    )
    .optional(),

  reportUpdates: z.array(StatusReportUpdateSchema).optional(),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export type StatusReportProps = z.infer<typeof StatusReportSchema>

export const StatusReportResponse = StatusReportSchema.partial()

export interface StatusReportResponse
  extends z.infer<typeof StatusReportResponse> {
  id: Id
  createdAt: Date
  updatedAt: Date
}

export class StatusReport extends AggregateRoot<
  StatusReportProps,
  StatusReportResponse
> {
  static create(props: StatusReportProps) {
    const statusReportProps = {
      id: generateId(),
      ...props,
      reportUpdates:
        props.reportUpdates?.map((update) => ({
          id: update.id || generateId(),
          ...update,
          affectedResources:
            update.affectedResources?.map((res) => ({
              id: res.id || generateId(),
              ...res,
            })) || [],
        })) || [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const latestUpdate =
      statusReportProps!.reportUpdates![props.reportUpdates!.length - 1]

    const statusReportPropsMapLatest = {
      ...statusReportProps,
      status: latestUpdate.status,
      affectedResources: latestUpdate.affectedResources,
    }

    const statusReport = new StatusReport({
      id: statusReportProps.id,
      props: statusReportPropsMapLatest,
    })

    return statusReport
  }

  static update({ id, props }: { id: string; props: StatusReportProps }) {
    const statusReport = {
      id: id,
      ...props,
      reportUpdates:
        props.reportUpdates?.map((update) => ({
          id: update.id || generateId(),
          ...update,
          affectedResources:
            update.affectedResources?.map((res) => ({
              id: res.id || generateId(),
              ...res,
            })) || [],
        })) || [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const latestUpdate =
      statusReport!.reportUpdates![props.reportUpdates!.length - 1]

    const statusReportPropsMapLatest = {
      ...statusReport,
      status: latestUpdate.status,
      affectedResources: latestUpdate.affectedResources,
    }

    const updatedStatusReport = new StatusReport({
      id: id,
      props: statusReportPropsMapLatest,
    })

    return updatedStatusReport
  }

  public toResponse(): StatusReportResponse {
    const props = this.getProps()

    return {
      id: props.id,
      statusPageId: props.statusPageId,
      summary: props.summary,
      reportType: props.reportType,

      from: props.from,
      to: props.to,
      reportUpdates: props.reportUpdates,

      status: props.status,
      affectedResources: props.affectedResources,

      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    }
  }
}
