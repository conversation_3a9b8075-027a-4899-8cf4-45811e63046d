import { z } from 'zod'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

export const StatusPageSchema = z.object({
  // Setting
  teamId: z.string().optional(),
  companyName: z.string().optional(),
  subDomain: z.string().optional(),
  getInTouchUrl: z.string().optional(),
  customdomain: z.string().optional(),

  // Advance Setting
  announcement: z.string().optional(),
  minIncidentLength: z.number().optional(),
  timezone: z.string().optional(),
  statusPageDay: z.number().optional(),
  autoUpdate: z.boolean().default(false).optional(),
  publishStatusPage: z.boolean().default(false).optional(),
  statusSections: z.array(
    z.object({
      sectionName: z.string(),
      resources: z.array(
        z.object({
          id: z.string().optional(),
          resourceType: z.string(),
          resourceId: z.string(),
          resourceName: z.string(),
          explanation: z.string(),
          widgetType: z.string().optional(),
        }),
      ),
    }),
  ),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export type StatusPageProps = z.infer<typeof StatusPageSchema>

export const StatusPageResponse = StatusPageSchema.partial()

export interface StatusPageResponse extends z.infer<typeof StatusPageResponse> {
  id: Id
  createdAt: Date
  updatedAt: Date
}

export class StatusPage extends AggregateRoot<
  StatusPageProps,
  StatusPageResponse
> {
  static create(props: StatusPageProps) {
    const statusPageProps = {
      ...props,
      statusSections: props.statusSections?.map((section) => ({
        ...section,
        resources: section.resources.map((resource) => ({
          ...resource,
          id: resource.id || generateId(),
        })),
      })),
    }

    const statusPage = new StatusPage({
      id: generateId(),
      props: statusPageProps,
    })

    return statusPage
  }

  static update({ id, props }: { id: Id; props: StatusPageProps }) {
    const statusPageProps = {
      ...props,
      statusSections: props.statusSections?.map((section) => ({
        ...section,
        resources: section.resources.map((resource) => ({
          ...resource,
          id: resource.id || generateId(),
        })),
      })),
    }

    const statusPage = new StatusPage({
      id: id,
      props: statusPageProps,
    })

    return statusPage
  }

  public toResponse(): StatusPageResponse {
    const props = this.getProps()

    return {
      teamId: props.teamId,
      id: props.id,
      companyName: props.companyName,
      subDomain: props.subDomain,
      getInTouchUrl: props.getInTouchUrl,
      customdomain: props.customdomain,
      // Advance Setting
      announcement: props.announcement,
      minIncidentLength: props.minIncidentLength,
      timezone: props.timezone,
      statusPageDay: props.statusPageDay,
      autoUpdate: props.autoUpdate,
      publishStatusPage: props.publishStatusPage,
      statusSections: props.statusSections,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    }
  }
}
