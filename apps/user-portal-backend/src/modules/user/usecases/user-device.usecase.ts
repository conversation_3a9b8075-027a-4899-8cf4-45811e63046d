import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { AuthService } from '@backend/cores/auth/auth.service'

import UserDeviceRepository, {
  USER_DEVICE_REPOSITORY,
} from '../applications/user-device.repository'
import { UserDevice } from '../entities/user-device.entity'
import { CreateUserDeviceDto } from '../applications/dto/user-device.request.dto'
import {
  RegisterDeviceDto,
  UserDeviceResponseDto,
} from '../applications/dto/user-device.response.dto'

@Injectable()
export class UserDeviceUseCase {
  constructor(
    @Inject(USER_DEVICE_REPOSITORY)
    private readonly repo: UserDeviceRepository,
    private readonly authService: AuthService,
  ) {}

  async create(
    createUserDeviceDto: CreateUserDeviceDto,
    userid: string,
  ): Promise<UserDeviceResponseDto | null> {
    try {
      const userDevice = await this.repo.findOne({
        filter: { deviceId: createUserDeviceDto.deviceId, userId: userid },
      })
      if (userDevice) {
        return null
      }

      const newUserDevice = await this.repo.create(
        UserDevice.create({
          ...createUserDeviceDto,
          userId: userid,
          createdBy: userid,
          updatedBy: userid,
        }),
      )

      return newUserDevice.toResponse()
    } catch (error) {
      console.error(error)
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  async find(
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<UserDeviceResponseDto>> {
    try {
      const userDevices = await this.repo.find({
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })
      return {
        data: userDevices.data.map((plan) => plan.toResponse()),
        total: userDevices.total,
        totalPage: userDevices.totalPage,
        limit: userDevices.limit,
        page: userDevices.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<UserDeviceResponseDto>> {
    const entity = await this.repo.findById(id)

    if (!entity) return null
    return entity.toResponse()
  }

  async remove(id: Id) {
    const result = await this.repo.delete(id)
    return result
  }

  async findUserDeviceByUserId(userId: string) {
    const userDevices = await this.repo.findAll({
      filter: {
        userId,
      },
      relations: {
        user: true,
      },
    })
    return userDevices?.map((subscription) => subscription.toResponse())
  }

  async registerDevice(
    idToken: string,
    createUserDeviceDto: CreateUserDeviceDto,
  ): Promise<RegisterDeviceDto | null> {
    const data = await this.authService.firebaseLogin(idToken, {
      mobile: true,
    })
    if (data == null) {
      throw new NotFoundException('QR Code is invalid')
    }

    const userid = data?.user?.id

    await this.create(createUserDeviceDto, userid)

    return {
      accessToken: data?.accessToken,
      refreshToken: data?.refreshToken,
      userId: userid,
    }
  }
}
