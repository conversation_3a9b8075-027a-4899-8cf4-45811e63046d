import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'
import { customAlphabet } from 'nanoid'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { generatePassword } from '@backend/commons/password'
import { NotificationFactory } from '@backend/frameworks/notification/notification.factory'

import { UserPlivoCredential } from '../entities/user-plivo-credential.entity'
import UserPlivoCredentialRepository, {
  USER_PLIVO_CREDENTIAL_REPOSITORY,
} from '../applications/user-plivo-credential.repository'
import { UserPlivoCredentialResponseDto } from '../applications/dto/user-plivo-credential.response.dto'

@Injectable()
export class UserPlivoCredentialUseCase {
  constructor(
    @Inject(USER_PLIVO_CREDENTIAL_REPOSITORY)
    private readonly repo: UserPlivoCredentialRepository,
    private notificationFactory: NotificationFactory,
  ) {}

  async create(userid: string): Promise<UserPlivoCredentialResponseDto | null> {
    try {
      const userPlivoCredential = await this.repo.findByUserId(userid)
      if (userPlivoCredential) {
        return null
      }

      const generateUsername = this.generatePlivoUsername()
      const genereatePassword = generatePassword()

      const plivoUsername = await this.registerSIPEndpoint(
        generateUsername,
        genereatePassword,
        generateUsername,
      )

      const newUserPlivoCredential = await this.repo.create(
        UserPlivoCredential.create({
          username: plivoUsername,
          alias: generateUsername,
          password: genereatePassword,
          userId: userid,
          createdBy: userid,
          updatedBy: userid,
        }),
      )

      return newUserPlivoCredential.toResponse()
    } catch (error) {
      console.error(error)
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  async find(
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<UserPlivoCredentialResponseDto>> {
    try {
      const userPlivoCredentials = await this.repo.find({
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })
      return {
        data: userPlivoCredentials.data.map((plan) => plan.toResponse()),
        total: userPlivoCredentials.total,
        totalPage: userPlivoCredentials.totalPage,
        limit: userPlivoCredentials.limit,
        page: userPlivoCredentials.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<UserPlivoCredentialResponseDto>> {
    const entity = await this.repo.findById(id)

    if (!entity) return null
    return entity.toResponse()
  }

  async findOneByUserId(
    userId: string,
  ): Promise<Nullable<UserPlivoCredentialResponseDto>> {
    const entity = await this.repo.findByUserId(userId)

    if (!entity) return null
    return entity.toResponse()
  }

  async remove(id: Id) {
    const result = await this.repo.delete(id)
    return result
  }

  generatePlivoUsername(size?: number) {
    return customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 15)(size)
  }

  async registerSIPEndpoint(
    username: string,
    password: string,
    alias: string,
  ): Promise<string> {
    try {
      const sipEndpoint = await this.notificationFactory
        .getNotificationService('plivo')
        .createSipEndpoint(username, password, alias)

      return sipEndpoint?.username
    } catch (error) {
      console.error(
        'Error details:',
        JSON.stringify(error, Object.getOwnPropertyNames(error), 2),
      )
      throw new HttpException(
        error.response ? error.response.data : error,
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }
}
