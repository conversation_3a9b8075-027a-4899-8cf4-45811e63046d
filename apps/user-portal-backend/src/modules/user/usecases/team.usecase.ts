import { Inject, Injectable, forwardRef } from '@nestjs/common'
import { omit } from 'lodash'
import { DataSource, QueryRunner } from 'typeorm'

import { Organization, Team } from '@backend/modules/user/entities'
import TeamRepository, {
  TEAM_REPOSITORY,
} from '@backend/modules/user/applications/team.repository'
import { Id } from '@backend/cores/base/id.type'
import { TeamResponseDto } from '@backend/modules/user/applications/dto/team.response.dto'
import {
  CreateTeamDto,
  UpdateTeamDto,
  UpdateTeamMember,
} from '@backend/modules/user/applications/dto/team.request.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import UserRepository, {
  USER_REPOSITORY,
} from '@backend/modules/user/applications/users.repository'
import RoleRepository, {
  ROLE_REPOSITORY,
} from '@backend/modules/user/applications/role.repository'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { SeverityUseCase } from '@backend/modules/escalation/usecases/severity.usecase'
import { ResourceBillingService } from '@backend/modules/subscription/services/resource-billing.service'

@Injectable()
export class TeamUseCase {
  constructor(
    @Inject(TEAM_REPOSITORY)
    private readonly teamRepository: TeamRepository,
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: RoleRepository,
    @Inject(forwardRef(() => SeverityUseCase))
    private readonly severityUsecase: SeverityUseCase,
    @Inject(forwardRef(() => ResourceBillingService))
    private readonly resourceBillingService: ResourceBillingService,
    private readonly dataSource: DataSource,
  ) {}

  async create(
    createTeamDto: CreateTeamDto,
    user: JwtPayload,
    orgTeamHeadersDto: OrgTeamHeadersDto,
  ): Promise<TeamResponseDto> {
    const qr: QueryRunner = this.dataSource.createQueryRunner()
    await qr.connect()
    await qr.startTransaction()

    try {
      const team = await this.teamRepository.create(
        Team.create({
          ...createTeamDto,
          createdBy: user.userId,
          updatedBy: user.userId,
          organization: new Organization({
            id: orgTeamHeadersDto.organizationId,
          }),
        }),
      )

      // Add current user as team admin
      const currentUser = await this.userRepository.findById(user.userId)
      const adminRole = await this.roleRepository.findOne({
        filter: {
          organizationId: orgTeamHeadersDto.organizationId,
          name: 'Admin',
        },
      })

      if (currentUser && adminRole) {
        await this.userRepository.assignRole(currentUser, {
          role: adminRole,
          team: team,
        })
      }
      console.log('creating severity')
      await this.severityUsecase.createDefaultSeverity(user.userId, team.id)

      await qr.commitTransaction()

      // Track team creation for billing purposes
      await this.resourceBillingService.trackTeamChange(
        orgTeamHeadersDto.organizationId,
        'CREATE',
        team.id,
        { name: team.getProps().name, ownerId: team.getProps().createdBy },
      )

      return team.toResponse()
    } catch (error) {
      await qr.rollbackTransaction()
      throw error
    } finally {
      await qr.release()
    }
  }

  async find(
    orgTeamParams: OrgTeamHeadersDto,
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<TeamResponseDto>> {
    const teams = await this.teamRepository.find({
      filter: {
        organization: {
          id: orgTeamParams.organizationId,
        },
      },
      relations: {
        userTeamRoles: true,
      },
      page: paginateOption.page,
      limit: paginateOption.limit,
      orderBy: {
        createdAt: 'ASC',
      },
    })

    return {
      data: teams.data.map((team) => team.toResponse()),
      total: teams.total,
      totalPage: teams.totalPage,
      limit: teams.limit,
      page: teams.page,
    }
  }

  async findMembers(teamId: Id, paginateOption: PaginateOptionsDto) {
    const teams = await this.teamRepository.findMembers(teamId, {
      page: paginateOption.page,
      limit: paginateOption.limit,
    })

    return {
      data: teams.data.map(({ user, role }) => ({
        role: omit(role?.toResponse(), 'permissions'),
        user: user?.toResponse(),
      })),
      total: teams.total,
      totalPage: teams.totalPage,
      limit: teams.limit,
      page: teams.page,
    }
  }

  async findAllMembers(teamId: Id) {
    const teams = await this.teamRepository.findAllMembers(teamId)

    return teams.map(({ user }) => user?.toResponse())
  }

  async findInvites(teamId: Id, paginateOption: PaginateOptionsDto) {
    const invites = await this.teamRepository.findInvites(teamId, {
      page: paginateOption.page,
      limit: paginateOption.limit,
    })

    return {
      data: invites.data.map((invite) => invite.toResponse()),
      total: invites.total,
      totalPage: invites.totalPage,
      limit: invites.limit,
      page: invites.page,
    }
  }

  async findAll() {
    return (
      await this.teamRepository.findAll({
        relations: {
          userTeamRoles: true,
        },
      })
    ).map((team) => team.toResponse())
  }

  async findOne(id: Id) {
    const team = await this.teamRepository.findById(id, {
      relations: {
        organization: true,
        userTeamRoles: true,
        invites: true,
      },
    })
    return team?.toResponse()
  }

  update(id: Id, updateTeamDto: UpdateTeamDto) {
    return this.teamRepository.updateById(id, updateTeamDto)
  }

  async remove(id: Id) {
    // Get team info before deletion for event publishing
    const team = await this.teamRepository.findById(id, {
      relations: {
        organization: true,
      },
    })

    const result = await this.teamRepository.delete(id)

    // Track team deletion for billing purposes
    if (team?.organization) {
      await this.resourceBillingService.trackTeamChange(
        team.organization.id,
        'DELETE',
        id,
        { name: team.getProps().name },
      )
    }

    return result
  }

  async removeMember(teamId: Id, userId: Id) {
    // Get team info before removal for event publishing
    const team = await this.teamRepository.findById(teamId, {
      relations: {
        organization: true,
      },
    })

    const result = await this.teamRepository.removeMember(teamId, userId)

    // Track member removal for billing purposes
    if (team?.organization) {
      await this.resourceBillingService.trackMemberChange(
        team.organization.id,
        'DELETE',
        userId,
        { teamId: teamId },
      )
    }

    return result
  }

  updateMember(teamId: Id, updateTeamMember: UpdateTeamMember) {
    return this.teamRepository.updateMember(teamId, updateTeamMember)
  }
}
