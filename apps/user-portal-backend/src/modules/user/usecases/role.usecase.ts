import { Inject, Injectable, NotFoundException } from '@nestjs/common'

import RoleRepository, {
  ROLE_REPOSITORY,
} from '@backend/modules/user/applications/role.repository'
import { Id } from '@backend/cores/base/id.type'
import { RoleResponseDto } from '@backend/modules/user/applications/dto/user.response.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'

import PermissionRepository, {
  PERMISSION_REPOSITORY,
} from '../applications/permission.repository'
import {
  CreateRoleDto,
  UpdateRoleDto,
} from '../applications/dto/role.request.dto'
import { Organization, Role } from '../entities'

@Injectable()
export class RoleUseCase {
  constructor(
    @Inject(ROLE_REPOSITORY) private readonly repo: RoleRepository,
    @Inject(PERMISSION_REPOSITORY)
    private readonly permissionRepo: PermissionRepository,
  ) {}

  async create(
    createRoleDto: CreateRoleDto,
    user: JwtPayload,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<RoleResponseDto> {
    const permissions = await this.permissionRepo.findAll({
      filter: {
        id: { $in: createRoleDto.rolePermissions },
      },
    })

    const role = await this.repo.create(
      Role.create({
        name: createRoleDto.name,
        permissions: permissions || [],
        organization: new Organization({ id: orgTeamParams.organizationId }),
        default: false,
        createdBy: user.userId,
        updatedBy: user.userId,
      }),
    )

    return role.toResponse()
  }

  async findAll(orgTeamParams: OrgTeamHeadersDto) {
    return (
      await this.repo.findAll({
        filter: {
          organization: {
            id: orgTeamParams.organizationId,
          },
        },
      })
    ).map((role) => role.toResponse())
  }

  async find(
    orgTeamParams: OrgTeamHeadersDto,
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<RoleResponseDto>> {
    const roles = await this.repo.find({
      filter: {
        organization: {
          id: orgTeamParams.organizationId,
        },
      },
      page: paginateOption.page,
      limit: paginateOption.limit,
      orderBy: {
        createdAt: 'ASC',
      },
    })

    return {
      data: roles.data.map((role) => role.toResponse()),
      total: roles.total,
      totalPage: roles.totalPage,
      limit: roles.limit,
      page: roles.page,
    }
  }

  async findOne(id: Id): Promise<Nullable<RoleResponseDto>> {
    const role = await this.repo.findById(id)
    if (!role) return null

    return role?.toResponse()
  }

  async getByUserId(userId: Id): Promise<Nullable<RoleResponseDto[]>> {
    return (await this.repo.getRolesByUserId(userId)).map((role) =>
      role.toResponse(),
    )
  }

  async update(id: Id, updateRoleDto: UpdateRoleDto) {
    const role = await this.repo.findById(id)
    if (!role) throw new NotFoundException('Role not found')

    return this.repo.updateById(id, role.updateRole(updateRoleDto).getProps())
  }

  remove(id: Id) {
    return this.repo.delete(id)
  }

  getAllPermissions() {
    return this.permissionRepo
      .findAll()
      .then((permissions) =>
        permissions.map((permission) => permission.toResponse()),
      )
  }
}
