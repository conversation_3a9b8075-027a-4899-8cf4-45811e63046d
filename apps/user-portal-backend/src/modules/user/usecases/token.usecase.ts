import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'

import { Token } from '../entities/token.entity'
import TokenRepository, {
  TOKEN_REPOSITORY,
} from '../applications/token.repository'
import { TokenResponseDto } from '../applications/dto/token.response.dto'
import {
  CreateTokenDto,
  UpdateTokenDto,
} from '../applications/dto/token.request.dto'
import { TokenLevel } from '../constants/token'

@Injectable()
export class TokenUseCase {
  constructor(
    @Inject(TOKEN_REPOSITORY) private readonly repo: TokenRepository,
  ) {}

  async create(
    createTokenDto: CreateTokenDto,
    user: JwtPayload,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<TokenResponseDto> {
    try {
      const ownerId =
        createTokenDto.level == 'team'
          ? orgTeamParams.teamId
          : orgTeamParams.organizationId
      const newToken = await this.repo.create(
        Token.create({
          ...createTokenDto,
          ownerId,
          createdBy: user.userId,
          updatedBy: user.userId,
        }),
      )

      return newToken.toResponse()
    } catch (error) {
      console.error(error)
      throw error
    }
  }
  async findAll(
    paginateOption: PaginateOptionsDto,
    orgTeamParams: OrgTeamHeadersDto,
    level: TokenLevel,
  ): Promise<PaginateDto<TokenResponseDto>> {
    const ownerId =
      level == 'team' ? orgTeamParams.teamId : orgTeamParams.organizationId
    const tokens = await this.repo.findAllInLevel(
      paginateOption,
      level,
      ownerId,
    )
    return {
      data: tokens.data.map((token) => token.toResponse()),
      total: tokens.total,
      totalPage: tokens.totalPage,
      limit: tokens.limit,
      page: tokens.page,
    }
  }

  async find(
    paginateOption: PaginateOptionsDto,
    orgTeamParams: OrgTeamHeadersDto,
    level: TokenLevel,
  ): Promise<PaginateDto<TokenResponseDto>> {
    try {
      const tokens = await this.repo.find({
        filter: {
          ownerId: {
            $eq:
              level == 'team'
                ? orgTeamParams.teamId
                : orgTeamParams.organizationId,
          },
        },
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })
      return {
        data: tokens.data.map((token) => token.toResponse()),
        total: tokens.total,
        totalPage: tokens.totalPage,
        limit: tokens.limit,
        page: tokens.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<TokenResponseDto>> {
    const entity = await this.repo.findById(id)

    if (!entity) return null
    return entity.toResponse()
  }

  async update(id: Id, updateTokenDto: UpdateTokenDto) {
    const token = await this.repo.findById(id)

    if (!token) return null

    const returnedToken = await this.repo.updateById(id, {
      ...token.getProps(),
      ...updateTokenDto,
    })

    if (!returnedToken) return null

    return returnedToken.toResponse()
  }

  async remove(id: Id) {
    const result = await this.repo.delete(id)
    return result
  }
}
