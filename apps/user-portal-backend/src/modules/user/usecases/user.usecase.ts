import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common'
import moment from 'moment'

import {
  Organization,
  PermissionResponse,
  Role,
  Team,
} from '@backend/modules/user/entities'
import OrganizationRepository, {
  ORGANIZATION_REPOSITORY,
} from '@backend/modules/user/applications/organization.repository'
import RoleRepository, {
  ROLE_REPOSITORY,
} from '@backend/modules/user/applications/role.repository'
import { Id } from '@backend/cores/base/id.type'
import { configService } from '@backend/cores/config/config.service'
import { AuthService } from '@backend/cores/auth/auth.service'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import TeamRepository, {
  TEAM_REPOSITORY,
} from '@backend/modules/user/applications/team.repository'
import PermissionRepository, {
  PERMISSION_REPOSITORY,
} from '@backend/modules/user/applications/permission.repository'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import UserInviteRepository, {
  USER_INVITE_REPOSITORY,
} from '@backend/modules/user/applications/user-invite.repository'
import {
  UserInvite,
  UserInviteStatus,
} from '@backend/modules/user/entities/user-invite.entity'
import { IPermissionQueries } from '@backend/cores/rbac/permission-queries'
import { SeverityUseCase } from '@backend/modules/escalation/usecases/severity.usecase'
import { ResourceBillingService } from '@backend/modules/subscription/services/resource-billing.service'
import { SubscriptionManagementUseCase } from '@backend/modules/subscription/usecases/subscription-management.usecase'

import {
  CurrentUserResponseDto,
  UserAuthResponseDto,
  UserResponseDto,
} from '../applications/dto/user.response.dto'
import UserRepository, {
  USER_REPOSITORY,
} from '../applications/users.repository'
import { User } from '../entities/user.entity'
import {
  UpdateUserDto,
  UpdateUserProfileDto,
  CreateUserDto,
  InviteUserDto,
  ResendInviteUserDto,
  CreateStaffDto,
} from '../applications/dto/user.request.dto'

import { TeamUseCase } from './team.usecase'

@Injectable()
export class UserUseCase {
  constructor(
    @Inject(USER_REPOSITORY) private readonly repo: UserRepository,
    @Inject(ORGANIZATION_REPOSITORY)
    private readonly organizationRepository: OrganizationRepository,
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: RoleRepository,
    @Inject(TEAM_REPOSITORY)
    private readonly teamRepository: TeamRepository,
    @Inject(PERMISSION_REPOSITORY)
    private readonly permissionRepository: PermissionRepository,
    private readonly authService: AuthService,
    @Inject(USER_INVITE_REPOSITORY)
    private readonly userInviteRepository: UserInviteRepository,
    @Inject(forwardRef(() => SeverityUseCase))
    private readonly severityUsecase: SeverityUseCase,
    @Inject(forwardRef(() => TeamUseCase))
    private readonly teamUseCase: TeamUseCase,
    @Inject(forwardRef(() => ResourceBillingService))
    private readonly resourceBillingService: ResourceBillingService,
    @Inject(forwardRef(() => SubscriptionManagementUseCase))
    private readonly subscriptionManagementUseCase: SubscriptionManagementUseCase,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    const newUser = User.create(createUserDto)
    return (await this.repo.create(newUser)).toResponse()
  }

  async createStaff(createStaffDto: CreateStaffDto): Promise<UserResponseDto> {
    const newUser = User.create(createStaffDto)
    return (await this.repo.create(newUser)).toResponse()
  }

  async findAll(query: IPermissionQueries) {
    const allUser = await this.repo.findAll({
      filter: {
        id: query.userId,
      },
      relations: {
        organizations: true,
      },
    })

    return allUser.map((user) => {
      return user.toResponse()
    })
  }

  async findOne(id: Id) {
    const user = await this.repo.findById(id, {
      relations: {
        organizations: true,
        userTeamRoles: {
          role: {
            permissions: true,
          },
          team: {
            organization: true,
          },
        },
      },
    })

    if (!user) return null

    return user?.toResponse()
  }

  async getCurrentUser(userId: Id): Promise<Nullable<CurrentUserResponseDto>> {
    const user = await this.repo.findById(userId, {
      relations: {
        organizations: {
          teams: true,
        },
        userTeamRoles: {
          role: {
            permissions: true,
          },
        },
      },
    })
    if (!user) return null
    const userResponse = user.toResponse()

    return {
      id: userResponse.id,
      firebaseId: userResponse.firebaseId,
      firstName: userResponse.firstName,
      lastName: userResponse.lastName,
      email: userResponse.email,
      phoneNumber: userResponse.phoneNumber,
      lastLoginAt: userResponse.lastLoginAt,
      lastLoginMethod: userResponse.lastLoginMethod,
      createdAt: userResponse.createdAt,
      internalRole: userResponse.internalRole,
      updatedAt: userResponse.updatedAt,
      organizations: userResponse.organizations?.map((org) => ({
        id: org.id,
        name: org.name,
        ownerId: org.ownerId,
        teams: org.teams?.reduce(
          (
            acc: {
              id: string
              name: string | undefined
              role: {
                id: string
                name: string | undefined
                permissions: PermissionResponse[]
              }
            }[],
            team,
          ) => {
            const role = userResponse.teams?.find(
              (userTeamRole) => userTeamRole.team.id === team.id,
            )?.role

            if (!role) return acc

            acc.push({
              id: team.id,
              name: team.name,
              role: {
                id: role.id,
                name: role.name,
                permissions: role.permissions,
              },
            })

            return acc
          },
          [],
        ),
      })),
    }
  }

  async getUserByFirebaseId(firebaseId: string) {
    const user = await this.repo.findOne({
      filter: {
        firebaseId,
      },
      fields: {
        id: true,
        firebaseId: true,
        internalRole: true,
      },
    })

    if (!user) return null

    return user.toResponse()
  }

  async getUserFirebase(userId: Id) {
    const user = await this.repo.findById(userId, {
      fields: {
        id: true,
        firebaseId: true,
      },
    })

    if (!user) return null

    return user.toResponse()
  }

  async getAuthUser(userId: Id): Promise<Nullable<UserAuthResponseDto>> {
    const user = await this.repo.getCachedUserAuth(userId)
    if (!user) return null
    const userProps = user.getProps()

    return {
      id: user.id,
      firebaseId: userProps.firebaseId as string,
      internalRole: userProps.internalRole,
      organizations: userProps.organizations?.map((organization) => ({
        id: organization.id,
        teams: organization.teams.map((team) => {
          const role = userProps.userTeamRoles?.find(
            (userTeamRole) => userTeamRole.team.id === team.id,
          )?.role

          return {
            id: team.id,
            ...(role && {
              role: {
                id: role?.id,
                name: role?.name,
                permissions: role?.permissions?.map((permission) => ({
                  id: permission.id,
                  scope: permission.scope,
                  action: permission.action,
                  type: permission.type,
                })),
              },
            }),
          }
        }),
      })),
    }
  }

  async update(id: Id, updateUserDto: UpdateUserDto) {
    const user = await this.repo.updateById(id, updateUserDto)
    if (!user) return null

    return user.toResponse()
  }

  async createDefaultUserProfile(userId: Id) {
    const currentUser = await this.repo.findById(userId)

    if (!currentUser) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST)
    }

    // Create organization
    const org: Organization = Organization.create({
      name: 'My Organization',
      teams: [],
    })
    await this.organizationRepository.create(org)

    // Create default role
    const adminPermissions = await this.permissionRepository.findAll({
      filter: {
        action: PermissionAction.MANAGE,
      },
    })

    const viewerPermissions = await this.permissionRepository.findAll({
      filter: {
        scope: {
          $in: [
            PermissionScope.TEAM_SETTINGS,
            PermissionScope.TEAM_MEMBERS,
            PermissionScope.CHECKS,
            PermissionScope.INCIDENTS,
          ],
        },
        type: {
          $in: [PermissionType.TEAM],
        },
        action: PermissionAction.VIEW,
      },
    })

    const adminRole = Role.generateRole({
      organizationId: org.id,
      roleName: 'Admin',
      permissions: adminPermissions,
      isDefault: true,
    })

    const viewerRole = Role.generateRole({
      organizationId: org.id,
      roleName: 'Viewer',
      permissions: viewerPermissions,
      isDefault: true,
    })

    await this.roleRepository.createMany([adminRole, viewerRole])

    // Create default team
    const team: Team = await this.teamRepository.create(
      Team.create({
        name: 'My Team',
        organization: org,
      }),
    )

    const updateUserReq = User.update({
      id: userId,
      props: {
        organizations: [org],
      },
    })

    await this.repo.assignRole(currentUser, {
      role: adminRole,
      team: team,
    })

    await this.repo.updateById(userId, updateUserReq.getProps())

    // Track member change for billing purposes - initial organization owner
    await this.resourceBillingService.trackMemberChange(
      org.id,
      'CREATE',
      userId,
      { isInitialOwner: true },
    )

    try {
      await this.subscriptionManagementUseCase.assignFreePlanToOrganization(
        org.id,
        userId,
      )
    } catch (error) {
      console.error('Failed to assign free plan:', error)
    }

    return await this.findOne(userId)
  }

  async updateUserProfile(updateUserProfileDto: UpdateUserProfileDto) {
    const currentUser = await this.repo.findById(updateUserProfileDto.userId)

    if (!currentUser) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST)
    }

    // Update organization name
    if (updateUserProfileDto.organizationName) {
      const organizations = currentUser.getProps().organizations
      if (organizations && organizations.length === 1) {
        const org = organizations[0]
        const updateOrgReq = Organization.update({
          id: org.id,
          props: {
            name: updateUserProfileDto.organizationName,
            teams: org.getProps().teams,
          },
        })
        await this.organizationRepository.updateById(
          org.id,
          updateOrgReq.getProps(),
        )
      }
    }

    // Update user profile
    const updateUserReq = User.update({
      id: updateUserProfileDto.userId,
      props: {
        firstName: updateUserProfileDto.firstName,
        lastName: updateUserProfileDto.lastName,
        phoneNumber: updateUserProfileDto.phoneNumber,
      },
    })

    await this.repo.updateById(
      updateUserProfileDto.userId,
      updateUserReq.getProps(),
    )

    return await this.findOne(updateUserProfileDto.userId)
  }

  remove(id: Id) {
    return this.repo.delete(id)
  }

  async sendInvitation(
    invitation: UserInvite,
    team: Team,
    currentUser: JwtPayload,
    redirectUrl: string = configService.getValue('WEB_PORTAL_EMAIL_AUTH'),
  ) {
    const userInfo = await this.repo.findById(currentUser.userId)
    if (!userInfo) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST)
    }

    const loginUrl = `${redirectUrl}?invitationId=${invitation.id}&email=${invitation.email}`
    const organizationName = team.organization?.getProps().name
    // Subscription limit is already checked at controller level via guard
    this.teamRepository.sendInvitationEmail({
      to: invitation.email,
      organizationName: organizationName || '',
      inviter: userInfo.fullName || 'Admin',
      acceptUrl: loginUrl,
      subject: `Monitoring Dog - Invites you to join ${organizationName}`,
    })
  }

  async inviteUser(inviteUserDto: InviteUserDto, currentUser: JwtPayload) {
    const team = await this.teamRepository.findById(inviteUserDto.teamId, {
      relations: {
        organization: true,
      },
    })
    if (!team) {
      throw new HttpException('Team not found', HttpStatus.BAD_REQUEST)
    }

    const invitation = await this.teamRepository.createInvitation(
      team.createInvitation({
        teamId: inviteUserDto.teamId,
        email: inviteUserDto.email,
        status: UserInviteStatus.PENDING,
        role: inviteUserDto.role,
        expiredAt: moment(new Date()).add(3, 'days').toDate(),
        createdBy: currentUser.userId,
      }),
    )

    await this.sendInvitation(
      invitation,
      team,
      currentUser,
      inviteUserDto.redirectUrl,
    )
  }

  async resendInvitation(
    resendInviteDto: ResendInviteUserDto,
    currentUser: JwtPayload,
  ) {
    const invitation = await this.userInviteRepository.findById(
      resendInviteDto.invitationId,
    )

    if (!invitation) {
      throw new HttpException('Invitation not found', HttpStatus.BAD_REQUEST)
    }

    const team = await this.teamRepository.findById(
      invitation.getProps().team.id,
      {
        relations: {
          organization: true,
        },
      },
    )
    if (!team) {
      throw new HttpException('Team not found', HttpStatus.BAD_REQUEST)
    }

    const userInfo = await this.repo.findById(currentUser.userId)
    if (!userInfo) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST)
    }

    await this.userInviteRepository.updateById(
      resendInviteDto.invitationId,
      invitation.resentInvite().getProps(),
    )

    await this.sendInvitation(
      invitation,
      team,
      currentUser,
      resendInviteDto.redirectUrl,
    )
  }

  async deleteInvitation(invitationId: Id) {
    await this.userInviteRepository.delete(invitationId)
  }

  async validateInvitedUser(email: string, invitationId: Id) {
    const invitation = await this.userInviteRepository.findOne({
      filter: {
        id: invitationId,
        email,
        expiredAt: {
          $gte: new Date(),
        },
      },
    })

    if (!invitation)
      throw new HttpException('Invitation expired', HttpStatus.BAD_REQUEST)

    return invitation
  }

  async checkOrgMemeberLimit(teamId: string, orgId?: string) {
    // Subscription limit is already checked at controller level via guard
    // This method is kept for backward compatibility but no longer performs limit checks
    if (!orgId) {
      const teamData = await this.teamUseCase.findOne(teamId)
      if (!teamData)
        throw new UnauthorizedException('Team not found: ' + teamId)
      if (!teamData.organization)
        throw new UnauthorizedException(
          'Org not found: ' + teamData.organization,
        )
    }
  }
  /**
   * Accepted invitation.
   *
   * @param user        User
   * @param invitation  User invite
   */
  async acceptInvitation(userId: Id, invitation: UserInvite) {
    const _user = await this.repo.findById(userId, {
      fields: {
        id: true,
      },
      relations: {
        organizations: true,
      },
    })

    if (!_user)
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST)

    const team = await this.teamRepository.findById(
      invitation.getProps().team.id,
      {
        relations: {
          organization: true,
        },
      },
    )

    // Get role
    const role = await this.roleRepository.findById(
      invitation.getProps().role.id,
    )

    // Assign user to org and team with role
    if (team?.organization && role) {
      _user.assignToOrganization(team.organization)

      await this.repo.updateById(userId, _user.getProps())

      await this.repo.assignRole(_user, {
        team: team,
        role: role,
      })

      // Create default severities for the team
      console.log('creating severity')
      await this.severityUsecase.createDefaultSeverity(userId, team.id)
    }

    await this.userInviteRepository.delete(invitation.id)

    // Track member change for billing purposes
    if (team?.organization) {
      await this.resourceBillingService.trackMemberChange(
        team.organization.id,
        'CREATE',
        userId,
        { invitationId: invitation.id, teamId: team.id },
      )
    }
  }

  async findUserByAliasToken(aliasToken: string) {
    const user = await this.repo.findOne({
      filter: { aliasToken },
      limit: 1,
    })

    if (!user) return null

    return user?.toResponse()
  }
}
