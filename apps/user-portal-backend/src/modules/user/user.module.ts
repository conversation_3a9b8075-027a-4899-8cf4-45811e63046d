import { Module, forwardRef } from '@nestjs/common'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { OrganizationInfrastructure } from '@backend/modules/user/infras/organization.infrastructure'
import { ORGANIZATION_REPOSITORY } from '@backend/modules/user/applications/organization.repository'
import { OrganizationUseCase } from '@backend/modules/user/usecases/organization.usecase'
import { NotificationsModule } from '@backend/frameworks/notification/notification.module'
import { TEAM_REPOSITORY } from '@backend/modules/user/applications/team.repository'
import { TeamInfrastructure } from '@backend/modules/user/infras/team.infrastructure'
import { PERMISSION_REPOSITORY } from '@backend/modules/user/applications/permission.repository'
import { PermissionInfrastructure } from '@backend/modules/user/infras/permission.infrastructure'
import { TeamController } from '@backend/modules/user/controllers/team.controller'
import { TeamUseCase } from '@backend/modules/user/usecases/team.usecase'
import { USER_INVITE_REPOSITORY } from '@backend/modules/user/applications/user-invite.repository'
import { UserInviteInfrastructure } from '@backend/modules/user/infras/user-invite.infrastructure'
import { SubscriptionModule } from '@backend/modules/subscription/subscription.module'

import { SeverityInfrastructure } from '../escalation/infras/severity.infrastructure'
import { SEVERITY_REPOSITORY } from '../escalation/applications/severity.repository'
import { INTEGRATION_SETTING_REPOSITORY } from '../integration-setting/applications/integration-setting.repository'
import { IntegrationSettingInfrastructure } from '../integration-setting/infras/integration-setting.infrastructure'
import { IntegrationSettingUseCase } from '../integration-setting/usecases/integration-setting.usecase'
import { EscalationModule } from '../escalation/escalation.module'

import { USER_REPOSITORY } from './applications/users.repository'
import { UserInfrastructure } from './infras/user.infrastructure'
import { UserController } from './controllers/user.controller'
import { UserUseCase } from './usecases/user.usecase'
import { RoleInfrastructure } from './infras/role.infrastructure'
import { ROLE_REPOSITORY } from './applications/role.repository'
import {
  PermissionController,
  RoleController,
  RolesController,
} from './controllers/role.controller'
import { RoleUseCase } from './usecases/role.usecase'
import { OrganizationController } from './controllers/organization.controller'
import { TokenController } from './controllers/token.controller'
import { TokenInfrastructure } from './infras/token.infrastructure.ts'
import { TOKEN_REPOSITORY } from './applications/token.repository'
import { TokenUseCase } from './usecases/token.usecase'
import { UserDeviceController } from './controllers/user-device.controller'
import { USER_DEVICE_REPOSITORY } from './applications/user-device.repository'
import { UserDeviceInfrastructure } from './infras/user-device.infrastructure'
import { UserDeviceUseCase } from './usecases/user-device.usecase'
import { UserPlivoCredentialController } from './controllers/user-plivo-credential.controller'
import { USER_PLIVO_CREDENTIAL_REPOSITORY } from './applications/user-plivo-credential.repository'
import { UserPlivoCredentialInfrastructure } from './infras/user-plivo-credential.infrastructure'
import { UserPlivoCredentialUseCase } from './usecases/user-plivo-credential.usecase'

@Module({
  imports: [
    DatabaseModule,
    NotificationsModule,
    EscalationModule,
    forwardRef(() => SubscriptionModule),
  ],
  controllers: [
    TokenController,
    UserController,
    TeamController,
    RoleController,
    RolesController,
    OrganizationController,
    PermissionController,
    UserDeviceController,
    UserPlivoCredentialController,
  ],
  providers: [
    { provide: USER_REPOSITORY, useClass: UserInfrastructure },
    { provide: ORGANIZATION_REPOSITORY, useClass: OrganizationInfrastructure },
    { provide: ROLE_REPOSITORY, useClass: RoleInfrastructure },
    { provide: TOKEN_REPOSITORY, useClass: TokenInfrastructure },
    { provide: TEAM_REPOSITORY, useClass: TeamInfrastructure },
    { provide: SEVERITY_REPOSITORY, useClass: SeverityInfrastructure },
    { provide: PERMISSION_REPOSITORY, useClass: PermissionInfrastructure },
    { provide: USER_INVITE_REPOSITORY, useClass: UserInviteInfrastructure },
    {
      provide: INTEGRATION_SETTING_REPOSITORY,
      useClass: IntegrationSettingInfrastructure,
    },
    { provide: USER_DEVICE_REPOSITORY, useClass: UserDeviceInfrastructure },
    {
      provide: USER_PLIVO_CREDENTIAL_REPOSITORY,
      useClass: UserPlivoCredentialInfrastructure,
    },
    UserUseCase,
    OrganizationUseCase,
    TokenUseCase,
    TeamUseCase,
    RoleUseCase,
    IntegrationSettingUseCase,
    UserDeviceUseCase,
    UserPlivoCredentialUseCase,
  ],
  exports: [
    UserUseCase,
    OrganizationUseCase,
    TeamUseCase,
    TokenUseCase,
    UserPlivoCredentialUseCase,
    ORGANIZATION_REPOSITORY,
  ],
})
export class UserModule {}
