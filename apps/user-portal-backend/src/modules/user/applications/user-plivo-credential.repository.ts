import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { UserPlivoCredentialModel } from '@backend/frameworks/database/models/user-plivo-credential.model'

import {
  UserPlivoCredential,
  UserPlivoCredentialProps,
} from '../entities/user-plivo-credential.entity'

interface UserPlivoCredentialRepository
  extends CrudRepository<
    UserPlivoCredential,
    Partial<UserPlivoCredentialProps>,
    ITypeOrmFilter<UserPlivoCredentialModel>
  > {
  findByUserId(userId: string): Promise<UserPlivoCredential | null>
}

export default UserPlivoCredentialRepository
export const USER_PLIVO_CREDENTIAL_REPOSITORY =
  'USER_PLIVO_CREDENTIAL_REPOSITORY'
