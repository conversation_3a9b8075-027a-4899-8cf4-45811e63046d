import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { UserDeviceModel } from '@backend/frameworks/database/models/user-device'

import { UserDevice, UserDeviceProps } from '../entities/user-device.entity'

type UserDeviceRepository = CrudRepository<
  UserDevice,
  Partial<UserDeviceProps>,
  ITypeOrmFilter<UserDeviceModel>
>

export default UserDeviceRepository
export const USER_DEVICE_REPOSITORY = 'USER_DEVICE_REPOSITORY'
