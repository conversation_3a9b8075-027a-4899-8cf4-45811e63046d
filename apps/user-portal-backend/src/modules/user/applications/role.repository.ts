import CrudRepository from '@backend/cores/base/crud-repository'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { RoleModel as RoleModel } from '@backend/frameworks/database/models/role.model'

import { Role, RoleProps } from '../entities'

interface RoleRepository
  extends CrudRepository<Role, Partial<RoleProps>, ITypeOrmFilter<RoleModel>> {
  createMany(roles: Role[]): Promise<Role[]>
  getRolesByUserId(userId: Id): Promise<Role[]>
}

export default RoleRepository
export const ROLE_REPOSITORY = 'ROLE_REPOSITORY'
