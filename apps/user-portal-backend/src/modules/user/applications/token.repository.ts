import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import CrudRepository from '@backend/cores/base/crud-repository'
import { TokenModel } from '@backend/frameworks/database/models/token.model.ts'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

import { Token, TokenProps } from '../entities'
import { TokenLevel } from '../constants/token'

interface TokenRepository
  extends CrudRepository<
    Token,
    Partial<TokenProps>,
    ITypeOrmFilter<TokenModel>
  > {
  findAllInLevel(
    query: ITypeOrmFilter<TokenModel>,
    level: TokenLevel,
    ownerId: string,
  ): Promise<IPaginate<Token>>
}

export default TokenRepository
export const TOKEN_REPOSITORY = 'TOKEN_REPOSITORY'
