import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'

import { UserPlivoCredentialSchema } from '../../entities/user-plivo-credential.entity'

export class CreateUserPlivoCredentialDto extends createZodDto(
  extendApi(
    UserPlivoCredentialSchema.omit({
      createdBy: true,
      updatedBy: true,
      userId: true,
      username: true,
      password: true,
      alias: true,
    }),
  ),
) {}
