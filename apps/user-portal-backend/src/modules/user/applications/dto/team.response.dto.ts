import { Id } from '@backend/cores/base/id.type'

import {
  OrganizationResponse,
  RoleResponse,
  TeamResponse,
  UserResponse,
} from '../../entities'

export class TeamResponseDto implements TeamResponse {
  id: Id
  name?: string
  createdAt?: Date
  updatedAt?: Date
  createdBy?: Id
  updatedBy?: Id
  organization?: OrganizationResponse
  members?: any[]
  invites?: any[]
  totalMembers?: number
}

export class TeamMemberResponseDto {
  user: UserResponse
  role: RoleResponse
}
