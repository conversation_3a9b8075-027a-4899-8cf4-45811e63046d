import { IsEnum, IsString } from 'class-validator'

import { TokenProps } from '../../entities'
import { TokenLevel } from '../../constants/token'

export class CreateTokenDto implements Partial<TokenProps> {
  @IsString()
  name: string

  @IsEnum(TokenLevel, {
    message: `level must be either 'team' or 'organization', current TokenLevel value: ${Object.values(TokenLevel).join(', ')}`,
  })
  level: TokenLevel
}

export class UpdateTokenDto implements Partial<TokenProps> {
  @IsString()
  name: string
}
