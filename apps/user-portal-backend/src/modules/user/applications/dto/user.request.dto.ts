import {
  IsDate,
  IsE<PERSON>,
  IsEnum,
  IsOptional,
  IsPhoneNumber,
  IsString,
} from 'class-validator'

import { Id } from '@backend/cores/base/id.type'

import { UserProps } from '../../entities'
import { InternalRole } from '../../constants/user'

export class CreateUserDto implements Partial<UserProps> {
  firebaseId: string
  email?: string
}

export class UpdateUserDto implements Partial<UserProps> {
  @IsOptional()
  @IsPhoneNumber()
  phoneNumber?: string

  @IsOptional()
  @IsString()
  firstName?: string

  @IsOptional()
  @IsString()
  lastName?: string

  @IsOptional()
  @IsDate()
  lastLoginAt?: Date

  @IsOptional()
  @IsString()
  lastLoginMethod?: string

  @IsOptional()
  @IsString()
  organizationName?: string

  @IsOptional()
  @IsEmail()
  email?: string
}

export class UpdateUserProfileDto {
  @IsString()
  @IsOptional()
  userId: Id

  @IsString()
  firstName: string

  @IsString()
  lastName: string

  @IsString()
  @IsOptional()
  organizationName: string

  @IsPhoneNumber()
  @IsOptional()
  phoneNumber: string

  @IsString()
  @IsOptional()
  roleId: Id
}

export class InviteUserDto {
  @IsEmail()
  email: string

  @IsString()
  role: Id

  @IsString()
  teamId: Id

  @IsString()
  @IsOptional()
  redirectUrl: string
}

export class ResendInviteUserDto {
  @IsString()
  invitationId: Id

  @IsString()
  @IsOptional()
  redirectUrl: string
}

export class CreateStaffDto implements Partial<UserProps> {
  firebaseId?: string

  @IsEmail()
  email: string

  @IsEnum(InternalRole, {
    message: 'Internal role must be a valid enum value',
  })
  internalRole: InternalRole
}
