import { IsArray, IsString } from 'class-validator'
import { Type } from 'class-transformer'

import { Id } from '@backend/cores/base/id.type'

import { RoleProps } from '../../entities'

export class CreateRoleDto implements Partial<RoleProps> {
  @IsString()
  name: string

  @IsArray()
  @IsString({ each: true })
  @Type(() => String)
  rolePermissions: Id[]
}

export class UpdateRoleDto implements Partial<RoleProps> {
  @IsString()
  name: string

  @IsArray()
  @IsString({ each: true })
  @Type(() => String)
  rolePermissions: Id[]
}
