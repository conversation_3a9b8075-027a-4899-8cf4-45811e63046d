import { extendApi } from '@anatine/zod-openapi'
import { createZodDto } from '@anatine/zod-nestjs'
import { z } from 'zod'

import { UserDeviceSchema } from '../../entities/user-device.entity'

export class CreateUserDeviceDto extends createZodDto(
  extendApi(
    UserDeviceSchema.omit({
      createdBy: true,
      updatedBy: true,
      userId: true,
    }).extend({
      idToken: z.string({ message: 'idToken is required' }),
    }),
  ),
) {}
