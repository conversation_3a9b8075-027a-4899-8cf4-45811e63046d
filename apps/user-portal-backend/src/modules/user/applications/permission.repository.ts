import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { PermissionModel as PermissionModel } from '@backend/frameworks/database/models/permission.model'

import { Permission } from '../entities'

interface PermissionRepository {
  createAll(permissions: Permission[]): Promise<Permission[]>
  count(): Promise<number>
  find(query: ITypeOrmFilter<PermissionModel>): Promise<IPaginate<Permission>>
  findAll(query?: ITypeOrmFilter<PermissionModel>): Promise<Permission[]>
}

export default PermissionRepository
export const PERMISSION_REPOSITORY = 'PERMISSION_REPOSITORY'
