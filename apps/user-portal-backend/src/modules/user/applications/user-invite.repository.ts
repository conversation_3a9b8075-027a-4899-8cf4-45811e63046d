import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import CrudRepository from '@backend/cores/base/crud-repository'
import {
  UserInvite,
  UserInviteProps,
} from '@backend/modules/user/entities/user-invite.entity'
import { UserInviteModel } from '@backend/frameworks/database/models/user-invite.model'

type UserInviteRepository = CrudRepository<
  UserInvite,
  Partial<UserInviteProps>,
  ITypeOrmFilter<UserInviteModel>
>

export default UserInviteRepository
export const USER_INVITE_REPOSITORY = 'USER_INVITE_REPOSITORY'
