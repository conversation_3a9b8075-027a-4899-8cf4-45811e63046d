import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import CrudRepository from '@backend/cores/base/crud-repository'
import { OrganizationModel as OrganizationModel } from '@backend/frameworks/database/models/organization.model'
import { Id } from '@backend/cores/base/id.type'

import { Organization, OrganizationProps } from '../entities'

interface OrganizationRepository
  extends CrudRepository<
    Organization,
    Partial<OrganizationProps>,
    ITypeOrmFilter<OrganizationModel>
  > {
  findAll(query?: ITypeOrmFilter<OrganizationModel>): Promise<Organization[]>
  findByUserId(userId: string): Promise<Organization[] | null>
  countActiveUsersByOrganizationId(organizationId: Id): Promise<number>
}

export default OrganizationRepository
export const ORGANIZATION_REPOSITORY = 'ORGANIZATION_REPOSITORY'
