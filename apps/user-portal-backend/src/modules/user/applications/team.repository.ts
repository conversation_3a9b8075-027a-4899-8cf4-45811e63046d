import { Id } from '@backend/cores/base/id.type'
import CrudRepository from '@backend/cores/base/crud-repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { TeamModel as TeamModel } from '@backend/frameworks/database/models/team.model'
import { UserInvite } from '@backend/modules/user/entities/user-invite.entity'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { UserTeamRoleModel } from '@backend/frameworks/database/models/user-team-role.model'
import { UpdateTeamMember } from '@backend/modules/user/applications/dto/team.request.dto'

import { Team, TeamProps, TeamUserRole } from '../entities'

interface TeamRepository
  extends CrudRepository<Team, Partial<TeamProps>, ITypeOrmFilter<TeamModel>> {
  findMembers(
    teamId: Id,
    query: ITypeOrmFilter<UserTeamRoleModel>,
  ): Promise<IPaginate<TeamUserRole>>

  findAllMembers(teamId: Id): Promise<TeamUserRole[]>

  findInvites(
    teamId: Id,
    query: ITypeOrmFilter<UserInvite>,
  ): Promise<IPaginate<UserInvite>>

  createInvitation(invitation: UserInvite): Promise<UserInvite>

  sendInvitationEmail(userInvite: {
    to: string | string[]
    subject: string
    organizationName: string
    inviter: string
    acceptUrl: string
  }): Promise<void>

  removeMember(teamId: Id, userId: Id): Promise<boolean>

  updateMember(teamId: Id, data: UpdateTeamMember): Promise<boolean>

  /**
   * Count teams by organization ID
   * @param organizationId The organization ID
   * @returns The number of teams in the organization
   */
  countTeamsByOrganizationId(organizationId: Id): Promise<number>
}

export default TeamRepository
export const TEAM_REPOSITORY = 'TEAM_REPOSITORY'
