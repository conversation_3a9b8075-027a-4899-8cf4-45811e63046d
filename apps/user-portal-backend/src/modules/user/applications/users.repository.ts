import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import CrudRepository from '@backend/cores/base/crud-repository'
import { UserModel as UserModel } from '@backend/frameworks/database/models/user.model'
import { Id } from '@backend/cores/base/id.type'

import { User, UserProps, UserTeamRole } from '../entities'

interface UserRepository
  extends CrudRepository<User, Partial<UserProps>, ITypeOrmFilter<UserModel>> {
  assignRole(user: User, userTeamRole: UserTeamRole): Promise<void>
  getCachedUserAuth(userId: Id): Promise<Nullable<User>>
}

export default UserRepository
export const USER_REPOSITORY = 'USER_REPOSITORY'
