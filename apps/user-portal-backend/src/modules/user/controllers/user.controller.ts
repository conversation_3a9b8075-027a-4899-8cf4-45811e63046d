import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import * as plivo from 'plivo'
import { ResourceType } from '@libs/shared/constants/subscription'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { PermissionQueries } from '@backend/commons/decorators/permission-queries.decorator'
import { IPermissionQueries } from '@backend/cores/rbac/permission-queries'
import { CheckSubscriptionLimit } from '@backend/commons/decorators/check-subscription-limit.decorator'

import {
  UpdateUserDto,
  UpdateUserProfileDto,
  CreateUserDto,
  InviteUserDto,
  ResendInviteUserDto,
} from '../applications/dto/user.request.dto'
import { UserUseCase } from '../usecases/user.usecase'

@ApiTags('User')
@Controller('user')
export class UserController {
  constructor(private readonly userUseCase: UserUseCase) {}

  @Get('plivo-call-test')
  async plivoCallTest() {
    const client = new plivo.PhloClient(
      'MAYZQZMZK1NGM5MJIXNJ',
      'ZDNiYmJiM2M1M2YxNTRhNGU2NmZmN2I3ZjZjZDZk',
      {},
    )
    const phloId = '481988cf-d27f-4ce6-9a07-a19bca407dfd'
    console.log(`Triggering Plivo PHLO ${phloId} with payload:`)
    try {
      const phlo = client.phlo(phloId)
      const response = await phlo.run({
        from: '+84848628719',
        incident_id: 'zamow5xtwiog',
        incident_title: 'https://test-url-6jxo.onrender.com/incident/o352r',
        incident_url:
          'http://localhost:3000/team/8luncqcmwymq/incidents/zamow5xtwiog',
        user_endpoint: 'sip:<EMAIL>',
      })
      console.log('Plivo PHLO API Response:', response)
      return response
    } catch (error) {
      console.error(`Error triggering Plivo PHLO ${phloId}:`, error)
      throw error // Re-throw the error for the caller to handle
    }
  }

  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return this.userUseCase.create(createUserDto)
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @Post('update-profile')
  updateProfile(
    @CurrentUser() user: JwtPayload,
    @Body() updateUserProfileDto: UpdateUserProfileDto,
  ) {
    updateUserProfileDto.userId = user.userId
    return this.userUseCase.updateUserProfile(updateUserProfileDto)
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_MEMBERS,
      type: PermissionType.TEAM,
    },
  ])
  @CheckSubscriptionLimit(ResourceType.MEMBER)
  @Post('invite-user')
  inviteUser(
    @CurrentUser() user: JwtPayload,
    @Body() inviteUserDto: InviteUserDto,
  ) {
    return this.userUseCase.inviteUser(inviteUserDto, user)
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_MEMBERS,
      type: PermissionType.TEAM,
    },
  ])
  @Post('resend-invite-user')
  resendInviteUser(
    @CurrentUser() user: JwtPayload,
    @Body() resendInviteUserDto: ResendInviteUserDto,
  ) {
    return this.userUseCase.resendInvitation(resendInviteUserDto, user)
  }

  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_MEMBERS,
      type: PermissionType.TEAM,
    },
  ])
  @Delete('invite-user/:id')
  deleteInvite(@Param('id') id: string) {
    return this.userUseCase.deleteInvitation(id)
  }

  @Get()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.USER_PROFILE,
    },
  ])
  findAll(@PermissionQueries() permissionQueries: IPermissionQueries) {
    return this.userUseCase.findAll(permissionQueries)
  }

  @Get('whoami')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.USER_PROFILE,
    },
  ])
  currentUser(@CurrentUser() user: JwtPayload) {
    return this.userUseCase.getCurrentUser(user.userId)
  }

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.USER_PROFILE,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.userUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.USER_PROFILE,
    },
  ])
  update(
    @CurrentUser() currentUser: JwtPayload,
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    if (currentUser.userId !== id) {
      throw new HttpException(
        'You are not allowed to update this user',
        HttpStatus.FORBIDDEN,
      )
    }

    return this.userUseCase.update(id, updateUserDto)
  }

  @Delete(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.USER_PROFILE,
    },
  ])
  remove(@Param('id') id: string) {
    return this.userUseCase.remove(id)
  }
}
