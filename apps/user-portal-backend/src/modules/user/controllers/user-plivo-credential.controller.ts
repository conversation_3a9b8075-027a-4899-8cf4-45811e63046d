import { Controller, Post, Body, UseGuards, Get, Param } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'

import { UserPlivoCredentialUseCase } from '../usecases/user-plivo-credential.usecase'
import { CreateUserPlivoCredentialDto } from '../applications/dto/user-plivo-credential.request.dto'

@ApiTags('UserPlivoCredential')
@Controller('user-plivo-credential')
export class UserPlivoCredentialController {
  constructor(
    private readonly userPlivoCredentialUseCase: UserPlivoCredentialUseCase,
  ) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  create(
    @Body() createUserPlivoCredentialDto: CreateUserPlivoCredentialDto,
    @CurrentUser() user: JwtPayload,
  ) {
    return this.userPlivoCredentialUseCase.create(user.userId)
  }

  @Get(':userId')
  findUserDevicesByUserId(@Param('userId') userId: string) {
    return this.userPlivoCredentialUseCase.findOneByUserId(userId)
  }
}
