import {
  <PERSON>,
  Get,
  Body,
  Patch,
  Param,
  UseGuards,
  Post,
  Delete,
  Query,
} from '@nestjs/common'
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

import {
  CreateTokenDto,
  UpdateTokenDto,
} from '../applications/dto/token.request.dto'
import { TokenUseCase } from '../usecases/token.usecase'
import { TokenLevel } from '../constants/token'

@ApiTags('Token')
@Controller('token')
export class TokenController {
  constructor(private readonly tokenUseCase: TokenUseCase) {}

  @Get('team')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TOKEN_MANAGEMENT,
      type: PermissionType.TEAM,
    },
  ])
  findTeam(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    const teamLevel = TokenLevel.TEAM
    return this.tokenUseCase.findAll(paginateOption, orgTeamParams, teamLevel)
  }

  @Get('org')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TOKEN_MANAGEMENT,
      type: PermissionType.TEAM,
    },
  ])
  findOrg(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    const orgLevel = TokenLevel.ORG
    return this.tokenUseCase.findAll(paginateOption, orgTeamParams, orgLevel)
  }

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TOKEN_MANAGEMENT,
      type: PermissionType.TEAM,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.tokenUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TOKEN_MANAGEMENT,
      type: PermissionType.TEAM,
    },
  ])
  update(
    @CurrentUser() currentUser: JwtPayload,
    @Param('id') id: string,
    @Body() updateOrgDto: UpdateTokenDto,
  ) {
    return this.tokenUseCase.update(id, updateOrgDto)
  }

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TOKEN_MANAGEMENT,
      type: PermissionType.TEAM,
    },
  ])
  create(
    @CurrentUser() user: JwtPayload,
    @Body() createTokenDto: CreateTokenDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.tokenUseCase.create(createTokenDto, user, orgTeamParams)
  }

  @Delete(':id')
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TOKEN_MANAGEMENT,
    },
  ])
  remove(@Param('id') id: string) {
    return this.tokenUseCase.remove(id)
  }
}
