import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'

import { CreateUserDeviceDto } from '../applications/dto/user-device.request.dto'
import { UserDeviceUseCase } from '../usecases/user-device.usecase'
import { UserDeviceResponseDto } from '../applications/dto/user-device.response.dto'

@ApiTags('UserDevice')
@Controller('user-device')
export class UserDeviceController {
  constructor(private readonly userDeviceUseCase: UserDeviceUseCase) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  create(
    @Body() createUserDeviceDto: CreateUserDeviceDto,
    @CurrentUser() user: JwtPayload,
  ) {
    return this.userDeviceUseCase.create(createUserDeviceDto, user.userId)
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(UserDeviceResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  find(@Query() paginateOption: PaginateOptionsDto) {
    return this.userDeviceUseCase.find(paginateOption)
  }

  @Get('user/:id')
  findUserDevicesByUserId(@Param('id') userId: string) {
    return this.userDeviceUseCase.findUserDeviceByUserId(userId)
  }

  @ApiResponse({
    type: UserDeviceResponseDto,
  })
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  findOne(@Param('id') id: string) {
    return this.userDeviceUseCase.findOne(id)
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.userDeviceUseCase.remove(id)
  }

  @Post('register')
  register(@Body() createUserDeviceDto: CreateUserDeviceDto) {
    return this.userDeviceUseCase.registerDevice(
      createUserDeviceDto.idToken,
      createUserDeviceDto,
    )
  }
}
