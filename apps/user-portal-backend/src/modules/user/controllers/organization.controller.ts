import {
  <PERSON>,
  Get,
  Body,
  Patch,
  Param,
  UseGuards,
  Post,
} from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'

import { OrganizationUseCase } from '../usecases/organization.usecase'
import {
  CreateOrganizationDto,
  UpdateOrganizationDto,
} from '../applications/dto/orgnization.request.dto'

@ApiTags('Organization')
@Controller('organization')
export class OrganizationController {
  constructor(private readonly organizationUseCase: OrganizationUseCase) {}

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ORGANIZATION_SETTINGS,
      type: PermissionType.ORGANIZATION,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.organizationUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ORGANIZATION_SETTINGS,
      type: PermissionType.ORGANIZATION,
    },
  ])
  update(
    @CurrentUser() currentUser: JwtPayload,
    @Param('id') id: string,
    @Body() updateOrgDto: UpdateOrganizationDto,
  ) {
    return this.organizationUseCase.update(id, updateOrgDto)
  }

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ORGANIZATION_SETTINGS,
      type: PermissionType.ORGANIZATION,
    },
  ])
  create(
    @CurrentUser() user: JwtPayload,
    @Body() createOrganizationDto: CreateOrganizationDto,
  ) {
    return this.organizationUseCase.create(createOrganizationDto, user.userId)
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ORGANIZATION_SETTINGS,
      type: PermissionType.ORGANIZATION,
    },
  ])
  find(@CurrentUser() user: JwtPayload) {
    return this.organizationUseCase.findAllByUserId(user.userId)
  }
}
