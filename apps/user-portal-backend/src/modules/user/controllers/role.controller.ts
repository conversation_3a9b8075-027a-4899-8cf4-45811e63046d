import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common'
import { Api<PERSON><PERSON>erAuth, ApiTags } from '@nestjs/swagger'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

import { TeamResponseDto } from '../applications/dto/team.response.dto'
import { RoleUseCase } from '../usecases/role.usecase'
import {
  CreateRoleDto,
  UpdateRoleDto,
} from '../applications/dto/role.request.dto'
import { RoleResponseDto } from '../applications/dto/role.response.dto'

@ApiTags('Role')
@Controller('role')
export class RoleController {
  constructor(private readonly roleUseCase: RoleUseCase) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ROLES,
      type: PermissionType.ORGANIZATION,
    },
  ])
  create(
    @Body() createRoleDto: CreateRoleDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.roleUseCase.create(createRoleDto, user, orgTeamParams)
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(TeamResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ROLES,
      type: PermissionType.ORGANIZATION,
    },
  ])
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.roleUseCase.find(orgTeamParams, paginateOption)
  }

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ROLES,
      type: PermissionType.ORGANIZATION,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.roleUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ROLES,
      type: PermissionType.ORGANIZATION,
    },
  ])
  update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.roleUseCase.update(id, updateRoleDto)
  }

  @Delete(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.ROLES,
      type: PermissionType.ORGANIZATION,
    },
  ])
  remove(@Param('id') id: string) {
    return this.roleUseCase.remove(id)
  }
}

@ApiTags('Role')
@Controller('roles')
export class RolesController {
  constructor(private readonly roleUseCase: RoleUseCase) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ROLES,
      type: PermissionType.ORGANIZATION,
    },
  ])
  findAll(
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ): Promise<RoleResponseDto[]> {
    return this.roleUseCase.findAll(orgTeamParams)
  }
}

@ApiTags('Permission')
@Controller('permissions')
export class PermissionController {
  constructor(private readonly roleUseCase: RoleUseCase) {}

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.ROLES,
      type: PermissionType.ORGANIZATION,
    },
  ])
  findAll() {
    return this.roleUseCase.getAllPermissions()
  }
}
