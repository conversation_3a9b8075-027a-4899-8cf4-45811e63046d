import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'
import { SelectQueryBuilder } from 'typeorm'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { TeamModel } from '@backend/frameworks/database/models/team.model'
import { UserInviteModel } from '@backend/frameworks/database/models/user-invite.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import {
  Organization,
  Permission,
  Role,
  Team,
  TeamProps,
  TeamUserRole,
  User,
} from '@backend/modules/user/entities'
import TeamRepository from '@backend/modules/user/applications/team.repository'
import { EmailTemplate } from '@backend/frameworks/notification/email/email.constant'
import { NotificationFactory } from '@backend/frameworks/notification/notification.factory'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { UserInvite } from '@backend/modules/user/entities/user-invite.entity'
import { OrganizationModel } from '@backend/frameworks/database/models/organization.model'
import { UserTeamRoleModel } from '@backend/frameworks/database/models/user-team-role.model'
import { RoleModel } from '@backend/frameworks/database/models/role.model'
import { UserModel } from '@backend/frameworks/database/models/user.model'
import { UpdateTeamMember } from '@backend/modules/user/applications/dto/team.request.dto'

@Injectable()
export class TeamInfrastructure implements TeamRepository {
  private readonly teamModel: TypeORMDriver<TeamModel>
  private readonly userInviteModel: TypeORMDriver<UserInviteModel>
  private readonly userTeamRoleModel: TypeORMDriver<UserTeamRoleModel>

  constructor(
    @Inject(Database) private database: Database,
    private notificationFactory: NotificationFactory,
  ) {
    this.teamModel = this.database.typeorm<TeamModel>('TeamModel')
    this.userInviteModel =
      this.database.typeorm<UserInviteModel>('UserInviteModel')

    this.userTeamRoleModel =
      this.database.typeorm<UserTeamRoleModel>('UserTeamRoleModel')
  }

  toDomain = (team: TeamModel): Team => {
    const {
      id,
      createdAt,
      updatedAt,
      organization,
      userTeamRoles,
      invites,
      ...props
    } = team

    return new Team({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        organization: this.toOrganization(organization),
        members: userTeamRoles?.map(this.toUserRole),
        invites: invites?.map(this.toInvite),
      },
    })
  }

  toOrganization = (organization: OrganizationModel) => {
    if (!organization) return undefined

    return new Organization({
      id: organization.id,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,
      props: {
        name: organization.name,
        teams: organization.teams?.map(this.toTeam),
      },
    })
  }

  toTeam = (team: TeamModel) => {
    if (!team) return undefined

    return new Team({
      id: team.id,
      createdAt: team.createdAt,
      updatedAt: team.updatedAt,
      props: {
        name: team.name,
        ...(team?.organization && {
          organization: this.toOrganization(team.organization),
        }),
      },
    })
  }

  toUserRole = (team: UserTeamRoleModel) => {
    if (!team)
      return {
        user: undefined,
        role: undefined,
      }

    return {
      user: this.toUser(team.user),
      role: this.toRole(team.role),
    }
  }

  toUser = (user: UserModel) => {
    return new User({
      id: user.id,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      props: {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
    })
  }

  toRole = (role: RoleModel) => {
    return new Role({
      id: role.id,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
      props: {
        name: role.name,
        default: role.default,
        permissions: role.permissions?.map(
          (permission) =>
            new Permission({
              id: permission.id,
              createdAt: permission.createdAt,
              updatedAt: permission.updatedAt,
              props: permission,
            }),
        ),
      },
    })
  }

  toInvite = (invite: UserInviteModel) => {
    return new UserInvite({
      id: invite.id,
      props: {
        email: invite.email,
        status: invite.status,
        role: invite.role
          ? this.toRole(invite.role)
          : new Role({ id: invite.roleId }),
        expiredAt: invite.expiredAt,
        team: this.toTeam(invite.team),
      },
    })
  }

  async findAll(query?: ITypeOrmFilter<TeamModel>): Promise<Team[]> {
    const teams = await this.teamModel.findAll(query)
    return teams.map(this.toDomain)
  }

  find(query: ITypeOrmFilter<TeamModel>): Promise<IPaginate<Team>> {
    return this.teamModel.find(query).then((teams) => {
      return {
        ...teams,
        data: teams.data.map(this.toDomain),
      }
    })
  }

  findOne(query: ITypeOrmFilter<TeamModel>): Promise<Nullable<Team>> {
    return this.teamModel.findOne(query).then((team) => {
      if (!team) {
        return null
      }

      return this.toDomain(team)
    })
  }

  async findById(
    id: Id,
    query?: ITypeOrmFilter<TeamModel>,
  ): Promise<Team | null> {
    return await this.teamModel.findById(id, query).then((team) => {
      if (!team) {
        return null
      }

      return this.toDomain(team)
    })
  }

  async create(team: Team): Promise<Team> {
    const teamProps = team.getProps()

    return await this.teamModel
      .create({
        ...teamProps,
        organization: {
          id: teamProps.organization?.id,
        },
        userTeamRoles: teamProps.members?.map((teamRole) => ({
          team: {
            id: team.id,
          },
          user: {
            id: teamRole.user?.id,
          },
          role: {
            id: teamRole.role?.id,
          },
        })),
        invites: teamProps.invites?.map((invite) => ({
          id: invite.id,
        })),
      })
      .then(this.toDomain)
  }

  updateById(id: Id, team: Partial<TeamProps>): Promise<Team> {
    return this.teamModel
      .updateById(id, {
        ...team,
        organization: {
          id: team.organization?.id,
        },
        userTeamRoles: team.members?.map((teamRole) => ({
          team: {
            id,
          },
          user: {
            id: teamRole.user?.id,
          },
          role: {
            id: teamRole.role?.id,
          },
        })),
        invites: team.invites?.map((invite) => ({
          id: invite.id,
        })),
      })
      .then(this.toDomain)
  }

  async delete(id: Id): Promise<boolean> {
    await this.teamModel.softDelete({
      id,
    })

    return Promise.resolve(true)
  }

  async sendInvitationEmail(userInvite: {
    to: string | string[]
    inviter: string
    organizationName: string
    subject: string
    acceptUrl: string
  }): Promise<void> {
    await this.notificationFactory.getNotificationService('email').send({
      to: userInvite.to,
      subject: userInvite.subject,
      template: EmailTemplate.USER_INVITE,
      templateData: {
        inviter: userInvite.inviter,
        organizationName: userInvite.organizationName,
        acceptUrl: userInvite.acceptUrl,
      },
    })
  }

  async createInvitation(invitation: UserInvite): Promise<UserInvite> {
    // Check if existing invitation
    const existingInvitation = await this.userInviteModel.findOne({
      filter: {
        email: invitation.getProps().email,
        team: {
          id: invitation.getProps().team.id,
        },
      },
    })

    if (existingInvitation) {
      throw new HttpException(
        `Exist invitation for email: ${invitation.getProps().email}`,
        HttpStatus.BAD_REQUEST,
      )
    }

    return await this.userInviteModel
      .create({
        ...invitation.getProps(),
        team: {
          id: invitation.getProps().team.id,
        },
        role: {
          id: invitation.getProps().role.id,
        },
      })
      .then(
        (userInvite) =>
          new UserInvite({
            id: userInvite.id,
            props: {
              email: userInvite.email,
              status: userInvite.status,
              role: userInvite.role
                ? this.toRole(userInvite.role)
                : new Role({ id: userInvite.roleId }),
              expiredAt: userInvite.expiredAt,
              team: this.toTeam(userInvite.team),
            },
          }),
      )
  }

  async findMembers(
    teamId: Id,
    query: ITypeOrmFilter<UserTeamRoleModel>,
  ): Promise<IPaginate<TeamUserRole>> {
    const userTeamRoles = await this.userTeamRoleModel.find({
      filter: {
        team: {
          id: teamId,
        },
      },
      relations: {
        user: true,
        role: true,
      },
      limit: query.limit,
      page: query.page,
      orderBy: query.orderBy,
    })

    return {
      ...userTeamRoles,
      data: userTeamRoles.data.map((teamRole) => ({
        user: this.toUser(teamRole.user),
        role: this.toRole(teamRole.role),
      })),
    }
  }

  async findAllMembers(teamId: Id): Promise<TeamUserRole[]> {
    const userTeamRoles = await this.userTeamRoleModel.find({
      filter: {
        team: {
          id: teamId,
        },
      },
      relations: {
        user: true,
      },
    })

    return userTeamRoles.data.map((teamRole) => ({
      user: this.toUser(teamRole.user),
    }))
  }

  async findInvites(
    teamId: Id,
    query: ITypeOrmFilter<UserInviteModel>,
  ): Promise<IPaginate<UserInvite>> {
    const invites = await this.userInviteModel.find({
      filter: {
        team: {
          id: teamId,
        },
      },
      relations: {
        role: true,
        team: true,
      },
      limit: query.limit,
      page: query.page,
      orderBy: query.orderBy,
    })

    return {
      ...invites,
      data: invites.data.map(this.toInvite),
    }
  }

  async removeMember(teamId: Id, userId: Id): Promise<boolean> {
    await this.userTeamRoleModel.delete({
      user: {
        id: userId,
      },
      team: {
        id: teamId,
      },
    })
    return Promise.resolve(true)
  }

  async updateMember(teamId: Id, data: UpdateTeamMember): Promise<boolean> {
    const teamMember = await this.userTeamRoleModel.findOne({
      filter: {
        team: {
          id: teamId,
        },
        user: {
          id: data.userId,
        },
      },
    })

    if (!teamMember) {
      throw new HttpException('Team member not found!', HttpStatus.BAD_REQUEST)
    }

    await this.userTeamRoleModel.update(
      {
        role: {
          id: data.roleId,
        },
      },
      {
        team: {
          id: teamId,
        },
        user: {
          id: data.userId,
        },
      },
    )

    return Promise.resolve(true)
  }

  async countTeamsByOrganizationId(organizationId: Id): Promise<number> {
    const queryBuilder =
      this.teamModel.getQueryBuilder() as SelectQueryBuilder<TeamModel>

    const count = await queryBuilder
      .withDeleted()
      .where('organization_id = :organizationId', { organizationId })
      .andWhere('deleted_at IS NULL')
      .getCount()

    return count || 0
  }
}
