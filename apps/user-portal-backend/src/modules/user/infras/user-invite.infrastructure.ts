import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import UserInviteRepository from '@backend/modules/user/applications/user-invite.repository'
import { UserInviteModel } from '@backend/frameworks/database/models/user-invite.model'
import {
  UserInvite,
  UserInviteProps,
} from '@backend/modules/user/entities/user-invite.entity'

import { Role, Team } from '../entities'

@Injectable()
export class UserInviteInfrastructure implements UserInviteRepository {
  private readonly userInviteModel: TypeORMDriver<UserInviteModel>

  constructor(
    @Inject(Database) private database: Database,
    private eventEmitter: EventEmitter2,
  ) {
    this.userInviteModel =
      this.database.typeorm<UserInviteModel>('UserInviteModel')
  }

  toDomain = (userInvite: UserInviteModel): UserInvite => {
    const { id, createdAt, updatedAt, teamId, roleId, ...props } = userInvite

    return new UserInvite({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        role: new Role({ id: roleId }),
        team: new Team({ id: teamId }),
      },
    })
  }

  async findAll(): Promise<UserInvite[]> {
    const userInvites = await this.userInviteModel.findAll()
    return userInvites.map(this.toDomain)
  }

  find(query: ITypeOrmFilter<UserInviteModel>): Promise<IPaginate<UserInvite>> {
    return this.userInviteModel.find(query).then((users) => {
      return {
        ...users,
        data: users.data.map(this.toDomain),
      }
    })
  }

  findById(
    id: Id,
    query?: ITypeOrmFilter<UserInviteModel>,
  ): Promise<Nullable<UserInvite>> {
    return this.userInviteModel.findById(id, query).then((userInvite) => {
      if (!userInvite) {
        return null
      }

      return this.toDomain(userInvite)
    })
  }

  async findOne(
    query: ITypeOrmFilter<UserInviteModel>,
  ): Promise<Nullable<UserInvite>> {
    const usr = await this.userInviteModel.findOne(query)

    if (!usr) {
      return null
    }

    return this.toDomain(usr)
  }

  async create(userInvite: UserInvite): Promise<UserInvite> {
    const userProps = userInvite.getProps()

    const newUserInvite = await this.userInviteModel
      .create({
        ...userProps,
        role: {
          id: userProps.role.id,
        },
        team: {
          id: userProps.team.id,
        },
      })
      .then(this.toDomain)

    userInvite.publishEvents(this.eventEmitter)

    return newUserInvite
  }

  async updateById(
    id: Id,
    userInvite: Partial<UserInviteProps>,
  ): Promise<UserInvite> {
    const { role, team, ...rest } = userInvite

    const invite = await this.userInviteModel.updateById(id, {
      ...rest,
      roleId: role?.id,
      teamId: team?.id,
    })

    if (!invite) {
      throw new Error('User invite not found')
    }

    return this.toDomain(invite)
  }

  async delete(id: Id): Promise<boolean> {
    await this.userInviteModel.softDelete({
      id,
    })
    return Promise.resolve(!!id)
  }
}
