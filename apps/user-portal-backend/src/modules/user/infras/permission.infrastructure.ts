import { Inject, Injectable } from '@nestjs/common'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { PermissionModel } from '@backend/frameworks/database/models/permission.model'
import Database from '@backend/frameworks/database/database'
import { Permission } from '@backend/modules/user/entities'
import PermissionRepository from '@backend/modules/user/applications/permission.repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

@Injectable()
export class PermissionInfrastructure implements PermissionRepository {
  private readonly permissionModel: TypeORMDriver<PermissionModel>

  constructor(
    @Inject(Database) private database: Database,
    // private eventEmitter: EventEmitter2,
  ) {
    this.permissionModel =
      this.database.typeorm<PermissionModel>('PermissionModel')
  }

  toDomain = (permission: PermissionModel): Permission => {
    const { id, createdAt, updatedAt, ...props } = permission

    return new Permission({
      id,
      createdAt,
      updatedAt,
      props,
    })
  }

  async createAll(permissions: Permission[]): Promise<Permission[]> {
    await this.permissionModel.createMany(
      permissions.map((permission) => permission.getProps() as any),
    )

    return permissions
  }

  async count(): Promise<number> {
    return await this.permissionModel.count()
  }

  find(query: ITypeOrmFilter<PermissionModel>): Promise<IPaginate<Permission>> {
    return this.permissionModel.find(query).then((permissions) => {
      return {
        ...permissions,
        data: permissions.data.map(this.toDomain),
      }
    })
  }

  async findAll(
    query?: ITypeOrmFilter<PermissionModel>,
  ): Promise<Permission[]> {
    const permissions = await this.permissionModel.findAll(query)

    return permissions.map(this.toDomain)
  }
}
