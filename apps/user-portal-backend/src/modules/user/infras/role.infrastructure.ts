import { Inject, Injectable } from '@nestjs/common'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import Database from '@backend/frameworks/database/database'
import {
  Organization,
  Permission,
  Role,
  RoleProps,
} from '@backend/modules/user/entities'
import RoleRepository from '@backend/modules/user/applications/role.repository'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { RoleModel } from '@backend/frameworks/database/models/role.model'
import { UserTeamRoleModel } from '@backend/frameworks/database/models/user-team-role.model'
import { Id } from '@backend/cores/base/id.type'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { OrganizationModel } from '@backend/frameworks/database/models/organization.model'
import { PermissionModel } from '@backend/frameworks/database/models/permission.model'

@Injectable()
export class RoleInfrastructure implements RoleRepository {
  private readonly roleModel: TypeORMDriver<RoleModel>
  private readonly userTeamRoleModel: TypeORMDriver<UserTeamRoleModel>

  constructor(@Inject(Database) private database: Database) {
    this.roleModel = this.database.typeorm<RoleModel>('RoleModel')
    this.userTeamRoleModel =
      this.database.typeorm<UserTeamRoleModel>('UserTeamRoleModel')
  }

  toDomain = (role: RoleModel): Role => {
    const {
      id,
      createdAt,
      updatedAt,
      permissions,
      organization,
      organizationId,
      ...props
    } = role

    return new Role({
      id,
      createdAt,
      updatedAt,
      props: {
        name: props.name,
        default: props.default,
        organization: organization
          ? this.toOrganization(organization)
          : new Organization({ id: organizationId }),
        permissions: permissions?.map(this.toPermission),
      },
    })
  }

  toOrganization = (organization: OrganizationModel) => {
    if (!organization) return undefined

    return new Organization({
      id: organization.id,
    })
  }

  toPermission = (permission: PermissionModel) => {
    return new Permission({
      id: permission.id,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt,
      props: {
        ...permission,
      },
    })
  }

  async create(data: Role): Promise<Role> {
    const roleProps = data.getProps()

    return await this.roleModel
      .create({
        ...roleProps,
        permissions: roleProps.permissions?.map((permission) =>
          permission.getProps(),
        ),
        organization: {
          id: roleProps.organization?.id,
        },
      })
      .then(this.toDomain)
  }

  findAll(query?: ITypeOrmFilter<RoleModel> | undefined): Promise<Role[]> {
    return this.roleModel.findAll(query).then((roles) => {
      return roles.map(this.toDomain)
    })
  }

  find(query: ITypeOrmFilter<RoleModel>): Promise<IPaginate<Role>> {
    return this.roleModel.find(query).then((roles) => {
      return {
        ...roles,
        data: roles.data.map(this.toDomain),
      }
    })
  }

  findById(
    id: string,
    query?: ITypeOrmFilter<RoleModel> | undefined,
  ): Promise<Nullable<Role>> {
    return this.roleModel.findById(id, query).then((role) => {
      if (!role) return null

      return this.toDomain(role)
    })
  }

  async findOne(query: ITypeOrmFilter<RoleModel>): Promise<Nullable<Role>> {
    const role = await this.roleModel.findOne(query)
    if (!role) return null

    return this.toDomain(role)
  }

  async getRolesByUserId(userId: Id): Promise<Role[]> {
    const userTeamRoles = await this.userTeamRoleModel.findAll({
      filter: {
        user: {
          id: userId,
        },
      },
      fields: {
        role: {
          permissions: true,
        },
      },
      relations: {
        role: true,
      },
    })

    return userTeamRoles.map((userTeamRole) => {
      return this.toDomain(userTeamRole.role)
    })
  }

  async updateById(
    id: string,
    data: Partial<RoleProps>,
  ): Promise<Nullable<Role>> {
    const role = await this.roleModel.updateById(id, {
      ...data,
      permissions: data.permissions?.map((permission) => permission.getProps()),
      organization: {
        id: data.organization?.id,
      },
    })

    if (!role) return null

    return this.toDomain(role)
  }

  async delete(id: Id): Promise<boolean> {
    await this.roleModel.softDelete({ id })

    return true
  }

  async createMany(roles: Role[]): Promise<Role[]> {
    const newRoles = await this.roleModel.createMany(
      roles.map((role) => {
        const roleProps = role.getProps()

        return {
          ...roleProps,
          permissions: roleProps.permissions?.map((permission) =>
            permission.getProps(),
          ),
          organization: {
            id: roleProps.organization?.id,
          },
        }
      }),
    )

    return newRoles.map(this.toDomain)
  }
}
