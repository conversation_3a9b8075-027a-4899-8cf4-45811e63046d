import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { TokenModel } from '@backend/frameworks/database/models/token.model.ts'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import TokenRepository from '@backend/modules/user/applications/token.repository'
import { Token, TokenProps } from '@backend/modules/user/entities'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

import { TokenLevel } from '../constants/token'

@Injectable()
export class TokenInfrastructure implements TokenRepository {
  private readonly tokenModel: TypeORMDriver<TokenModel>

  constructor(
    @Inject(Database) private database: Database,
    private eventEmitter: EventEmitter2,
  ) {
    this.tokenModel = this.database.typeorm<TokenModel>('TokenModel')
  }

  async findOne(query: ITypeOrmFilter<TokenModel>): Promise<Nullable<Token>> {
    const token = await this.tokenModel.findOne(query)
    if (token) {
      return this.toDomain(token)
    }
    return null
  }

  private toDomain = (token: TokenModel): Token => {
    const { id, createdAt, updatedAt, ...props } = token
    return new Token({
      id,
      createdAt,
      updatedAt,
      props: props as any,
    })
  }

  async findAll(): Promise<Token[]> {
    const tokens = await this.tokenModel.findAll()
    return tokens.map(this.toDomain)
  }

  async findAllInLevel(
    query: ITypeOrmFilter<TokenModel>,
    level: TokenLevel,
    ownerId: string,
  ): Promise<IPaginate<Token>> {
    console.log(ownerId, level)
    return this.tokenModel
      .find({
        ...query,
        filter: {
          ownerId: ownerId,
          level: level,
        },
      })
      .then((tokens) => {
        return {
          ...tokens,
          data: tokens.data.map(this.toDomain),
        }
      })
  }

  async create(token: Token): Promise<Token> {
    const newOrg = await this.tokenModel.create({
      ...token.getProps(),
    })
    return this.toDomain(newOrg)
  }

  async updateById(id: Id, token: TokenProps): Promise<Nullable<Token>> {
    const updatedOrg = await this.tokenModel.updateById(id, {
      ...token,
    })

    if (!updatedOrg) {
      return null
    }

    return this.toDomain(updatedOrg)
  }

  async delete(id: Id): Promise<boolean> {
    await this.tokenModel.softDelete({
      id,
    })

    return true
  }

  find(query: ITypeOrmFilter<TokenModel>): Promise<IPaginate<Token>> {
    return this.tokenModel.find(query).then((tokens) => {
      return {
        ...tokens,
        data: tokens.data.map(this.toDomain),
      }
    })
  }

  findById(
    id: string,
    query?: ITypeOrmFilter<TokenModel> | undefined,
  ): Promise<Nullable<Token>> {
    return this.tokenModel.findById(id, query).then((token) => {
      if (!token) {
        return null
      }

      return this.toDomain(token)
    })
  }
}
