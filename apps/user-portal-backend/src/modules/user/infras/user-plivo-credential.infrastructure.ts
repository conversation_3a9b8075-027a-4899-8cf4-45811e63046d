import { Inject, Injectable } from '@nestjs/common'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { AuthService } from '@backend/cores/auth/auth.service'
import { UserPlivoCredentialModel } from '@backend/frameworks/database/models/user-plivo-credential.model'

import UserPlivoCredentialRepository from '../applications/user-plivo-credential.repository'
import { UserPlivoCredential } from '../entities/user-plivo-credential.entity'

@Injectable()
export class UserPlivoCredentialInfrastructure
  implements UserPlivoCredentialRepository
{
  private readonly userPlivoCredentialModel: TypeORMDriver<UserPlivoCredentialModel>
  constructor(
    @Inject(Database) private database: Database,
    private readonly authService: AuthService,
  ) {
    this.userPlivoCredentialModel =
      this.database.typeorm<UserPlivoCredentialModel>(
        'UserPlivoCredentialModel',
      )
  }
  async findByUserId(userId: string): Promise<UserPlivoCredential | null> {
    const userPlivoCredential = await this.userPlivoCredentialModel.findOne({
      filter: { userId },
    })

    if (!userPlivoCredential) {
      return null
    }

    return this.toDomain(userPlivoCredential)
  }

  toDomain = (
    userPlivoCredentialModel: UserPlivoCredentialModel,
  ): UserPlivoCredential => {
    const { id, createdAt, updatedAt, ...props } = userPlivoCredentialModel
    return new UserPlivoCredential({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  async findAll(
    query?: ITypeOrmFilter<UserPlivoCredentialModel>,
  ): Promise<UserPlivoCredential[]> {
    const userPlivoCredentials =
      await this.userPlivoCredentialModel.findAll(query)
    return userPlivoCredentials.map(this.toDomain)
  }
  find(
    query: ITypeOrmFilter<UserPlivoCredentialModel>,
  ): Promise<IPaginate<UserPlivoCredential>> {
    return this.userPlivoCredentialModel
      .find(query)
      .then((userPlivoCredentials) => {
        return {
          ...userPlivoCredentials,
          data: userPlivoCredentials.data.map(this.toDomain),
        }
      })
  }
  findById(
    id: Id,
    query?: ITypeOrmFilter<UserPlivoCredentialModel>,
  ): Promise<Nullable<UserPlivoCredential>> {
    return this.userPlivoCredentialModel
      .findById(id, query)
      .then((userPlivoCredential) => {
        if (!userPlivoCredential) {
          return null
        }

        return this.toDomain(userPlivoCredential)
      })
  }
  async findOne(
    query: ITypeOrmFilter<UserPlivoCredentialModel>,
  ): Promise<Nullable<UserPlivoCredential>> {
    const userPlivoCredential =
      await this.userPlivoCredentialModel.findOne(query)

    if (!userPlivoCredential) {
      return null
    }

    return this.toDomain(userPlivoCredential)
  }
  async create(
    userPlivoCredential: UserPlivoCredential,
  ): Promise<UserPlivoCredential> {
    const userPlivoCredentialProps = userPlivoCredential.getProps()
    const newUserPlivoCredential = await this.userPlivoCredentialModel
      .create({
        ...userPlivoCredentialProps,
      })
      .then(this.toDomain)

    return newUserPlivoCredential
  }

  updateById(
    id: Id,
    data: {
      userPlivoCredentialId: string
      userId: string
      user?: { id: string } | undefined
      createdBy?: string | undefined
      updatedBy?: string | undefined
    },
  ): Promise<Nullable<UserPlivoCredential>> {
    throw new Error('Method not implemented.')
  }
  async delete(id: Id): Promise<boolean> {
    await this.userPlivoCredentialModel.softDelete({
      id,
    })
    return true
  }
}
