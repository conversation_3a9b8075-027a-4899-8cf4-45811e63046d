import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { SelectQueryBuilder } from 'typeorm'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { OrganizationModel } from '@backend/frameworks/database/models/organization.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import OrganizationRepository from '@backend/modules/user/applications/organization.repository'
import { Organization, OrganizationProps } from '@backend/modules/user/entities'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
@Injectable()
export class OrganizationInfrastructure implements OrganizationRepository {
  private readonly organizationModel: TypeORMDriver<OrganizationModel>

  constructor(
    @Inject(Database) private database: Database,
    private eventEmitter: EventEmitter2,
  ) {
    this.organizationModel =
      this.database.typeorm<OrganizationModel>('OrganizationModel')
  }

  async findOne(
    query: ITypeOrmFilter<OrganizationModel>,
  ): Promise<Nullable<Organization>> {
    const organization = await this.organizationModel.findOne(query)
    if (organization) {
      return this.toDomain(organization)
    }
    return null
  }

  private toDomain = (organization: OrganizationModel): Organization => {
    const { id, createdAt, updatedAt, ...props } = organization
    return new Organization({
      id,
      createdAt,
      updatedAt,
      props: props as any,
    })
  }

  async findAll(): Promise<Organization[]> {
    const organizations = await this.organizationModel.findAll()
    return organizations.map(this.toDomain)
  }

  async create(organization: Organization): Promise<Organization> {
    const newOrg = await this.organizationModel.create({
      ...organization.getProps(),
      teams: undefined,
    })
    return this.toDomain(newOrg)
  }

  async updateById(
    id: Id,
    organization: OrganizationProps,
  ): Promise<Nullable<Organization>> {
    const updatedOrg = await this.organizationModel.updateById(id, {
      ...organization,
      teams: undefined,
    })

    if (!updatedOrg) {
      return null
    }

    return this.toDomain(updatedOrg)
  }

  async delete(id: Id): Promise<boolean> {
    await this.organizationModel.softDelete({
      id,
    })

    return true
  }

  find(
    query: ITypeOrmFilter<OrganizationModel>,
  ): Promise<IPaginate<Organization>> {
    return this.organizationModel.find(query).then((organizations) => {
      return {
        ...organizations,
        data: organizations.data.map(this.toDomain),
      }
    })
  }

  findById(
    id: string,
    query?: ITypeOrmFilter<OrganizationModel> | undefined,
  ): Promise<Nullable<Organization>> {
    return this.organizationModel.findById(id, query).then((organization) => {
      if (!organization) {
        return null
      }

      return this.toDomain(organization)
    })
  }

  async findByUserId(userId: string): Promise<Organization[] | null> {
    const queryBuilder =
      this.organizationModel.getQueryBuilder() as SelectQueryBuilder<OrganizationModel>

    const orgs = await queryBuilder
      .withDeleted()
      .innerJoinAndSelect(
        'users_organizations',
        'users_organizations',
        'users_organizations.organizations_id = organizations.id',
      )
      .where('users_organizations.users_id = :id', { id: userId })
      .andWhere('organizations.deletedAt IS NULL')
      .getMany()

    if (!orgs) return null

    return orgs.map(this.toDomain)
  }

  async countActiveUsersByOrganizationId(organizationId: Id): Promise<number> {
    const queryBuilder =
      this.organizationModel.getQueryBuilder() as SelectQueryBuilder<OrganizationModel>

    const orgUsers = await queryBuilder
      .withDeleted()
      .innerJoin(
        'users_organizations',
        'users_organizations',
        'users_organizations.organizations_id = organizations.id',
      )
      .where('organizations.id = :organizationId', {
        organizationId: organizationId,
      })
      .getCount()

    return orgUsers || 0
  }
}
