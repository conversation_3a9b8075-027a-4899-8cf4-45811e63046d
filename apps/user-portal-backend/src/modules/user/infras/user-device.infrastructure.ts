import { Inject, Injectable } from '@nestjs/common'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import Database from '@backend/frameworks/database/database'
import { UserDeviceModel } from '@backend/frameworks/database/models/user-device'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { AuthService } from '@backend/cores/auth/auth.service'

import UserDeviceRepository from '../applications/user-device.repository'
import { UserDevice } from '../entities/user-device.entity'

@Injectable()
export class UserDeviceInfrastructure implements UserDeviceRepository {
  private readonly userDeviceModel: TypeORMDriver<UserDeviceModel>
  constructor(
    @Inject(Database) private database: Database,
    private readonly authService: AuthService,
  ) {
    this.userDeviceModel =
      this.database.typeorm<UserDeviceModel>('UserDeviceModel')
  }

  toDomain = (userDeviceModel: UserDeviceModel): UserDevice => {
    const { id, createdAt, updatedAt, ...props } = userDeviceModel
    return new UserDevice({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
      },
    })
  }

  async findAll(
    query?: ITypeOrmFilter<UserDeviceModel>,
  ): Promise<UserDevice[]> {
    const userDevices = await this.userDeviceModel.findAll(query)
    return userDevices.map(this.toDomain)
  }
  find(query: ITypeOrmFilter<UserDeviceModel>): Promise<IPaginate<UserDevice>> {
    return this.userDeviceModel.find(query).then((userDevices) => {
      return {
        ...userDevices,
        data: userDevices.data.map(this.toDomain),
      }
    })
  }
  findById(
    id: Id,
    query?: ITypeOrmFilter<UserDeviceModel>,
  ): Promise<Nullable<UserDevice>> {
    return this.userDeviceModel.findById(id, query).then((userDevice) => {
      if (!userDevice) {
        return null
      }

      return this.toDomain(userDevice)
    })
  }
  async findOne(
    query: ITypeOrmFilter<UserDeviceModel>,
  ): Promise<Nullable<UserDevice>> {
    const userDevice = await this.userDeviceModel.findOne(query)

    if (!userDevice) {
      return null
    }

    return this.toDomain(userDevice)
  }
  async create(userDevice: UserDevice): Promise<UserDevice> {
    const userDeviceProps = userDevice.getProps()
    const newUserDevice = await this.userDeviceModel
      .create({
        ...userDeviceProps,
      })
      .then(this.toDomain)

    return newUserDevice
  }
  updateById(
    id: Id,
    data: {
      userDeviceId: string
      deviceId: string
      deviceName: string
      deviceType: string
      userId: string
      user?: { id: string } | undefined
      createdBy?: string | undefined
      updatedBy?: string | undefined
    },
  ): Promise<Nullable<UserDevice>> {
    throw new Error('Method not implemented.')
  }
  async delete(id: Id): Promise<boolean> {
    await this.userDeviceModel.softDelete({
      id,
    })
    return true
  }
}
