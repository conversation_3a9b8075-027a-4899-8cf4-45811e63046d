import { Inject, Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { pick } from 'lodash'
import { SelectQueryBuilder } from 'typeorm'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { UserModel } from '@backend/frameworks/database/models/user.model'
import { UserTeamRoleModel } from '@backend/frameworks/database/models/user-team-role.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import {
  Organization,
  Permission,
  Role,
  Team,
} from '@backend/modules/user/entities'
import { OrganizationModel } from '@backend/frameworks/database/models/organization.model'
import { RoleModel } from '@backend/frameworks/database/models/role.model'
import { TeamModel } from '@backend/frameworks/database/models/team.model'
import { OnCallSchedulerModel } from '@backend/frameworks/database/models/on-call-scheduler.model'
import { OnCallScheduler } from '@backend/modules/oncall/entities/on-call-scheduler.entity'

import UserRepository from '../applications/users.repository'
import { User, UserProps, UserTeamRole } from '../entities/user.entity'

const CACHE_KEYS = {
  userAuth: (id: Id) => `user:auth:${id}`,
}

@Injectable()
export class UserInfrastructure implements UserRepository {
  private readonly userModel: TypeORMDriver<UserModel>
  private readonly userTeamRoleModel: TypeORMDriver<UserTeamRoleModel>

  constructor(
    @Inject(Database) private database: Database,
    private eventEmitter: EventEmitter2,
  ) {
    this.userModel = this.database.typeorm<UserModel>('UserModel')
    this.userTeamRoleModel =
      this.database.typeorm<UserTeamRoleModel>('UserTeamRoleModel')
  }

  toDomain = (user: UserModel): User => {
    const { id, createdAt, updatedAt, organizations, userTeamRoles, ...props } =
      user

    return new User({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        organizations: organizations?.map(this.toOrganization),
        userTeamRoles: userTeamRoles?.map(this.toTeamRole),
        onCallSchedulers: user.onCallSchedulers?.map(this.toScheduler),
      },
    })
  }

  toOrganization = (organization: OrganizationModel): Organization => {
    return new Organization({
      id: organization.id,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,
      props: {
        name: organization.name,
        ownerId: organization.ownerId,
        teams: organization.teams?.map(this.toTeam),
      },
    })
  }

  toTeam = (team: TeamModel) => {
    return new Team({
      id: team.id,
      createdAt: team.createdAt,
      updatedAt: team.updatedAt,
      props: {
        name: team.name,
        ...(team?.organization && {
          organization: this.toOrganization(team.organization),
        }),
      },
    })
  }

  toTeamRole = (team: UserTeamRoleModel) => {
    return {
      team: team.team ? this.toTeam(team.team) : new Team({ id: team.teamId }),
      role: team.role ? this.toRole(team.role) : new Role({ id: team.roleId }),
    }
  }

  toRole = (role: RoleModel): Role => {
    return new Role({
      id: role.id,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
      props: {
        name: role.name,
        default: role.default,
        permissions: role.permissions?.map(
          (permission) =>
            new Permission({
              id: permission.id,
              createdAt: permission.createdAt,
              updatedAt: permission.updatedAt,
              props: pick(permission, ['scope', 'action', 'type']),
            }),
        ),
      },
    })
  }

  toScheduler = (scheduler: OnCallSchedulerModel): OnCallScheduler => {
    return new OnCallScheduler({
      id: scheduler.id,
      createdAt: scheduler.createdAt,
      updatedAt: scheduler.updatedAt,
    })
  }

  async findAll(query: ITypeOrmFilter<UserModel>): Promise<User[]> {
    const users = await this.userModel.findAll(query)
    return users.map(this.toDomain)
  }

  find(query: ITypeOrmFilter<UserModel>): Promise<IPaginate<User>> {
    return this.userModel.find(query).then((users) => {
      return {
        ...users,
        data: users.data.map(this.toDomain),
      }
    })
  }

  findById(id: Id, query?: ITypeOrmFilter<UserModel>): Promise<Nullable<User>> {
    return this.userModel.findById(id, query).then((user) => {
      if (!user) {
        return null
      }

      return this.toDomain(user)
    })
  }

  async findOne(query: ITypeOrmFilter<UserModel>): Promise<Nullable<User>> {
    const usr = await this.userModel.findOne(query)

    if (!usr) {
      return null
    }

    return this.toDomain(usr)
  }

  async create(user: User): Promise<User> {
    const userProps = user.getProps()

    const newUser = await this.userModel
      .create({
        ...userProps,
        organizations: userProps.organizations?.map((organization) => ({
          id: organization.id,
        })),
        userTeamRoles: userProps.userTeamRoles?.map((userTeamRole) => ({
          user: {
            id: user.id,
          },
          team: {
            id: userTeamRole.team.id,
          },
          role: {
            id: userTeamRole.role.id,
          },
        })),
        onCallSchedulers: userProps.onCallSchedulers?.map(
          (onCallScheduler) => ({
            id: onCallScheduler.id,
          }),
        ),
      })
      .then(this.toDomain)

    user.publishEvents(this.eventEmitter)

    return newUser
  }

  async updateById(id: Id, user: Partial<UserProps>): Promise<User> {
    return this.userModel
      .updateById(id, {
        ...user,
        organizations: user.organizations?.map((organization) => ({
          id: organization.id,
        })),
        userTeamRoles: user.userTeamRoles?.map((userTeamRole) => ({
          user: {
            id,
          },
          team: {
            id: userTeamRole.team.id,
          },
          role: {
            id: userTeamRole.role.id,
          },
        })),
        onCallSchedulers: user.onCallSchedulers?.map((onCallScheduler) => ({
          id: onCallScheduler.id,
        })),
      })
      .then(this.toDomain)
  }

  delete(id: Id): Promise<boolean> {
    return Promise.resolve(!!id)
  }

  async assignRole(user: User, userTeamRole: UserTeamRole) {
    await this.userModel.invalidateQueryCache(CACHE_KEYS.userAuth(user.id))

    await this.userTeamRoleModel.create({
      user: {
        id: user.id,
      },
      team: {
        id: userTeamRole.team.id,
      },
      role: {
        id: userTeamRole.role.id,
      },
    })
  }

  async getCachedUserAuth(userId: Id): Promise<Nullable<User>> {
    const queryBuilder =
      this.userModel.getQueryBuilder() as SelectQueryBuilder<UserModel>

    const user = await queryBuilder
      .withDeleted()
      .innerJoinAndSelect('users.organizations', 'organizations')
      .innerJoinAndSelect('organizations.teams', 'teams')
      .innerJoinAndSelect('users.userTeamRoles', 'userTeamRoles')
      .innerJoinAndSelect('userTeamRoles.role', 'roles')
      .innerJoinAndSelect('roles.permissions', 'permissions')
      .where('users.id = :id', { id: userId })
      .andWhere('teams.deletedAt IS NULL')
      .andWhere('roles.deletedAt IS NULL')
      .andWhere('permissions.deletedAt IS NULL')
      .andWhere('organizations.deletedAt IS NULL')
      .getOne()

    if (!user) return null

    return this.toDomain(user)
  }
}
