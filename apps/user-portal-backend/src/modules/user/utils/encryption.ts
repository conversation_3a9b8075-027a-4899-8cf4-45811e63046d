import { configService } from '@backend/cores/config/config.service'

import { promisify } from 'util'
import { createCipheriv, createDecipheriv, scrypt } from 'crypto'

export const ENCRYPTION_KEY = {
  USER_INVITE: {
    KEY: configService.getValue('USER_INVITE_SECRET_KEY'),
    IV: configService.getValue('USER_INVITE_SECRET_IV'),
  },
}
export const encrypt = async (
  encryptKey: keyof typeof ENCRYPTION_KEY,
  data: any,
) => {
  const key = (await promisify(scrypt)(
    ENCRYPTION_KEY[encryptKey].KEY,
    'salt',
    32,
  )) as Buffer
  const cipher = createCipheriv(
    'aes-256-ctr',
    key,
    ENCRYPTION_KEY[encryptKey].IV,
  )
  return cipher.update(data, 'utf-8', 'hex')
  // return cipher.final('hex')
}

export const decrypt = async (
  encryptKey: keyof typeof ENCRYPTION_KEY,
  encryptedData: any,
) => {
  const decipher = createDecipheriv(
    'aes-256-ctr',
    ENCRYPTION_KEY[encryptKey].KEY,
    ENCRYPTION_KEY[encryptKey].IV,
  )
  decipher.update(encryptedData)
  return decipher.final('hex')
}
