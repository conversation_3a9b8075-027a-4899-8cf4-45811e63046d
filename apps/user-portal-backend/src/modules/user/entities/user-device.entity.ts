import { z } from 'zod'

import { generateId } from '@backend/commons/id'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

export const UserDeviceSchema = z.object({
  deviceId: z.string(),
  deviceName: z.string(),
  deviceType: z.string(),
  userId: z.string(),
  user: z
    .object({
      id: z.string(),
    })
    .optional(),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export type UserDeviceProps = z.infer<typeof UserDeviceSchema>

export const UserDeviceResponse = UserDeviceSchema.partial().extend({
  id: z.string().optional(),
  deviceId: z.string().optional(),
  deviceName: z.string().optional(),
  deviceType: z.string().optional(),
  updatedAt: z.date().optional(),
  createdAt: z.date().optional(),
  userId: z.string(),
  user: z.object({
    id: z.string().optional(),
  }),
})

export type UserDeviceResponse = z.infer<typeof UserDeviceResponse>

export class UserDevice extends AggregateRoot<
  UserDeviceProps,
  UserDeviceResponse
> {
  static create(props: UserDeviceProps) {
    const userDevice = new UserDevice({
      id: generateId(),
      props: {
        ...props,
        createdBy: props.createdBy,
        updatedBy: props.updatedBy,
      },
    })

    return userDevice
  }

  public toResponse(): UserDeviceResponse {
    const props = this.getProps()

    return {
      id: props.id,
      userId: props.userId,
      user: {
        id: props.user?.id,
      },
      deviceId: props.deviceId,
      deviceName: props.deviceName,
      deviceType: props.deviceType,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    }
  }
}
