import moment from 'moment'

import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { UserCreatedDomainEvent } from '@backend/modules/user/entities/events/user-created.event'

import { Team, TeamResponse } from './team.entity'
import { Role, RoleResponse } from './role.entity'

export enum UserInviteStatus {
  PENDING = 'pending',
  RESENT = 'resent',
  ACCEPTED = 'accepted',
}

export interface UserInviteProps {
  email: string
  status: string
  role: Role
  team: Team
  expiredAt: Date
}

export interface UserInviteResponse {
  id: string
  team?: TeamResponse
  email?: string
  role?: RoleResponse
  status?: string
  createdAt?: Date
  updatedAt?: Date
}

export class UserInvite extends AggregateRoot<
  UserInviteProps,
  UserInviteResponse
> {
  static create(props: UserInviteProps) {
    const userInvite = new UserInvite({
      id: generateId(),
      props,
    })

    userInvite.addEvent(
      new UserCreatedDomainEvent({
        aggregateId: userInvite.id,
      }),
    )

    return userInvite
  }

  resentInvite() {
    this.props.status = UserInviteStatus.RESENT
    this.props.expiredAt = moment().add(3, 'days').toDate()

    return this
  }

  get email() {
    return this.props.email
  }

  public toResponse(): UserInviteResponse {
    const userInviteProps = this.getProps()

    return {
      id: this.id,
      email: userInviteProps.email,
      status: userInviteProps.status,
      role: userInviteProps.role?.toResponse(),
      team: userInviteProps.team?.toResponse(),
    }
  }
}
