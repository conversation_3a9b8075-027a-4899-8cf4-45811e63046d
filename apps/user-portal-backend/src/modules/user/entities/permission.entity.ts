import { Id } from '@backend/cores/base/id.type'
import { generateId } from '@backend/commons/id'
import { BaseEntity } from '@backend/cores/base/entity.base'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'

export interface PermissionProps {
  scope: PermissionScope
  action: PermissionAction
  type: PermissionType
}

export interface PermissionResponse {
  id: Id
  scope?: PermissionScope
  action?: PermissionAction
  type?: PermissionType
  createdAt?: Date
  updatedAt?: Date
}

export class Permission extends BaseEntity<
  PermissionProps,
  PermissionResponse
> {
  static create(props: PermissionProps) {
    const permission = new Permission({
      id: generateId(),
      props,
    })

    return permission
  }

  public toResponse(): PermissionResponse {
    return {
      id: this.id,
      scope: this.props.scope,
      action: this.props.action,
      type: this.props.type,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
