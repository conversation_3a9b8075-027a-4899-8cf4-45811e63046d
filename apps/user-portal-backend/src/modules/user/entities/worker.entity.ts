import { SubscriptionPlan } from '@libs/shared/constants/check.enum'

import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { Id } from '@backend/cores/base/id.type'

import { Region } from './region.entity'

export interface WorkerProps {
  ip: string
  location: string
  type: SubscriptionPlan
  region: Region
}

export interface WorkerResponse {
  id: Id
  ip: string
  location: string
  type: SubscriptionPlan
  region: Region
  createdAt?: Date
  updatedAt?: Date
}

export class Worker extends AggregateRoot<WorkerProps, WorkerResponse> {
  static create(id: Id, props: WorkerProps) {
    const worker = new Worker({
      id: id,
      props,
    })

    return worker
  }

  public toResponse(): WorkerResponse {
    const props = this.getProps()

    return {
      id: this.id,
      ip: props.ip,
      location: props.location,
      type: props.type,
      region: props.region,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
