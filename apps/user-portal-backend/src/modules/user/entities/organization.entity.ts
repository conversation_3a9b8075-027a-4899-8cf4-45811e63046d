import { Id } from '@backend/cores/base/id.type'
import { generateId } from '@backend/commons/id'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

import { OrganizationCreatedDomainEvent } from './events/organization-created.event'
import { Team, TeamResponse } from './team.entity'

export interface OrganizationProps {
  name: string
  ownerId?: string
  teams: Team[]
}

export interface OrganizationResponse {
  id: Id
  name?: string
  ownerId?: string
  teams?: TeamResponse[]
  createdAt?: Date
  updatedAt?: Date
}

export class Organization extends AggregateRoot<
  OrganizationProps,
  OrganizationResponse
> {
  static create(props: OrganizationProps) {
    const organization = new Organization({
      id: generateId(),
      props,
    })

    organization.addEvent(
      new OrganizationCreatedDomainEvent({
        aggregateId: organization.id,
      }),
    )

    return organization
  }

  static update({ id, props }: { id: Id; props: OrganizationProps }) {
    return new Organization({
      id,
      props,
    })
  }

  get teams() {
    return this.props.teams
  }

  public toResponse(): OrganizationResponse {
    return {
      id: this.id,
      name: this.props.name,
      ownerId: this.props.ownerId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      teams: this.props.teams?.map((team) => team.toResponse()),
    }
  }
}
