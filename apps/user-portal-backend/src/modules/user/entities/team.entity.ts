import { Id } from '@backend/cores/base/id.type'
import { generateId } from '@backend/commons/id'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import {
  UserInvite,
  UserInviteResponse,
} from '@backend/modules/user/entities/user-invite.entity'

import { User, UserResponse } from './user.entity'
import { Organization, OrganizationResponse } from './organization.entity'
import { Role, RoleResponse } from './role.entity'

export interface TeamUserRole {
  user?: User
  role?: Role
}

export interface TeamProps {
  name: string
  organization?: Organization
  createdBy?: Id
  updatedBy?: Id
  members?: TeamUserRole[]
  invites?: UserInvite[]
}

export interface TeamResponse {
  id: Id
  name?: string
  createdAt?: Date
  updatedAt?: Date
  organization?: OrganizationResponse
  members?: {
    user?: UserResponse
    role?: RoleResponse
  }[]
  invites?: UserInviteResponse[]
  createdBy?: Id
  updatedBy?: Id
  totalMembers?: number
}

export class Team extends AggregateRoot<TeamProps, TeamResponse> {
  static create(props: TeamProps) {
    const team = new Team({
      id: generateId(),
      props,
    })

    return team
  }

  get members() {
    return this.props.members
  }

  get organization() {
    return this.props.organization
  }

  createInvitation({
    teamId,
    email,
    status,
    role,
    expiredAt,
  }: {
    teamId: Id
    email: string
    status: string
    role: string
    expiredAt: Date
    createdBy: Id
  }) {
    const invitation: UserInvite = UserInvite.create({
      team: new Team({ id: teamId }),
      email,
      status,
      role: new Role({ id: role }),
      expiredAt,
    })

    this.props.invites?.push(invitation)

    return invitation
  }

  public toResponse(): TeamResponse {
    const teamProps = this.getProps()

    return {
      id: this.id,
      name: teamProps.name,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      organization: teamProps.organization?.toResponse(),
      members: teamProps.members?.map((member) => ({
        user: member.user?.toResponse(),
        role: member.role?.toResponse(),
      })),
      invites: teamProps.invites?.map((invite) => invite.toResponse()),
      totalMembers: teamProps.members?.length,
      createdBy: teamProps.createdBy,
      updatedBy: teamProps.updatedBy,
    }
  }
}
