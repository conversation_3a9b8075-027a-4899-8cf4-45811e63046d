import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { OnCallScheduler } from '@backend/modules/oncall/entities/on-call-scheduler.entity'

import { InternalRole } from '../constants/user'

import { UserCreatedDomainEvent } from './events/user-created.event'
import { Organization, OrganizationResponse } from './organization.entity'
import { Team, TeamResponse } from './team.entity'
import { Role, RoleResponse } from './role.entity'

export interface UserTeamRole {
  team: Team
  role: Role
}

export interface UserProps {
  firebaseId?: string
  email?: string
  phoneNumber?: string
  firstName?: string
  lastName?: string
  lastLoginAt?: Date
  lastLoginMethod?: string
  internalRole?: InternalRole
  organizations?: Organization[]
  userTeamRoles?: UserTeamRole[]
  onCallSchedulers?: OnCallScheduler[]
  stripeCustomerId?: string // Added for Stripe integration
}

export interface UserResponse {
  id: Id
  firebaseId?: string
  email?: string
  phoneNumber?: string
  firstName?: string
  lastName?: string
  lastLoginAt?: Date
  lastLoginMethod?: string
  internalRole?: InternalRole
  createdAt?: Date
  updatedAt?: Date
  organizations?: OrganizationResponse[]
  teams?: {
    team: TeamResponse
    role: RoleResponse
  }[]
}

export class User extends AggregateRoot<UserProps, UserResponse> {
  static create(props: UserProps) {
    const user = new User({
      id: generateId(),
      props,
    })

    user.addEvent(
      new UserCreatedDomainEvent({
        aggregateId: user.id,
      }),
    )

    return user
  }

  static update({ id, props }: { id: Id; props: UserProps }) {
    const user = new User({
      id: id,
      props,
    })

    return user
  }

  get fullName() {
    if (!this.props.firstName && !this.props.lastName) return ''

    return `${this.props.firstName} ${this.props.lastName}`
  }

  assignToOrganization(organization: Organization) {
    if (!this.props.organizations) {
      this.props.organizations = []
    }

    this.props.organizations.push(organization)
  }

  public toResponse(): UserResponse {
    const userProps = this.getProps()

    return {
      id: userProps.id,
      firebaseId: userProps.firebaseId,
      email: userProps.email,
      phoneNumber: userProps.phoneNumber,
      firstName: userProps.firstName,
      lastName: userProps.lastName,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      lastLoginAt: userProps.lastLoginAt,
      lastLoginMethod: userProps.lastLoginMethod,
      internalRole: userProps.internalRole,
      organizations: userProps.organizations?.map((org) => org.toResponse()),
      teams: userProps.userTeamRoles?.map((userTeamRole) => ({
        team: userTeamRole.team.toResponse(),
        role: userTeamRole.role.toResponse(),
      })),
    }
  }
}
