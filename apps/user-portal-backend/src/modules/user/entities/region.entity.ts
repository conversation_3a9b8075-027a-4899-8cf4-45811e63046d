import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { Id } from '@backend/cores/base/id.type'

export interface RegionProps {
  ip: string
  name: string
}

export interface RegionResponse {
  id: Id
  name: string
  createdAt?: Date
  updatedAt?: Date
}

export class Region extends AggregateRoot<RegionProps, RegionResponse> {
  static create(id: Id, props: RegionProps) {
    const worker = new Region({
      id: id,
      props,
    })

    return worker
  }

  public toResponse(): RegionResponse {
    const props = this.getProps()

    return {
      id: this.id,
      name: props.name,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
