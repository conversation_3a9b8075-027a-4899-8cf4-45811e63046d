import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { BaseEntity } from '@backend/cores/base/entity.base'

import { Permission, PermissionResponse } from './permission.entity'
import { Organization, OrganizationResponse } from './organization.entity'

export interface RoleProps {
  name: string
  permissions: Permission[]
  default?: boolean
  organization?: Organization
  createdBy?: Id
  updatedBy?: Id
}

export interface RoleResponse {
  id: Id
  name?: string
  default?: boolean
  permissions: PermissionResponse[]
  organization?: OrganizationResponse
  createdAt?: Date
  updatedAt?: Date
  createdBy?: Id
  updatedBy?: Id
}

export class Role extends BaseEntity<RoleProps, RoleResponse> {
  static create(props: RoleProps) {
    return new Role({
      id: generateId(),
      props,
    })
  }

  static generateRole({
    organizationId,
    roleName,
    permissions,
    isDefault,
  }: {
    organizationId: Id
    roleName: string
    permissions: Permission[]
    isDefault: boolean
  }): Role {
    return Role.create({
      name: roleName,
      permissions,
      organization: new Organization({ id: organizationId }),
      default: isDefault,
    })
  }

  updateRole(props: Partial<RoleProps> & { rolePermissions: Id[] }) {
    if (props.name) this.props.name = props.name
    this.props.permissions = props.rolePermissions.map(
      (permissionId) => new Permission({ id: permissionId }),
    )

    return this
  }

  get name() {
    return this.props.name
  }

  get default() {
    return this.props.default
  }

  get permissions() {
    return this.props.permissions?.map((permission) => ({
      id: permission.id,
      action: permission.getProps().action,
      scope: permission.getProps().scope,
      type: permission.getProps().type,
    }))
  }

  public toResponse(): RoleResponse {
    const props = this.getProps()
    return {
      ...props,
      permissions: props.permissions?.map((permission) =>
        permission.toResponse(),
      ),
      organization: props.organization?.toResponse(),
    }
  }
}
