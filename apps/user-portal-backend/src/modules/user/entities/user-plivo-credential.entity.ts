import { z } from 'zod'

import { generateId } from '@backend/commons/id'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

export const UserPlivoCredentialSchema = z.object({
  username: z.string(),
  password: z.string(),
  alias: z.string(),
  userId: z.string(),
  user: z
    .object({
      id: z.string(),
    })
    .optional(),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
})

export type UserPlivoCredentialProps = z.infer<typeof UserPlivoCredentialSchema>

export const UserPlivoCredentialResponse =
  UserPlivoCredentialSchema.partial().extend({
    id: z.string().optional(),
    username: z.string().optional(),
    password: z.string().optional(),
    alias: z.string().optional(),
    updatedAt: z.date().optional(),
    createdAt: z.date().optional(),
    userId: z.string(),
    user: z.object({
      id: z.string().optional(),
    }),
  })

export type UserPlivoCredentialResponse = z.infer<
  typeof UserPlivoCredentialResponse
>

export class UserPlivoCredential extends AggregateRoot<
  UserPlivoCredentialProps,
  UserPlivoCredentialResponse
> {
  static create(props: UserPlivoCredentialProps) {
    const userPlivoCredential = new UserPlivoCredential({
      id: generateId(),
      props: {
        ...props,
        createdBy: props.createdBy,
        updatedBy: props.updatedBy,
      },
    })

    return userPlivoCredential
  }

  public toResponse(): UserPlivoCredentialResponse {
    const props = this.getProps()

    return {
      id: props.id,
      userId: props.userId,
      user: {
        id: props.user?.id,
      },
      username: props.username,
      password: props.password,
      alias: props.alias,
      createdBy: props.createdBy,
      updatedBy: props.updatedBy,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    }
  }
}
