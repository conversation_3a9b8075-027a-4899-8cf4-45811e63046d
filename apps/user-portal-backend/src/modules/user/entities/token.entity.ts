import { Id } from '@backend/cores/base/id.type'
import { generateId } from '@backend/commons/id'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

import { TokenLevel } from '../constants/token'
// import { TokenCreatedDomainEvent } from './events/token-created.event'

export interface TokenProps {
  name: string
  level: TokenLevel
  ownerId: string
  createdBy?: Id
  updatedBy?: Id
}

export interface TokenResponse {
  id: Id
  name: string
  level: TokenLevel
  ownerId: string
  createdBy?: Id
  updatedBy?: Id
  createdAt?: Date
  updatedAt?: Date
}

export class Token extends AggregateRoot<TokenProps, TokenResponse> {
  static create(props: TokenProps) {
    const token = new Token({
      id: generateId(),
      props,
    })

    // token.addEvent(
    //   new TokenCreatedDomainEvent({
    //     aggregateId: token.id,
    //   }),
    // )

    return token
  }

  static update({ id, props }: { id: Id; props: TokenProps }) {
    return new Token({
      id,
      props,
    })
  }

  get ownerId() {
    return this.props.ownerId
  }

  public toResponse(): TokenResponse {
    return {
      id: this.id,
      ownerId: this.ownerId,
      name: this.props.name,
      level: this.props.level,
      createdBy: this.props.createdBy,
      updatedBy: this.props.updatedBy,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    }
  }
}
