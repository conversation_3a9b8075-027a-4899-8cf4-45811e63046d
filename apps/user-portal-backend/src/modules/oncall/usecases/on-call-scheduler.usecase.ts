import { orderBy } from 'lodash'
import { Inject, Injectable } from '@nestjs/common'

import { Id } from '@backend/cores/base/id.type'
import OnCallSchedulerRepository, {
  ON_CALL_SCHEDULER_REPOSITORY,
} from '@backend/modules/oncall/applications/on-call-scheduler.repository'
import {
  CreateOnCallSchedulerDto,
  FindOnCallSchedulerDto,
  OnCallSchedulerFilterDto,
  UpdateOnCallSchedulerDto,
} from '@backend/modules/oncall/applications/dto/on-call-scheduler.request.dto'
import { OnCallSchedulerResponseDto } from '@backend/modules/oncall/applications/dto/on-call-scheduler.response.dto'
import {
  OnCallScheduler,
  OnCallSchedulerResponse,
} from '@backend/modules/oncall/entities/on-call-scheduler.entity'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { Team, UserResponse } from '@backend/modules/user/entities'
import UserRepository, {
  USER_REPOSITORY,
} from '@backend/modules/user/applications/users.repository'

@Injectable()
export class OnCallSchedulerUseCase {
  constructor(
    @Inject(ON_CALL_SCHEDULER_REPOSITORY)
    private readonly repo: OnCallSchedulerRepository,
    @Inject(USER_REPOSITORY)
    private readonly userRepo: UserRepository,
  ) {}

  async create(
    createUserDto: CreateOnCallSchedulerDto,
    user: JwtPayload,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<OnCallSchedulerResponseDto | null> {
    const onCallSchedulerData = OnCallScheduler.create({
      ...createUserDto,
      createdBy: user?.userId,
      updatedBy: user?.userId,
      team: new Team({ id: orgTeamParams.teamId }),
    })

    const scheduler = await this.repo.create(onCallSchedulerData)

    if (createUserDto.memberIds) {
      for (const userId of createUserDto.memberIds) {
        const userDetail = await this.userRepo.findById(userId, {
          relations: {
            onCallSchedulers: true,
          },
        })

        if (userDetail) {
          const schedulers = userDetail.getProps().onCallSchedulers || []
          schedulers.push(scheduler)

          await this.userRepo.updateById(userId, {
            onCallSchedulers: schedulers,
          })
        }
      }
    }

    return await this.findOne(scheduler.id)
  }

  async findAll(
    onCallSchedulerFilterDto: OnCallSchedulerFilterDto,
    orgTeamParams: OrgTeamHeadersDto,
  ) {
    let allOnCallSchedulers: OnCallScheduler[] = []
    if (
      onCallSchedulerFilterDto.startDate &&
      onCallSchedulerFilterDto.endDate
    ) {
      allOnCallSchedulers = await this.repo.getOnCallSchedulerByDateRange(
        orgTeamParams?.teamId || '',
        onCallSchedulerFilterDto.startDate,
        onCallSchedulerFilterDto.endDate,
      )
    } else {
      allOnCallSchedulers = await this.repo.findAll({
        filter: {
          teamId: orgTeamParams?.teamId,
        },
        relations: {
          users: true,
          team: true,
        },
      })
    }

    return allOnCallSchedulers.map((onCallScheduler) => {
      return onCallScheduler.toResponse()
    })
  }

  async findOne(id: Id) {
    const onCallScheduler = await this.repo.findById(id, {
      relations: {
        users: true,
        team: true,
      },
    })

    if (!onCallScheduler) return null

    return onCallScheduler?.toResponse()
  }

  async update(id: Id, updateOnCallSchedulerDto: UpdateOnCallSchedulerDto) {
    // End current scheduler
    const currentOnCallScheduler = await this.repo.findById(id, {
      relations: {
        users: true,
        team: true,
      },
    })

    await this.repo.updateById(id, {
      ...currentOnCallScheduler?.getProps(),
      ...updateOnCallSchedulerDto,
    })

    return await this.findOne(id)
  }

  remove(id: Id) {
    return this.repo.delete(id)
  }

  async getOnCallMembers(teamId: Id): Promise<UserResponse[]> {
    const currentOnCallSchedulers =
      await this.repo.getCurrentOnCallSchedulers(teamId)
    return (
      currentOnCallSchedulers
        ?.map((scheduler) => scheduler.getProps().users || [])
        ?.flat()
        ?.map((user) => user.toResponse()) || []
    )
  }

  async getOnCallSchedulers(
    teamId: Id,
    options: FindOnCallSchedulerDto,
  ): Promise<OnCallSchedulerResponse[]> {
    const currentOnCallSchedulers =
      await this.repo.getOnCallSchedulerByDateRange(
        teamId,
        options.startDate,
        options.endDate,
      )
    return orderBy(
      currentOnCallSchedulers
        ?.map((scheduler) =>
          scheduler.toSchedulers({
            date: options.startDate,
            toDate: options.endDate,
          }),
        )
        .flat(),
      ['startDate'],
      ['asc'],
    )
  }
}
