import { Inject, Injectable } from '@nestjs/common'
import { SelectQueryBuilder } from 'typeorm'

import TypeORMDriver from '@backend/frameworks/database/drivers/typeorm/typeorm.database'
import { UserModel } from '@backend/frameworks/database/models/user.model'
import Database from '@backend/frameworks/database/database'
import { Id } from '@backend/cores/base/id.type'
import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { Team, User } from '@backend/modules/user/entities'
import OnCallSchedulerRepository from '@backend/modules/oncall/applications/on-call-scheduler.repository'
import { OnCallSchedulerModel } from '@backend/frameworks/database/models/on-call-scheduler.model'
import {
  OnCallScheduler,
  OnCallSchedulerProps,
} from '@backend/modules/oncall/entities/on-call-scheduler.entity'

@Injectable()
export class OnCallSchedulerInfrastructure
  implements OnCallSchedulerRepository
{
  private readonly onCallSchedulerModel: TypeORMDriver<OnCallSchedulerModel>

  constructor(@Inject(Database) private database: Database) {
    this.onCallSchedulerModel = this.database.typeorm<OnCallSchedulerModel>(
      'OnCallSchedulerModel',
    )
  }

  toDomain = (user: Nullable<OnCallSchedulerModel>): OnCallScheduler => {
    if (!user) {
      return new OnCallScheduler()
    }

    const { id, createdAt, updatedAt, team, users, ...props } = user

    return new OnCallScheduler({
      id,
      createdAt,
      updatedAt,
      props: {
        ...props,
        team: new Team({ id: team.id }),
        users: users?.map(this.toMember),
      },
    })
  }

  toMember = (user: UserModel): User => {
    return new User({
      id: user.id,
      props: {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber,
      },
    })
  }

  async getOnCallSchedulerByDateRange(
    teamId: string,
    startDate: Date,
    endDate?: Date,
  ): Promise<OnCallScheduler[]> {
    const queryBuilder =
      this.onCallSchedulerModel.getQueryBuilder() as SelectQueryBuilder<OnCallSchedulerModel>

    const query = queryBuilder
      .leftJoinAndSelect('on_call_schedulers.users', 'users')
      .leftJoinAndSelect('on_call_schedulers.team', 'team')
      .where('on_call_schedulers.teamId = :teamId', {
        teamId: teamId,
      })
      .andWhere(
        `((on_call_schedulers.type = :recurringType AND
            EXISTS (
              SELECT 1
              FROM _rrule.get_scheduler_occurrences( -- Corrected schema to _rrule
                on_call_schedulers.recurring_pattern::TEXT,
                on_call_schedulers.start_date::TIMESTAMP,
                -- 'between' range for occurrence start times:
                tsrange(
                  :startDate::TIMESTAMP - (on_call_schedulers.end_date - on_call_schedulers.start_date), -- Potential start of an overlapping occurrence
                  :endDate::TIMESTAMP  -- An occurrence must start before :endDate to overlap
                )
              ) AS occ(occurrence_dt) -- Alias the output column of the set-returning function
              WHERE
                -- Check if the full interval of the found occurrence overlaps with the query range
                tsrange(
                  occ.occurrence_dt,
                  occ.occurrence_dt + (on_call_schedulers.end_date - on_call_schedulers.start_date) -- Calculate end of this specific occurrence
                ) && tsrange(:startDate::TIMESTAMP, :endDate::TIMESTAMP)
            )
          ) OR
          (on_call_schedulers.type = :dateRangeType AND
            tsrange(on_call_schedulers.start_date::TIMESTAMP, on_call_schedulers.end_date::TIMESTAMP) -- Use tsrange
            &&
            tsrange(:startDate::TIMESTAMP, :endDate::TIMESTAMP) -- Use tsrange and &&
          ))`,
        {
          recurringType: 'recurring',
          dateRangeType: 'date_range',
          startDate: startDate,
          endDate: endDate,
        },
      )

    const [schedulers, total] = await query.getManyAndCount()
    return schedulers.map(this.toDomain)
  }

  async findAll(
    query: ITypeOrmFilter<OnCallSchedulerModel>,
  ): Promise<OnCallScheduler[]> {
    const onCallSchedulers = await this.onCallSchedulerModel.findAll(query)
    return onCallSchedulers.map(this.toDomain)
  }

  find(
    query: ITypeOrmFilter<OnCallSchedulerModel>,
  ): Promise<IPaginate<OnCallScheduler>> {
    return this.onCallSchedulerModel.find(query).then((onCallSchedulers) => {
      return {
        ...onCallSchedulers,
        data: onCallSchedulers.data.map(this.toDomain),
      }
    })
  }

  findById(
    id: Id,
    query?: ITypeOrmFilter<OnCallSchedulerModel>,
  ): Promise<Nullable<OnCallScheduler>> {
    return this.onCallSchedulerModel
      .findById(id, query)
      .then((onCallScheduler) => {
        if (!onCallScheduler) {
          return null
        }

        return this.toDomain(onCallScheduler)
      })
  }

  async findOne(
    query: ITypeOrmFilter<OnCallSchedulerModel>,
  ): Promise<Nullable<OnCallScheduler>> {
    const onCallScheduler = await this.onCallSchedulerModel.findOne(query)

    if (!onCallScheduler) {
      return null
    }

    return this.toDomain(onCallScheduler)
  }

  async create(onCallScheduler: OnCallScheduler): Promise<OnCallScheduler> {
    const onCallSchedulerProps = onCallScheduler.getProps()

    const newOnCallScheduler = await this.onCallSchedulerModel
      .create({
        ...onCallSchedulerProps,
        users: onCallSchedulerProps.users?.map((user) => ({
          id: user.id,
        })),
        team: {
          id: onCallSchedulerProps.team?.id,
        },
        id: onCallScheduler.id,
      })
      .then(this.toDomain)

    return newOnCallScheduler
  }

  async updateById(
    id: Id,
    onCallSchedulerProps: Partial<OnCallSchedulerProps>,
  ): Promise<OnCallScheduler> {
    return this.onCallSchedulerModel
      .updateById(id, {
        ...onCallSchedulerProps,
        users: onCallSchedulerProps.users?.map((member) => ({
          id: member.id,
        })),
        team: {
          id: onCallSchedulerProps.team?.id,
        },
      })
      .then(this.toDomain)
  }

  async delete(id: Id): Promise<boolean> {
    return this.onCallSchedulerModel
      .updateById(id, { deletedAt: new Date() })
      .then(() => true)
  }

  async getCurrentOnCallSchedulers(teamId: string): Promise<OnCallScheduler[]> {
    const now = new Date()

    const oneSecondLater = new Date(now.getTime() + 1000)
    return this.getOnCallSchedulerByDateRange(teamId, now, oneSecondLater)
  }
}
