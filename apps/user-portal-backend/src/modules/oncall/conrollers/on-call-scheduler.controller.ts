import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
} from '@backend/cores/rbac/permission.constant'
import { OnCallSchedulerUseCase } from '@backend/modules/oncall/usecases/on-call-scheduler.usecase'
import {
  CreateOnCallSchedulerDto,
  FindOnCallSchedulerDto,
  OnCallSchedulerFilterDto,
  UpdateOnCallSchedulerDto,
} from '@backend/modules/oncall/applications/dto/on-call-scheduler.request.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'

@ApiTags('OnCallScheduler')
@Controller('oncall-scheduler')
export class OnCallSchedulerController {
  constructor(
    private readonly onCallSchedulerUseCase: OnCallSchedulerUseCase,
  ) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
    },
  ])
  create(
    @Body() createOnCallSchedulerDto: CreateOnCallSchedulerDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.onCallSchedulerUseCase.create(
      createOnCallSchedulerDto,
      user,
      orgTeamParams,
    )
  }

  @Get()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
    },
  ])
  findAll(
    @Query() onCallSchedulerFilterDto: OnCallSchedulerFilterDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.onCallSchedulerUseCase.findAll(
      onCallSchedulerFilterDto,
      orgTeamParams,
    )
  }

  @Get('current/:id') // NOTE: If not using this endpoint, change to current-members or remove it
  getOnCallMembers(@Param('id') id: string) {
    return this.onCallSchedulerUseCase.getOnCallMembers(id)
  }

  @Get('parsed/:teamId')
  @UseGuards(AuthGuard, PermissionsGuard)
  getOnCallScheduler(
    @Param('teamId') teamId: string,
    @Query() options: FindOnCallSchedulerDto,
  ) {
    return this.onCallSchedulerUseCase.getOnCallSchedulers(teamId, options)
  }

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.onCallSchedulerUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
    },
  ])
  update(
    @Param('id') id: string,
    @Body() updateOnCallSchedulerDto: UpdateOnCallSchedulerDto,
  ) {
    return this.onCallSchedulerUseCase.update(id, updateOnCallSchedulerDto)
  }

  @Delete(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
    },
  ])
  remove(@Param('id') id: string) {
    return this.onCallSchedulerUseCase.remove(id)
  }
}
