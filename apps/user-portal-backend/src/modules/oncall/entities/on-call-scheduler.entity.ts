import ScheduleParser, {
  ScheduleParserOptions,
} from '@libs/shared/scheduler/schedule-parser'
import { OnCallSchedulerType } from '@libs/shared/constants/scheduler.enum'

import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'
import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import {
  Team,
  TeamResponse,
  User,
  UserResponse,
} from '@backend/modules/user/entities'

export interface OnCallSchedulerProps {
  type?: string
  startDate?: Date
  endDate?: Date
  allDay?: boolean
  recurringPattern?: string
  users?: User[]
  team?: Team
  createdBy?: string
  updatedBy?: string
}

export interface OnCallSchedulerResponse {
  id: Id
  type?: `${OnCallSchedulerType}`
  startDate?: Date
  endDate?: Date
  allDay?: boolean
  recurringPattern?: string
  teamId?: Id
  users?: UserResponse[]
  team?: TeamResponse
  createdAt?: Date
  createdBy?: string
  updatedBy?: string
  updatedAt?: Date
  deletedAt?: Date
}

export class OnCallScheduler extends AggregateRoot<
  OnCallSchedulerProps,
  OnCallSchedulerResponse
> {
  static create(props: OnCallSchedulerProps) {
    const onCallScheduler = new OnCallScheduler({
      id: generateId(),
      props,
    })

    return onCallScheduler
  }

  static update({ id, props }: { id: Id; props: OnCallSchedulerProps }) {
    const onCallScheduler = new OnCallScheduler({
      id: id,
      props,
    })

    return onCallScheduler
  }

  public toResponse(): OnCallSchedulerResponse {
    const onCallSchedulerProps = this.getProps()

    return {
      id: onCallSchedulerProps.id,
      type: onCallSchedulerProps.type as `${OnCallSchedulerType}`,
      startDate: onCallSchedulerProps.startDate,
      endDate: onCallSchedulerProps.endDate,
      allDay: onCallSchedulerProps.allDay,
      recurringPattern: onCallSchedulerProps.recurringPattern,
      users: onCallSchedulerProps.users?.map((user) => user.toResponse()),
      team: onCallSchedulerProps.team?.toResponse(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      createdBy: onCallSchedulerProps.createdBy,
      updatedBy: onCallSchedulerProps.updatedBy,
    }
  }

  // this method return parsed data by rrule in timerange
  public toSchedulers(
    options: ScheduleParserOptions,
  ): OnCallSchedulerResponse[] {
    const data = this.toResponse()
    return ScheduleParser.parse<OnCallSchedulerResponse>(data, options)
  }
}
