import { Module } from '@nestjs/common'

import { DatabaseModule } from '@backend/frameworks/database/database.module'
import { NotificationsModule } from '@backend/frameworks/notification/notification.module'
import { OnCallSchedulerController } from '@backend/modules/oncall/conrollers/on-call-scheduler.controller'
import { ON_CALL_SCHEDULER_REPOSITORY } from '@backend/modules/oncall/applications/on-call-scheduler.repository'
import { OnCallSchedulerInfrastructure } from '@backend/modules/oncall/infras/on-call-scheduler.infrastructure'
import { OnCallSchedulerUseCase } from '@backend/modules/oncall/usecases/on-call-scheduler.usecase'
import { USER_REPOSITORY } from '@backend/modules/user/applications/users.repository'
import { UserInfrastructure } from '@backend/modules/user/infras/user.infrastructure'

@Module({
  imports: [DatabaseModule, NotificationsModule],
  controllers: [OnCallSchedulerController],
  providers: [
    {
      provide: ON_CALL_SCHEDULER_REPOSITORY,
      useClass: OnCallSchedulerInfrastructure,
    },
    {
      provide: USER_REPOSITORY,
      useClass: UserInfrastructure,
    },
    OnCallSchedulerUseCase,
  ],
  exports: [OnCallSchedulerUseCase],
})
export class OnCallSchedulerModule {}
