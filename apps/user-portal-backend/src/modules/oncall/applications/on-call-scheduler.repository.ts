import { ITypeOrmFilter } from '@backend/frameworks/database/drivers/typeorm/typeorm.filter'
import CrudRepository from '@backend/cores/base/crud-repository'
import {
  OnCallScheduler,
  OnCallSchedulerProps,
} from '@backend/modules/oncall/entities/on-call-scheduler.entity'
import { OnCallSchedulerModel } from '@backend/frameworks/database/models/on-call-scheduler.model'

interface OnCallSchedulerRepository
  extends CrudRepository<
    OnCallScheduler,
    Partial<OnCallSchedulerProps>,
    ITypeOrmFilter<OnCallSchedulerModel>
  > {
  getCurrentOnCallSchedulers(teamId: string): Promise<OnCallScheduler[]>
  getOnCallSchedulerByDateRange(
    teamId: string,
    startDate: Date,
    endDate?: Date,
  ): Promise<OnCallScheduler[]>
}

export default OnCallSchedulerRepository
export const ON_CALL_SCHEDULER_REPOSITORY = 'ON_CALL_SCHEDULER_REPOSITORY'
