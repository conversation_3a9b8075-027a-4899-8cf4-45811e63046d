import { IsArray, IsDate, IsOptional, IsString } from 'class-validator'
import { z } from 'zod'
import { createZodDto } from '@anatine/zod-nestjs'
import { extendApi } from '@anatine/zod-openapi'

import { OnCallSchedulerProps } from '@backend/modules/oncall/entities/on-call-scheduler.entity'

export class OnCallSchedulerFilterDto implements Partial<OnCallSchedulerProps> {
  @IsOptional()
  @IsDate()
  startDate: Date

  @IsOptional()
  @IsDate()
  endDate?: Date

  @IsOptional()
  @IsDate()
  filterDate?: Date
}

const FindOnCallSchedulerDtoSchema = z.object({
  startDate: z.coerce.date().transform((v) => new Date(v)),
  endDate: z.coerce.date().transform((v) => new Date(v)),
})

export class FindOnCallSchedulerDto extends createZodDto(
  extendApi(FindOnCallSchedulerDtoSchema),
) {}

export class CreateOnCallSchedulerDto implements Partial<OnCallSchedulerProps> {
  @IsString()
  type: string

  @IsDate()
  startDate: Date

  @IsOptional()
  @IsDate()
  endDate?: Date

  @IsString()
  recurringPattern: string

  @IsArray()
  memberIds: string[]
}

export class UpdateOnCallSchedulerDto implements Partial<OnCallSchedulerProps> {
  @IsString()
  type: string

  @IsDate()
  startDate: Date

  @IsOptional()
  @IsDate()
  endDate?: Date

  @IsString()
  recurringPattern: string
}
