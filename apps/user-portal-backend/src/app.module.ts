import { APP_INTERCEPTOR } from '@nestjs/core'
import { Module } from '@nestjs/common'
import { RequestContextModule } from 'nestjs-request-context'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { FirebaseModule } from 'nestjs-firebase'
import { ConfigModule } from '@nestjs/config'
import { WinstonModule } from 'nest-winston'
import { BullModule } from '@nestjs/bullmq'
import { DynamooseModule } from 'nestjs-dynamoose'
import { BullBoardModule } from '@bull-board/nestjs'
import { ExpressAdapter } from '@bull-board/express'
import { DatabaseModule } from '@libs/database/lib/database.module'
import { DefaultLogger, loggerConfig } from '@libs/shared/logger'

import { OnCallSchedulerModule } from '@backend/modules/oncall/oncall.module'
import { SubscriptionModule } from '@backend/modules/subscription/subscription.module'

import { ContextInterceptor } from './commons/context/ContextInterceptor'
import { IncidentModule } from './modules/incident/incident.module'
import { UserModule } from './modules/user/user.module'
import { CheckModule } from './modules/check/check.module'
import { configService } from './cores/config/config.service'
import { AuthModule } from './cores/auth/auth.module'
import {
  LoggingInterceptor,
  INTERCEPTOR_LOGGER,
} from './commons/logger/winstonlogger.interceptor'
import { CaslModule } from './cores/rbac/casl.module'
import { EscalationModule } from './modules/escalation/escalation.module'
import { IntegrationSettingModule } from './modules/integration-setting/integration-setting.module'
import { CustomIncidentModule } from './modules/customIncident/customIncident.module'
import { StatusPageModule } from './modules/status-page/status-page.module'
import { NotificationsModule } from './frameworks/notification/notification.module'

const interceptors = [
  {
    provide: APP_INTERCEPTOR,
    useClass: ContextInterceptor,
  },
  DefaultLogger,
  {
    provide: INTERCEPTOR_LOGGER,
    useClass: LoggingInterceptor,
  },
]

@Module({
  imports: [
    ConfigModule.forRoot(),
    EventEmitterModule.forRoot(),
    FirebaseModule.forRoot(configService.getFirebaseConfig()),
    WinstonModule.forRoot(loggerConfig),
    BullModule.forRoot({
      connection: configService.getRedisConfig(),
      defaultJobOptions: {
        removeOnComplete: 1000,
        removeOnFail: 5000,
        attempts: 3,
      },
    }),
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter,
    }),
    DynamooseModule.forRootAsync({
      useFactory: async () => {
        return configService.getDynamooseOptions()
      },
    }),
    RequestContextModule,
    CaslModule,
    AuthModule,
    UserModule,
    CheckModule,
    CustomIncidentModule,
    IncidentModule,
    EscalationModule,
    OnCallSchedulerModule,
    IntegrationSettingModule,
    SubscriptionModule,
    StatusPageModule,
    DatabaseModule,
    NotificationsModule,
  ],
  controllers: [],
  providers: [...interceptors],
  exports: [],
})
export class AppModule {}
