import { NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { Logger, VersioningType } from '@nestjs/common'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { patchNestjsSwagger, ZodValidationPipe } from '@anatine/zod-nestjs'
// import IncidentIntegrationCallback from '@libs/integration-callback/incident-integration-callback'

import { AppModule } from './app.module'
import { LoggingInterceptor } from './commons/logger/winstonlogger.interceptor'

declare const module: any

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { rawBody: true })

  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER))

  // TODO: bring back when validation later using zod validation
  app.useGlobalPipes(new ZodValidationPipe())

  app.useGlobalInterceptors(
    new LoggingInterceptor(app.get(WINSTON_MODULE_NEST_PROVIDER)),
  )

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  })

  // Swagger configuration
  const options = new DocumentBuilder()
    .setTitle('User Portal API Documentation')
    .setDescription('User Portal API Documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build()

  patchNestjsSwagger()

  const document = SwaggerModule.createDocument(app, options)
  SwaggerModule.setup('api', app, document)

  // Enable CORS
  // TODO: Config detail for development & production later
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 204,
  })

  await app.listen(process.env.PORT || 3000)
  Logger.log(
    `Server running on http://localhost:${process.env.PORT || 3000}`,
    'Bootstrap',
  )

  if (module.hot) {
    module.hot.accept()
    module.hot.dispose(() => app.close())
  }
}
bootstrap()
