import { Injectable } from '@nestjs/common'
import { InfluxDBClient, Point, QueryType } from '@influxdata/influxdb3-client'

import { configService } from '@backend/cores/config/config.service'

@Injectable()
export class InfluxClient {
  private database = configService.getInfluxConfig().bucket
  private client: InfluxDBClient
  constructor() {
    const influxConfig = configService.getInfluxConfig()
    this.client = new InfluxDBClient({
      host: influxConfig.url,
      token: influxConfig.token,
    })
  }

  async write(point: Point): Promise<void> {
    return await this.client.write(point, this.database)
  }

  async query(
    query: string,
    query_type: QueryType,
    bucket: string,
  ): Promise<AsyncGenerator<Record<string, any>, void, void>> {
    const result = await this.client.query(query, bucket, {
      type: query_type,
    })
    return result
  }
}
