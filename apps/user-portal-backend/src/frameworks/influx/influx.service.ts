import { Injectable } from '@nestjs/common'
import { Point, QueryType } from '@influxdata/influxdb3-client'

import { InfluxClient } from './client/influx.client'

@Injectable()
export class InfluxService {
  constructor(private readonly client: InfluxClient) {}

  // onModuleDestroy(): void {
  //   this.client.closeConnection()
  // }

  async write(
    measurement: string,
    tags: { [key: string]: string },
    fields: { [key: string]: number | string },
  ): Promise<void> {
    try {
      const point = Point.measurement(measurement)
      for (const [tagName, tagValue] of Object.entries(tags)) {
        point.setTag(tagName, tagValue)
      }
      for (const [fieldName, fieldValue] of Object.entries(fields)) {
        if (typeof fieldValue === 'number') {
          point.setFloatField(fieldName, fieldValue)
        } else if (typeof fieldValue === 'string') {
          point.setStringField(fieldName, fieldValue)
        }
      }
      return await this.client.write(point)
    } catch (err) {
      console.error(`Write to InfluxDB error:`, err.stack)
      throw new Error(`Write to InfluxDB error`)
    }
  }

  async query(
    query: string,
    query_type: QueryType,
    bucket: string,
  ): Promise<AsyncGenerator<Record<string, any>, void, void>> {
    try {
      return await this.client.query(query, query_type, bucket)
    } catch (err) {
      console.error('Query from InfluxDB error', err.stack)
      throw new Error(`Query from InfluxDB error`)
    }
  }
}
