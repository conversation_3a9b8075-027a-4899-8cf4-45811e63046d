import { Injectable } from '@nestjs/common'
import { Job, Queue, RepeatableJob } from 'bullmq'

import { IQueueService } from './interface/queue.interface'
import { BullClient } from './bull/bull.client'

@Injectable()
export class QueueService<T> implements IQueueService<T, Job> {
  constructor(private readonly client: BullClient) {}

  public async publishToQueue(
    queueName: string,
    jobName: string,
    message: string,
  ): Promise<void> {
    await this.client.publishToQueue(queueName, jobName, message)
  }

  public async removeQueue(queueName: string): Promise<void> {
    await this.client.removeQueue(queueName)
  }

  public getQueue(queueName: string): Queue | null {
    return this.client.getQueue(queueName)
  }

  async addRepeatableJob(
    jobName: string,
    data: T,
    intervalSec: number,
  ): Promise<Job> {
    return await this.client.addRepeatedJob(jobName, data, intervalSec * 1000)
  }

  async addRepeatedJobPattern(
    jobName: string,
    data: T,
    cronExpression: string,
  ): Promise<Job> {
    return await this.client.addRepeatedJobPattern(
      jobName,
      data,
      cronExpression,
    )
  }

  async addJob(jobName: string, data: T): Promise<Job> {
    return await this.client.addJob(jobName, data)
  }

  async updateRepeatableJob(
    jobName: string,
    data: T,
    intervalSec: number,
  ): Promise<Job> {
    await this.client.deleteRepeatableJob(jobName)
    return await this.addRepeatableJob(jobName, data, intervalSec)
  }

  async updateJob(jobName: string, data: T): Promise<Job> {
    await this.client.deleteJob(jobName)
    return await this.addJob(jobName, data)
  }

  async deleteRepeatableJob(jobName: string): Promise<void> {
    await this.client.deleteRepeatableJob(jobName)
  }

  async deleteJob(jobName: string): Promise<void> {
    await this.client.deleteRepeatableJob(jobName)
  }

  async listJob(): Promise<void> {
    await this.client.listJobs()
  }

  async pauseJob(jobName: string): Promise<void> {
    await this.deleteJob(jobName)
  }

  async unpauseJob(jobName: string, data: T): Promise<void> {
    await this.addJob(jobName, data)
  }

  async getJob(jobName: string): Promise<RepeatableJob | undefined | null> {
    return await this.client.getRepeatableJob(jobName)
  }
}
