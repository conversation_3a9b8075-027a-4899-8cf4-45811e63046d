import { Injectable } from '@nestjs/common'
import { Queue, Job, RepeatableJob } from 'bullmq'

import { configService } from '@backend/cores/config/config.service'

@Injectable()
export class BullClient {
  private readonly queue: Queue
  private queueList: Map<string, Queue> = new Map()
  private redisConnection = {
    host: configService.getRedisConfig().host,
    port: configService.getRedisConfig().port,
  }
  constructor() {
    this.queue = new Queue('monitoringdog', {
      connection: this.redisConnection,
    })
  }

  private initializeQueue(queueName: string): Queue {
    const queue = new Queue(`${queueName}`, {
      connection: this.redisConnection,
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: 5,
      },
    })
    this.queueList.set(queueName, queue)
    return queue
  }

  public async publishToQueue(
    queueName: string,
    jobName: string,
    message: string,
  ): Promise<void> {
    let queue = this.queueList.get(queueName)
    if (!queue) {
      queue = this.initializeQueue(queueName)
      console.log(`New queue ${queueName} created`)
    }
    await queue.add(jobName, { message })
  }

  public async removeQueue(queueName: string): Promise<void> {
    const queue = this.queueList.get(queueName)
    if (queue) {
      await queue.close()
      this.queueList.delete(queueName)
      console.log(`Queue ${queueName} removed.`)
    } else {
      console.log(`Queue ${queueName} does not exist.`)
    }
  }

  public getQueue(queueName: string): Queue | null {
    return this.queueList.get(queueName) || null
  }
  //
  // REPEATABLE Job section
  //

  // cron expression: This example mean Repeat job once every day at 3:15 (am)
  //  repeat: {
  //    pattern: '0 15 3 * * *',
  //  },
  async addRepeatedJobPattern(
    jobName: string,
    data: any,
    cronExpression: string,
  ): Promise<Job> {
    return await this.queue.add(jobName, data, {
      repeat: {
        pattern: cronExpression,
      },
      removeOnComplete: true,
    })
  }

  // This example mean Repeat job every 10 seconds but no more than 100 times
  //  repeat: {
  //   every: 10000,
  //   limit: 100,
  // },
  async addRepeatedJob(
    jobName: string,
    data: any,
    everyInputSecond: number,
  ): Promise<Job> {
    return await this.queue.add(jobName, data, {
      repeat: {
        every: everyInputSecond,
      },
      jobId: jobName,
      // removeOnComplete: true,
    })
  }

  async listRepeatableJobs(): Promise<RepeatableJob[]> {
    const getjob = await this.queue.getRepeatableJobs()
    console.log('List of all repeatable jobs')
    console.log(getjob)
    return getjob
  }

  async getRepeatableJob(
    jobName: string,
  ): Promise<RepeatableJob | undefined | null> {
    const repeatableJobs = await this.queue.getRepeatableJobs()
    if (!repeatableJobs) return null
    return repeatableJobs.find((job) => job.name === jobName)
  }

  async deleteRepeatableJob(jobName: string): Promise<boolean> {
    const repeatableJobs = await this.queue.getRepeatableJobs()
    const foundJob = repeatableJobs.find((job) => job.name === jobName)
    if (!foundJob) return false
    return await this.queue.removeRepeatableByKey(foundJob.key)
  }

  //
  // Basic Job section
  //
  async addJob(name: string, data: any): Promise<Job> {
    return await this.queue.add(name, data)
  }

  async listJobs(): Promise<Job[]> {
    const getjob = await this.queue.getJobs()
    console.log('List of all jobs')
    console.log(getjob)
    return getjob
  }

  async getJob(jobName: string): Promise<Job | undefined> {
    const getJob = await this.queue.getJob(jobName)
    console.log(getJob)
    return getJob
  }

  async deleteJob(jobName: string): Promise<void> {
    const getJob = await this.queue.getJob(jobName)
    if (!getJob) return
    else await this.queue.remove(jobName)
  }
}

export const BULL_CLIENT = 'BULL_CLIENT'
