import {
  ConfigurableModuleBuilder,
  DynamicModule,
  Module,
} from '@nestjs/common'
import { BullBoardModule } from '@bull-board/nestjs'
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter'
import { BullModule } from '@nestjs/bullmq'
import { SqsModule } from '@ssut/nestjs-sqs'
import { SqsConsumerOptions } from '@ssut/nestjs-sqs/dist/sqs.types'

import { configService } from '@backend/cores/config/config.service'

import { RedisClient } from '../cache/redis/redis.client'

import { QueueService } from './queue.service'
import { BullClient } from './bull/bull.client'

interface QueueModuleOptions {
  queues: string[]
  flows?: string[]
  sqsQueues?: SqsConsumerOptions[]
}

const { ConfigurableModuleClass, OPTIONS_TYPE } =
  new ConfigurableModuleBuilder<QueueModuleOptions>().build()

@Module({
  imports: [],
  providers: [RedisClient, BullClient, QueueService],
  exports: [QueueService],
})
export class QueueModule extends ConfigurableModuleClass {
  static register(options: typeof OPTIONS_TYPE): DynamicModule {
    let bullModules: DynamicModule[] = []
    let bullBoards: DynamicModule[] = []

    if (options.queues?.length) {
      bullModules = options.queues.map((name) =>
        BullModule.registerQueue({ name }),
      )

      bullBoards = options.queues.map((name) =>
        BullBoardModule.forFeature({
          name,
          adapter: BullMQAdapter,
        }),
      )
    }

    let sqs: DynamicModule | null = null

    if (options.sqsQueues?.length) {
      sqs = SqsModule.register({
        consumers: options.sqsQueues.map((queue) => ({
          ...queue,
          sqs: configService.getSqsClient(),
        })),
      })
    }

    return {
      ...super.register(options),
      imports: [...bullModules, ...bullBoards, ...(sqs ? [sqs] : [])],
      exports: [...bullModules, ...(sqs ? [sqs] : [])],
    }
  }
}
