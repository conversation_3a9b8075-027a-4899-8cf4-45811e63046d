import { Module } from '@nestjs/common'
import { AwsSdkModule } from 'aws-sdk-v3-nest'
import { SFNClient } from '@aws-sdk/client-sfn'

import { configService } from '@backend/cores/config/config.service'

import { StateMachineService } from './state-machine.service'

@Module({
  imports: [
    AwsSdkModule.register({
      client: new SFNClient({
        credentials: {
          accessKeyId: configService.getAwsConfig().accessKeyId,
          secretAccessKey: configService.getAwsConfig().secretAccessKey,
        },
        region: configService.getAwsConfig().region,
      }),
    }),
  ],
  providers: [StateMachineService],
  exports: [StateMachineService],
})
export class StateMachineModule {}
