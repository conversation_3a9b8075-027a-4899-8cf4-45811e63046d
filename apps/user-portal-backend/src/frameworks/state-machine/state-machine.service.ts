import { Injectable } from '@nestjs/common'
import { InjectAws } from 'aws-sdk-v3-nest'
import {
  SFNClient,
  StartExecutionCommand,
  StopExecutionCommand,
} from '@aws-sdk/client-sfn'

import { IStateMachine } from './interfaces/state-machine.interface'

@Injectable()
export class StateMachineService implements IStateMachine {
  private readonly defaultStepFunctionArn =
    'arn:aws:states:us-east-1:851725359289:stateMachine:IncidentEscalation'

  constructor(@InjectAws(SFNClient) private readonly client: SFNClient) {}

  async startExecution(params: any): Promise<string> {
    const stepFunction = await this.client.send(
      new StartExecutionCommand({
        stateMachineArn: this.defaultStepFunctionArn,
        input: JSON.stringify(params),
      }),
    )

    if (!stepFunction.executionArn)
      throw new Error('State machine execution failed')

    return stepFunction.executionArn
  }

  async stopExecution(executionArn: string): Promise<void> {
    await this.client.send(
      new StopExecutionCommand({
        executionArn: executionArn,
      }),
    )
  }
}
