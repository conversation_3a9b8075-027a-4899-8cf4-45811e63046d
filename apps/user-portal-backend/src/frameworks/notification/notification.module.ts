import { Module } from '@nestjs/common'

import { EmailService } from './email/email.service'
import { NotificationFactory } from './notification.factory'
import { SlackService } from './slack/slack.service'
import { PlivoService } from './plivo/plivo.service'
import { PushNotificationService } from './push/push-notification.service'

@Module({
  providers: [
    EmailService,
    SlackService,
    PlivoService,
    PushNotificationService,
    NotificationFactory,
  ],
  exports: [NotificationFactory],
})
export class NotificationsModule {}
