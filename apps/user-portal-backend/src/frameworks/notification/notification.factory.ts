import { Inject, Injectable } from '@nestjs/common'

import { EmailService } from './email/email.service'
import { SlackService } from './slack/slack.service'
import { PlivoService } from './plivo/plivo.service'
import { PushNotificationService } from './push/push-notification.service'

interface NotificationServiceMap {
  email: EmailService
  slack: SlackService
  plivo: PlivoService
  push: PushNotificationService
}

@Injectable()
export class NotificationFactory {
  constructor(
    @Inject(EmailService) private emailService: EmailService,
    @Inject(SlackService) private slackService: SlackService,
    @Inject(PlivoService) private plivoService: PlivoService,
    @Inject(PushNotificationService)
    private pushNotificationService: PushNotificationService,
  ) {}

  getNotificationService<T extends keyof NotificationServiceMap>(
    type: T,
  ): NotificationServiceMap[T] {
    switch (type) {
      case 'email':
        return this.emailService as NotificationServiceMap[T]
      case 'slack':
        return this.slackService as NotificationServiceMap[T]
      case 'plivo':
        return this.plivoService as NotificationServiceMap[T]
      case 'push':
        return this.pushNotificationService as NotificationServiceMap[T]
      default:
        throw new Error(`Notification service for type ${type} not found.`)
    }
  }
}
