import { WebClient } from '@slack/web-api'
import { IncomingWebhook } from '@slack/webhook'

import { configService } from '@backend/cores/config/config.service'

import {
  INotificationService,
  NotificationMessage,
} from '../notification.interface'

import { SlackMessageType, TEST_WEBHOOK_URL } from './slack.constant'

interface SlackMessage extends NotificationMessage {
  type: SlackMessageType
  url?: string
  method?: string
  cause?: string
  startedAt?: Date
  length?: string
}

export class SlackService implements INotificationService {
  // private readonly slackClient: WebClient
  private readonly webhook: IncomingWebhook
  constructor() {
    // this.slackClient = new WebClient(BOT_TOKEN)
    this.webhook = new IncomingWebhook(TEST_WEBHOOK_URL)
  }

  async send(message: SlackMessage): Promise<void> {
    switch (message.type) {
      case SlackMessageType.IncidentStarted:
        await this.sendIncidentStarted(message)
        break
      case SlackMessageType.IncidentAutoResolved:
        await this.sendIncidentAutoResolved(message)
        break
      default:
        break
    }
  }

  private formatSlackDate(date: Date = new Date()): string {
    return `<!date^${Math.round(date.getTime() / 1000)}^{date} {time_secs}|${date.toISOString()}>`
  }

  async sendIncidentStarted(message: SlackMessage): Promise<void> {
    this.webhook.send({
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*New incident for* *${message?.url}*`,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Monitor:* ${message?.url}`,
            },
            {
              type: 'mrkdwn',
              text: `*Cause:* ${message?.cause}`,
            },
            {
              type: 'mrkdwn',
              text: `*Checked URL:* GET ${message?.url}`,
            },
          ],
        },
        {
          type: 'actions',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Acknowledge',
              },
              style: 'primary',
              value: 'acknowledge_incident',
            },
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Escalate',
              },
              style: 'danger',
              value: 'escalate_incident',
            },
          ],
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: ':warning: *Incident*',
            },
            {
              type: 'mrkdwn',
              text: ':globe_with_meridians: *Monitor*',
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `Incident started *${this.formatSlackDate(message?.startedAt)}*`,
          },
        },
      ],
    })
  }

  async sendIncidentAutoResolved(message: SlackMessage): Promise<void> {
    this.webhook.send({
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Automatically resolved* *${message?.url}* incident`,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Monitor:* ${message?.url}`,
            },
            {
              type: 'mrkdwn',
              text: `*Cause:* ${message?.cause}`,
            },
            {
              type: 'mrkdwn',
              text: '*Length:* 5 minutes',
            },
            {
              type: 'mrkdwn',
              text: `*Checked URL:* GET ${message?.url}`,
            },
          ],
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: ':warning: *Incident*',
            },
            {
              type: 'mrkdwn',
              text: ':globe_with_meridians: *Monitor*',
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `Incident started *${this.formatSlackDate(message?.startedAt)}*`,
          },
        },
      ],
    })
  }

  async getAccess(code: string, redirectUrl?: string) {
    return new WebClient().oauth.v2.access({
      client_id: configService.getValue('SLACK_CLIENT_ID'),
      client_secret: configService.getValue('SLACK_CLIENT_SECRET'),
      code,
      redirect_uri: redirectUrl,
    })
  }
}
