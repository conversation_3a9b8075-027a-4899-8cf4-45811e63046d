import { Injectable } from '@nestjs/common'

import {
  INotificationService,
  NotificationMessage,
} from '../notification.interface'

import { DEFAULT_EMAIL_SENDER, EmailTemplate } from './email.constant'
import { transport } from './mailer.service'

interface EmailMessage extends NotificationMessage {
  to: string | string[]
  subject?: string
  from?: string
  bcc?: string[]
  cc?: string[]
  template?: EmailTemplate
  templateData?: Record<string, any>
}

@Injectable()
export class EmailService implements INotificationService {
  private getTemplate(template: string): string {
    return `${template}`
  }

  async send(message: EmailMessage): Promise<void> {
    try {
      await transport.sendMail({
        to: message.to,
        from: message.from || DEFAULT_EMAIL_SENDER,
        bcc: message.bcc,
        subject: message.subject,
        ...(message.template && {
          templateLayoutName: this.getTemplate(message.template),
          templateData: message.templateData,
        }),
      })
    } catch (error) {
      console.log('🚀 ~ EmailService ~ send ~ error:', error)

      throw new Error('Failed to send email')
    }
  }
}
