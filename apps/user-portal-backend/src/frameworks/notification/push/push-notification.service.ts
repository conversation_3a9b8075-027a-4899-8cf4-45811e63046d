import { Injectable } from '@nestjs/common'
import {
  OneSignalAppClient,
  NotificationBySegmentBuilder,
  NotificationByDeviceBuilder,
  NotificationByFilterBuilder,
  ICreateDeviceInput,
  IViewDeviceInput,
  IUpdateDeviceInput,
  ITrackOpenInput,
  INewSessionInput,
  DeviceType,
} from 'onesignal-api-client-core'

import { configService } from '@backend/cores/config/config.service'

import { NotificationMessage } from '../notification.interface'

interface PushNotiMessage extends NotificationMessage {
  segments?: string[]
  userIds?: string[]
  filters?: any[]
}

@Injectable()
export class PushNotificationService {
  private appClient: OneSignalAppClient

  constructor() {
    const appId = configService.getOneSignalConfig().appId
    const restApiKey = configService.getOneSignalConfig().apiKey

    this.appClient = new OneSignalAppClient(appId, restApiKey)
  }

  async send(input: PushNotiMessage): Promise<void> {
    let notificationInput
    if (input.segments) {
      notificationInput =
        new NotificationBySegmentBuilder().setIncludedSegments(input.segments)
    } else if (input.userIds) {
      notificationInput =
        new NotificationByDeviceBuilder().setIncludeExternalUserIds(
          input.userIds,
        )
    } else if (input.filters) {
      notificationInput = new NotificationByFilterBuilder().setFilters(
        input.filters,
      )
    } else {
      throw new Error('Invalid notification target')
    }
    notificationInput = notificationInput
      .notification()
      .setContents({ en: input.body ?? '' })
      .build()
    await this.appClient.createNotification(notificationInput)
  }

  // =======================
  // Notifications
  // =======================
  async sendNotificationToSegment(segments: string[], message: string) {
    const input = new NotificationBySegmentBuilder()
      .setIncludedSegments(segments)
      .notification()
      .setContents({ en: message })
      .build()

    return await this.appClient.createNotification(input)
  }

  async sendNotificationToUsersByIds(userIds: string[], message: string) {
    const input = new NotificationByDeviceBuilder()
      .setIncludeExternalUserIds(userIds)
      .notification()
      .setContents({ en: message })
      .build()

    return await this.appClient.createNotification(input)
  }

  async sendNotificationWithFilters(filters: any[], message: string) {
    const input = new NotificationByFilterBuilder()
      .setFilters(filters)
      .notification()
      .setContents({ en: message })
      .build()

    return await this.appClient.createNotification(input)
  }

  // =======================
  // Other function from onesignal-api-client-core
  // =======================

  async cancelNotification(notificationId: string) {
    return await this.appClient.cancelNotification({ id: notificationId })
  }

  async createDevice(deviceType: DeviceType) {
    const input: ICreateDeviceInput = { device_type: deviceType }
    return await this.appClient.createDevice(input)
  }

  async viewDevice(deviceId: string) {
    const input: IViewDeviceInput = { id: deviceId }
    return await this.appClient.viewDevice(input)
  }

  async viewDevices() {
    return await this.appClient.viewDevices()
  }

  async updateDevice(deviceId: string, deviceType: DeviceType) {
    const input: IUpdateDeviceInput = { id: deviceId, device_type: deviceType }
    return await this.appClient.updateDevice(input)
  }

  // =======================
  // Tracking & Sessions
  // =======================
  async trackOpen(notificationId: string) {
    const input: ITrackOpenInput = { notificationId, opened: true }
    return await this.appClient.trackOpen(input)
  }

  async newSession(deviceId: string) {
    const input: INewSessionInput = { deviceId }
    return await this.appClient.newSession(input)
  }
}
