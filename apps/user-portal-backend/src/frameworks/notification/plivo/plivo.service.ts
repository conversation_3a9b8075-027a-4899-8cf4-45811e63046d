import { Injectable } from '@nestjs/common'
import * as Plivo from 'plivo'

import { configService } from '@backend/cores/config/config.service'

import {
  INotificationService,
  NotificationMessage,
} from '../notification.interface'

@Injectable()
export class PlivoService implements INotificationService {
  private client: Plivo.Client

  constructor() {
    this.client = new Plivo.Client(
      configService.getPlivoConfig().plivoAuthId,
      configService.getPlivoConfig().plivoAuthToken,
    )
  }

  async send(message: NotificationMessage): Promise<void> {
    return undefined
  }

  async createSipEndpoint(username: string, password: string, alias: string) {
    try {
      const response = await this.client.endpoints.create(
        username,
        password,
        alias,
        configService.getPlivoConfig().plivoAppId,
      )

      return response
    } catch (error) {
      console.error(
        'Error details:',
        JSON.stringify(error, Object.getOwnPropertyNames(error), 2),
      )
      throw error
    }
  }
}
