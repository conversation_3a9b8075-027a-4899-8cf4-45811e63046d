import { Injectable } from '@nestjs/common'

import { ICacheService } from './interface/cache.interface'
import { RedisClient } from './redis/redis.client'

@Injectable()
export class CacheService implements ICacheService {
  constructor(private readonly client: RedisClient) {}

  onModuleDestroy(): void {
    this.client.disconnect()
  }

  async get<T>(key: string, prefix: string): Promise<T | null> {
    const data = await this.client.get(`${prefix}:${key}`)
    if (!data) return null
    try {
      return JSON.parse(data) as T
    } catch (error) {
      return null
    }
  }

  async set<T>(
    key: string,
    prefix: string,
    value: T,
    expiry?: number,
  ): Promise<void> {
    if (expiry) {
      await this.client.setWithExpiry(
        `${prefix}:${key}`,
        JSON.stringify(value),
        expiry,
      )
    } else {
      await this.client.set(`${prefix}:${key}`, JSON.stringify(value))
    }
  }

  async delete(key: string, prefix: string): Promise<void> {
    await this.client.delete(`${prefix}:${key}`)
  }

  async lpush(key: string, prefix: string, value: any): Promise<void> {
    await this.client.lpush(`${prefix}:${key}`, value)
  }

  async lrange(
    key: string,
    prefix: string,
    start: number,
    end: number,
  ): Promise<string[]> {
    return await this.client.lrange(`${prefix}:${key}`, start, end)
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    return await this.client.hset(key, field, value)
  }

  async hget(key: string, field: string): Promise<string | null> {
    return await this.client.hget(key, field)
  }

  async publish(channel: string, message: string): Promise<number> {
    return this.client.publish(channel, message)
  }
}

export const REDIS_SERVICE = 'REDIS_SERVICE'
