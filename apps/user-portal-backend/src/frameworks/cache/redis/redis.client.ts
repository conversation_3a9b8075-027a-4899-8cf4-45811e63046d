import { Injectable } from '@nestjs/common'
import Redis from 'ioredis'
import { Logger } from '@libs/shared/logger'

import { configService } from '@backend/cores/config/config.service'

@Injectable()
export class RedisClient {
  private readonly client: Redis
  private readonly logger = Logger

  constructor() {
    const redisConfig = configService.getRedisConfig()
    this.client = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      lazyConnect: true, // Prevent automatic connection attempts
      showFriendlyErrorStack: false, // Disable pretty error formatting
      retryStrategy: (times) => {
        this.logger.warn({
          message: `Redis connection retry attempt: ${times}`,
          level: 'warn',
          timestamp: new Date().toISOString(),
        })
        return Math.min(times * 1000, 60000)
      },
    })

    // Override default error handler to prevent duplicate logging
    this.client.on('error', (err: Error & { code?: string }) => {
      if (err.code !== 'ECONNREFUSED') {
        // Skip connection refused errors since retryStrategy handles them
        this.logger.error({
          message: `Redis error: ${err.message}`,
          error: err.stack,
          level: 'error',
          timestamp: new Date().toISOString(),
        })
      }
    })
  }

  async connect(): Promise<void> {
    await this.client.connect()
  }

  async disconnect(): Promise<void> {
    await this.client.quit()
  }

  async setWithExpiry(
    key: string,
    value: any,
    expiry: number,
  ): Promise<string | null> {
    return this.client.set(`${key}`, value, 'EX', expiry)
  }

  async get(key: string): Promise<string | null> {
    return this.client.get(`${key}`)
  }

  async set(key: string, value: string): Promise<void> {
    await this.client.set(`${key}`, value)
  }

  async delete(key: string): Promise<void> {
    await this.client.del(`${key}`)
  }

  async publish(channel: string, message: string): Promise<number> {
    return this.client.publish(channel, message)
  }

  async lpush(key: string, value: any): Promise<void> {
    await this.client.lpush(`${key}`, value)
  }

  async lrange(key: string, start: number, end: number): Promise<string[]> {
    return await this.client.lrange(`${key}`, start, end)
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    return await this.client.hset(key, field, value)
  }

  async hget(key: string, field: string): Promise<string | null> {
    return await this.client.hget(key, field)
  }
}

export const REDIS_CLIENT = 'REDIS_CLIENT'
