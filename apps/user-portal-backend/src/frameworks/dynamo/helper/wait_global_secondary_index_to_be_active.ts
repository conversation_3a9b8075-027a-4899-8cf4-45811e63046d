import dynamoose from 'dynamoose'

import { timeout } from '@backend/commons/utils/promises'

export const waitGlobalSecondaryIndexToBeActive = async (
  tableName: string,
  globalSecondaryIndexName: string,
  nullAttempts = 0,
): Promise<void> => {
  if (nullAttempts > 5) {
    throw new Error('Global secondary index is not being created')
  }
  const ddb = dynamoose.aws.ddb()
  const tableSettings = await ddb.describeTable({
    TableName: `${tableName}`,
  })
  const globalSecondaryIndexes = tableSettings.Table?.GlobalSecondaryIndexes
  if (
    Array.isArray(globalSecondaryIndexes) &&
    globalSecondaryIndexes.length > 0
  ) {
    const globalSecondaryIndex = globalSecondaryIndexes.find(
      (globalIndex) => globalIndex.IndexName === globalSecondaryIndexName,
    )

    if (
      globalSecondaryIndex?.IndexStatus !== 'ACTIVE' ||
      !globalSecondaryIndex
    ) {
      await timeout(1000)
      if (!globalSecondaryIndex) {
        nullAttempts++
      }
      return waitGlobalSecondaryIndexToBeActive(
        tableName,
        globalSecondaryIndexName,
        nullAttempts,
      )
    }
  }
}
