import dynamoose from 'dynamoose'

export const getGlobalSecondaryIndexesFromTable = async (
  tableName: string,
): Promise<string[]> => {
  const ddb = dynamoose.aws.ddb()
  const tableSettings = await ddb.describeTable({
    TableName: `${tableName}`,
  })
  const globalSecondaryIndexes =
    tableSettings.Table?.GlobalSecondaryIndexes?.filter(
      (index) => index.IndexName !== undefined,
    )?.map((index) => index.IndexName as string)
  if (!globalSecondaryIndexes) return []
  return globalSecondaryIndexes
}
