import dynamoose from 'dynamoose'
import {
  AttributeDefinition,
  CreateGlobalSecondaryIndexAction,
  ScalarAttributeType,
} from '@aws-sdk/client-dynamodb/dist-types/models/models_0'
// import { AttributeDefinition } from 'aws-sdk/clients/dynamodb'

import { IndexDefinition } from './get_global_secondary_indexes_from_schema'

export const createGlobalSecondaryIndex = async (
  tableName: string,
  globalSecondaryIndex: IndexDefinition,
): Promise<void> => {
  const ddb = dynamoose.aws.ddb()
  const createCommand: CreateGlobalSecondaryIndexAction = {
    IndexName: globalSecondaryIndex.name,
    Projection: { ProjectionType: 'ALL' },
    KeySchema: [
      {
        AttributeName: globalSecondaryIndex.attribute,
        KeyType: 'HASH',
      },
    ],
  }
  if (typeof globalSecondaryIndex.rangeKey === 'string') {
    createCommand.KeySchema?.push({
      AttributeName: globalSecondaryIndex.rangeKey,
      KeyType: 'RANGE',
    })
  }
  const AttributeDefinitions: AttributeDefinition[] = [
    {
      AttributeName: globalSecondaryIndex.attribute,
      AttributeType: globalSecondaryIndex.attributeType as ScalarAttributeType,
    },
  ]
  if (typeof globalSecondaryIndex.rangeKey === 'string') {
    AttributeDefinitions.push({
      AttributeName: globalSecondaryIndex.rangeKey,
      AttributeType: globalSecondaryIndex.attributeType as ScalarAttributeType,
    })
  }
  await ddb.updateTable({
    TableName: tableName,
    AttributeDefinitions: AttributeDefinitions,
    GlobalSecondaryIndexUpdates: [
      {
        Create: createCommand,
      },
    ],
  })
}
