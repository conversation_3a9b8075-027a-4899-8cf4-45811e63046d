import dynamoose from 'dynamoose'

import { timeout } from '@backend/commons/utils/promises'

export const waitGlobalSecondaryIndexToBeDeleted = async (
  tableName: string,
  globalSecondaryIndexName: string,
): Promise<void> => {
  const ddb = dynamoose.aws.ddb()
  const tableSettings = await ddb.describeTable({
    TableName: `${tableName}`,
  })
  const globalSecondaryIndexes = tableSettings.Table?.GlobalSecondaryIndexes
  if (
    Array.isArray(globalSecondaryIndexes) &&
    globalSecondaryIndexes.length > 0
  ) {
    const globalSecondaryIndex = globalSecondaryIndexes.find(
      (globalIndex) => globalIndex.IndexName === globalSecondaryIndexName,
    )
    if (globalSecondaryIndex) {
      await timeout(1000)
      return waitGlobalSecondaryIndexToBeDeleted(
        tableName,
        globalSecondaryIndexName,
      )
    }
  }
}
