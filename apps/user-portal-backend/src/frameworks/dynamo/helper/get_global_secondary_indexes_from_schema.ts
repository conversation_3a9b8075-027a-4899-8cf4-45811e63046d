import { Schema } from 'dynamoose/dist/Schema'

export const getGlobalSecondaryIndexesFromSchema = (
  schema: Schema,
): IndexDefinition[] => {
  const indexAttributes = schema.indexAttributes
  if (indexAttributes.length === 0) return []
  const indexDefinitions: IndexDefinition[] = []
  for (const indexAttribute of indexAttributes) {
    const attribute = schema.getAttributeValue(indexAttribute)
    const attributeType =
      attribute.type === String
        ? 'S'
        : attribute.type === Number
          ? 'N'
          : attribute.type === Boolean
            ? 'B'
            : null

    if (typeof attribute?.index !== 'boolean' && attributeType !== null) {
      const attributeIndexArray = (
        attribute.index === undefined
          ? []
          : Array.isArray(attribute.index)
            ? attribute.index
            : [attribute.index]
      ).filter((attributeIndex) => attributeIndex?.type !== 'local')
      indexDefinitions.push(
        ...attributeIndexArray.map((attributeIndex) => ({
          ...attributeIndex,
          attribute: indexAttribute,
          attributeType: attributeType,
        })),
      )
    }
  }
  return indexDefinitions
}
export declare enum IndexType {
  /**
   * A global secondary index (GSI) is a secondary index in a DynamoDB table that is not local to a single partition key value.
   */
  global = 'global',
  /**
   * A local secondary index (LSI) is a secondary index in a DynamoDB table that is local to a single partition key value.
   */
  local = 'local',
}

export interface IndexDefinition {
  attributeType: string
  attribute: string
  name?: string
  type?: IndexType | keyof typeof IndexType
  rangeKey?: string
  project?: boolean | string[]
  throughput?:
    | 'ON_DEMAND'
    | number
    | {
        read: number
        write: number
      }
}
