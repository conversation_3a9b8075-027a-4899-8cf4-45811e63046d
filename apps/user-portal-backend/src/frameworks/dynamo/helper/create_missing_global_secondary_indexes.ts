import { configService } from '@backend/cores/config/config.service'

// import { DynamooseModelSchema } from '../../database/types/dynamoose_model_schema'

import { getGlobalSecondaryIndexesFromTable } from './get_global_secondary_indexes_from_table'
import { getGlobalSecondaryIndexesFromSchema } from './get_global_secondary_indexes_from_schema'
import { createGlobalSecondaryIndex } from './create_global_secondary_index'
import { waitGlobalSecondaryIndexToBeActive } from './wait_global_secondary_index_to_be_active'

type DynamooseModelSchema = {
  schema: any
  model: any
}

export const createMissingGlobalSecondaryIndexes = async (
  modelSchema: DynamooseModelSchema,
) => {
  const schema = modelSchema.schema
  const globalSecondaryIndexesFromSchema =
    getGlobalSecondaryIndexesFromSchema(schema)
  if (globalSecondaryIndexesFromSchema.length === 0) return
  const table = modelSchema.model
  const prefix = configService.getDynamooseOptions().table?.prefix || ''
  const tableName = `${prefix}${table.name}`
  const globalSecondaryIndexesCurrentlyOnTable =
    await getGlobalSecondaryIndexesFromTable(tableName)
  const notCreatedGlobalSecondaryIndexes =
    globalSecondaryIndexesFromSchema.filter(
      (index) =>
        typeof index.name !== 'string' ||
        !globalSecondaryIndexesCurrentlyOnTable.includes(index.name),
    )

  for (const globalSecondaryIndex of notCreatedGlobalSecondaryIndexes) {
    console.log(
      `Creating global secondary index: ${globalSecondaryIndex.name} on table: ${tableName}`,
    )

    await createGlobalSecondaryIndex(tableName, globalSecondaryIndex)
    await waitGlobalSecondaryIndexToBeActive(
      tableName,
      globalSecondaryIndex.name || '',
    )
  }
}
