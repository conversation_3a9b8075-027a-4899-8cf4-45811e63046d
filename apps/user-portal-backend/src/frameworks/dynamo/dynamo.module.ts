import * as dynamoose from 'dynamoose'
import { Module } from '@nestjs/common'
import { DynamooseModule } from 'nestjs-dynamoose'
import {
  INCIDENT_SCHEMA,
  INCIDENT_TABLE,
  IncidentSchema,
} from '@libs/database/lib/dynamo/incident-check.schema'
import {
  INCIDENT_EVENT_SCHEMA,
  INCIDENT_EVENT_TABLE,
  IncidentEventSchema,
} from '@libs/database/lib/dynamo/incident-event.schema'
import {
  STATUS_PAGE_SCHEMA,
  STATUS_PAGE_TABLE,
  StatusPageSchema,
} from '@libs/database/lib/dynamo/status-page.schema'
import {
  STATUS_REPORT_SCHEMA,
  STATUS_REPORT_TABLE,
  StatusReportSchema,
} from '@libs/database/lib/dynamo/status-report.schema'
import {
  INCIDENT_CUSTOM_SCHEMA,
  IncidentCustomIncidentSchema,
} from '@libs/database/lib/dynamo/incident-customIncident.schema'

import { checkTableExists } from './helper/checkTableExists'
import { createMissingGlobalSecondaryIndexes } from './helper/create_missing_global_secondary_indexes'

const schemas = [
  {
    name: INCIDENT_SCHEMA,
    schema: IncidentSchema,
    tableName: INCIDENT_TABLE,
  },
  {
    name: INCIDENT_CUSTOM_SCHEMA,
    schema: IncidentCustomIncidentSchema,
    tableName: INCIDENT_TABLE,
  },
  {
    name: INCIDENT_EVENT_SCHEMA,
    schema: IncidentEventSchema,
    tableName: INCIDENT_EVENT_TABLE,
  },
  {
    name: STATUS_PAGE_SCHEMA,
    schema: StatusPageSchema,
    tableName: STATUS_PAGE_TABLE,
  },
  {
    name: STATUS_REPORT_SCHEMA,
    schema: StatusReportSchema,
    tableName: STATUS_REPORT_TABLE,
  },
]

@Module({
  imports: [
    DynamooseModule.forFeatureAsync(
      schemas.map((schema) => ({
        name: schema.name,
        useFactory: async () => {
          const Model = dynamoose.model(schema.tableName, schema.schema)
          const table = new dynamoose.Table(schema.tableName, [Model])

          const tableExists = await checkTableExists(schema.tableName)

          if (!tableExists) {
            await table.create()
          }

          await createMissingGlobalSecondaryIndexes({
            schema: schema.schema,
            model: dynamoose.model(schema.tableName, schema.schema),
          })

          return {
            schema: schema.schema,
            options: {
              tableName: schema.tableName,
            },
          }
        },
      })),
    ),
  ],
  exports: [DynamooseModule],
})
export class DynamoModule {}
