import { Injectable } from '@nestjs/common'
import {
  DataSource,
  DeepPartial,
  FindManyOptions,
  FindOptionsWhere,
  ObjectLiteral,
  QueryBuilder,
  QueryRunner,
  Repository,
} from 'typeorm'
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity'

import { Id } from '@backend/cores/base/id.type'

import { IPaginate } from '../../interfaces/paginate.interface'
import { IDatabase } from '../../interfaces/database.interface'

import { ITypeOrmFilter } from './typeorm.filter'
import { buildTypeORMQuery } from './typeorm.querybuilder'

@Injectable()
class TypeORMDriver<T extends ObjectLiteral = ObjectLiteral>
  implements IDatabase<T, ITypeOrmFilter<T>, QueryRunner>
{
  private repository: Repository<T>
  private dataSource: DataSource

  constructor(repository: Repository<T>, dataSource: DataSource) {
    this.repository = repository
    this.dataSource = dataSource
  }

  private getRepository(transactionManager?: QueryRunner): Repository<T> {
    return transactionManager
      ? transactionManager.manager.getRepository<T>(this.repository.target)
      : this.repository
  }

  private getBuildedFilter(
    filter: ITypeOrmFilter<T>['filter'],
  ): FindOptionsWhere<T> | FindOptionsWhere<T>[] {
    if (Array.isArray(filter)) {
      return filter.map((item) => buildTypeORMQuery<T>(item))
    }

    return buildTypeORMQuery<T>(filter)
  }

  private async paginate(
    query: ITypeOrmFilter<T>,
    transactionManager?: QueryRunner,
  ): Promise<IPaginate<T>> {
    const { page = 1, limit = 10, orderBy } = query
    const repo = this.getRepository(transactionManager)

    const [data, total] = await repo.findAndCount({
      where: this.getBuildedFilter(query.filter),
      select: query.fields,
      take: limit,
      skip: (page - 1) * limit,
      relations: query.relations,
      cache: query.cache,
      ...(orderBy && { order: orderBy }),
    })

    return {
      data,
      total,
      limit,
      totalPage: Math.ceil(total / limit),
      page,
    }
  }

  count(
    filter?: ITypeOrmFilter<T>['filter'],
    transactionManager?: QueryRunner,
  ): Promise<number> {
    const repo = this.getRepository(transactionManager)
    return filter
      ? repo.count(this.getBuildedFilter(filter) as FindManyOptions<T>)
      : repo.count()
  }

  findAll(
    query?: ITypeOrmFilter<T>,
    transactionManager?: QueryRunner,
  ): Promise<T[]> {
    const repo = this.getRepository(transactionManager)
    return query
      ? repo.find({
          where: this.getBuildedFilter(query.filter),
          select: query.fields,
          relations: query.relations,
          order: query.orderBy,
          cache: query.cache,
        })
      : repo.find()
  }

  async find(
    query: ITypeOrmFilter<T>,
    transactionManager?: QueryRunner,
  ): Promise<IPaginate<T>> {
    const result = await this.paginate(query, transactionManager)
    return result
  }

  findOne(
    query: ITypeOrmFilter<T>,
    transactionManager?: QueryRunner,
  ): Promise<Nullable<T>> {
    const repo = this.getRepository(transactionManager)
    return repo.findOne({
      where: this.getBuildedFilter(query.filter),
      select: query.fields,
      relations: query.relations,
      cache: query.cache,
    })
  }

  async findById(
    id: Id,
    query?: ITypeOrmFilter<T>,
    transactionManager?: QueryRunner,
  ): Promise<Nullable<T>> {
    const repo = this.getRepository(transactionManager)
    return repo.findOne({
      where: { id } as any,
      ...(query && {
        select: query.fields,
        relations: query.relations,
        cache: query.cache,
      }),
    })
  }

  async create(
    data: DeepPartial<T>,
    transactionManager?: QueryRunner,
  ): Promise<T> {
    const repo = this.getRepository(transactionManager)
    const entity = repo.create(data)
    return repo.save(entity)
  }

  async createMany(
    data: DeepPartial<T>[],
    transactionManager?: QueryRunner,
  ): Promise<T[]> {
    const repo = this.getRepository(transactionManager)
    const entities = data.map((item) => repo.create(item))
    return repo.save(entities)
  }

  async updateById(
    id: Id,
    data: DeepPartial<T>,
    transactionManager?: QueryRunner,
  ): Promise<Nullable<T>> {
    const repo = this.getRepository(transactionManager)
    const loadedData = await repo.preload({ ...data, id })

    if (!loadedData) {
      return null
    }

    return repo.save(loadedData)
  }

  async update(
    data: QueryDeepPartialEntity<T>,
    filter: ITypeOrmFilter<T>['filter'],
    transactionManager?: QueryRunner,
  ): Promise<number> {
    const repo = this.getRepository(transactionManager)
    const updatedResult = await repo.update(buildTypeORMQuery<T>(filter), data)
    return updatedResult.affected ?? 0
  }

  async upsert(
    data: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
    conflictPaths: {
      [P in keyof T]?: true
    },
    transactionManager?: QueryRunner,
  ): Promise<void> {
    const repo = this.getRepository(transactionManager)
    await repo.upsert(data, {
      conflictPaths,
    })
  }

  async delete(
    filter: ITypeOrmFilter<T>['filter'],
    transactionManager?: QueryRunner,
  ): Promise<void> {
    const repo = this.getRepository(transactionManager)
    await repo.delete(buildTypeORMQuery<T>(filter))
  }

  async softDelete(
    filter: ITypeOrmFilter<T>['filter'],
    transactionManager?: QueryRunner,
  ): Promise<void> {
    const repo = this.getRepository(transactionManager)
    await repo.softDelete(buildTypeORMQuery<T>(filter))
  }

  async restore(
    filter: ITypeOrmFilter<T>['filter'],
    transactionManager?: QueryRunner,
  ): Promise<void> {
    const repo = this.getRepository(transactionManager)
    await repo.restore(buildTypeORMQuery<T>(filter))
  }

  getQueryBuilder(transactionManager?: QueryRunner): QueryBuilder<T> {
    const repo = this.getRepository(transactionManager)
    return repo.createQueryBuilder(repo.metadata.givenTableName)
  }

  async startTransaction(): Promise<QueryRunner> {
    const queryRunner = this.dataSource.createQueryRunner()
    await queryRunner.connect()
    await queryRunner.startTransaction()

    return queryRunner
  }

  async commitTransaction(transactionManager: QueryRunner): Promise<void> {
    await transactionManager.commitTransaction()
  }

  async rollbackTransaction(transactionManager: QueryRunner): Promise<void> {
    await transactionManager.rollbackTransaction()
  }

  async releaseTransaction(transactionManager: QueryRunner): Promise<void> {
    await transactionManager.release()
  }

  async invalidateQueryCache(key: string): Promise<void> {
    await this.dataSource.queryResultCache?.remove([key])
  }
}

export default TypeORMDriver
