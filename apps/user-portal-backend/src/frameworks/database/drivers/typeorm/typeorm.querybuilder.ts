import {
  FindOptionsWhere,
  And,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  MoreThan<PERSON>r<PERSON><PERSON><PERSON>,
  LessThanOrEqual,
  Like,
  Or,
  ArrayContains,
  In,
  Any,
  Equal,
  FindOperator,
  Between,
} from 'typeorm'

import {
  FilterObject,
  FilterValue,
  Operator,
  OperatorValue,
  isOperatorValue,
} from '../../constants/operators'

export function buildTypeORMQuery<T>(
  filter?: FilterObject<T>,
): FindOptionsWhere<T> {
  if (!filter) return {}

  const parsedFilter: FindOptionsWhere<T> = {}

  for (const [key, value] of Object.entries(filter)) {
    if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
      // Check if the value is a nested filter object
      if (!isOperatorValue(value)) {
        // If it is, recursively parse it
        parsedFilter[key as keyof T] = buildTypeORMQuery(
          value as FilterObject<T>,
        ) as any
      } else {
        const operatorFilter = parseOperators(value as OperatorValue<T>)
        if (operatorFilter !== undefined) {
          parsedFilter[key as keyof T] = operatorFilter as any
        }
      }
    } else {
      parsedFilter[key as keyof T] = value as T[keyof T] as any
    }
  }

  return parsedFilter
}

function parseOperators<T>(operators: {
  [operator in Operator]?: FilterValue<T>
}): FindOperator<any> | FindOptionsWhere<T> {
  const entries = Object.entries(operators)

  if (entries.length === 1) {
    const [operator, value] = entries[0]

    switch (operator) {
      case Operator.$and:
        return And(
          ...((value as any[]).map((v) => parseOperators(v as any)) as any[]),
        )
      case Operator.$or:
        return Or(
          ...((value as any[]).map((v) => parseOperators(v as any)) as any[]),
        )
      case Operator.$any:
        return Any(value as any)
      case Operator.$contains:
        return ArrayContains(value as any)
      case Operator.$between:
        return Between((value as any[])[0], (value as any[])[1])
      case Operator.$eq:
        return Equal(value)
      case Operator.$in:
        return In(value as any[])
      case Operator.$lt:
        return LessThan(value)
      case Operator.$lte:
        return LessThanOrEqual(value)
      case Operator.$ilike:
        return ILike(value)
      case Operator.$like:
        return Like(value)
      case Operator.$gt:
        return MoreThan(value)
      case Operator.$gte:
        return MoreThanOrEqual(value)
      case Operator.$not:
        return Not(
          parseOperators(value as { [operator in Operator]?: FilterValue<T> }),
        )
      default:
        throw new Error(`Unsupported operator: ${operator}`)
    }
  } else {
    // Wrap multiple entries in an AND clause
    return And(
      ...(entries.map(([op, val]) => parseOperators({ [op]: val })) as any[]),
    )
  }
}
