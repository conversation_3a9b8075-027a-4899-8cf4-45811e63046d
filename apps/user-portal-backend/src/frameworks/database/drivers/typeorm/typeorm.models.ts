import { DataSource } from 'typeorm'

import { UserModel } from '@backend/frameworks/database/models/user.model'
import { UserTeamRoleModel } from '@backend/frameworks/database/models/user-team-role.model'
import { UserInviteModel } from '@backend/frameworks/database/models/user-invite.model'
import { CheckModel } from '@backend/frameworks/database/models/check.model'
import { OrganizationModel } from '@backend/frameworks/database/models/organization.model'
import { TeamModel } from '@backend/frameworks/database/models/team.model'
import { RoleModel } from '@backend/frameworks/database/models/role.model'
import { PermissionModel } from '@backend/frameworks/database/models/permission.model'
import { WorkerModel } from '@backend/frameworks/database/models/worker.model'
import { RegionModel } from '@backend/frameworks/database/models/region.model'
import { OnCallSchedulerModel } from '@backend/frameworks/database/models/on-call-scheduler.model'
import { IntegrationSettingModel } from '@backend/frameworks/database/models/integration-setting.model'
import { SubscriptionPlanModel } from '@backend/frameworks/database/models/subscription-plan.model'
import { SubscriptionModel } from '@backend/frameworks/database/models/subscription.model'
import { SubscriptionItemModel } from '@backend/frameworks/database/models/subscription-item.model'
import { PlanResourceLimitModel } from '@backend/frameworks/database/models/plan-resource-limit.model'

import ModelRegistry from '../../model.registry'
import {
  EscalationContactModel,
  EscalationModel,
  EscalationStepModel,
} from '../../models/escalation.model'
import { SeverityModel } from '../../models/severity.model'
import { CustomIncidentModel } from '../../models/customIncident.model'
import { TokenModel } from '../../models/token.model.ts'
import { UserDeviceModel } from '../../models/user-device'
import { UserPlivoCredentialModel } from '../../models/user-plivo-credential.model'
import { SubscriptionUsageModel } from '../../models/subscription-usage.model'

import TypeORMDriver from './typeorm.database'

const registerTypeOrmModel = (dataSource: DataSource) => {
  const TypeOrmModelRegistry = new ModelRegistry()

  TypeOrmModelRegistry.registerModel(
    'UserModel',
    new TypeORMDriver<UserModel>(
      dataSource.getRepository(UserModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'UserTeamRoleModel',
    new TypeORMDriver<UserTeamRoleModel>(
      dataSource.getRepository(UserTeamRoleModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'UserInviteModel',
    new TypeORMDriver<UserInviteModel>(
      dataSource.getRepository(UserInviteModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'CheckModel',
    new TypeORMDriver<CheckModel>(
      dataSource.getRepository(CheckModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'OrganizationModel',
    new TypeORMDriver<OrganizationModel>(
      dataSource.getRepository(OrganizationModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'TeamModel',
    new TypeORMDriver<TeamModel>(
      dataSource.getRepository(TeamModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'RoleModel',
    new TypeORMDriver<RoleModel>(
      dataSource.getRepository(RoleModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'PermissionModel',
    new TypeORMDriver<PermissionModel>(
      dataSource.getRepository(PermissionModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'WorkerModel',
    new TypeORMDriver<WorkerModel>(
      dataSource.getRepository(WorkerModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'RegionModel',
    new TypeORMDriver<RegionModel>(
      dataSource.getRepository(RegionModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'EscalationModel',
    new TypeORMDriver<EscalationModel>(
      dataSource.getRepository(EscalationModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'OnCallSchedulerModel',
    new TypeORMDriver<OnCallSchedulerModel>(
      dataSource.getRepository(OnCallSchedulerModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'EscalationStepModel',
    new TypeORMDriver<EscalationStepModel>(
      dataSource.getRepository(EscalationStepModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'EscalationContactModel',
    new TypeORMDriver<EscalationContactModel>(
      dataSource.getRepository(EscalationContactModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'SeverityModel',
    new TypeORMDriver<SeverityModel>(
      dataSource.getRepository(SeverityModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'IntegrationSettingModel',
    new TypeORMDriver<IntegrationSettingModel>(
      dataSource.getRepository(IntegrationSettingModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'CustomIncidentModel',
    new TypeORMDriver<CustomIncidentModel>(
      dataSource.getRepository(CustomIncidentModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'TokenModel',
    new TypeORMDriver<TokenModel>(
      dataSource.getRepository(TokenModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'SubscriptionPlanModel',
    new TypeORMDriver<SubscriptionPlanModel>(
      dataSource.getRepository(SubscriptionPlanModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'SubscriptionModel',
    new TypeORMDriver<SubscriptionModel>(
      dataSource.getRepository(SubscriptionModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'SubscriptionItemModel',
    new TypeORMDriver<SubscriptionItemModel>(
      dataSource.getRepository(SubscriptionItemModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'SubscriptionUsageModel',
    new TypeORMDriver<SubscriptionUsageModel>(
      dataSource.getRepository(SubscriptionUsageModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'PlanResourceLimitModel',
    new TypeORMDriver<PlanResourceLimitModel>(
      dataSource.getRepository(PlanResourceLimitModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'UserDeviceModel',
    new TypeORMDriver<UserDeviceModel>(
      dataSource.getRepository(UserDeviceModel),
      dataSource,
    ),
  )

  TypeOrmModelRegistry.registerModel(
    'UserPlivoCredentialModel',
    new TypeORMDriver<UserPlivoCredentialModel>(
      dataSource.getRepository(UserPlivoCredentialModel),
      dataSource,
    ),
  )

  return TypeOrmModelRegistry
}

export default registerTypeOrmModel
