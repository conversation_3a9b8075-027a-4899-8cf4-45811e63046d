import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryC<PERSON>umn, Relation } from 'typeorm'

import { Id } from '@backend/cores/base/id.type'

import { UserModel } from './user.model'
import { RoleModel } from './role.model'
import { TeamModel } from './team.model'

@Entity('users_teams_roles')
export class UserTeamRoleModel {
  @PrimaryColumn()
  userId: Id

  @PrimaryColumn()
  teamId: Id

  @PrimaryColumn()
  roleId: Id

  @ManyToOne(() => UserModel, (user) => user.userTeamRoles, {
    eager: true,
    cascade: true,
  })
  @JoinColumn({ name: 'user_id' })
  user: Relation<UserModel>

  @ManyToOne(() => TeamModel, (team) => team.userTeamRoles, {
    eager: true,
    cascade: true,
  })
  @JoinColumn({ name: 'team_id' })
  team: Relation<TeamModel>

  @ManyToOne(() => RoleModel, (role) => role.userTeamRoles, {
    eager: true,
    cascade: true,
  })
  @JoinColumn({ name: 'role_id' })
  role: Relation<RoleModel>
}
