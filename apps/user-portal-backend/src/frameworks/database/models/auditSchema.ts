/* eslint-disable @typescript-eslint/no-var-requires */
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm'

import { Id } from '@backend/cores/base/id.type'

import { BaseSchema } from './schema.base'

import type { UserModel } from './user.model'

// Avoid circular dependency
const getUserModel = (): typeof UserModel => require('./user.model').UserModel

export class AuditableSchema extends BaseSchema {
  @ManyToOne(() => getUserModel(), (user) => user.id)
  @JoinColumn({ name: 'created_by' })
  createdBy: Id

  @ManyToOne(() => getUserModel(), (user) => user.id)
  @JoinColumn({ name: 'updated_by' })
  updatedBy: Id
}
