import {
  Column,
  <PERSON><PERSON><PERSON>,
  DeleteDate<PERSON><PERSON>umn,
  ManyToOne,
  <PERSON>lation,
  JoinColumn,
} from 'typeorm'

import { TeamModel } from '@backend/frameworks/database/models/team.model'

import { AuditableSchema } from './auditSchema'
import { EscalationModel } from './escalation.model'

@Entity('checks')
export class CheckModel extends AuditableSchema {
  //
  // Basic section
  //
  @Column('varchar', { nullable: false })
  type: string

  @Column('varchar', { nullable: true })
  title: string

  @Column('text', { array: true, nullable: true })
  tags: string[]

  @Column('varchar', { nullable: false })
  url: string

  @Column('int', { nullable: true })
  port: number

  @Column('text', { array: true, nullable: true })
  locations: string[]

  @Column('varchar', { nullable: false })
  status: string

  @Column('int', { nullable: false })
  interval: number

  @Column('int', { nullable: false })
  recoverPeriod: number

  @Column('int', { nullable: true })
  confirmPeriod: number

  @Column({ default: true, nullable: true })
  handleRedirect: boolean
  //
  // Advance section: customize request/response
  //
  @Column('varchar', { nullable: false })
  method: string

  @Column({ type: 'jsonb', nullable: true })
  requestHeaders: Record<string, string>

  @Column('text', { nullable: true })
  requestBody: string

  @Column('text', { nullable: true })
  requiredKeyword: string

  @Column('int', { nullable: true })
  timeout: number

  @Column('int', { array: true, nullable: true })
  expectedStatusCodes: number[]

  @Column('timestamp', { nullable: true })
  lastCheckedAt: Date

  @Column('timestamp', { nullable: true })
  pausedAt: Date

  @ManyToOne(() => EscalationModel, (escalation) => escalation, {
    nullable: true,
  })
  @JoinColumn()
  escalation?: Relation<EscalationModel>

  @Column('varchar', { nullable: true })
  escalationId: string

  //
  // Maintenance window
  //
  @Column('varchar', { array: true, nullable: true })
  maintenanceDays: string[]

  @Column('varchar', { nullable: true })
  maintenanceFrom: string

  @Column('varchar', { nullable: true })
  maintenanceTo: string

  @Column('varchar', { nullable: true })
  maintenanceTimeZone: string

  //
  // Basic Auth
  //
  @Column('varchar', { nullable: true })
  authUsername: string

  @Column('varchar', { nullable: true })
  authPassword: string

  //
  // Delete
  //
  @DeleteDateColumn()
  deletedAt?: Date

  // Relations
  @ManyToOne(() => TeamModel, (team) => team.checks)
  team: Relation<TeamModel>

  @Column('varchar', { nullable: false })
  teamId: string
}
