import { <PERSON>tity, Column, OneToMany } from 'typeorm'

import {
  PlanType,
  Period,
  PlanSource,
} from '../../../modules/subscription/types/subscription-plan.types'

import { AuditableSchema } from './auditSchema'
import { PlanResourceLimitModel } from './plan-resource-limit.model'

@Entity('subscription_plans')
export class SubscriptionPlanModel extends AuditableSchema {
  @Column()
  name: string

  @Column({
    type: 'varchar',
    default: PlanType.BASE_PLAN,
  })
  planType: PlanType

  @Column({
    type: 'varchar',
    default: PlanSource.STRIPE,
  })
  source: PlanSource

  @Column({
    type: 'varchar',
    default: Period.MONTH,
  })
  period: Period

  @Column({ name: 'stripe_price_id', nullable: true })
  stripePriceId: string

  @Column({ name: 'is_active', default: true })
  isActive: boolean

  @OneToMany(() => PlanResourceLimitModel, (limit) => limit.subscriptionPlan, {
    cascade: true,
    eager: true,
  })
  resourceLimits: PlanResourceLimitModel[]
}
