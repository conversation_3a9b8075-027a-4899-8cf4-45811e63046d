import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany, Relation } from 'typeorm'

import { UserInviteModel } from '@backend/frameworks/database/models/user-invite.model'
import { OnCallSchedulerModel } from '@backend/frameworks/database/models/on-call-scheduler.model'
import { IntegrationSettingModel } from '@backend/frameworks/database/models/integration-setting.model'

import { OrganizationModel } from './organization.model'
import { CheckModel } from './check.model'
import { UserTeamRoleModel } from './user-team-role.model'
import { AuditableSchema } from './auditSchema'
import { SeverityModel } from './severity.model'
import { EscalationModel } from './escalation.model'
import { CustomIncidentModel } from './customIncident.model'

@Entity('teams')
export class TeamModel extends AuditableSchema {
  @Column()
  name: string

  @ManyToOne(() => OrganizationModel, (organization) => organization.teams)
  organization: Relation<OrganizationModel>

  @OneToMany(() => UserTeamRoleModel, (userTeamRole) => userTeamRole.team)
  userTeamRoles: Relation<UserTeamRoleModel>[]

  @OneToMany(() => CheckModel, (check) => check.team)
  checks: Relation<CheckModel>[]

  @OneToMany(() => CustomIncidentModel, (customIncident) => customIncident.team)
  customIncidents: Relation<CustomIncidentModel>[]

  @OneToMany(() => SeverityModel, (severity) => severity.team)
  severities: Relation<SeverityModel>[]

  @OneToMany(() => EscalationModel, (escalation) => escalation.team)
  escalations: Relation<EscalationModel>[]

  @OneToMany(() => UserInviteModel, (userInvite) => userInvite.team)
  invites: Relation<UserInviteModel>[]

  @OneToMany(
    () => OnCallSchedulerModel,
    (onCallScheduler) => onCallScheduler.team,
  )
  onCallSchedulers: Relation<OnCallSchedulerModel>[]

  @OneToMany(
    () => IntegrationSettingModel,
    (integrationSetting) => integrationSetting.team,
  )
  integrationSettings: Relation<IntegrationSettingModel>[]
}
