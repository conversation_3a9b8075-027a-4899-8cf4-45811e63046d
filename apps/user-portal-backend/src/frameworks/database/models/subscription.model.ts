import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  <PERSON><PERSON>,
} from 'typeorm'
import {
  SubscriptionStatus,
  OveragePolicyType,
} from '@libs/shared/constants/subscription'

import { AuditableSchema } from '@backend/frameworks/database/models/auditSchema'
import { Id } from '@backend/cores/base/id.type'
import { SubscriptionItemModel } from '@backend/frameworks/database/models/subscription-item.model'
import { PlanSource } from '@backend/modules/subscription/types/subscription-plan.types'

import { OrganizationModel } from './organization.model'

@Entity('subscriptions')
export class SubscriptionModel extends AuditableSchema {
  @Column({ name: 'organization_id' })
  organizationId: Id

  @Column({ name: 'stripe_customer_id', nullable: true })
  stripeCustomerId: string

  @Column({ name: 'stripe_subscription_id', nullable: true })
  stripeSubscriptionId: string // This is a subscription ID from Stripe

  @Column({ type: 'varchar', default: SubscriptionStatus.INCOMPLETE })
  status: SubscriptionStatus

  @Column({ name: 'current_period_start_date', type: 'date', nullable: true })
  currentPeriodStartDate: Date

  @Column({ name: 'current_period_end_date', type: 'date', nullable: true })
  currentPeriodEndDate: Date

  @Column({ name: 'canceled_at', type: 'timestamp', nullable: true })
  canceledAt: Date

  @Column({ name: 'grace_period_ends_at', type: 'timestamp', nullable: true })
  gracePeriodEndsAt: Date

  @Column({ name: 'usage_billing_enabled', default: true })
  usageBillingEnabled: boolean

  @Column({
    name: 'overage_policy_fixed_resources',
    type: 'varchar',
    default: OveragePolicyType.CHARGE,
  })
  overagePolicyFixedResources: OveragePolicyType

  @ManyToOne(
    () => OrganizationModel,
    (organization) => organization.subscriptions,
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Relation<OrganizationModel>

  @OneToMany(
    () => SubscriptionItemModel,
    (subscriptionItem) => subscriptionItem.subscription,
  )
  subscriptionItems: Relation<SubscriptionItemModel[]>

  @Column({ name: 'source', type: 'varchar' })
  source: PlanSource
}
