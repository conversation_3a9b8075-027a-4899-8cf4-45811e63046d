import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>,
  ManyToMany,
  OneToMany,
  OneToOne,
  Relation,
} from 'typeorm'

import { OnCallSchedulerModel } from '@backend/frameworks/database/models/on-call-scheduler.model'
import { InternalRole } from '@backend/modules/user/constants/user'

import { BaseSchema } from './schema.base'
import { OrganizationModel } from './organization.model'
import { UserTeamRoleModel } from './user-team-role.model'
import { UserDeviceModel } from './user-device'
import { UserPlivoCredentialModel } from './user-plivo-credential.model'

@Entity('users')
export class UserModel extends BaseSchema {
  @Column()
  firebaseId: string

  @Column()
  email: string

  @Column('varchar', { nullable: true })
  phoneNumber?: string

  @Column('varchar', { nullable: true })
  firstName?: string

  @Column('varchar', { nullable: true })
  lastName?: string

  @Column('timestamptz', { nullable: true })
  lastLoginAt?: Date

  @Column('varchar', { nullable: true })
  lastLoginMethod?: string

  @Column('varchar', { nullable: true })
  internalRole?: InternalRole

  @Column('varchar', { nullable: true })
  stripeCustomerId: string

  @Column('varchar', { nullable: true })
  aliasToken?: string

  @ManyToMany(() => OrganizationModel, (organization) => organization.users, {
    cascade: ['insert', 'update'],
    onDelete: 'CASCADE',
  })
  @JoinTable({ name: 'users_organizations' })
  organizations?: Relation<OrganizationModel>[]

  @OneToMany(() => UserTeamRoleModel, (userRole) => userRole.user)
  @JoinTable({
    name: 'users_teams_roles',
  })
  userTeamRoles?: Relation<UserTeamRoleModel>[]

  @ManyToMany(() => OnCallSchedulerModel, (scheduler) => scheduler.users, {
    cascade: ['insert', 'update'],
    onDelete: 'CASCADE',
  })
  @JoinTable({ name: 'on_call_schedulers_users' })
  onCallSchedulers?: Relation<OnCallSchedulerModel>[]

  @OneToMany(() => UserDeviceModel, (device) => device.user)
  devices: Relation<UserDeviceModel>[]

  @OneToOne(
    () => UserPlivoCredentialModel,
    (plivoCredential) => plivoCredential.user,
    { cascade: true },
  )
  plivoCredential: Relation<UserPlivoCredentialModel>
}
