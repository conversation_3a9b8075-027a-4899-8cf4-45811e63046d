import { Entity, Column } from 'typeorm'

import { BaseSchema } from './schema.base'

@Entity('workers')
export class WorkerModel extends BaseSchema {
  @Column()
  ip: string

  @Column()
  type: string

  @Column()
  location: string

  @Column()
  region: string

  // @ManyToMany(() => CheckModel, (check) => check.workerIds)
  // @JoinTable({ name: 'checks_workers' })
  // checkIds?: Relation<CheckModel>[]
}
