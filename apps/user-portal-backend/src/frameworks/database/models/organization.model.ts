import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>any, Relation } from 'typeorm'

import { SubscriptionModel } from '@backend/frameworks/database/models/subscription.model'

import { BaseSchema } from './schema.base'
import { UserModel } from './user.model'
import { TeamModel } from './team.model'
import { RoleModel } from './role.model'

@Entity('organizations')
export class OrganizationModel extends BaseSchema {
  @Column()
  name: string

  @Column({ nullable: true })
  ownerId: string

  @OneToMany(() => UserModel, (user) => user.organizations)
  @JoinTable({ name: 'users_organizations' })
  users: Relation<UserModel>[]

  @OneToMany(() => TeamModel, (team) => team.organization)
  teams: Relation<TeamModel>[]

  @OneToMany(() => RoleModel, (role) => role.organization)
  roles: Relation<RoleModel>[]

  @OneToMany(
    () => SubscriptionModel,
    (subscription) => subscription.organization,
  )
  subscriptions: Relation<SubscriptionModel>[]
}
