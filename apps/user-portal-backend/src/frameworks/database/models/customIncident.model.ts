import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  DeleteDateColumn,
  ManyTo<PERSON>ne,
  <PERSON>lation,
  Join<PERSON><PERSON>umn,
} from 'typeorm'

import { TeamModel } from '@backend/frameworks/database/models/team.model'

import { AuditableSchema } from './auditSchema'
import { EscalationModel } from './escalation.model'

@Entity('custom_incidents')
export class CustomIncidentModel extends AuditableSchema {
  @Column('varchar', { nullable: true })
  title: string

  // @Column('varchar', { nullable: false })
  // url: string

  @Column('varchar', { nullable: false })
  status: string

  @ManyToOne(() => EscalationModel, (escalation) => escalation, {
    nullable: true,
  })
  @JoinColumn()
  escalation?: Relation<EscalationModel>

  @Column('varchar', { nullable: true })
  escalationId: string

  //
  // Delete
  //
  @DeleteDateColumn()
  deletedAt?: Date

  // Relations
  @ManyToOne(() => TeamModel, (team) => team.customIncidents)
  team: Relation<TeamModel>

  @Column('varchar', { nullable: false })
  teamId: string
}
