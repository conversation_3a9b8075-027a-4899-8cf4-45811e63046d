import { Column, Entity } from 'typeorm'

import { BaseSchema } from './schema.base'

@Entity('subscription_usage')
export class SubscriptionUsageModel extends BaseSchema {
  @Column({ name: 'organization_id' })
  organizationId: string

  @Column({ name: 'subscription_item_id', nullable: true })
  subscriptionItemId: string

  @Column({ name: 'usage_type' })
  usageType: string

  @Column({ name: 'usage_value' })
  usageValue: number

  @Column({ name: 'usage_timestamp' })
  usageTimestamp: Date

  @Column({ name: 'billing_cycle_start_date', nullable: true })
  billingCycleStartDate: Date

  @Column({ name: 'billing_cycle_end_date', nullable: true })
  billingCycleEndDate: Date

  @Column({ name: 'reported_to_stripe', nullable: true })
  reportedToStripe: boolean

  @Column({ name: 'stripe_price_id', nullable: true })
  stripePriceId: string

  @Column({ name: 'stripe_usage_record_id', nullable: true })
  stripeUsageRecordId: string

  @Column({ name: 'is_billable', nullable: true })
  isBillable: boolean

  @Column({ name: 'notes', nullable: true })
  notes: string

  @Column({ name: 'created_by', nullable: true })
  createdBy: string

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string
}
