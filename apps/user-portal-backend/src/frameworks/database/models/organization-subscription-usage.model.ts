import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, Relation } from 'typeorm'

import { SubscriptionModel } from '@backend/frameworks/database/models/subscription.model'
import { OrganizationModel } from '@backend/frameworks/database/models/organization.model'

import { BaseSchema } from './schema.base'

@Entity('organization-subscription-usages')
export class OrganizationSubscriptionUsageModel extends BaseSchema {
  @ManyToOne(() => OrganizationModel, (organization) => organization.teams)
  organization: Relation<OrganizationModel>

  @ManyToOne(
    () => SubscriptionModel,
    (subscription) => subscription.organization,
  )
  subscription: Relation<OrganizationModel>

  @Column({ default: 0 })
  membersAllowed: number

  @Column({ default: 0 })
  teamsAllowed: number

  @Column({ default: 0 })
  checksAllowed: number

  @Column({ default: 0 })
  integrationsAllowed: number

  @Column({ default: 0 })
  smsAllowed: number

  @Column({ default: 0 })
  phoneCallsAllowed: number
}
