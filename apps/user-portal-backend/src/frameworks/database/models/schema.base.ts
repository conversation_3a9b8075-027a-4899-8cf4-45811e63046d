import {
  BaseEntity,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryColumn,
} from 'typeorm'

import { Id } from '@backend/cores/base/id.type'

export class BaseSchema extends BaseEntity {
  @PrimaryColumn('varchar', { nullable: false })
  id: Id

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date

  @DeleteDateColumn()
  deletedAt?: Date
}
