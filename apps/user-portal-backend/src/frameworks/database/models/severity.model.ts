import { Entity, Column, ManyToOne, Relation } from 'typeorm'

import { AuditableSchema } from './auditSchema'
import { TeamModel } from './team.model'

@Entity('severities')
export class SeverityModel extends AuditableSchema {
  @Column('varchar', { default: 'Critical alert', nullable: false })
  name: string

  // Store severityIntegration as an array of strings
  @Column('varchar', { array: true, nullable: true })
  alerts: string[]

  // @OneToMany(
  //   () => SeverityIntegrationModel,
  //   (integration) => integration.severity,
  //   {
  //     cascade: true,
  //     onDelete: 'CASCADE',
  //     // eager: true,
  //   },
  // )
  // severityIntegration: Relation<SeverityIntegrationModel>[]

  // Relations
  @ManyToOne(() => TeamModel, (team) => team.severities)
  team: Relation<TeamModel>

  @Column('varchar', { nullable: false })
  teamId: string
}

// @Entity('severity_integrations')
// export class SeverityIntegrationModel extends BaseSchema {
//   @Column('varchar', { nullable: false })
//   integrationType: string

//   @Column('varchar', { nullable: true })
//   integrationId: Id

//   @ManyToOne(() => SeverityModel, (severity) => severity.severityIntegration, {
//     onDelete: 'CASCADE',
//     eager: true,
//   })
//   severity: Relation<SeverityModel>
// }
