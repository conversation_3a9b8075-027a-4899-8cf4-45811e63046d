import { <PERSON><PERSON><PERSON>, Column } from 'typeorm'

import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'

import { BaseSchema } from './schema.base'

@Entity('permissions')
export class PermissionModel extends BaseSchema {
  @Column('text')
  scope: PermissionScope

  @Column('text')
  action: PermissionAction

  @Column('text')
  type: PermissionType
}
