import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'
import { ResourceType } from '@libs/shared/constants/subscription'

import { AuditableSchema } from './auditSchema'
import { SubscriptionPlanModel } from './subscription-plan.model'

@Entity('plan_resource_limits')
export class PlanResourceLimitModel extends AuditableSchema {
  @Column()
  subscriptionPlanId: string

  @Column({
    type: 'varchar',
    enum: ResourceType,
  })
  resourceType: ResourceType

  @Column({ type: 'int', default: 0 })
  includedQuantity: number

  @Column()
  overagePrice: number

  @Column()
  overageStripePriceId: string

  // Relations
  @ManyToOne(() => SubscriptionPlanModel, (plan) => plan.id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'subscription_plan_id' })
  subscriptionPlan: SubscriptionPlanModel
}
