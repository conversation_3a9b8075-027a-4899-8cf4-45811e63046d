import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryColumn,
  <PERSON>lation,
} from 'typeorm'

import { Id } from '@backend/cores/base/id.type'

import { UserModel } from './user.model'
import { BaseSchema } from './schema.base'

@Entity('user_plivo_credential')
export class UserPlivoCredentialModel extends BaseSchema {
  @PrimaryColumn()
  userId: Id

  @OneToOne(() => UserModel, (user) => user.plivoCredential, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user: Relation<UserModel>

  @Column()
  username: string

  @Column()
  password: string

  @Column()
  alias: string
}
