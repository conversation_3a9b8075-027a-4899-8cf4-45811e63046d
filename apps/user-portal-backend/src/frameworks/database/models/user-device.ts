import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryColumn,
  Relation,
} from 'typeorm'

import { Id } from '@backend/cores/base/id.type'

import { UserModel } from './user.model'
import { BaseSchema } from './schema.base'

@Entity('user_devices')
export class UserDeviceModel extends BaseSchema {
  @PrimaryColumn()
  userId: Id

  @Column()
  deviceId: string

  @Column()
  deviceName: string

  @Column()
  deviceType: string

  @ManyToOne(() => UserModel, (user) => user.devices, {
    eager: true,
    cascade: true,
  })
  @JoinColumn({ name: 'user_id' })
  user: Relation<UserModel>
}
