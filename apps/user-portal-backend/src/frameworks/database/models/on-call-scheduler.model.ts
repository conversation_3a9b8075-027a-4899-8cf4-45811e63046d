import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToMany,
} from 'typeorm'

import { BaseSchema } from '@backend/frameworks/database/models/schema.base'
import { UserModel } from '@backend/frameworks/database/models/user.model'
import { TeamModel } from '@backend/frameworks/database/models/team.model'
import { Id } from '@backend/cores/base/id.type'

@Entity('on_call_schedulers')
export class OnCallSchedulerModel extends BaseSchema {
  @Column()
  type: string

  @Column()
  startDate: Date

  @Column({ nullable: true })
  endDate: Date

  @Column({ nullable: true })
  allDay: boolean

  @Column({ nullable: true })
  recurringPattern: string

  @Column({ name: 'team_id' })
  teamId: Id

  @ManyToMany(() => UserModel, (user) => user.onCallSchedulers)
  @JoinTable({ name: 'on_call_schedulers_users' })
  users: Relation<UserModel>[]

  @ManyToOne(() => TeamModel, (team) => team.onCallSchedulers)
  @JoinColumn({ name: 'team_id' })
  team: Relation<TeamModel>

  @Column('varchar', { nullable: true })
  createdBy: string

  @Column('varchar', { nullable: true })
  updatedBy: string
}
