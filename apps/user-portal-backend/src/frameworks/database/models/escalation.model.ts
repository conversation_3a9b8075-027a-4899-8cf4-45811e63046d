import { Entity, Column, Relation, <PERSON>ToOne, OneToMany } from 'typeorm'

import { AuditableSchema } from './auditSchema'
import { SeverityModel } from './severity.model'
import { BaseSchema } from './schema.base'
import { TeamModel } from './team.model'
import { CheckModel } from './check.model'

@Entity('escalations')
export class EscalationModel extends AuditableSchema {
  @Column('varchar', { nullable: false })
  name: string

  @Column('int', { default: 0, nullable: false })
  repeatCount: number

  @Column('int', { default: 0, nullable: false })
  repeatDelay: number

  @OneToMany(() => EscalationStepModel, (step) => step.escalation, {
    cascade: true,
    onDelete: 'CASCADE', // Cascade delete on the foreign key constraint
  })
  escalationSteps: Relation<EscalationStepModel>[]

  @OneToMany(() => CheckModel, (check) => check, { nullable: true })
  check?: Relation<CheckModel>

  @ManyToOne(() => TeamModel, (team) => team.escalations)
  team: Relation<TeamModel>

  // Created and Updated By fields
  @Column('varchar', { nullable: true })
  createdBy: string

  @Column('varchar', { nullable: true })
  updatedBy: string
}

@Entity('escalation_steps')
export class EscalationStepModel extends BaseSchema {
  @OneToMany(() => EscalationContactModel, (step) => step.escalationStep, {
    eager: true,
    cascade: true,
    onDelete: 'CASCADE', // Cascade delete on the foreign key constraint
  })
  contacts: Relation<EscalationContactModel>[]

  @Column('int', { nullable: false })
  stepDelay: number

  // ManyToOne relationship with SeverityModel
  @ManyToOne(() => SeverityModel, (severity) => severity.id, {
    nullable: false,
  })
  severity: Relation<SeverityModel>

  @ManyToOne(
    () => EscalationModel,
    (escalation) => escalation.escalationSteps,
    { nullable: false },
  )
  escalation: Relation<EscalationModel>
}

@Entity('escalation_contacts')
export class EscalationContactModel extends BaseSchema {
  @Column('varchar', { nullable: false })
  contactType: string

  @Column('varchar', { nullable: true })
  contactId: string | null

  @ManyToOne(() => EscalationStepModel)
  escalationStep: Relation<EscalationStepModel>
}
