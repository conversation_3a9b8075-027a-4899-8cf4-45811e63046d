import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  OneToMany,
  ManyToOne,
  ManyToMany,
  Join<PERSON><PERSON>,
  Re<PERSON>,
} from 'typeorm'

import { Id } from '@backend/cores/base/id.type'

import { OrganizationModel } from './organization.model'
import { PermissionModel } from './permission.model'
import { UserTeamRoleModel } from './user-team-role.model'
import { AuditableSchema } from './auditSchema'

@Entity('roles')
export class RoleModel extends AuditableSchema {
  @Column()
  name: string

  @ManyToMany(() => PermissionModel, { eager: true })
  @JoinTable({ name: 'roles_permissions' })
  permissions: Relation<PermissionModel>[]

  @ManyToOne(() => OrganizationModel, (organization) => organization.roles)
  organization: Relation<OrganizationModel>

  @Column()
  organizationId: Id

  @Column('boolean', { nullable: false })
  default: boolean

  @OneToMany(() => UserTeamRoleModel, (userTeamRole) => userTeamRole.role)
  userTeamRoles: Relation<UserTeamRoleModel>[]
}
