import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  Join<PERSON><PERSON><PERSON>n,
  ManyToOne,
  PrimaryColumn,
  Relation,
} from 'typeorm'

import { TeamModel } from '@backend/frameworks/database/models/team.model'
import { BaseSchema } from '@backend/frameworks/database/models/schema.base'
import { Id } from '@backend/cores/base/id.type'

import { RoleModel } from './role.model'

@Entity('user_invites')
@Index(['email', 'teamId'], { unique: true })
export class UserInviteModel extends BaseSchema {
  @PrimaryColumn('varchar', { nullable: false })
  email: string

  @PrimaryColumn('varchar', { nullable: false })
  teamId: Id

  @Column('varchar', { nullable: false })
  roleId: Id

  @ManyToOne(() => TeamModel, (team) => team.invites)
  @JoinColumn({ name: 'team_id' })
  team: Relation<TeamModel>

  @Column()
  status: string

  @ManyToOne(() => RoleModel)
  @JoinColumn({ name: 'role_id' })
  role: Relation<RoleModel>

  @Column()
  expiredAt: Date
}
