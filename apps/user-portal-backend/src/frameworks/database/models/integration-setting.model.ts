import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm'

import { BaseSchema } from '@backend/frameworks/database/models/schema.base'
import { TeamModel } from '@backend/frameworks/database/models/team.model'
import { Id } from '@backend/cores/base/id.type'

@Entity('integration-settings')
export class IntegrationSettingModel extends BaseSchema {
  @Column()
  name: string

  @Column()
  type: string

  @Column({ nullable: true })
  identity: string

  @Column({ nullable: true, type: 'jsonb' })
  config: object

  @Column({ name: 'team_id' })
  teamId: Id

  @ManyToOne(() => TeamModel, (team) => team.integrationSettings)
  @JoinColumn({ name: 'team_id' })
  team: Relation<TeamModel>

  @Column('varchar', { nullable: true })
  createdBy: string

  @Column('varchar', { nullable: true })
  updatedBy: string
}
