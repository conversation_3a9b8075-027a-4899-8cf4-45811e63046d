export enum Operator {
  $eq = '$eq',
  $gt = '$gt',
  $lt = '$lt',
  $gte = '$gte',
  $lte = '$lte',
  $like = '$like',
  $ilike = '$ilike',
  $and = '$and',
  $or = '$or',
  $not = '$not',
  $contains = '$contains',
  $in = '$in',
  $any = '$any',
  $between = '$between',
}

export type FilterValue<T> = T | T[] | FilterObject<T> | FilterObject<T>[]

export type OperatorValue<T> = {
  [operator in Operator]?: FilterValue<T> | OperatorValue<T>
}

export type FilterObject<T> = {
  [P in keyof T]?: FilterValue<T[P]> | OperatorValue<T[P]>
}

export const isOperatorValue = <T>(value: any): value is OperatorValue<T> => {
  return Object.keys(value).every((key) =>
    Object.values(Operator).includes(key as any),
  )
}
