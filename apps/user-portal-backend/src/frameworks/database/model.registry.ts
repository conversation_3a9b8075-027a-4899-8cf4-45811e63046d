import { ObjectLiteral } from 'typeorm'

import { IDatabase } from './interfaces/database.interface'
import TypeORMDatabase from './drivers/typeorm/typeorm.database'

class ModelRegistry {
  private services: { [key: string]: IDatabase<any> }

  constructor() {
    this.services = {}
  }

  registerModel<T>(modelName: string, service: IDatabase<T>): void {
    this.services[modelName] = service
  }

  getModel<T>(modelName: string): IDatabase<T> {
    if (!this.services[modelName]) {
      throw new Error(`No service registered for model: ${modelName}`)
    }
    return this.services[modelName]
  }

  getTypeOrm<T extends ObjectLiteral>(modelName: string): TypeORMDatabase<T> {
    const service = this.services[modelName]
    if (!service || !(service instanceof TypeORMDatabase)) {
      throw new Error(`No TypeORM service registered for model: ${modelName}`)
    }
    return service as TypeORMDatabase<T>
  }
}

export default ModelRegistry
