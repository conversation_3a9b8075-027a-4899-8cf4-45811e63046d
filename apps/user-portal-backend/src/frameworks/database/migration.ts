// eslint-disable-next-line import/order
import { config } from 'dotenv'
config()

import { DataSource } from 'typeorm'

import { configService } from '@backend/cores/config/config.service'

import { exit } from 'process'

const entityContexts = require.context('./models/', true, /\.ts$/)

const entities = entityContexts
  .keys()
  .map((modulePath) => entityContexts(modulePath))
  .reduce((result, migrationModule) => {
    return Object.assign(result, migrationModule)
  })

const migrationContexts = require.context('../../migration/', true, /\.ts$/)

const migrations = migrationContexts
  .keys()
  .map((modulePath) => migrationContexts(modulePath))
  .reduce((result, migrationModule) => {
    return Object.assign(result, migrationModule)
  })

const dataSource = new DataSource({
  ...configService.getTypeOrmConfig(),
  entities: Object.values(entities),
  migrations: Object.values(migrations),
  synchronize: false, // Override synchronize to false for migrations
})

async function runMigrations() {
  try {
    await dataSource.initialize()
    const transactions = await dataSource.runMigrations({
      transaction: 'each',
    })

    console.log(
      'Migrations executed:',
      JSON.stringify(
        transactions.map((t) => t.name),
        null,
        1,
      ),
    )
  } catch (error) {
    console.error('Error executing migrations:', error)
  } finally {
    await dataSource.destroy()

    exit(0)
  }
}

runMigrations()
