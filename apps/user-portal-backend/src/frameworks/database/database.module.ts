import { Modu<PERSON> } from '@nestjs/common'
import { DataSource } from 'typeorm'

import { configService } from '@backend/cores/config/config.service'

import { DatabaseProvider } from './database.provider'

// Workaround for webpack compilation cannot resolve entities
const contexts = (require as any).context('./models/', true, /\.ts$/)
const entities = contexts
  .keys()
  .map((modulePath) => contexts(modulePath))
  .reduce(
    (result, entityModule) =>
      result.concat(Object.keys(entityModule).map((key) => entityModule[key])),
    [],
  )

export const databaseProviders = [
  {
    provide: DataSource,
    useFactory: async () => {
      return new DataSource({
        ...configService.getTypeOrmConfig(),
        entities,
      }).initialize()
    },
  },
  DatabaseProvider,
]

@Module({
  imports: [],
  providers: [...databaseProviders],
  exports: [DatabaseProvider, DataSource],
})
export class DatabaseModule {}
