import { Injectable } from '@nestjs/common'
import { ObjectLiteral } from 'typeorm'

import ModelRegistry from './model.registry'

@Injectable()
class Database {
  private modelRegistry: ModelRegistry

  constructor(modelRegistry: ModelRegistry) {
    this.modelRegistry = modelRegistry
  }

  get models() {
    return new Proxy(this.modelRegistry, {
      get: (target, modelName: string) => {
        return target.getModel(modelName)
      },
    })
  }

  get typeorm() {
    return <T extends ObjectLiteral>(modelName: string) =>
      this.modelRegistry.getTypeOrm<T>(modelName)
  }
}

export default Database
