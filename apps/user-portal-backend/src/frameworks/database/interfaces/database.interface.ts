import { Id } from '@backend/cores/base/id.type'

import { Filter, IFilter } from './filter.interface'
import { IPaginate } from './paginate.interface'
import { ITransactional } from './transaction.interface'

export interface IDatabase<T, DBFilter = IFilter, TransactionManager = any>
  extends ITransactional<TransactionManager> {
  count(
    filter: Filter<T>,
    transactionManager?: TransactionManager,
  ): Promise<number>

  findAll(
    query?: DBFilter,
    transactionManager?: TransactionManager,
  ): Promise<T[]>
  find(
    query: DBFilter,
    transactionManager?: TransactionManager,
  ): Promise<IPaginate<T>>

  findById(
    id: Id,
    query?: DBFilter,
    transactionManager?: TransactionManager,
  ): Promise<Nullable<T>>
  findOne(query: DBFilter): Promise<Nullable<T>>

  create(data: any, transaction?: TransactionManager): Promise<T>
  createMany(data: T[], transaction?: TransactionManager): Promise<T[]>

  updateById(
    id: Id,
    data: any,
    transaction?: TransactionManager,
  ): Promise<Nullable<T>>
  update(
    data: any,
    filter: Filter<T>,
    transaction?: TransactionManager,
  ): Promise<number> // return number of affected rows

  delete(filter: Filter<T>, transaction?: TransactionManager): Promise<void>

  softDelete(filter: Filter<T>, transaction?: TransactionManager): Promise<void>

  restore(filter: Filter<T>, transaction?: TransactionManager): Promise<void>

  getQueryBuilder(transaction?: TransactionManager): any

  invalidateQueryCache(key: string): Promise<void>
}
