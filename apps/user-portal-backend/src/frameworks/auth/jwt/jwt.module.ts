import { Module } from '@nestjs/common'
import { JwtModule, JwtService } from '@nestjs/jwt'
import { PassportModule } from '@nestjs/passport'

import { JwtStrategy } from './jwt.strategy'
import { RefreshTokenStrategy } from './refreshToken.strategy'

@Module({
  imports: [PassportModule, JwtModule.register({})],
  providers: [JwtStrategy, RefreshTokenStrategy, JwtService],
  exports: [JwtModule, JwtService],
})
export class JwtAuthModule {}
