import { Injectable } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { Strategy } from 'passport-jwt'

import { configService } from '@backend/cores/config/config.service'
import { JwtPayload } from '@backend/cores/auth/auth.interface'

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor() {
    super(configService.getJWTConfig('token'))
  }

  async validate(payload: JwtPayload) {
    return payload
  }
}
