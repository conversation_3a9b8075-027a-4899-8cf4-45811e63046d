import { PassportStrategy } from '@nestjs/passport'
import { Strategy } from 'passport-jwt'
import { Request } from 'express'
import { Injectable, UnauthorizedException } from '@nestjs/common'

import { configService } from '@backend/cores/config/config.service'

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  constructor() {
    super(configService.getJWTConfig('refreshToken'))
  }

  validate(req: Request, payload: any) {
    const refreshToken = req.get('Authorization')?.replace('Bearer ', '').trim()

    if (!refreshToken) {
      throw new UnauthorizedException('Refresh token is missing')
    }

    return { ...payload, refreshToken }
  }
}
