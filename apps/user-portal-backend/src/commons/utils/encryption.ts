import { configService } from '@backend/cores/config/config.service'

import * as crypto from 'crypto'

const iv = '16charactersiv!!'

export class EncryptionHelper {
  static decrypt(encryptedText: string): string {
    const encryptedBuffer = Buffer.from(encryptedText, 'base64url')
    const decipher = crypto.createDecipheriv(
      'aes-128-cbc',
      configService.getEncryptionConfig().enc,
      iv,
    )
    let decrypted = decipher.update(encryptedBuffer, undefined, 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  }
}
