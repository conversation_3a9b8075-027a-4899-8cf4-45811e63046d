import { SetMetadata } from '@nestjs/common'

import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'

export const CHECK_PERMISSIONS_KEY = 'check_permissions'
export const CheckPermissions = (
  permissions?: {
    action: PermissionAction
    scope: PermissionScope
    type?: PermissionType
  }[],
) => SetMetadata(CHECK_PERMISSIONS_KEY, permissions)
