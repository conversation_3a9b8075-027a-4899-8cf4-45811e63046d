import { createParamDecorator, ExecutionContext } from '@nestjs/common'

import { OrgTeamHeadersDto } from '../dto/org-team-headers.dto'

export const OrgTeamHeaders = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest()

    const orgTeamHeadersDto = new OrgTeamHeadersDto()
    orgTeamHeadersDto.organizationId = request.headers['organization-id']
    orgTeamHeadersDto.teamId = request.headers['team-id']

    return orgTeamHeadersDto
  },
)
