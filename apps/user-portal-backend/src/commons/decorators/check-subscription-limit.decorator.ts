import { UseGuards, applyDecorators, SetMetadata } from '@nestjs/common'
import { ResourceType } from '@libs/shared/constants/subscription'

import { SubscriptionLimitGuard } from '../guard/subscription-limit.guard'

export const SUBSCRIPTION_LIMIT_KEY = 'subscription_limit'

export function CheckSubscriptionLimit(ResourceType: ResourceType) {
  return applyDecorators(
    SetMetadata(SUBSCRIPTION_LIMIT_KEY, {
      ResourceType,
    }),
    UseGuards(SubscriptionLimitGuard),
  )
}
