import { createParamDecorator, ExecutionContext } from '@nestjs/common'

import {
  PERMISSION_QUERIES_KEY,
  IPermissionQueries,
} from '@backend/cores/rbac/permission-queries'

export const PermissionQueries = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest()

    return request[PERMISSION_QUERIES_KEY] as IPermissionQueries
  },
)
