import { createZodDto } from '@anatine/zod-nestjs'
import { extendApi } from '@anatine/zod-openapi'
import { z } from 'zod'

const PaginateOptionsSchema = z.object({
  page: z.coerce
    .number()
    .min(1)
    .optional()
    .default(1)
    .transform((v) => Number(v)),
  limit: z.coerce
    .number()
    .min(1)
    .max(50)
    .optional()
    .default(10)
    .transform((v) => Number(v)),
  lastItemId: z.string().optional(),
})

export class PaginateOptionsDto extends createZodDto(
  extendApi(PaginateOptionsSchema),
) {}

const OutputSchema = PaginateOptionsSchema.partial()

export type PaginateOptions = z.output<typeof OutputSchema>
