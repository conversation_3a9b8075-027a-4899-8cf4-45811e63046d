import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
  HttpStatus,
  HttpException,
} from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { ResourceType } from '@libs/shared/constants/subscription'

import { SubscriptionLimitService } from '@backend/modules/subscription/services/subscription-limit.service'

import { SUBSCRIPTION_LIMIT_KEY } from '../decorators/check-subscription-limit.decorator'
import { OrgTeamHeadersDto } from '../dto/org-team-headers.dto'

interface SubscriptionLimitMetadata {
  ResourceType: ResourceType
}

@Injectable()
export class SubscriptionLimitGuard implements CanActivate {
  private readonly logger = new Logger(SubscriptionLimitGuard.name)

  constructor(
    private reflector: Reflector,
    private subscriptionLimitService: SubscriptionLimitService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const metadata =
      this.reflector.getAllAndOverride<SubscriptionLimitMetadata>(
        SUBSCRIPTION_LIMIT_KEY,
        [context.getHandler(), context.getClass()],
      )

    if (!metadata) {
      return true
    }

    const request = context.switchToHttp().getRequest()
    const orgTeamHeadersDto = new OrgTeamHeadersDto()
    orgTeamHeadersDto.organizationId = request.headers['organization-id']

    if (!orgTeamHeadersDto.organizationId) {
      this.logger.error('Organization ID is missing from request headers')
      throw new UnauthorizedException('Organization ID is required')
    }

    const { ResourceType } = metadata
    const count = 1

    try {
      await this.subscriptionLimitService.checkResourceLimitByOrg(
        orgTeamHeadersDto.organizationId,
        ResourceType,
        count,
      )

      return true
    } catch (error) {
      throw new HttpException(
        {
          message: error.message,
          code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
        },
        HttpStatus.BAD_REQUEST,
      )
    }
  }
}
