import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common'
import { Request } from 'express'

import { AccessTokenGuard } from '@backend/cores/auth/guards/accessToken.guard'

@Injectable()
export class AuthGuard extends AccessTokenGuard {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const canActivate = (await super.canActivate(context)) as boolean
    if (!canActivate) {
      return false
    }
    const request = context.switchToHttp().getRequest()
    return this.validateRequest(request)
  }

  validateRequest(request: Request & { user: unknown }): boolean {
    if (!request.user) {
      throw new UnauthorizedException('User not found')
    }

    return true
  }
}
