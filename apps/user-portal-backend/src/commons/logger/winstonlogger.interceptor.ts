import {
  CallHand<PERSON>,
  ExecutionContext,
  Inject,
  Injectable,
  NestInterceptor,
} from '@nestjs/common'
import { Observable, throwError } from 'rxjs'
import { tap, catchError } from 'rxjs/operators'
import { WINSTON_MODULE_PROVIDER } from 'nest-winston'
import * as winston from 'winston'

import { inspect } from 'util'
import { format } from 'util'

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: winston.Logger,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const request = context.switchToHttp().getRequest()
    const response = context.switchToHttp().getResponse()
    const method = request.method
    const url = request.url
    const userId = request.user ? request.user.userId : 'No user' // TODO: Get userID from auth token
    const payload = format(request.body)
    const requestContext = context.getClass().name
    // const requestContext2 = context.getHandler().name // Method name
    this.logger.log(
      `[HTTP] Incoming request: ${method} ${url} by ${userId}, body: ${payload}`,
      requestContext,
    )

    return next.handle().pipe(
      catchError((error) => {
        this.logger.error(
          `[HTTP] Request ${method} to ${url} by ${userId} FAILED`,
          inspect(error),
        )
        return throwError(() => error)
      }),
      tap(() =>
        this.logger.log(
          `[HTTP] Request ${method} to ${url} by ${userId} completed, response: ${response.statusCode}`,
          requestContext,
        ),
      ),
    )
  }
}
export const INTERCEPTOR_LOGGER = 'INTERCEPTOR_LOGGER'
