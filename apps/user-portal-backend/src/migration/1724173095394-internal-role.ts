import { MigrationInterface, QueryRunner } from 'typeorm'
import { NestFactory } from '@nestjs/core'

import { AppModule } from '@backend/app.module'
import { generateId } from '@backend/commons/id'
import { AuthService } from '@backend/cores/auth/auth.service'

export class InternalRole1724173095394 implements MigrationInterface {
  private authService: AuthService

  public async up(queryRunner: QueryRunner): Promise<void> {
    const app = await NestFactory.create(AppModule)
    this.authService = app.get(AuthService)

    const columnExists = await queryRunner.hasColumn('users', 'internal_role')
    if (!columnExists) {
      await queryRunner.query(`
        ALTER TABLE "users" 
        ADD "internal_role" varchar NULL
      `)
    }

    const id = generateId()

    const userRecord = await this.authService.createAdminAccount(
      '<EMAIL>',
      'Dog@1234',
    )

    await queryRunner.query(
      `
    INSERT INTO "users" ("id", "firebase_id", "email", "internal_role")
    VALUES ($1, $2, $3, $4)`,
      [id, userRecord.uid, userRecord.email, 'super_admin'],
    )

    setTimeout(() => {
      process.exit()
    }, 3000)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const columnExists = await queryRunner.hasColumn('users', 'internal_role')
    if (columnExists) {
      await queryRunner.query(`
        ALTER TABLE "users" 
        DROP COLUMN "internal_role"
      `)
    }
  }
}
