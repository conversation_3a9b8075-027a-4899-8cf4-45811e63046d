import { MigrationInterface, QueryRunner } from 'typeorm'

export class InsertWorkersAndRegions1713550243207
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const totalWorkers = await queryRunner.query(`SELECT COUNT(*) FROM workers`)
    if (totalWorkers[0].count > 0) return
    // Insert workers
    await queryRunner.query(`
      INSERT INTO workers (id, ip, location, type, region) VALUES
      ('usa_nv', '***********', 'northvirginia'   , 'free'    , 'usa'),
      ('usa_oh', '***********', 'ohio'            , 'free'    , 'usa'),
      ('usa_nc', '***********', 'northcalifornia' , 'premium' , 'usa'),
      ('vie_hn', '***********', 'hanoi'           , 'free'    , 'vie'),
      ('vie_sg', '***********', 'hochiminh'       , 'free'    , 'vie'),
      ('vie_dn', '***********', 'danang'          , 'premium' , 'vie');
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM workers WHERE id IN ('usa_nv', 'usa_oh', 'usa_nc', 'vie_hn', 'vie_sg', 'vie_dn');
    `)
  }
}
