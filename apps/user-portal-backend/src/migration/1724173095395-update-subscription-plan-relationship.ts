import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateSubscriptionPlanRelationship1724173095395
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, drop the existing unique constraint
    await queryRunner.query(`
      ALTER TABLE subscriptions
      DROP CONSTRAINT IF EXISTS "REL_e45fca5d912c3a2fab512ac25d"
    `)

    // Then, drop the existing foreign key constraint
    await queryRunner.query(`
      ALTER TABLE subscriptions
      DROP CONSTRAINT IF EXISTS "FK_e45fca5d912c3a2fab512ac25d"
    `)

    // Finally, add the new foreign key constraint without uniqueness
    await queryRunner.query(`
      ALTER TABLE subscriptions
      ADD CONSTRAINT "FK_subscription_plan"
      FOREIGN KEY (plan_id)
      REFERENCES subscription_plans(id)
      ON DELETE SET NULL
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the new foreign key constraint
    await queryRunner.query(`
      ALTER TABLE subscriptions
      DROP CONSTRAINT IF EXISTS "FK_subscription_plan"
    `)

    // Re-add the original one-to-one relationship constraints
    await queryRunner.query(`
      ALTER TABLE subscriptions
      ADD CONSTRAINT "FK_e45fca5d912c3a2fab512ac25d"
      FOREIGN KEY (plan_id)
      REFERENCES subscription_plans(id)
      ON DELETE SET NULL
    `)

    await queryRunner.query(`
      ALTER TABLE subscriptions
      ADD CONSTRAINT "REL_e45fca5d912c3a2fab512ac25d"
      UNIQUE (plan_id)
    `)
  }
}
