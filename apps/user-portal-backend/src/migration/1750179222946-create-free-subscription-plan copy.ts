import { MigrationInterface, QueryRunner } from 'typeorm'

import { generateId } from '@backend/commons/id'

export class CreateFreeSubscriptionPlan1750179222946
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const freePlanId = generateId()
    const now = new Date().toISOString()

    await queryRunner.query(`
      INSERT INTO subscription_plans (
        id, 
        name, 
        plan_type, 
        source, 
        period, 
        stripe_price_id, 
        is_active,
        created_at,
        updated_at,
        created_by,
        updated_by
      ) VALUES (
        '${freePlanId}',
        'Free Plan',
        'BASE_PLAN',
        'FREE',
        'month',
        NULL,
        true,
        '${now}',
        '${now}',
        NULL,
        NULL
      ) ON CONFLICT (id) DO NOTHING;
    `)

    const resourceLimits = [
      { resourceType: 'MEMBER', includedQuantity: 5 },
      { resourceType: 'TEAM', includedQuantity: 2 },
      { resourceType: 'CHECK', includedQuantity: 5 },
      { resourceType: 'INTEGRATION', includedQuantity: 1 },
      { resourceType: 'STATUS_PAGE', includedQuantity: 1 },
      { resourceType: 'SMS', includedQuantity: 10 },
      { resourceType: 'PHONE_CALL', includedQuantity: 5 },
    ]

    for (const limit of resourceLimits) {
      const limitId = generateId()

      await queryRunner.query(`
        INSERT INTO plan_resource_limits (
          id,
          subscription_plan_id,
          resource_type,
          included_quantity,
          overage_price,
          overage_stripe_price_id,
          created_at,
          updated_at,
          created_by,
          updated_by
        ) VALUES (
          '${limitId}',
          '${freePlanId}',
          '${limit.resourceType}',
          ${limit.includedQuantity},
          0,
          'free-no-overage',
          '${now}',
          '${now}',
          NULL,
          NULL
        ) ON CONFLICT (id) DO NOTHING;
      `)
    }

    console.log('✅ Created free subscription plan with resource limits:')
    console.log(`   Plan ID: ${freePlanId}`)
    console.log(`   Plan Name: Free Plan`)
    resourceLimits.forEach((limit) => {
      console.log(`   ${limit.resourceType}: ${limit.includedQuantity}`)
    })
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const freePlanId = 'free-plan-default'

    // Remove resource limits first (due to foreign key constraint)
    await queryRunner.query(`
      DELETE FROM plan_resource_limits 
      WHERE subscription_plan_id = '${freePlanId}';
    `)

    // Remove the free plan
    await queryRunner.query(`
      DELETE FROM subscription_plans 
      WHERE id = '${freePlanId}';
    `)

    console.log('❌ Removed free subscription plan and its resource limits')
  }
}
