import { MigrationInterface, QueryRunner } from 'typeorm'

export class RefactorSubscriptionPlan1750179222945
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns if they don't exist (safe check)
    await queryRunner.query(`
      ALTER TABLE subscription_plans
      ADD COLUMN IF NOT EXISTS plan_type VARCHAR(50) DEFAULT 'BASE_PLAN' NOT NULL,
      ADD COLUMN IF NOT EXISTS stripe_price_id VARCHAR(255) NOT NULL,
      ADD COLUMN IF NOT EXISTS member_overage_stripe_price_id VARCHAR(255) NOT NULL,
      ADD COLUMN IF NOT EXISTS team_overage_stripe_price_id VARCHAR(255) NOT NULL,
      ADD COLUMN IF NOT EXISTS check_overage_stripe_price_id VARCHAR(255) NOT NULL,
      ADD COLUMN IF NOT EXISTS integration_overage_stripe_price_id VARCHAR(255),
      ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true NOT NULL
    `)

    // Check if old columns exist before renaming
    const membersColumn = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'subscription_plans' AND column_name = 'members_allowed'
    `)

    if (membersColumn.length > 0) {
      await queryRunner.query(`
        ALTER TABLE subscription_plans
        RENAME COLUMN members_allowed TO members_included
      `)
    }

    const teamsColumn = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'subscription_plans' AND column_name = 'teams_allowed'
    `)

    if (teamsColumn.length > 0) {
      await queryRunner.query(`
        ALTER TABLE subscription_plans
        RENAME COLUMN teams_allowed TO teams_included
      `)
    }

    const checksColumn = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'subscription_plans' AND column_name = 'checks_allowed'
    `)

    if (checksColumn.length > 0) {
      await queryRunner.query(`
        ALTER TABLE subscription_plans
        RENAME COLUMN checks_allowed TO checks_included
      `)
    }

    const integrationsColumn = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'subscription_plans' AND column_name = 'integrations_allowed'
    `)

    if (integrationsColumn.length > 0) {
      await queryRunner.query(`
        ALTER TABLE subscription_plans
        RENAME COLUMN integrations_allowed TO integrations_included
      `)
    }

    // Check if old columns exist before dropping
    const priceColumn = await queryRunner.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'subscription_plans' AND column_name = 'price'
    `)

    if (priceColumn.length > 0) {
      await queryRunner.query(`
        ALTER TABLE subscription_plans
        DROP COLUMN price,
        DROP COLUMN actual_price
      `)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back old columns
    await queryRunner.query(`
      ALTER TABLE subscription_plans
      ADD COLUMN price NUMERIC NOT NULL DEFAULT 0,
      ADD COLUMN actual_price NUMERIC NOT NULL DEFAULT 0
    `)

    // Rename columns back
    await queryRunner.query(`
      ALTER TABLE subscription_plans
      RENAME COLUMN members_included TO members_allowed
    `)

    await queryRunner.query(`
      ALTER TABLE subscription_plans
      RENAME COLUMN teams_included TO teams_allowed
    `)

    await queryRunner.query(`
      ALTER TABLE subscription_plans
      RENAME COLUMN checks_included TO checks_allowed
    `)

    await queryRunner.query(`
      ALTER TABLE subscription_plans
      RENAME COLUMN integrations_included TO integrations_allowed
    `)

    // Drop new columns (including plan_type which is varchar)
    await queryRunner.query(`
      ALTER TABLE subscription_plans
      DROP COLUMN plan_type,
      DROP COLUMN stripe_price_id,
      DROP COLUMN member_overage_stripe_price_id,
      DROP COLUMN team_overage_stripe_price_id,
      DROP COLUMN check_overage_stripe_price_id,
      DROP COLUMN integration_overage_stripe_price_id,
      DROP COLUMN is_active
    `)
  }
}
