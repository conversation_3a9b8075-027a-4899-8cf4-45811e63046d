import { MigrationInterface, QueryRunner } from 'typeorm'

export class InsertContact1712315153137 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Insert workers
    // await queryRunner.query(`
    //   INSERT INTO escalation_contacts (id, contact_type, contact_id) VALUES
    //   ('oc', 'on_call', 'oc'),
    //   ('et', 'entire_team', 'et'),
    //   ('person1', 'user', '0kjc4yk10fz3'),
    //   ('person2', 'user', 'mbi9d7jwmc4w');
    // `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.query(`
    //   DELETE FROM escalation_contacts WHERE id IN ('oc', 'et', 'person1', 'person2');
    // `)
  }
}
