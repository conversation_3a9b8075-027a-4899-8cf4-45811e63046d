import { MigrationInterface, QueryRunner } from 'typeorm'

import { generateId } from '@backend/commons/id'

export class DefaultPermission1724173095374 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const totalPermissions = await queryRunner.query(
      `SELECT COUNT(*) FROM permissions`,
    )
    if (totalPermissions[0].count > 0) return

    const permissionOrganizationScopes = {
      organization: [
        'organizationSettings',
        'billing',
        'teamListing',
        'teamCreate',
        'organizationMembers',
        'roles',
      ],
      team: [
        'assignRoles',
        'teamSettings',
        'teamMembers',
        'checks',
        'incidents',

        'escalations',
        'onCall',
        'statusPage',
        'integrations',
        'tokenManagement',
      ],
    }

    const permissionActions = ['view', 'manage']

    for (const [type, scopes] of Object.entries(permissionOrganizationScopes)) {
      for (const scope of scopes) {
        for (const action of permissionActions) {
          await queryRunner.query(
            `INSERT INTO permissions (id, scope, action, type) VALUES ($1, $2, $3, $4)`,
            [generateId(), scope, action, type],
          )
        }
      }
    }

    const newViewerPermissionsIds: string[] = []
    const newAdminPermissionsIds: string[] = []
    for (const [type, scopes] of Object.entries(permissionOrganizationScopes)) {
      for (const scope of scopes) {
        for (const action of permissionActions) {
          const id = generateId()
          if (action == 'view') newViewerPermissionsIds.push(id)
          newAdminPermissionsIds.push(id)
          await queryRunner.query(
            `INSERT INTO permissions (id, scope, action, type) VALUES ($1, $2, $3, $4)`,
            [id, scope, action, type],
          )
        }
      }
    }

    const adminRoleId: [] = await queryRunner.query(
      `SELECT id FROM roles WHERE name = $1`,
      ['Admin'],
    )
    const viewerRoleId: string[] = await queryRunner.query(
      `SELECT id FROM roles WHERE name = $1`,
      ['Viewer'],
    )

    for (const id of adminRoleId) {
      for (const perm of newAdminPermissionsIds) {
        await queryRunner.query(
          `INSERT INTO roles_permissions (roles_id, permissions_id) VALUES ($1, $2)`,
          [id['id'], perm],
        )
      }
    }

    for (const id of viewerRoleId) {
      for (const perm of newViewerPermissionsIds) {
        await queryRunner.query(
          `INSERT INTO roles_permissions (roles_id, permissions_id) VALUES ($1, $2)`,
          [id['id'], perm],
        )
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM permission`)
  }
}
