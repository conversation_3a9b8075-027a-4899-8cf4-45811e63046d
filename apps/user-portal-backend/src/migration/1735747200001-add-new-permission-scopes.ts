import { MigrationInterface, QueryRunner } from 'typeorm'

import { generateId } from '@backend/commons/id'

export class AddNewPermissionScopes1735747200001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new permission scopes that were added to PermissionScope enum
    const newPermissionScopes = {
      team: ['statusPage', 'integrations', 'tokenManagement'],
    }

    const permissionActions = ['view', 'manage']

    // Insert new permissions
    const newPermissionIds: string[] = []
    const newViewerPermissionIds: string[] = []

    for (const [type, scopes] of Object.entries(newPermissionScopes)) {
      for (const scope of scopes) {
        for (const action of permissionActions) {
          const id = generateId()
          newPermissionIds.push(id)

          if (action === 'view') {
            newViewerPermissionIds.push(id)
          }

          await queryRunner.query(
            `INSERT INTO permissions (id, scope, action, type) VALUES ($1, $2, $3, $4)`,
            [id, scope, action, type],
          )
        }
      }
    }

    // Add new permissions to existing Admin roles
    const adminRoles = await queryRunner.query(
      `SELECT id FROM roles WHERE name = $1`,
      ['Admin'],
    )

    for (const role of adminRoles) {
      for (const permissionId of newPermissionIds) {
        await queryRunner.query(
          `INSERT INTO roles_permissions (roles_id, permissions_id) VALUES ($1, $2)`,
          [role.id, permissionId],
        )
      }
    }

    // Add view permissions to existing Viewer roles
    const viewerRoles = await queryRunner.query(
      `SELECT id FROM roles WHERE name = $1`,
      ['Viewer'],
    )

    for (const role of viewerRoles) {
      for (const permissionId of newViewerPermissionIds) {
        await queryRunner.query(
          `INSERT INTO roles_permissions (roles_id, permissions_id) VALUES ($1, $2)`,
          [role.id, permissionId],
        )
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove new permission scopes
    await queryRunner.query(
      `DELETE FROM permissions WHERE scope IN ($1, $2, $3)`,
      ['statusPage', 'integrations', 'tokenManagement'],
    )
  }
}
