import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddSourceToSubscription1750179222947
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE "subscriptions"
        ADD COLUMN "source" VARCHAR;
      `)

    await queryRunner.query(`
        UPDATE "subscriptions"
        SET "source" = 'FREE';
      `)

    await queryRunner.query(`
        ALTER TABLE "subscriptions"
        ALTER COLUMN "source" SET NOT NULL;
      `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE "subscriptions"
        DROP COLUMN "source";
      `)
  }
}
