import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  ValidationPipe,
  UsePipes,
  Param,
} from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'

import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { CreateStaffDto } from '@backend/modules/user/applications/dto/user.request.dto'

import { AuthService } from './auth.service'
import { RefreshTokenGuard } from './guards/refreshToken.guard'
import {
  EmailLoginDto,
  FirebaseLoginDto,
  RefreshTokenDto,
} from './dto/auth.request.dto'
import { JwtPayload } from './auth.interface'

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('firebase-login')
  async loginWithFirebase(@Body() firebaseLoginDto: FirebaseLoginDto) {
    return this.authService.firebaseLogin(firebaseLoginDto.firebaseToken, {
      invitation: firebaseLoginDto.invitation,
    })
  }

  @Post('email-login')
  async loginWithEmail(@Body() emailLoginDto: EmailLoginDto) {
    return this.authService.emailLogin(emailLoginDto)
  }

  @Post('reset-password')
  async resetPassword(@Body() emailLoginDto: EmailLoginDto) {
    return this.authService.resetPassword(emailLoginDto)
  }

  @Post('refresh-token')
  @UseGuards(RefreshTokenGuard)
  async refreshAccessToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshAccessToken(
      refreshTokenDto.refreshToken,
      refreshTokenDto.mobile,
    )
  }

  @Get('firebase-custom-token')
  @UseGuards(AuthGuard)
  async getFirebaseCustomToken(@CurrentUser() req: JwtPayload) {
    return this.authService.getFirebaseCustomToken(req)
  }

  @Get('firebase-sign-in-methods')
  @UseGuards(AuthGuard)
  async getFirebaseSignInMethods(@CurrentUser() req: JwtPayload) {
    return this.authService.getUserFirebaseSignInMethods(req)
  }

  @Post('create-staff')
  @UsePipes(new ValidationPipe({ transform: true }))
  async createStaff(@Body() createStaffDto: CreateStaffDto) {
    return this.authService.createStaffAccount(
      createStaffDto.email,
      createStaffDto.internalRole,
    )
  }

  @Get('/mobile/custom-token/:aliasToken')
  async getCustomTokenByAlias(@Param('aliasToken') aliasToken: string) {
    return this.authService.getCustomTokenByAlias(aliasToken)
  }

  @Get('/mobile/generate-alias-token/:firebaseId')
  async generateAliasToken(@Param('firebaseId') firebaseId: string) {
    return this.authService.generateAliasToken(firebaseId)
  }

  @Get('/mobile/overview/:encryptedToken')
  async mobileOveview(@Param('encryptedToken') encryptedToken: string) {
    return this.authService.secretTokenLogin(encryptedToken)
  }

  @Get('/mobile/view-incident/:encryptedToken')
  async mobileViewIncident(@Param('encryptedToken') encryptedToken: string) {
    return this.authService.secretTokenLoginAndViewIncident(encryptedToken)
  }
}
