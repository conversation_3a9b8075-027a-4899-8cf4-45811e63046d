import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { FirebaseAdmin, InjectFirebaseAdmin } from 'nestjs-firebase'
import {
  parseQueryParams,
  stringifyQueryParams,
} from '@libs/shared/utils/query-string'
import { Database } from '@libs/database/lib/database'
import { REDIS_ALIAS_TOKEN_PREFIX } from '@libs/shared/constants/key-prefix'
import { Timestamp } from 'firebase-admin/firestore'

import { UserUseCase } from '@backend/modules/user/usecases/user.usecase'
import { UserInvite } from '@backend/modules/user/entities/user-invite.entity'
import { NotificationFactory } from '@backend/frameworks/notification/notification.factory'
import { EmailTemplate } from '@backend/frameworks/notification/email/email.constant'
import { OrganizationUseCase } from '@backend/modules/user/usecases/organization.usecase'
import { InternalRole } from '@backend/modules/user/constants/user'
import { generatePassword } from '@backend/commons/password'
import { generateAliasToken } from '@backend/commons/aliasToken'
import { EncryptionHelper } from '@backend/commons/utils/encryption'
import { UserPlivoCredentialUseCase } from '@backend/modules/user/usecases/user-plivo-credential.usecase'

import { configService } from '../config/config.service'

import { JwtPayload } from './auth.interface'
import { EmailLoginDto } from './dto/auth.request.dto'

@Injectable()
export class AuthService {
  constructor(
    @InjectFirebaseAdmin() private readonly firebase: FirebaseAdmin,
    private readonly jwtService: JwtService,
    @Inject(forwardRef(() => UserUseCase))
    private readonly userUseCase: WrapperType<UserUseCase>,
    @Inject(forwardRef(() => OrganizationUseCase))
    private readonly organizationUseCase: WrapperType<OrganizationUseCase>,
    @Inject(forwardRef(() => UserPlivoCredentialUseCase))
    private readonly userPlivoCredentialUseCase: WrapperType<UserPlivoCredentialUseCase>,
    private notificationFactory: NotificationFactory,
    @Inject(Database) private readonly db: Database,
  ) {}

  async validateUser({
    firebaseId,
    email,
    userInvite,
  }: {
    firebaseId: string
    email?: string
    userInvite?: UserInvite
  }) {
    let user = await this.userUseCase.getUserByFirebaseId(firebaseId)

    // For the old user
    if (user?.id) {
      await this.userPlivoCredentialUseCase.create(user.id)
    }

    if (!user) {
      if (userInvite) {
        await this.userUseCase.checkOrgMemeberLimit(
          userInvite.getProps().team.id,
        )
        user = await this.userUseCase.create({ firebaseId, email })
        await this.userPlivoCredentialUseCase.create(user.id)
        await this.userUseCase.acceptInvitation(user.id, userInvite)
      } else {
        user = await this.userUseCase.create({ firebaseId, email })
        await this.userPlivoCredentialUseCase.create(user.id)
        await this.organizationUseCase.create(
          {
            name: 'My Organization',
            ownerId: user.id,
          },
          user.id,
        )
      }
    } else if (userInvite) {
      await this.userUseCase.checkOrgMemeberLimit(
        userInvite!.getProps().team.id,
      )
      await this.userUseCase.acceptInvitation(user.id, userInvite)
    }

    return user
  }

  async firebaseLogin(
    firebaseToken: string,
    options?: { invitation?: string; mobile?: boolean },
  ) {
    let decodedToken
    try {
      decodedToken = await this.firebase.auth.verifyIdToken(firebaseToken)
    } catch (error) {
      throw new UnauthorizedException('error: ' + error)
    }
    if (!decodedToken) {
      throw new UnauthorizedException('Invalid Firebase token')
    }

    // Validate inviter code
    let userInvite: UserInvite | undefined
    if (options?.invitation && decodedToken.email) {
      userInvite = await this.userUseCase.validateInvitedUser(
        decodedToken.email,
        options?.invitation,
      )
    }

    const payload = {
      firebaseId: decodedToken.uid,
      email: decodedToken.email,
      userInvite,
    }

    console.log(
      '🚀 ~ AuthService ~ firebaseLogin ~ payload.userInvite:',
      payload.userInvite,
    )

    const user = await this.validateUser(payload)

    if (!user) throw new UnauthorizedException('User not found')

    const signPayload = this.getSignPayload(user)

    return {
      ...(options?.mobile
        ? this.getJwtSignsMobile(signPayload)
        : this.getJwtSigns(signPayload)),
      user,
    }
  }

  getSignPayload(user: any): JwtPayload {
    return {
      userId: user.id,
      firebaseId: user.firebaseId,
      internalRole: user.internalRole,
    }
  }

  async getLoginUrl(email: string, redirectUrl: string) {
    return await this.firebase.auth.generateSignInWithEmailLink(email, {
      handleCodeInApp: true,
      url: redirectUrl,
    })
  }

  getJwtSigns(payload: JwtPayload) {
    return {
      accessToken: this.jwtService.sign(
        payload,
        configService.getJWTSecretSignOptions(),
      ),
      refreshToken: this.jwtService.sign(
        payload,
        configService.getJWTRefreshSecretSignOptions(),
      ),
    }
  }

  getJwtSignsMobile(payload: JwtPayload) {
    return {
      accessToken: this.jwtService.sign(
        payload,
        configService.getJWTSecretSignMobileOptions(),
      ),
      refreshToken: this.jwtService.sign(
        payload,
        configService.getJWTRefreshSecretSignMobileOptions(),
      ),
    }
  }

  async refreshAccessToken(refreshToken: string, mobile?: boolean) {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: configService.getJWTRefreshSecretSignOptions().secret,
      })

      const user = await this.userUseCase.getUserByFirebaseId(
        payload.firebaseId,
      )

      if (!user) {
        throw new UnauthorizedException('User not found')
      }

      return {
        accessToken: this.jwtService.sign(
          this.getSignPayload(user),
          mobile
            ? configService.getJWTSecretSignMobileOptions()
            : configService.getJWTSecretSignOptions(),
        ),
        user,
      }
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token')
    }
  }

  async getFirebaseCustomToken(payload: JwtPayload) {
    const user = await this.userUseCase.getUserFirebase(payload.userId)

    if (!user?.firebaseId) throw new UnauthorizedException('User not found')

    return {
      customToken: await this.firebase.auth.createCustomToken(user.firebaseId),
    }
  }

  async getUserFirebaseSignInMethods(payload: JwtPayload) {
    const user = await this.userUseCase.getUserFirebase(payload.userId)

    if (!user?.firebaseId) throw new UnauthorizedException('User not found')

    const firebaseUser = await this.firebase.auth.getUser(user.firebaseId)

    return {
      signInMethods: firebaseUser.providerData.map(
        (profile) => profile.providerId,
      ),
    }
  }

  async emailLogin(emailLoginDto: EmailLoginDto) {
    const {
      email,
      redirectUrl = configService.getValue('WEB_PORTAL_EMAIL_AUTH'),
    } = emailLoginDto

    const loginUrl = await this.getLoginUrl(
      email,
      `${redirectUrl}?${stringifyQueryParams({ email })}`,
    )

    await this.notificationFactory.getNotificationService('email').send({
      to: email,
      subject: 'Sign in to Monitoring Dog',
      template: EmailTemplate.EMAIL_LOGIN,
      templateData: {
        loginUrl,
      },
    })
  }

  async resetPassword(emailLoginDto: EmailLoginDto) {
    const {
      email,
      redirectUrl = configService.getValue('WEB_PORTAL_EMAIL_AUTH'),
    } = emailLoginDto

    const resetPasswordUrl =
      await this.firebase.auth.generatePasswordResetLink(email)

    const params = parseQueryParams(resetPasswordUrl)

    await this.notificationFactory.getNotificationService('email').send({
      to: email,
      subject: 'Reset your password',
      template: EmailTemplate.RESET_PASSWORD,
      templateData: {
        resetPasswordUrl: `${redirectUrl}?${stringifyQueryParams({
          ...params,
          email,
        })}`,
      },
    })
  }

  async createAdminAccount(email: string, password: string) {
    try {
      const userRecord = await this.firebase.auth.createUser({
        email: email,
        password: password,
      })
      return userRecord
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        throw new BadRequestException(
          'The email address is already in use by another account.',
        )
      } else {
        throw error
      }
    }
  }

  async createStaffAccount(email: string, internalRole: InternalRole) {
    try {
      const userRecord = await this.firebase.auth.createUser({
        email: email,
        password: generatePassword(),
      })

      const newStaff = await this.userUseCase.createStaff({
        firebaseId: userRecord.uid,
        email: email,
        internalRole: internalRole,
      })

      //TODO: send resetpassword to staff

      return newStaff
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        throw new BadRequestException(
          'The email address is already in use by another account.',
        )
      } else {
        throw error
      }
    }
  }

  async getCustomTokenByAlias(aliasToken: string) {
    const customToken = await this.db.redisGet(
      aliasToken,
      REDIS_ALIAS_TOKEN_PREFIX.ALIAS_TOKEN,
    )
    if (!customToken) {
      throw new NotFoundException('Invalid QR Code')
    }
    return { customToken: customToken }
  }

  async generateAliasToken(firebaseId: string) {
    const customToken = await this.firebase.auth.createCustomToken(firebaseId)
    const aliasToken = generateAliasToken()
    this.db.redisSetWithExpired(
      aliasToken,
      REDIS_ALIAS_TOKEN_PREFIX.ALIAS_TOKEN,
      customToken,
      300,
    )
    return { aliasToken: aliasToken }
  }

  async secretTokenLogin(encrypted: string) {
    try {
      const data = this.decryptAndValidate(encrypted)
      if (!data) {
        console.error('Invalid or expired token')
        return null
      }

      const { firebaseId, userId } = data
      return this.getJwtSigns({ firebaseId, userId, internalRole: undefined })
    } catch (error) {
      console.error('Error:', error)
      return null
    }
  }

  async secretTokenLoginAndViewIncident(encrypted: string) {
    try {
      const data = this.decryptAndValidateViewIncident(encrypted)
      if (!data) {
        console.error('Invalid or expired token')
        return null
      }

      const { firebaseId, userId, incidentId, teamId } = data

      return {
        jwtSigns: this.getJwtSigns({
          firebaseId,
          userId,
          internalRole: undefined,
        }),
        incidentId,
        teamId,
      }
    } catch (error) {
      console.error('Error:', error)
      return null
    }
  }

  private decryptAndValidate(encrypted: string) {
    try {
      const data = JSON.parse(EncryptionHelper.decrypt(encrypted))
      const currentTimestamp = Timestamp.now().seconds

      if (
        !data.firebaseId ||
        !data.userId ||
        !data.timestamp ||
        currentTimestamp - data.timestamp > 300
      ) {
        return null
      }

      return data
    } catch {
      return null
    }
  }

  private decryptAndValidateViewIncident(encrypted: string) {
    try {
      const data = JSON.parse(EncryptionHelper.decrypt(encrypted))

      if (
        !data.firebaseId ||
        !data.userId ||
        !data.incidentId ||
        !data.teamId
      ) {
        return null
      }

      return data
    } catch {
      return null
    }
  }
}
