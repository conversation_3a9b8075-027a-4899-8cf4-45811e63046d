import { IsBoolean, IsEmail, IsOptional, IsString } from 'class-validator'

export class FirebaseLoginDto {
  @IsString()
  readonly firebaseToken: string

  @IsString()
  @IsOptional()
  readonly invitation?: string
}

export class EmailLoginDto {
  @IsEmail()
  readonly email: string

  @IsString()
  @IsOptional()
  readonly redirectUrl?: string
}

export class RefreshTokenDto {
  @IsString()
  readonly refreshToken: string

  @IsBoolean()
  @IsOptional()
  readonly mobile?: boolean
}

export class AliasTokenDto {
  @IsString()
  readonly aliasToken: string
}
