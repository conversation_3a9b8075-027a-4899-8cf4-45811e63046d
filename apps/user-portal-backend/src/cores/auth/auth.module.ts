import { Global, Module, forwardRef } from '@nestjs/common'

import { JwtAuthModule } from '@backend/frameworks/auth/jwt/jwt.module'
import { UserModule } from '@backend/modules/user/user.module'
import { NotificationsModule } from '@backend/frameworks/notification/notification.module'
import { DatabaseModule } from '@backend/frameworks/database/database.module'

import { AuthService } from './auth.service'
import { AuthController } from './auth.controller'

@Global()
@Module({
  imports: [
    JwtAuthModule,
    forwardRef(() => UserModule),
    NotificationsModule,
    DatabaseModule,
  ],
  providers: [AuthService],
  controllers: [AuthController],
  exports: [AuthService],
})
export class AuthModule {}
