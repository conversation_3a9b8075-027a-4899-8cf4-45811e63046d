import { RequestContextService } from '@backend/commons/context/AppRequestContext'

import { randomUUID } from 'crypto'

import { Id } from './id.type'

type DomainEventMetadata = {
  /** Timestamp when this domain event occurred */
  readonly timestamp: number

  /** ID for correlation purposes (for Integration Events,logs correlation, etc).
   */
  readonly correlationId: string

  /**
   * Causation id used to reconstruct execution order if needed
   */
  readonly causationId?: string

  /**
   * User ID for debugging and logging purposes
   */
  readonly userId?: string
}

export type DomainEventProps<T, P> = Omit<T, 'id' | 'metadata'> & {
  aggregateId: Id
  props?: P
  metadata?: DomainEventMetadata
}

export abstract class DomainEvent {
  public readonly id: string

  /** Aggregate ID where domain event occurred */
  public readonly aggregateId: Id

  public readonly metadata: DomainEventMetadata

  public readonly props?: unknown

  constructor(props: DomainEventProps<unknown, unknown>) {
    this.id = randomUUID()
    this.aggregateId = props.aggregateId
    this.metadata = {
      correlationId:
        props?.metadata?.correlationId || RequestContextService.getRequestId(),
      causationId: props?.metadata?.causationId,
      timestamp: props?.metadata?.timestamp || Date.now(),
      userId: props?.metadata?.userId,
    }

    this.props = props.props
  }
}
