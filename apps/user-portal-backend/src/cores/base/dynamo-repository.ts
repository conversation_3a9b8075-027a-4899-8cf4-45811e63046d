import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { IDynamoFilter } from '@backend/frameworks/database/interfaces/dynamo-filter.interface'

import { Id } from './id.type'

export default abstract class DynamoRepository<
  Entity,
  EntityProps = Partial<Entity>,
  Query = IDynamoFilter,
> {
  abstract findAll(query?: Query): Promise<Entity[]>
  abstract find(query: Query): Promise<IPaginate<Entity>>
  abstract findById(id: Id): Promise<Nullable<Entity>>
  abstract findOne(query: Query): Promise<Nullable<Entity>>

  abstract create(data: Entity | EntityProps): Promise<Entity>
  abstract updateById(id: Id, data: EntityProps): Promise<Nullable<Entity>>
  abstract delete(id: Id): Promise<boolean>
}
