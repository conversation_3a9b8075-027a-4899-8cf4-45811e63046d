import { Id } from '@backend/cores/base/id.type'

export interface BaseEntityProps {
  id: Id
  createdAt: Date
  updatedAt: Date
}

export interface CreateEntityProps<T> {
  id?: Id
  createdAt?: Date
  updatedAt?: Date
  props?: T
}

export abstract class BaseEntity<EntityProps, ResponseProps> {
  constructor(data?: CreateEntityProps<EntityProps>) {
    this._id = data?.id || ''
    const now = new Date()
    this.createdAt = data?.createdAt || now
    this.updatedAt = data?.updatedAt || now
    this.props = data?.props || ({} as EntityProps)
  }

  protected readonly _id: Id
  protected readonly createdAt: Date
  protected readonly updatedAt: Date
  protected readonly props: EntityProps

  get id(): Id {
    return this._id
  }

  public getProps(): EntityProps & BaseEntityProps {
    const propsCopy = {
      id: this._id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      ...this.props,
    }

    return Object.freeze(propsCopy)
  }

  public toResponse(): ResponseProps {
    return this.getProps() as ResponseProps
  }
}
