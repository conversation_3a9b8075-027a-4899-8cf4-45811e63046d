import { IFilter } from '@backend/frameworks/database/interfaces/filter.interface'
import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'

import { Id } from './id.type'

export default abstract class CrudRepository<
  Entity,
  EntityProps = Partial<Entity>,
  Query = IFilter,
> {
  abstract findAll(query?: Query): Promise<Entity[]>
  abstract find(query: Query): Promise<IPaginate<Entity>>
  abstract findById(id: Id, query?: Query): Promise<Nullable<Entity>>
  abstract findOne(query: Query): Promise<Nullable<Entity>>

  abstract create(data: Entity | EntityProps): Promise<Entity>
  abstract updateById(id: Id, data: EntityProps): Promise<Nullable<Entity>>
  abstract delete(id: Id): Promise<boolean>
}
