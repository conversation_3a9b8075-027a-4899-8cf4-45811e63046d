import {
  ForbiddenException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common'
import { AbilityQuery, rulesToQuery } from '@casl/ability/extra'

import { UserUseCase } from '@backend/modules/user/usecases/user.usecase'

import { Id } from '../base/id.type'

import { CaslAbilityFactory } from './casl-ability.factory'
import {
  FORBIDDEN_MESSAGE,
  PermissionAction,
  PermissionScope,
  PermissionType,
} from './permission.constant'
import { IPermissionQueries } from './permission-queries'

@Injectable()
class CaslService {
  constructor(
    private caslAbilityFactory: CaslAbilityFactory,
    @Inject(forwardRef(() => UserUseCase))
    private readonly userUseCase: WrapperType<UserUseCase>,
  ) {}

  async verifyPermissions(
    userId: Id,
    requiredPermissions: {
      action: PermissionAction
      scope: PermissionScope
      type?: PermissionType
    }[],
  ): Promise<IPermissionQueries> {
    const userWithAuth = await this.userUseCase.getAuthUser(userId)

    if (!userWithAuth) throw new ForbiddenException(FORBIDDEN_MESSAGE)

    const ability = this.caslAbilityFactory.createForUser(userWithAuth)

    const hasPermission = requiredPermissions.every((permission) => {
      const { action, scope } = permission

      return ability.can(action, scope)
    })

    if (!hasPermission) {
      throw new ForbiddenException(FORBIDDEN_MESSAGE)
    }

    const queries: AbilityQuery<any>[] = []

    requiredPermissions.forEach((permission) => {
      let query: AbilityQuery<any> | null = rulesToQuery(
        ability,
        permission.action,
        permission.scope,
        (rule) => rule.conditions,
      )

      if (
        query &&
        typeof query === 'object' &&
        !Array.isArray(query) &&
        Object.keys(query).length === 1 &&
        Array.isArray(query.$or) &&
        query.$or.length === 1
      ) {
        query = query.$or[0]
      }

      query && queries.push(query)
    })

    return queries.reduce((acc, query) => {
      if (query.$and) {
        return acc
      }

      return {
        ...acc,
        ...query,
      }
    }, {}) as IPermissionQueries
  }
}

export default CaslService
