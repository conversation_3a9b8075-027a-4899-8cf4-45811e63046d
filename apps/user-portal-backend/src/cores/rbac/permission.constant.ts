export enum PermissionType {
  ORGANIZATION = 'organization',
  TEAM = 'team',
}

export enum PermissionScope {
  // ORGANIZATION
  ORGANIZATION_SETTINGS = 'organizationSettings',
  BILLING = 'billing',
  TEAM_LISTING = 'teamListing',
  TEAM_CREATE = 'teamCreate',
  ORGANIZATION_MEMBERS = 'organizationMembers',
  ROLES = 'roles',

  // TEAM
  ASSIGN_ROLES = 'assignRoles',
  TEAM_SETTINGS = 'teamSettings',
  TEAM_MEMBERS = 'teamMembers',
  CHECKS = 'checks',
  INCIDENTS = 'incidents',

  ON_CALL = 'onCall',
  ESCALATION_POLICY = 'escalations',
  STATUS_PAGE = 'statusPage',
  INTEGRATIONS = 'integrations',
  TOKEN_MANAGEMENT = 'tokenManagement',

  // USER
  USER_PROFILE = 'userProfile',
}

export enum PermissionAction {
  VIEW = 'view',
  MANAGE = 'manage',
}

export const FORBIDDE<PERSON>_MESSAGE =
  'You do not have permission to access this resource'
