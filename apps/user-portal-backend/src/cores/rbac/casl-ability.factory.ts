import { Injectable } from '@nestjs/common'
import {
  AbilityBuilder,
  AbilityClass,
  ExtractSubjectType,
  InferSubjects,
  PureAbility,
  mongoQueryMatcher,
} from '@casl/ability'

import { UserAuthResponseDto } from '@backend/modules/user/applications/dto/user.response.dto'
import { InternalRole } from '@backend/modules/user/constants/user'

import { PermissionAction, PermissionScope } from './permission.constant'

type Subjects = InferSubjects<PermissionScope | 'all'>
type AppAbility = PureAbility<[PermissionAction, Subjects]>

@Injectable()
export class CaslAbilityFactory {
  createForUser(payload: UserAuthResponseDto) {
    const { can, build } = new AbilityBuilder<PureAbility<[string, Subjects]>>(
      PureAbility as AbilityClass<AppAbility>,
    )

    if (payload.internalRole) {
      if (payload.internalRole === InternalRole.SUPER_ADMIN) {
        Object.values(PermissionScope).forEach((scope) => {
          can(PermissionAction.MANAGE, scope)
          can(PermissionAction.VIEW, scope)
        })
        return build({
          detectSubjectType: (item) => item as ExtractSubjectType<Subjects>,
          conditionsMatcher: mongoQueryMatcher,
        })
      }
    }

    can(PermissionAction.MANAGE, PermissionScope.USER_PROFILE, {
      userId: payload.id,
    })

    const { organizations } = payload

    organizations?.forEach(({ id: organizationId, teams }) => {
      teams?.forEach(({ id: teamId, role }) => {
        role?.permissions?.forEach((permission) => {
          if (permission?.action && permission?.scope) {
            can(permission.action, permission.scope, {
              teamId,
              organizationId,
            })
          }
        })
      })
    })

    return build({
      detectSubjectType: (item) => item as ExtractSubjectType<Subjects>,
      conditionsMatcher: mongoQueryMatcher,
    })
  }
}
