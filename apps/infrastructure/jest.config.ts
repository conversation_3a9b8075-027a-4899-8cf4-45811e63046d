/* eslint-disable */
export default {
  displayName: 'infrastructure',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/apps/infrastructure',
  moduleNameMapper: {
    '^@infra/(.*)$': '<rootDir>/src/$1',
    '^@libs/shared/constants/dynamo-constants$': '<rootDir>/../libs/shared/src/constants/dynamo-constants.ts',
    '^@libs/shared/constants/shared.interface$': '<rootDir>/../libs/shared/src/constants/shared.interface.ts',
    '^@libs/shared/constants/incident$': '<rootDir>/../libs/shared/src/constants/incident.ts',
    '^@libs/shared/(.*)$': '<rootDir>/../libs/shared/src/$1',
    '^@libs/notifier/incident-notifier$': '<rootDir>/../libs/notifier/src/incident-notifier/index.ts',
    '^@libs/notifier/(.*)$': '<rootDir>/../libs/notifier/src/$1',
    '^@libs/notifier$': '<rootDir>/../libs/notifier/src/index.ts',
    '^@libs/integration-callback/incident-integration-callback$': '<rootDir>/../libs/integration-callback/src/incident-integration-callback/index.ts',
    '^@libs/integration-callback/(.*)$': '<rootDir>/../libs/integration-callback/src/$1',
    '^@libs/integration-callback$': '<rootDir>/../libs/integration-callback/src/index.ts',
    '^@libs/database/lib/schema/schema$': '<rootDir>/../libs/database/src/lib/schema/schema.ts',
    '^@libs/database/lib/dynamo/incident-check.schema$': '<rootDir>/../libs/database/src/lib/dynamo/incident-check.schema.ts',
    '^@libs/database/lib/dynamo/incident-customIncident.schema$': '<rootDir>/../libs/database/src/lib/dynamo/incident-customIncident.schema.ts',
    '^@libs/database/lib/dynamo/incident-event.schema$': '<rootDir>/../libs/database/src/lib/dynamo/incident-event.schema.ts',
    '^@libs/database/lib/dynamo/webhook-log.schema$': '<rootDir>/../libs/database/src/lib/dynamo/webhook-log.schema.ts',
    '^@libs/database/(.*)$': '<rootDir>/../libs/database/src/$1',
    '^@libs/pubsub-queue/(.*)$': '<rootDir>/../libs/pubsub-queue/src/$1',
    '^@backend/frameworks/database/interfaces/paginate.interface$': '<rootDir>/../user-portal-backend/src/frameworks/database/interfaces/paginate.interface.ts',
    '^@backend/commons/dto/paginateOptions.dto$': '<rootDir>/../user-portal-backend/src/commons/dto/paginateOptions.dto.ts',
    '^@backend/(.*)$': '<rootDir>/../user-portal-backend/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/main.ts', // Exclude future main entry point
    '!cdk.out/**/*',
    '!jest.config.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  coverageReporters: ['lcov', 'text'],
  resetMocks: true,
};
