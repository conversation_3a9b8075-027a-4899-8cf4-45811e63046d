import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as sqs from 'aws-cdk-lib/aws-sqs'
import * as iam from 'aws-cdk-lib/aws-iam'
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch'

/**
 * Configuration properties for the StandardSQSQueueConstruct.
 */
export interface StandardSQSQueueConstructProps {
  /**
   * The name of the queue (will be prefixed with environment)
   */
  readonly queueName: string

  /**
   * Environment name (e.g., 'development', 'NonProd', 'Prod')
   */
  readonly environment: string

  /**
   * The visibility timeout for messages in the queue
   * @default cdk.Duration.seconds(60)
   */
  readonly visibilityTimeout?: cdk.Duration

  /**
   * The message retention period
   * @default cdk.Duration.days(14)
   */
  readonly messageRetentionPeriod?: cdk.Duration

  /**
   * Maximum number of times a message can be received before being sent to DLQ
   * @default 3
   */
  readonly maxReceiveCount?: number

  /**
   * Whether to create a Dead Letter Queue
   * @default true
   */
  readonly createDeadLetterQueue?: boolean

  /**
   * Custom Dead Letter Queue if you want to use an existing one
   * @default - A new DLQ will be created if createDeadLetterQueue is true
   */
  readonly deadLetterQueue?: sqs.IQueue

  /**
   * Queue encryption type
   * @default sqs.QueueEncryption.KMS_MANAGED
   */
  readonly encryption?: sqs.QueueEncryption

  /**
   * Additional tags to apply to the queue
   * @default {}
   */
  readonly additionalTags?: { [key: string]: string }

  /**
   * Service name for tagging purposes
   * @default 'Unknown'
   */
  readonly serviceName?: string

  /**
   * Purpose description for the queue
   */
  readonly purpose: string

  /**
   * Whether to create CloudWatch alarms for the queue
   * @default true
   */
  readonly createAlarms?: boolean

  /**
   * Threshold for high queue depth alarm
   * @default 100
   */
  readonly highDepthThreshold?: number

  /**
   * Threshold for old messages alarm (in seconds)
   * @default 300 (5 minutes)
   */
  readonly oldMessageThreshold?: number
}

/**
 * Creates a standardized SQS Queue with Dead Letter Queue, monitoring, and proper IAM permissions
 * following MonitoringDog infrastructure patterns.
 */
export class StandardSQSQueueConstruct extends Construct {
  /**
   * The main SQS queue
   */
  public readonly queue: sqs.Queue

  /**
   * The Dead Letter Queue (if created)
   */
  public readonly deadLetterQueue?: sqs.Queue

  /**
   * IAM policy statements for sending messages to this queue
   */
  public readonly sendMessagesPolicyStatements: iam.PolicyStatement[]

  /**
   * IAM policy statements for receiving messages from this queue
   */
  public readonly receiveMessagesPolicyStatements: iam.PolicyStatement[]

  /**
   * CloudWatch alarms for this queue (if created)
   */
  public readonly alarms: cloudwatch.Alarm[]

  constructor(
    scope: Construct,
    id: string,
    props: StandardSQSQueueConstructProps,
  ) {
    super(scope, id)

    this.alarms = []

    // Create Dead Letter Queue if needed
    if (props.createDeadLetterQueue !== false && !props.deadLetterQueue) {
      this.deadLetterQueue = this.createDeadLetterQueue(props)
    } else if (props.deadLetterQueue) {
      this.deadLetterQueue = props.deadLetterQueue as sqs.Queue
    }

    // Create main queue
    this.queue = this.createMainQueue(props)

    // Create IAM policy statements
    this.sendMessagesPolicyStatements =
      this.createSendMessagesPolicyStatements()
    this.receiveMessagesPolicyStatements =
      this.createReceiveMessagesPolicyStatements()

    // Create CloudWatch alarms if requested
    if (props.createAlarms !== false) {
      this.createCloudWatchAlarms(props)
    }

    // Create outputs
    this.createOutputs(props)
  }

  /**
   * Creates the Dead Letter Queue
   */
  private createDeadLetterQueue(
    props: StandardSQSQueueConstructProps,
  ): sqs.Queue {
    const dlqName = `${props.environment}-${props.queueName}-dlq`

    const dlq = new sqs.Queue(this, 'DeadLetterQueue', {
      queueName: dlqName,
      retentionPeriod: props.messageRetentionPeriod ?? cdk.Duration.days(14),
      encryption: props.encryption ?? sqs.QueueEncryption.KMS_MANAGED,
    })

    // Add tags
    cdk.Tags.of(dlq).add('Environment', props.environment)
    cdk.Tags.of(dlq).add('Service', props.serviceName ?? 'Unknown')
    cdk.Tags.of(dlq).add('Purpose', 'DeadLetterQueue')
    cdk.Tags.of(dlq).add('ParentQueue', props.queueName)

    // Add additional tags if provided
    if (props.additionalTags) {
      Object.entries(props.additionalTags).forEach(([key, value]) => {
        cdk.Tags.of(dlq).add(key, value)
      })
    }

    return dlq
  }

  /**
   * Creates the main SQS queue
   */
  private createMainQueue(props: StandardSQSQueueConstructProps): sqs.Queue {
    const queueName = `${props.environment}-${props.queueName}`

    const queueProps: sqs.QueueProps = {
      queueName: queueName,
      visibilityTimeout: props.visibilityTimeout ?? cdk.Duration.seconds(60),
      retentionPeriod: props.messageRetentionPeriod ?? cdk.Duration.days(14),
      encryption: props.encryption ?? sqs.QueueEncryption.KMS_MANAGED,
    }

    // Add dead letter queue configuration if available
    if (this.deadLetterQueue) {
      ;(queueProps as any).deadLetterQueue = {
        queue: this.deadLetterQueue,
        maxReceiveCount: props.maxReceiveCount ?? 3,
      }
    }

    const queue = new sqs.Queue(this, 'Queue', queueProps)

    // Add tags
    cdk.Tags.of(queue).add('Environment', props.environment)
    cdk.Tags.of(queue).add('Service', props.serviceName ?? 'Unknown')
    cdk.Tags.of(queue).add('Purpose', props.purpose)

    // Add additional tags if provided
    if (props.additionalTags) {
      Object.entries(props.additionalTags).forEach(([key, value]) => {
        cdk.Tags.of(queue).add(key, value)
      })
    }

    return queue
  }

  /**
   * Creates IAM policy statements for sending messages to the queue
   */
  private createSendMessagesPolicyStatements(): iam.PolicyStatement[] {
    return [
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'sqs:SendMessage',
          'sqs:GetQueueAttributes',
          'sqs:GetQueueUrl',
        ],
        resources: [this.queue.queueArn],
      }),
    ]
  }

  /**
   * Creates IAM policy statements for receiving messages from the queue
   */
  private createReceiveMessagesPolicyStatements(): iam.PolicyStatement[] {
    const resources = [this.queue.queueArn]

    // Add DLQ permissions if it exists
    if (this.deadLetterQueue) {
      resources.push(this.deadLetterQueue.queueArn)
    }

    return [
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'sqs:ReceiveMessage',
          'sqs:DeleteMessage',
          'sqs:GetQueueAttributes',
          'sqs:GetQueueUrl',
          'sqs:ChangeMessageVisibility',
        ],
        resources: resources,
      }),
    ]
  }

  /**
   * Creates CloudWatch alarms for the queue
   */
  private createCloudWatchAlarms(props: StandardSQSQueueConstructProps): void {
    const alarmPrefix = `${props.environment}-${props.queueName}`

    // High queue depth alarm
    const highDepthAlarm = new cloudwatch.Alarm(this, 'HighDepthAlarm', {
      alarmName: `${alarmPrefix}-HighDepth`,
      alarmDescription: `High message count in ${props.queueName} queue`,
      metric: this.queue.metricApproximateNumberOfMessagesVisible(),
      threshold: props.highDepthThreshold ?? 100,
      evaluationPeriods: 2,
      datapointsToAlarm: 2,
    })
    this.alarms.push(highDepthAlarm)

    // Old messages alarm
    const oldMessagesAlarm = new cloudwatch.Alarm(this, 'OldMessagesAlarm', {
      alarmName: `${alarmPrefix}-OldMessages`,
      alarmDescription: `Old messages detected in ${props.queueName} queue`,
      metric: this.queue.metricApproximateAgeOfOldestMessage(),
      threshold: props.oldMessageThreshold ?? 300,
      evaluationPeriods: 1,
      comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
    })
    this.alarms.push(oldMessagesAlarm)

    // DLQ alarm if DLQ exists
    if (this.deadLetterQueue) {
      const dlqAlarm = new cloudwatch.Alarm(this, 'DLQMessagesAlarm', {
        alarmName: `${alarmPrefix}-DLQ-Messages`,
        alarmDescription: `Messages detected in ${props.queueName} Dead Letter Queue`,
        metric: this.deadLetterQueue.metricApproximateNumberOfMessagesVisible(),
        threshold: 0,
        evaluationPeriods: 1,
        comparisonOperator:
          cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
      })
      this.alarms.push(dlqAlarm)
    }
  }

  /**
   * Creates CloudFormation outputs for the queue
   */
  private createOutputs(props: StandardSQSQueueConstructProps): void {
    const outputPrefix = `${props.queueName.replace(/-/g, '')}`

    // Main queue outputs
    new cdk.CfnOutput(this, 'QueueUrl', {
      value: this.queue.queueUrl,
      description: `URL of the ${props.queueName} queue`,
      exportName: `${props.environment}-${outputPrefix}QueueUrl`,
    })

    new cdk.CfnOutput(this, 'QueueArn', {
      value: this.queue.queueArn,
      description: `ARN of the ${props.queueName} queue`,
      exportName: `${props.environment}-${outputPrefix}QueueArn`,
    })

    // DLQ outputs if it exists
    if (this.deadLetterQueue) {
      new cdk.CfnOutput(this, 'DLQUrl', {
        value: this.deadLetterQueue.queueUrl,
        description: `URL of the ${props.queueName} Dead Letter Queue`,
        exportName: `${props.environment}-${outputPrefix}DLQUrl`,
      })

      new cdk.CfnOutput(this, 'DLQArn', {
        value: this.deadLetterQueue.queueArn,
        description: `ARN of the ${props.queueName} Dead Letter Queue`,
        exportName: `${props.environment}-${outputPrefix}DLQArn`,
      })
    }
  }

  /**
   * Grants permissions to send messages to this queue
   */
  public grantSendMessages(grantee: iam.IGrantable): iam.Grant {
    return this.queue.grantSendMessages(grantee)
  }

  /**
   * Grants permissions to consume messages from this queue
   */
  public grantConsumeMessages(grantee: iam.IGrantable): iam.Grant {
    return this.queue.grantConsumeMessages(grantee)
  }
}
