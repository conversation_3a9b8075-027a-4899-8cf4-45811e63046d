import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as ses from 'aws-cdk-lib/aws-ses'
import * as route53 from 'aws-cdk-lib/aws-route53'
import * as iam from 'aws-cdk-lib/aws-iam'

/**
 * Configuration properties for the StandardSesConfigurationConstruct.
 */
export interface StandardSesConfigurationConstructProps {
  /**
   * The domain name to verify with SES.
   * This domain will be configured for sending emails through SES.
   */
  readonly domainName: string

  /**
   * Whether to enable DKIM signing for the domain.
   * DKIM (DomainKeys Identified Mail) helps prevent email spoofing.
   * @default true
   */
  readonly enableDkim?: boolean

  /**
   * Optional Route53 hosted zone for the domain.
   * If provided, DNS records for domain verification and DKIM will be automatically created.
   * If not provided, the necessary DNS records will be output for manual creation.
   * @default - No hosted zone, manual DNS record creation required.
   */
  readonly hostedZone?: route53.IHostedZone

  /**
   * Whether to create a configuration set.
   * Configuration sets let you publish email sending events to other AWS services.
   * @default false
   */
  readonly createConfigurationSet?: boolean

  /**
   * The name of the configuration set to create.
   * Only applicable if createConfigurationSet is true.
   * @default - A name is automatically generated based on the domain name.
   */
  readonly configurationSetName?: string

  /**
   * Whether to create a receipt rule set.
   * Receipt rule sets define actions to take when emails are received.
   * @default false
   */
  readonly createReceiptRuleSet?: boolean

  /**
   * The name of the receipt rule set to create.
   * Only applicable if createReceiptRuleSet is true.
   * @default - A name is automatically generated based on the domain name.
   */
  readonly receiptRuleSetName?: string

  /**
   * Optional IAM role to use for SES actions.
   * If not provided, appropriate permissions will be created.
   * @default - A new role with necessary permissions is created.
   */
  readonly sesRole?: iam.IRole

  /**
   * Tags to apply to all resources created by this construct.
   * @default - No tags are applied.
   */
  readonly tags?: { [key: string]: string }
}

/**
 * Creates a standardized AWS SES (Simple Email Service) configuration with common settings.
 *
 * This construct provisions SES domain identity verification, DKIM setup, and optionally
 * configuration sets and receipt rule sets. It follows organizational best practices
 * for email sending and receiving configuration.
 */
export class StandardSesConfigurationConstruct extends Construct {
  /**
   * The SES domain identity created by this construct.
   */
  public readonly domainIdentity: ses.CfnEmailIdentity

  /**
   * The configuration set, if created.
   */
  public readonly configurationSet?: ses.CfnConfigurationSet

  /**
   * The receipt rule set, if created.
   */
  public readonly receiptRuleSet?: ses.CfnReceiptRuleSet

  /**
   * The verification DNS records that need to be created manually.
   * This is populated only if a hosted zone is not provided and DKIM is enabled.
   */
  public readonly verificationRecords: {
    name: string
    type: string
    value: string
  }[] = []

  constructor(
    scope: Construct,
    id: string,
    props: StandardSesConfigurationConstructProps,
  ) {
    super(scope, id)

    // Validate required properties
    if (!props.domainName) {
      throw new Error('domainName is required')
    }

    // Apply tags if provided
    if (props.tags) {
      Object.entries(props.tags).forEach(([key, value]) => {
        cdk.Tags.of(this).add(key, value)
      })
    }

    // --- Domain Identity and DKIM ---
    const dkimEnabled = props.enableDkim !== false
    this.domainIdentity = new ses.CfnEmailIdentity(this, 'DomainIdentity', {
      emailIdentity: props.domainName,
      ...(dkimEnabled && { dkimAttributes: { signingEnabled: true } }),
    })

    // --- DNS Records (if DKIM enabled) ---
    if (dkimEnabled) {
      if (props.hostedZone) {
        // Auto-create DKIM records in Route53
        new route53.CnameRecord(this, 'DkimRecord1', {
          zone: props.hostedZone,
          recordName: `${this.domainIdentity.attrDkimDnsTokenName1}._domainkey.${props.domainName}`,
          domainName: `${this.domainIdentity.attrDkimDnsTokenValue1}.dkim.amazonses.com`,
          ttl: cdk.Duration.minutes(30),
        })
        new route53.CnameRecord(this, 'DkimRecord2', {
          zone: props.hostedZone,
          recordName: `${this.domainIdentity.attrDkimDnsTokenName2}._domainkey.${props.domainName}`,
          domainName: `${this.domainIdentity.attrDkimDnsTokenValue2}.dkim.amazonses.com`,
          ttl: cdk.Duration.minutes(30),
        })
        new route53.CnameRecord(this, 'DkimRecord3', {
          zone: props.hostedZone,
          recordName: `${this.domainIdentity.attrDkimDnsTokenName3}._domainkey.${props.domainName}`,
          domainName: `${this.domainIdentity.attrDkimDnsTokenValue3}.dkim.amazonses.com`,
          ttl: cdk.Duration.minutes(30),
        })
      } else {
        // Populate verificationRecords for manual creation
        this.verificationRecords.push({
          name: `${this.domainIdentity.attrDkimDnsTokenName1}._domainkey.${props.domainName}`,
          type: 'CNAME',
          value: `${this.domainIdentity.attrDkimDnsTokenValue1}.dkim.amazonses.com`,
        })
        this.verificationRecords.push({
          name: `${this.domainIdentity.attrDkimDnsTokenName2}._domainkey.${props.domainName}`,
          type: 'CNAME',
          value: `${this.domainIdentity.attrDkimDnsTokenValue2}.dkim.amazonses.com`,
        })
        this.verificationRecords.push({
          name: `${this.domainIdentity.attrDkimDnsTokenName3}._domainkey.${props.domainName}`,
          type: 'CNAME',
          value: `${this.domainIdentity.attrDkimDnsTokenValue3}.dkim.amazonses.com`,
        })
      }
    }

    // --- Configuration Set ---
    if (props.createConfigurationSet) {
      const configSetName =
        props.configurationSetName ||
        `${props.domainName.replace(/\./g, '-')}-config-set`
      // Assign to the readonly property within the constructor
      const configSet = new ses.CfnConfigurationSet(this, 'ConfigurationSet', {
        name: configSetName,
        sendingOptions: { sendingEnabled: true },
        reputationOptions: { reputationMetricsEnabled: true },
      })
      this.configurationSet = configSet // Assign after creation

      // Output the configuration set name
      new cdk.CfnOutput(this, 'ConfigurationSetName', {
        value: configSet.ref,
        description: `Name of the SES Configuration Set for ${props.domainName}`,
      })
    }

    // --- Receipt Rule Set ---
    if (props.createReceiptRuleSet) {
      const ruleSetName =
        props.receiptRuleSetName ||
        `${props.domainName.replace(/\./g, '-')}-rule-set`
      // Assign to the readonly property within the constructor
      const ruleSet = new ses.CfnReceiptRuleSet(this, 'ReceiptRuleSet', {
        ruleSetName: ruleSetName,
        // Note: Rules need to be added separately using CfnReceiptRule
      })
      this.receiptRuleSet = ruleSet // Assign after creation

      // Output the receipt rule set name
      new cdk.CfnOutput(this, 'ReceiptRuleSetName', {
        value: ruleSet.ref,
        description: `Name of the SES Receipt Rule Set for ${props.domainName}`,
      })
      // Note: Activation needs to be handled manually/via console/CLI/Custom Resource.
    }

    // --- Outputs ---

    // Output the verified domain identity name
    new cdk.CfnOutput(this, 'VerifiedDomainIdentity', {
      value: this.domainIdentity.emailIdentity, // Output the domain name itself
      description: `Verified SES Domain Identity for ${props.domainName}`,
    })

    // Output DKIM verification records if they need to be created manually
    if (this.verificationRecords.length > 0) {
      new cdk.CfnOutput(this, 'DkimVerificationRecords', {
        value: JSON.stringify(this.verificationRecords, null, 2),
        description: `Manual DKIM CNAME records needed for SES verification of ${props.domainName}`,
      })
    }
  } // End of constructor
} // End of class
