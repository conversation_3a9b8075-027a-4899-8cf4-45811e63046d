import { Construct } from 'constructs'
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions'
import * as logs from 'aws-cdk-lib/aws-logs'
import { RemovalPolicy } from 'aws-cdk-lib'

/**
 * Properties for the StandardStateMachineConstruct.
 */
export interface StandardStateMachineConstructProps {
  /**
   * The name for the state machine. Should be unique within the AWS account and region.
   */
  readonly stateMachineName: string

  /**
   * The definition of the state machine's workflow.
   * This can be provided either as a DefinitionBody object or, preferably,
   * as an IChainable object (e.g., the result of chaining Step Functions states like Pass, Task, Choice).
   * Using IChainable allows for better type checking and composition within CDK.
   */
  readonly definition: stepfunctions.IChainable | stepfunctions.DefinitionBody

  /**
   * The type of the state machine (STANDARD or EXPRESS).
   * STANDARD workflows are ideal for long-running, durable, and auditable workflows.
   * EXPRESS workflows are suitable for high-volume, short-duration, event-processing workloads.
   * @default stepfunctions.StateMachineType.STANDARD
   */
  readonly stateMachineType?: stepfunctions.StateMachineType

  /**
   * Optional configuration for CloudWatch Logs logging of state machine executions.
   * If provided, a Log Group will be created or an existing one can be used.
   * @default - Logging is disabled.
   */
  readonly logs?: {
    /**
     * The CloudWatch Log Group to send execution logs to.
     * @default - A new Log Group is created specifically for this state machine.
     */
    readonly destination?: logs.ILogGroup
    /**
     * Specifies whether execution data (input and output) is included in the logs.
     * Be mindful of sensitive data when enabling this.
     * @default false
     */
    readonly includeExecutionData?: boolean
    /**
     * The level of detail for logging execution history events.
     * Options range from OFF to ALL.
     * @default stepfunctions.LogLevel.ERROR Only log errors.
     */
    readonly level?: stepfunctions.LogLevel
    /**
     * The removal policy to apply to the log group created by this construct.
     * Does not apply if an existing `destination` log group is provided.
     * @default RemovalPolicy.RETAIN Log group is kept when the stack is deleted.
     */
    readonly removalPolicy?: RemovalPolicy
  }

  /**
   * Specifies whether AWS X-Ray tracing is enabled for this state machine.
   * Enabling tracing helps visualize and debug workflow executions.
   * @default false Tracing is disabled.
   */
  readonly tracingEnabled?: boolean
}

/**
 * Creates a standardized AWS Step Functions State Machine following best practices.
 *
 * This construct simplifies the provisioning of a state machine by providing
 * sensible defaults and configurable options for common features like logging
 * and tracing. It accepts the state machine's workflow definition via props,
 * allowing consuming stacks to define their specific logic.
 *
 * @example
 * // Define the state machine logic (e.g., using Pass states)
 * const startState = new stepfunctions.Pass(this, 'StartState');
 * const definition = stepfunctions.Chain.start(startState);
 *
 * // Instantiate the construct
 * new StandardStateMachineConstruct(this, 'MyWorkflow', {
 *   stateMachineName: 'my-application-workflow',
 *   definition: definition,
 *   logs: { level: stepfunctions.LogLevel.ALL },
 *   tracingEnabled: true,
 * });
 */
export class StandardStateMachineConstruct extends Construct {
  /**
   * The underlying Step Functions State Machine instance created by this construct.
   * This can be used to grant permissions or reference the state machine elsewhere.
   */
  public readonly stateMachine: stepfunctions.IStateMachine

  constructor(
    scope: Construct,
    id: string,
    props: StandardStateMachineConstructProps,
  ) {
    super(scope, id)

    let logGroup: logs.ILogGroup | undefined
    let logOptions: stepfunctions.LogOptions | undefined

    // Configure logging if requested
    if (props.logs) {
      logGroup =
        props.logs.destination ??
        new logs.LogGroup(this, 'StateMachineLogGroup', {
          // Using a predictable but specific name is often helpful
          logGroupName: `/aws/stepfunctions/${props.stateMachineName}`,
          removalPolicy: props.logs.removalPolicy ?? RemovalPolicy.RETAIN,
        })

      logOptions = {
        destination: logGroup,
        includeExecutionData: props.logs.includeExecutionData ?? false,
        level: props.logs.level ?? stepfunctions.LogLevel.ERROR,
      }
    }

    // Determine the definition body based on the type provided in props
    let definitionBody: stepfunctions.DefinitionBody
    // Check if the definition is an instance of DefinitionBody or needs conversion from IChainable
    if (props.definition instanceof stepfunctions.DefinitionBody) {
      definitionBody = props.definition
    } else {
      // Assume it's IChainable and convert it
      definitionBody = stepfunctions.DefinitionBody.fromChainable(
        props.definition,
      )
    }

    // Create the state machine
    this.stateMachine = new stepfunctions.StateMachine(this, 'Resource', {
      // Using 'Resource' as the logical ID within the construct
      stateMachineName: props.stateMachineName,
      definitionBody: definitionBody,
      stateMachineType:
        props.stateMachineType ?? stepfunctions.StateMachineType.STANDARD,
      ...(logOptions && { logs: logOptions }),
      tracingEnabled: props.tracingEnabled ?? false,
    })

    // Ensure the state machine depends on the log group if it was created here
    if (logGroup && !props.logs?.destination) {
      this.stateMachine.node.addDependency(logGroup)
    }
  }
}
