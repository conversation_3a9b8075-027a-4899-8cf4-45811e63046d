import { Construct } from 'constructs'
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager'
import * as iam from 'aws-cdk-lib/aws-iam'
import * as cdk from 'aws-cdk-lib' // Import the core CDK library for Stack
import { Environment } from '@infra/shared/environment'
import { StackProps } from '@infra/app'

export type SecretProviderProps = StackProps

/**
 * Provides a centralized way to access all application configurations from a single AWS Secret Manager secret.
 * Assumes a single secret is pre-existing and follows the naming convention:
 * /monitoring-dog/{environment}/application_config
 */
export class SecretProvider extends Construct {
  // --- Common Environment Variables (not from Secrets Manager directly but useful to have) ---
  public readonly awsRegion: string
  public readonly nodeEnv: 'development' | 'production' | 'test'
  public readonly appLogLevel: 'debug' | 'info' | 'warn' | 'error'

  // --- Database Configuration ---
  public readonly dbHost: string
  public readonly dbPort: string
  public readonly dbName: string
  public readonly dbUsername: string
  public readonly dbPassword: string

  // --- Stripe Configuration ---
  public readonly stripeSecretKey: string
  public readonly stripeSmsPriceId: string
  public readonly stripeVoicePriceId: string

  // --- SES Configuration ---
  public readonly sesSmtpUsername: string
  public readonly sesSmtpPassword: string
  public readonly sesFromAddress: string
  public readonly sesHost: string
  public readonly sesPort: string

  // --- Slack Configuration ---
  public readonly slackBotToken: string
  public readonly slackSigningSecret: string

  // --- Application Configuration ---
  public readonly webPortalUrl: string

  // --- Plivo Configuration ---
  public readonly plivoAuthId: string
  public readonly plivoAuthToken: string
  public readonly plivoPhloId: string

  // --- Stringee Configuration ---
  public readonly stringeeAuthToken: string
  public readonly stringeeBrandId: string
  public readonly stringeeFromNumber: string

  // --- OneSignal Configuration ---
  public readonly oneSignalApiKey: string

  // --- Mocking Configuration ---
  public readonly mockSmsVoiceCall: string

  private readonly applicationConfigSecret: secretsmanager.ISecret

  constructor(
    scope: Construct,
    id: string,
    props: {
      environment: Environment
    },
  ) {
    super(scope, id)

    const env = props.environment
    const stack = cdk.Stack.of(this)

    // Common environment settings
    this.awsRegion = stack.region
    this.nodeEnv = env === Environment.Prod ? 'production' : 'development'
    this.appLogLevel = env === Environment.Prod ? 'info' : 'debug'

    // Fetch the single application configuration secret
    this.applicationConfigSecret = secretsmanager.Secret.fromSecretCompleteArn(
      this,
      'CDKConfigSM',
      `arn:aws:secretsmanager:us-east-1:851725359289:secret:cdk-${this.nodeEnv}-DbxEbr`,
    )
    console.log('added to forced cdk redeploy for new secret')
    // --- Populate properties from the single secret ---
    // Note: The JSON keys in the secret *must* match these strings (e.g., "DB_HOST", "STRIPE_SECRET_KEY")

    // Database
    this.dbHost = this.applicationConfigSecret
      .secretValueFromJson('DB_HOST')
      .toString()
    this.dbPort = this.applicationConfigSecret
      .secretValueFromJson('DB_PORT')
      .toString()
    this.dbName = this.applicationConfigSecret
      .secretValueFromJson('DB_DATABASE')
      .toString()
    this.dbUsername = this.applicationConfigSecret
      .secretValueFromJson('DB_USERNAME')
      .toString()
    this.dbPassword = this.applicationConfigSecret
      .secretValueFromJson('DB_PASSWORD')
      .toString()

    // Stripe
    this.stripeSecretKey = this.applicationConfigSecret
      .secretValueFromJson('STRIPE_SECRET_KEY')
      .toString()

    this.stripeSmsPriceId = this.applicationConfigSecret
      .secretValueFromJson('STRIPE_SMS_PRICE_ID')
      .toString()
    this.stripeVoicePriceId = this.applicationConfigSecret
      .secretValueFromJson('STRIPE_VOICE_PRICE_ID')
      .toString()

    // SES
    this.sesSmtpUsername = this.applicationConfigSecret
      .secretValueFromJson('SES_SMTP_USERNAME')
      .toString()
    this.sesSmtpPassword = this.applicationConfigSecret
      .secretValueFromJson('SES_SMTP_PASSWORD')
      .toString()
    this.sesFromAddress = this.applicationConfigSecret
      .secretValueFromJson('SES_FROM_MAIL')
      .toString() // Matched JSON key to previous lambda env
    this.sesHost = this.applicationConfigSecret
      .secretValueFromJson('SES_HOST')
      .toString()
    this.sesPort = this.applicationConfigSecret
      .secretValueFromJson('SES_PORT')
      .toString()

    // Slack
    this.slackBotToken = this.applicationConfigSecret
      .secretValueFromJson('SLACK_BOT_TOKEN')
      .toString()
    this.slackSigningSecret = this.applicationConfigSecret
      .secretValueFromJson('SLACK_SIGNING_SECRET')
      .toString()

    // Application
    this.webPortalUrl = this.applicationConfigSecret
      .secretValueFromJson('WEB_PORTAL_URL')
      .toString()

    // Plivo
    this.plivoAuthId = this.applicationConfigSecret
      .secretValueFromJson('PLIVO_AUTH_ID')
      .toString()
    this.plivoAuthToken = this.applicationConfigSecret
      .secretValueFromJson('PLIVO_AUTH_TOKEN')
      .toString()
    this.plivoPhloId = this.applicationConfigSecret
      .secretValueFromJson('PLIVO_PHLO_ID')
      .toString()

    // Stringee
    this.stringeeAuthToken = this.applicationConfigSecret
      .secretValueFromJson('STRINGEE_AUTH_TOKEN')
      .toString()
    this.stringeeBrandId = this.applicationConfigSecret
      .secretValueFromJson('STRINGEE_BRAND_ID')
      .toString()
    this.stringeeFromNumber = this.applicationConfigSecret
      .secretValueFromJson('STRINGEE_FROM_NUMBER')
      .toString()

    this.oneSignalApiKey = this.applicationConfigSecret
      .secretValueFromJson('ONE_SIGNAL_API_KEY')
      .toString()

    // Mocking
    this.mockSmsVoiceCall = this.applicationConfigSecret
      .secretValueFromJson('MOCK_SMS_VOICE_CALL')
      .toString()
  }

  /**
   * Grants the necessary read permissions for the application configuration secret to the grantee.
   * @param grantee The IAM principal (e.g., Lambda function's role) to grant permissions to.
   */
  public grantReadPermissionsTo(grantee: iam.IGrantable): void {
    if (!grantee || !('grantPrincipal' in grantee)) {
      console.warn(
        'SecretProvider: Invalid grantee provided for grantReadPermissionsTo. Grantee must be an iam.IGrantable.',
      )
      return
    }
    this.applicationConfigSecret.grantRead(grantee)
  }
}
