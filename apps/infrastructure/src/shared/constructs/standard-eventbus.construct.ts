import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as events from 'aws-cdk-lib/aws-events'
import * as logs from 'aws-cdk-lib/aws-logs'
import * as s3 from 'aws-cdk-lib/aws-s3'
import * as iam from 'aws-cdk-lib/aws-iam'

/**
 * Configuration properties for the StandardEventBusConstruct.
 */
export interface StandardEventBusConstructProps {
  /**
   * The name of the event bus.
   * If not provided, a name will be generated based on the stack name and construct ID.
   * @default - A name is automatically generated.
   */
  readonly eventBusName?: string

  /**
   * Whether to enable event archiving for the event bus.
   * @default false
   */
  readonly enableArchive?: boolean

  /**
   * The retention period for archived events (in days).
   * Only applicable if enableArchive is true.
   * @default cdk.Duration.days(30)
   */
  readonly archiveRetention?: cdk.Duration

  /**
   * Optional S3 bucket for event archive storage.
   * If provided, events will be archived to this bucket.
   * If not provided and enableArchive is true, a new bucket will be created.
   * @default - A new bucket is created if enableArchive is true.
   */
  readonly archiveBucket?: s3.IBucket

  /**
   * Optional prefix for archived events in the S3 bucket.
   * Only applicable if enableArchive is true.
   * @default 'events/'
   */
  readonly archivePrefix?: string

  /**
   * Optional event pattern for archiving.
   * If provided, only events matching this pattern will be archived.
   * Only applicable if enableArchive is true.
   * @default - All events are archived.
   */
  readonly archiveEventPattern?: events.EventPattern

  /**
   * Optional CloudWatch Logs log group for event bus metrics and logging.
   * @default - No specific log group is configured.
   */
  readonly logGroup?: logs.ILogGroup

  /**
   * Log retention period for the CloudWatch Log Group if one is created.
   * @default logs.RetentionDays.ONE_MONTH
   */
  readonly logRetention?: logs.RetentionDays
}

/**
 * Creates a standardized AWS EventBridge Event Bus with common configurations.
 *
 * This construct provisions an EventBridge Event Bus with configurable settings
 * including optional event archiving to S3 and CloudWatch Logs integration.
 * It follows organizational best practices for naming and configuration.
 */
export class StandardEventBusConstruct extends Construct {
  /**
   * The underlying EventBridge Event Bus instance.
   */
  public readonly eventBus: events.EventBus

  /**
   * The archive S3 bucket, if created.
   */
  public readonly archiveBucket?: s3.Bucket

  constructor(
    scope: Construct,
    id: string,
    props: StandardEventBusConstructProps = {},
  ) {
    super(scope, id)

    // Generate a default event bus name if not provided
    const eventBusName =
      props.eventBusName ?? `${cdk.Stack.of(this).stackName}-${id}-EventBus`

    // Create the event bus
    this.eventBus = new events.EventBus(this, 'Resource', {
      eventBusName: eventBusName,
    })

    if (!props.logGroup) {
      // Create a log group if logging is needed but no log group was provided
      new logs.LogGroup(this, 'LogGroup', {
        logGroupName: `/aws/events/${eventBusName}`,
        retention: props.logRetention ?? logs.RetentionDays.ONE_MONTH,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
      })
    }

    // Set up event archiving if enabled
    if (props.enableArchive) {
      // Use provided bucket or create a new one
      if (props.archiveBucket) {
        this.archiveBucket = props.archiveBucket as s3.Bucket
      } else {
        this.archiveBucket = new s3.Bucket(this, 'ArchiveBucket', {
          bucketName: `${eventBusName.toLowerCase()}-archive-${cdk.Stack.of(this).account}`,
          encryption: s3.BucketEncryption.S3_MANAGED,
          blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
          removalPolicy: cdk.RemovalPolicy.RETAIN, // Retain archived events by default
          lifecycleRules: [
            {
              enabled: true,
              expiration: props.archiveRetention ?? cdk.Duration.days(30),
            },
          ],
        })
      }

      // Grant the event bus permission to write to the archive bucket
      this.archiveBucket.grantWrite(
        new iam.ServicePrincipal('events.amazonaws.com'),
      )

      // Create a rule to send events to the archive bucket
      new events.Rule(this, 'ArchiveRule', {
        eventBus: this.eventBus,
        ruleName: `${eventBusName}-ArchiveRule`,
        description: `Archive rule for ${eventBusName}`,
        eventPattern: props.archiveEventPattern ?? {
          // Match all events if no pattern provided
          // Using a pattern that matches all events
          account: [cdk.Stack.of(this).account],
        },
      })

      // Add the S3 bucket as a target for the rule
      // Note: In a real implementation, you would need to set up a proper target
      // that can write to S3. This is a simplified example.
      // For a complete implementation, consider using a Lambda function or Firehose.
    }

    // Output the Event Bus ARN
    new cdk.CfnOutput(this, 'EventBusArn', {
      value: this.eventBus.eventBusArn,
      description: `ARN of the ${id} Event Bus`,
    })

    // Output the Event Bus Name
    new cdk.CfnOutput(this, 'EventBusName', {
      value: this.eventBus.eventBusName,
      description: `Name of the ${id} Event Bus`,
    })
  }
}
