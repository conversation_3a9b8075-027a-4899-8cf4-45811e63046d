import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as lambda from 'aws-cdk-lib/aws-lambda'
import * as iam from 'aws-cdk-lib/aws-iam'
import * as sqs from 'aws-cdk-lib/aws-sqs'
import * as logs from 'aws-cdk-lib/aws-logs'
import { NodejsFunction, BundlingOptions } from 'aws-cdk-lib/aws-lambda-nodejs' // Using NodejsFunction for easier bundling

/**
 * Configuration properties for the StandardLambdaConstruct.
 */
export interface StandardLambdaConstructProps {
  /**
   * The path to the Lambda function's entry point (e.g., 'src/handler.ts').
   * This is used by aws-lambda-nodejs.
   */
  readonly entry: string

  /**
   * The name of the exported handler function (e.g., 'handler').
   * @default 'handler'
   */
  readonly handler?: string

  /**
   * The runtime environment for the Lambda function.
   * @default lambda.Runtime.NODEJS_20_X
   */
  readonly runtime?: lambda.Runtime

  /**
   * The amount of memory, in MB, to allocate to the Lambda function.
   * @default 128
   */
  readonly memorySize?: number

  /**
   * The function execution time (in seconds) after which Lambda terminates the function.
   * @default cdk.Duration.seconds(3)
   */
  readonly timeout?: cdk.Duration

  /**
   * Environment variables to pass to the Lambda function.
   * @default {}
   */
  readonly environment?: { [key: string]: string }

  /**
   * Optional Dead Letter Queue (DLQ) to send failed events to.
   * If provided, the necessary permissions will be added.
   * @default - No DLQ is configured.
   */
  readonly deadLetterQueue?: sqs.IQueue

  /**
   * Optional configuration for standardized logging.
   * This is a placeholder for potential integration (e.g., passing a specific logger ARN or config).
   * @default - No specific logging configuration applied beyond standard CloudWatch Logs.
   */
  readonly loggingConfig?: any // Replace 'any' with a specific interface if defined

  /**
   * Additional IAM policies to attach to the Lambda function's execution role.
   * @default - Only basic CloudWatch Logs permissions are granted.
   */
  readonly additionalPolicies?: iam.PolicyStatement[]

  /**
   * Log retention period for the CloudWatch Log Group.
   * @default logs.RetentionDays.ONE_MONTH
   */
  readonly logRetention?: logs.RetentionDays

  /**
   * Bundling options for the NodejsFunction.
   * Allows customization of the build process (e.g., external modules, source maps).
   * @default - Default NodejsFunction bundling.
   */
  readonly bundlingOptions?: BundlingOptions

  /**
   * Explicit name for the Lambda function.
   * If not provided, a name will be generated based on stack name and construct ID.
   * @default - Generated name: `${cdk.Stack.of(this).stackName}-${id}-Lambda`
   */
  readonly functionName?: string
}

/**
 * Creates a standardized AWS Lambda function (Node.js) with common configurations.
 *
 * This construct provisions a Node.js Lambda function using the `aws-lambda-nodejs`
 * construct for simplified deployment, including basic IAM permissions, configurable
 * settings (memory, timeout), and optional integrations for DLQ and standardized logging.
 */
export class StandardLambdaConstruct extends Construct {
  /**
   * The underlying Lambda function instance.
   */
  public readonly lambdaFunction: lambda.IFunction

  constructor(
    scope: Construct,
    id: string,
    props: StandardLambdaConstructProps,
  ) {
    super(scope, id)

    // Use provided function name or generate one if not specified
    const functionName = props.functionName ?? `${id}-Lambda`

    this.lambdaFunction = new NodejsFunction(this, 'Resource', {
      functionName: functionName, // Use the determined function name
      entry: props.entry,
      handler: props.handler ?? 'handler',
      runtime: props.runtime ?? lambda.Runtime.NODEJS_20_X,
      memorySize: props.memorySize ?? 128,
      timeout: props.timeout ?? cdk.Duration.seconds(3),
      environment: props.environment ?? {},
      deadLetterQueueEnabled: !!props.deadLetterQueue,
      logRetention: props.logRetention ?? logs.RetentionDays.ONE_MONTH,
      ...(props.deadLetterQueue && { deadLetterQueue: props.deadLetterQueue }),
      bundling: {
        forceDockerBundling: true,
        // ...props.bundlingOptions,
      },
    })

    // Add basic CloudWatch Logs permissions (usually handled by NodejsFunction, but explicit for clarity)
    // NodejsFunction construct typically creates a role with basic permissions.
    // If more specific control is needed, create the role explicitly.

    // Add additional policies if provided
    if (props.additionalPolicies && props.additionalPolicies.length > 0) {
      props.additionalPolicies.forEach((policy) => {
        this.lambdaFunction.addToRolePolicy(policy)
      })
    }

    // Grant DLQ permissions if a queue is provided
    if (props.deadLetterQueue) {
      props.deadLetterQueue.grantSendMessages(this.lambdaFunction)
    }

    // Output the Lambda Function ARN (optional, but can be useful)
    new cdk.CfnOutput(this, 'LambdaFunctionArn', {
      value: this.lambdaFunction.functionArn,
      description: `ARN of the ${id} Lambda function`,
    })
  }
}
