import * as cdk from 'aws-cdk-lib'
import * as apigateway from 'aws-cdk-lib/aws-apigateway'
import * as lambda from 'aws-cdk-lib/aws-lambda'
import { Construct } from 'constructs'

/**
 * Properties for the StandardLambdaAuthorizerConstruct.
 */
export interface StandardLambdaAuthorizerConstructProps {
  /**
   * The Lambda function to use as the authorizer.
   * This function should be designed to handle API Gateway Lambda authorizer events.
   */
  readonly authorizerLambdaFunction: lambda.IFunction

  /**
   * The identity source for the authorizer. This is the location
   * API Gateway uses to extract the authorization token (e.g., a header).
   * @default 'method.request.header.Authorization'
   */
  readonly identitySource?: string

  /**
   * A friendly name for the authorizer.
   * @default - A name is automatically generated.
   */
  readonly authorizerName?: string

  // Note: The 'type' (TOKEN vs REQUEST) is implicitly defined by using TokenAuthorizer.
  // A separate construct could be made for RequestAuthorizer if needed.

  /**
   * The duration for which authorization results are cached.
   * Set to Duration.seconds(0) to disable caching.
   * @default Duration.minutes(5)
   */
  readonly resultsCacheTtl?: cdk.Duration
}

/**
 * Creates a standardized API Gateway Lambda Authorizer for REST APIs.
 *
 * This construct simplifies the creation of Lambda authorizers by providing
 * sensible defaults and requiring only the authorizer Lambda function.
 *
 * @example
 * // Assuming 'myAuthorizerFunction' is a lambda.Function or lambda.IFunction
 * const authorizerConstruct = new StandardLambdaAuthorizerConstruct(this, 'MyLambdaAuthorizer', {
 *   authorizerLambdaFunction: myAuthorizerFunction,
 *   identitySources: ['method.request.header.X-Api-Key'], // Optional: Override default identity source
 *   resultsCacheTtl: cdk.Duration.minutes(10), // Optional: Override cache TTL
 * });
 *
 * // Use authorizerConstruct.authorizer when defining API methods
 */
export class StandardLambdaAuthorizerConstruct extends Construct {
  /**
   * The created API Gateway Lambda Authorizer instance.
   */
  public readonly authorizer: apigateway.IAuthorizer

  constructor(
    scope: Construct,
    id: string,
    props: StandardLambdaAuthorizerConstructProps,
  ) {
    super(scope, id)

    // Defaulting to TokenAuthorizer based on common use cases and default identity source.
    // If REQUEST type is explicitly needed and passed in props, a more robust implementation
    // might use a conditional or factory pattern. For this task, TokenAuthorizer is used.
    this.authorizer = new apigateway.TokenAuthorizer(this, 'LambdaAuthorizer', {
      handler: props.authorizerLambdaFunction,
      identitySource:
        props.identitySource ??
        apigateway.IdentitySource.header('Authorization'), // Corrected: singular identitySource
      authorizerName: props.authorizerName ?? 'DefaultAuthorizerName', // Provide a default name if undefined
      resultsCacheTtl: props.resultsCacheTtl ?? cdk.Duration.minutes(5),
    })
  }
}
