import { eq } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import * as schema from '@libs/database/lib/schema/schema'

export default class TokenData {
  private db: NodePgDatabase
  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
  }

  async getTokenById(tokenId: string) {
    const tokens = await this.db
      .select()
      .from(schema.tokens)
      .where(eq(schema.tokens.id, tokenId))
      .execute()

    if (tokens?.length) return tokens[0]

    return null
  }
}
