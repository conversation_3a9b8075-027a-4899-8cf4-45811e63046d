import { and, eq, isNull, sql } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import * as schema from '@libs/database/lib/schema/schema'

export interface IntegrationSettingItem {
  id: string
  name?: string
  type?: string
  config?: object
  identity?: string
  team?: string
}

export default class IntegrationSettingData {
  private db: NodePgDatabase
  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
  }

  async getIntegrationSettingById(
    webhookId: string,
  ): Promise<IntegrationSettingItem | null> {
    const integrationSettings = await this.db
      .select()
      .from(schema.integrationSetting)
      .where(
        and(
          eq(schema.integrationSetting.id, webhookId),
          isNull(schema.integrationSetting.deletedAt),
        ),
      )
      .execute()
    if (integrationSettings?.length)
      return integrationSettings[0] as IntegrationSettingItem
    return null
  }

  async getIntegrationSettingByTeam(teamId: string) {
    const integrationSettings = await this.db
      .select()
      .from(schema.integrationSetting)
      .where(
        and(
          eq(schema.integrationSetting.teamId, teamId),
          isNull(schema.integrationSetting.deletedAt),
        ),
      )
      .execute()

    if (integrationSettings?.length) return integrationSettings

    return null
  }

  async getIntegrationSettingByTeamAndSendOnTrue(
    teamId: string,
    sendOnEvent: string,
  ): Promise<IntegrationSettingItem[] | null> {
    const integrationSettings = await this.db
      .select()
      .from(schema.integrationSetting)
      .where(
        and(
          eq(schema.integrationSetting.teamId, teamId),
          sql.raw(`config::jsonb ->> '${sendOnEvent}' = 'true'`),
          isNull(schema.integrationSetting.deletedAt),
        ),
      )
      .execute()

    if (integrationSettings?.length)
      return integrationSettings as IntegrationSettingItem[]
    return null
  }
}
