import { eq, and, count, isNull } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import {
  subscriptions,
  subscriptionItems,
  subscriptionPlans,
  planResourceLimits,
  subscriptionUsage,
  users,
  teams,
  checks,
  integrationSetting,
} from '@libs/database/lib/schema/schema'
// Import utilities for usage type generation
import { ResourceType } from '@libs/shared/constants/subscription/resource-mapping'
import { UsageTypeUtils } from '@libs/shared/utils/usage-type.utils'
// Import DynamoDB for status page counting
import { DynamoDBClient, ScanCommand } from '@aws-sdk/client-dynamodb'

export interface ResourceManipulationMessage {
  organizationId: string
  resourceType: ResourceType | string // Support for any resource type
  eventTimestamp: string
  action: 'CREATED' | 'DELETED' | 'UPDATED'
  resourceId: string
  metadata?: {
    previousCount?: number
    newCount?: number
    userId?: string
    teamId?: string
  }
  planContext?: {
    subscriptionPlanId: string
    resourceLimits: Array<{
      resourceType: ResourceType | string // Support for any resource type
      includedQuantity: number
      overageStripePriceId: string
    }>
    overagePolicyFixedResources: string
  }
}

export interface OverageCalculationResult {
  organizationId: string
  resourceType: string
  currentUsage: number
  planLimit: number
  overageAmount: number
  overagePolicy: string
  requiresStripeReporting: boolean
  billingPeriodStart: Date
  billingPeriodEnd: Date
  stripeCustomerId: string | null
  stripeSubscriptionId: string | null
  overageStripePriceId: string | null
  subscriptionItemId: string | null
}

export default class OverageCalculationData {
  private db: NodePgDatabase
  private dynamoClient: DynamoDBClient

  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
    this.dynamoClient = new DynamoDBClient({
      region: process.env['AWS_REGION'] || 'us-east-1',
    })
  }

  /**
   * Calculate resource overage using plan context from message (primary strategy)
   */
  async calculateOverageFromPlanContext(
    message: ResourceManipulationMessage,
  ): Promise<OverageCalculationResult> {
    // Get minimal subscription info (billing period, stripe customer ID, subscription item)
    const subscriptionResults = await this.db
      .select({
        subscriptionId: subscriptions.id,
        currentPeriodStartDate: subscriptions.currentPeriodStartDate,
        currentPeriodEndDate: subscriptions.currentPeriodEndDate,
        stripeCustomerId: subscriptions.stripeCustomerId,
        stripeSubscriptionId: subscriptions.stripeSubscriptionId,
        overagePolicyFixedResources: subscriptions.overagePolicyFixedResources,
        subscriptionItemId: subscriptionItems.id,
      })
      .from(subscriptions)
      .innerJoin(
        subscriptionItems,
        and(
          eq(subscriptions.id, subscriptionItems.subscriptionId),
          eq(subscriptionItems.itemType, 'BASE_PLAN'),
          eq(subscriptionItems.status, 'active'),
        ),
      )
      .where(
        and(
          eq(subscriptions.organizationId, message.organizationId),
          eq(subscriptions.status, 'active'),
        ),
      )
      .limit(1)

    if (subscriptionResults.length === 0) {
      return this.createEmptyOverageResult(
        message.organizationId,
        message.resourceType,
      )
    }

    const subscription = subscriptionResults[0]!

    // Get current actual usage for this resource type
    const currentUsage = await this.getCurrentResourceUsage(
      message.organizationId,
      message.resourceType,
    )

    // Get plan limit and overage price ID from planContext
    const resourceLimit = message.planContext!.resourceLimits.find(
      (limit) => limit.resourceType === message.resourceType,
    )

    if (!resourceLimit) {
      return this.createEmptyOverageResult(
        message.organizationId,
        message.resourceType,
      )
    }

    const planLimit = resourceLimit.includedQuantity
    const overageStripePriceId = resourceLimit.overageStripePriceId

    // Calculate overage amount
    const overageAmount = Math.max(0, currentUsage - planLimit)
    const overagePolicy =
      message.planContext!.overagePolicyFixedResources || 'BLOCK'

    const requiresStripeReporting =
      overageAmount > 0 &&
      overageStripePriceId !== null &&
      overagePolicy === 'CHARGE'

    return {
      organizationId: message.organizationId,
      resourceType: message.resourceType,
      currentUsage,
      planLimit,
      overageAmount,
      overagePolicy,
      requiresStripeReporting,
      billingPeriodStart: subscription.currentPeriodStartDate
        ? new Date(subscription.currentPeriodStartDate)
        : new Date(),
      billingPeriodEnd: subscription.currentPeriodEndDate
        ? new Date(subscription.currentPeriodEndDate)
        : new Date(),
      stripeCustomerId: subscription.stripeCustomerId,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      overageStripePriceId,
      subscriptionItemId: subscription.subscriptionItemId,
    }
  }

  /**
   * Calculate overage using database query (fallback strategy)
   */
  async calculateOverageFromDatabase(
    message: ResourceManipulationMessage,
  ): Promise<OverageCalculationResult> {
    // Get subscription and plan with resource limits
    const subscriptionResults = await this.db
      .select({
        subscriptionId: subscriptions.id,
        currentPeriodStartDate: subscriptions.currentPeriodStartDate,
        currentPeriodEndDate: subscriptions.currentPeriodEndDate,
        stripeCustomerId: subscriptions.stripeCustomerId,
        stripeSubscriptionId: subscriptions.stripeSubscriptionId,
        overagePolicyFixedResources: subscriptions.overagePolicyFixedResources,
        subscriptionItemId: subscriptionItems.id,
        subscriptionPlanId: subscriptionPlans.id,
      })
      .from(subscriptions)
      .innerJoin(
        subscriptionItems,
        and(
          eq(subscriptions.id, subscriptionItems.subscriptionId),
          eq(subscriptionItems.itemType, 'BASE_PLAN'),
          eq(subscriptionItems.status, 'active'),
        ),
      )
      .innerJoin(
        subscriptionPlans,
        eq(subscriptionItems.subscriptionPlanId, subscriptionPlans.id),
      )
      .where(
        and(
          eq(subscriptions.organizationId, message.organizationId),
          eq(subscriptions.status, 'active'),
        ),
      )
      .limit(1)

    if (subscriptionResults.length === 0) {
      return this.createEmptyOverageResult(
        message.organizationId,
        message.resourceType,
      )
    }

    const subscription = subscriptionResults[0]!

    // Get plan resource limit for this resource type
    const resourceLimitResults = await this.db
      .select({
        includedQuantity: planResourceLimits.includedQuantity,
        overageStripePriceId: planResourceLimits.overageStripePriceId,
      })
      .from(planResourceLimits)
      .where(
        and(
          eq(
            planResourceLimits.subscriptionPlanId,
            subscription.subscriptionPlanId,
          ),
          eq(planResourceLimits.resourceType, message.resourceType),
        ),
      )
      .limit(1)

    if (resourceLimitResults.length === 0) {
      return this.createEmptyOverageResult(
        message.organizationId,
        message.resourceType,
      )
    }

    const resourceLimit = resourceLimitResults[0]!

    // Get current actual usage for this resource type
    const currentUsage = await this.getCurrentResourceUsage(
      message.organizationId,
      message.resourceType,
    )

    // Calculate overage amount
    const planLimit = resourceLimit.includedQuantity
    const overageAmount = Math.max(0, currentUsage - planLimit)
    const overagePolicy = subscription.overagePolicyFixedResources || 'BLOCK'

    const requiresStripeReporting =
      overageAmount > 0 &&
      resourceLimit.overageStripePriceId !== null &&
      overagePolicy === 'CHARGE'

    return {
      organizationId: message.organizationId,
      resourceType: message.resourceType,
      currentUsage,
      planLimit,
      overageAmount,
      overagePolicy,
      requiresStripeReporting,
      billingPeriodStart: subscription.currentPeriodStartDate
        ? new Date(subscription.currentPeriodStartDate)
        : new Date(),
      billingPeriodEnd: subscription.currentPeriodEndDate
        ? new Date(subscription.currentPeriodEndDate)
        : new Date(),
      stripeCustomerId: subscription.stripeCustomerId,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      overageStripePriceId: resourceLimit.overageStripePriceId,
      subscriptionItemId: subscription.subscriptionItemId,
    }
  }

  /**
   * Get current resource usage for organization
   */
  async getCurrentResourceUsage(
    organizationId: string,
    resourceType: string,
  ): Promise<number> {
    let result: any[]

    // Query the actual resource counts from the application's primary database
    switch (resourceType) {
      case ResourceType.MEMBER:
        // Count active users in the organization
        result = await this.db
          .select({ count: count() })
          .from(users)
          .where(
            and(
              eq(users.organizationId, organizationId),
              eq(users.status, 'active'),
              isNull(users.deletedAt),
            ),
          )
        break
      case ResourceType.TEAM:
        // Count active teams in the organization
        result = await this.db
          .select({ count: count() })
          .from(teams)
          .where(
            and(
              eq(teams.organizationId, organizationId),
              isNull(teams.deletedAt),
            ),
          )
        break
      case ResourceType.CHECK:
        // Count active checks in the organization (through teams)
        result = await this.db
          .select({ count: count() })
          .from(checks)
          .innerJoin(teams, eq(checks.teamId, teams.id))
          .where(
            and(
              eq(teams.organizationId, organizationId),
              isNull(checks.deletedAt),
            ),
          )
        break
      case ResourceType.INTEGRATION:
        // Count active integrations in the organization (through teams)
        result = await this.db
          .select({ count: count() })
          .from(integrationSetting)
          .innerJoin(teams, eq(integrationSetting.teamId, teams.id))
          .where(
            and(
              eq(teams.organizationId, organizationId),
              isNull(integrationSetting.deletedAt),
            ),
          )
        break
      case ResourceType.STATUS_PAGE:
        // Status pages are in DynamoDB - count them properly
        return await this.countStatusPagesFromDynamoDB(organizationId)
      default:
        throw new Error(`Unsupported resource type: ${resourceType}`)
    }

    return Number(result[0]?.count || 0)
  }

  /**
   * Count status pages from DynamoDB for the organization
   */
  private async countStatusPagesFromDynamoDB(
    organizationId: string,
  ): Promise<number> {
    try {
      // Status pages table structure: organizationId as partition key
      const statusPagesTableName =
        process.env['STATUS_PAGES_TABLE_NAME'] || 'status-pages'

      const scanCommand = new ScanCommand({
        TableName: statusPagesTableName,
        FilterExpression:
          'organizationId = :orgId AND attribute_not_exists(deletedAt)',
        ExpressionAttributeValues: {
          ':orgId': { S: organizationId },
        },
        Select: 'COUNT',
      })

      const result = await this.dynamoClient.send(scanCommand)
      return result.Count || 0
    } catch (error) {
      console.error(
        `Failed to count status pages from DynamoDB for organization ${organizationId}:`,
        error,
      )

      // Return 0 on DynamoDB errors to prevent blocking billing operations
      // This maintains system availability during DynamoDB issues
      return 0
    }
  }

  /**
   * Create subscription usage record for overage tracking
   */
  async createSubscriptionUsageRecord(
    result: OverageCalculationResult,
    stripeUsageRecordId: string | null = null,
    error: Error | null = null,
  ): Promise<void> {
    if (!result.subscriptionItemId) {
      return
    }

    // Create SubscriptionUsage record for this overage calculation (DYNAMIC)
    const usageType = UsageTypeUtils.generateOverageUsageType(
      result.resourceType as ResourceType,
    )

    await this.db.insert(subscriptionUsage).values({
      organizationId: result.organizationId,
      id: `overage_${Date.now()}_${Math.random().toString(36).substring(7)}`,
      subscriptionItemId: result.subscriptionItemId,
      usageType,
      usageValue: result.overageAmount,
      usageTimestamp: new Date(), // Current timestamp for overage calculation
      billingCycleStartDate: result.billingPeriodStart,
      billingCycleEndDate: result.billingPeriodEnd,
      reportedToStripe: stripeUsageRecordId !== null,
      stripePriceId: result.overageStripePriceId,
      stripeUsageRecordId,
      isBillable:
        error === null &&
        result.overageAmount > 0 &&
        result.overagePolicy === 'CHARGE',
      notes: error
        ? error.message
        : `Resource overage: ${result.currentUsage} ${result.resourceType.toLowerCase()}s (limit: ${result.planLimit}), policy: ${result.overagePolicy}`,
    } as any)
  }

  /**
   * Helper function to create empty overage result
   */
  private createEmptyOverageResult(
    organizationId: string,
    resourceType: string,
  ): OverageCalculationResult {
    return {
      organizationId,
      resourceType,
      currentUsage: 0,
      planLimit: 0,
      overageAmount: 0,
      overagePolicy: 'BLOCK',
      requiresStripeReporting: false,
      billingPeriodStart: new Date(),
      billingPeriodEnd: new Date(),
      stripeCustomerId: null,
      stripeSubscriptionId: null,
      overageStripePriceId: null,
      subscriptionItemId: null,
    }
  }
}
