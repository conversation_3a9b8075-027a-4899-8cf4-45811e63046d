import { eq } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import * as schema from '@libs/database/lib/schema/schema'

export default class TeamData {
  private db: NodePgDatabase
  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
  }

  async getTeamById(teamId: string) {
    const teams = await this.db
      .select()
      .from(schema.teams)
      .where(eq(schema.teams.id, teamId))
      .execute()

    if (teams?.length) return teams[0]

    return null
  }
}
