import { model } from 'dynamoose'
import { customAlphabet } from 'nanoid'
import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'
import {
  IncidentEventInterface,
  IncidentEventSchema,
} from '@libs/database/lib/dynamo/incident-event.schema'

const generateId = (size?: number) => {
  return customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 12)(size)
}

const IncidentEvent = model(
  DYNAMO_DB_TABLE.INCIDENT_EVENTS,
  IncidentEventSchema,
  {
    create: false,
    waitForActive: false,
  },
)

export default class IncidentEventData {
  static async addIncidentEvent(
    body: Partial<IncidentEventInterface>,
  ): Promise<IncidentEventInterface> {
    const cleanedBody = JSON.parse(
      JSON.stringify(body, (_, value) => {
        if (value === null || value === undefined) {
          return undefined
        }
        return value
      }),
    )

    const createdItem = await IncidentEvent.create({
      ...cleanedBody,
      id: generateId(),
      createdAt: new Date(),
    })
    return createdItem as unknown as IncidentEventInterface
  }

  static async getIncidentEventById(id: string) {
    return IncidentEvent.get(id)
  }
}
