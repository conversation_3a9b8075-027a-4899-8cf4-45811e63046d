import { model } from 'dynamoose'
import { SortOrder } from 'dynamoose/dist/General'
import { customAlphabet } from 'nanoid'
import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'
import {
  WebhookLogItem,
  WebhookLogSchema,
} from '@libs/database/lib/dynamo/webhook-log.schema'

import { IPaginate } from '@backend/frameworks/database/interfaces/paginate.interface'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'

const generateId = () => {
  return customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 12)()
}

const WebhookLogModel = model<WebhookLogItem>(
  DYNAMO_DB_TABLE.WEBHOOK_EVENTS,
  WebhookLogSchema,
  {
    create: false,
    waitForActive: false,
    // update: true,
  },
)

export default class WebhookLogData {
  static async queryLogsByWebhookId(
    webhookId: string,
    paginateOption: PaginateOptionsDto,
  ): Promise<IPaginate<WebhookLogItem>> {
    try {
      const startAt = paginateOption.lastItemId
        ? await WebhookLogModel.get(paginateOption.lastItemId || '')
        : null

      const total = await WebhookLogModel.query('webhookId')
        .eq(webhookId)
        .count()
        .exec()
      if (!total || total.count === 0) {
        return {
          data: [],
          total: 0,
          totalPage: 0,
          page: 0,
          limit: 0,
        }
      }

      const query = await WebhookLogModel.query('webhookId')
        .eq(webhookId)
        .using('webhookId-createdAt-index')
        .sort('descending')

      if (startAt) {
        query.startAt(startAt)
      }
      if (paginateOption.limit) {
        query.limit(paginateOption.limit)
      }
      query.sort(SortOrder.descending)
      const data = await query.exec()
      return {
        data: data,
        total: total.count,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    } catch (error) {
      console.log(error)
      return {
        data: [],
        total: 0,
        totalPage: 0,
        page: 0,
        limit: 0,
      }
    }
  }

  static async addLog(body: Partial<WebhookLogItem>): Promise<WebhookLogItem> {
    const cleanedBody = JSON.parse(
      JSON.stringify(body, (_, value) => {
        if (value === null || value === undefined) {
          return undefined
        }
        return value
      }),
    )

    const createdItem = await WebhookLogModel.create({
      ...cleanedBody,
      id: generateId(),
    })
    return createdItem
  }
}
