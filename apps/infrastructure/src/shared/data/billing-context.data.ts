import { eq } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import { organizations, subscriptions } from '@libs/database/lib/schema/schema'
import { SubscriptionStatus } from '@libs/shared/constants/subscription'

export interface BillingContext {
  organizationId: string
  stripeCustomerId: string | null
  subscriptionStatus: SubscriptionStatus
  usageBillingEnabled: boolean
  gracePeriodExpiresAt: string | null
  entitlementStatus: boolean // Derived from subscription status and grace period
}

export default class BillingContextData {
  private db: NodePgDatabase

  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
  }

  /**
   * Fetch billing context for an organization
   */
  async fetchBillingContext(organizationId: string): Promise<BillingContext> {
    // Query organization subscription details with Drizzle
    const orgSubscriptionResults = await this.db
      .select({
        organizationId: organizations.id,
        stripeCustomerId: subscriptions.stripeCustomerId,
        subscriptionStatus: subscriptions.status,
        usageBillingEnabled: subscriptions.usageBillingEnabled,
        gracePeriodExpiresAt: subscriptions.gracePeriodEndsAt,
      })
      .from(organizations)
      .leftJoin(
        subscriptions,
        eq(organizations.id, subscriptions.organizationId),
      )
      .where(eq(organizations.id, organizationId))
      .limit(1)

    if (orgSubscriptionResults.length === 0) {
      return this.createDefaultBillingContext(organizationId)
    }

    const orgRecord = orgSubscriptionResults[0]

    if (!orgRecord) {
      return this.createDefaultBillingContext(organizationId)
    }

    // Determine entitlement status based on subscription status and grace period
    const isActiveSubscription =
      orgRecord.subscriptionStatus === SubscriptionStatus.ACTIVE
    const isInGracePeriod =
      orgRecord.subscriptionStatus === SubscriptionStatus.PAST_DUE &&
      orgRecord.gracePeriodExpiresAt &&
      new Date(orgRecord.gracePeriodExpiresAt) > new Date()

    const entitlementStatus = isActiveSubscription || isInGracePeriod

    return {
      organizationId,
      stripeCustomerId: orgRecord?.stripeCustomerId || null,
      subscriptionStatus:
        (orgRecord?.subscriptionStatus as SubscriptionStatus) ||
        SubscriptionStatus.CANCELED,
      usageBillingEnabled: orgRecord?.usageBillingEnabled ?? true,
      gracePeriodExpiresAt:
        orgRecord?.gracePeriodExpiresAt?.toISOString() || null,
      entitlementStatus: entitlementStatus ?? false,
    }
  }

  /**
   * Create default billing context for organizations without subscriptions
   */
  createDefaultBillingContext(organizationId: string): BillingContext {
    return {
      organizationId,
      stripeCustomerId: null,
      subscriptionStatus: SubscriptionStatus.CANCELED,
      usageBillingEnabled: false,
      gracePeriodExpiresAt: null,
      entitlementStatus: false,
    }
  }
}
