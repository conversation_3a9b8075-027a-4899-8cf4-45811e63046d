import { eq } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import * as schema from '@libs/database/lib/schema/schema'

export default class CustomIncidentAndCheckData {
  private db: NodePgDatabase<typeof schema>

  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient, { schema })
  }

  async getCustomIncidentById(customIncidentId: string) {
    const customIncident = await this.db.query.customIncidentSchema.findFirst({
      where: eq(schema.customIncidentSchema.id, customIncidentId),
      with: {
        escalation: {
          with: {
            escalationSteps: {
              with: {
                escalationContacts: true,
                severity: true,
              },
            },
          },
        },
      },
    })

    return customIncident
  }

  async getCheckById(checkId: string) {
    const check = await this.db.query.checks.findFirst({
      where: eq(schema.checks.id, checkId),
      with: {
        escalation: {
          with: {
            escalationSteps: {
              with: {
                escalationContacts: true,
                severity: true,
              },
            },
          },
        },
      },
    })
    return check
  }

  async updateCustomIncident(id: string, data: Partial<any>) {
    return this.db
      .update(schema.customIncidentSchema)
      .set(data)
      .where(eq(schema.customIncidentSchema.id, id))
  }

  async updateCheck(id: string, data: Partial<any>) {
    return this.db
      .update(schema.checks)
      .set(data)
      .where(eq(schema.checks.id, id))
  }

  // async getEscalationPolicy(
  //   escalationId: string,
  //   incidentId: string,
  // ): Promise<EscalationPolicy> {
  //   const escalation = await this.db.query.escalations.findFirst({
  //     where: (escalations, { eq }) => eq(escalations.id, escalationId),
  //     with: {
  //       escalationSteps: {
  //         with: {
  //           escalationContacts: true,
  //           severity: true,
  //         },
  //       },
  //     },
  //   })

  //   if (!escalation) {
  //     throw new Error('Escalation policy not found')
  //   }

  //   return await this.mapEscalationPolicyToDynamo(escalation, incidentId)
  // }

  // private async mapEscalationPolicyToDynamo(
  //   escalation: any,
  //   incidentId: string,
  // ): Promise<EscalationPolicy> {
  //   const { teamId } = escalation

  //   return {
  //     repeat: [...new Array(escalation.repeatCount + 1)].map((_, i) => i + 1),
  //     repeatDelay: escalation.repeatDelay,
  //     incidentId,
  //     escalationSteps: escalation.escalationSteps.map(
  //       (step: {
  //         stepDelay: any
  //         severityId: any
  //         escalationContacts: any
  //         severity: { alerts: any }
  //       }) => ({
  //         stepDelay: step.stepDelay,
  //         severityId: step.severityId,
  //         incidentId,
  //         teamId,
  //         contacts: (step.escalationContacts || []).map(
  //           (contact: { contactType: any; contactId: any }) => ({
  //             contactType: contact.contactType,
  //             contactId: contact.contactId,
  //             alerts: step.severity.alerts,
  //           }),
  //         ),
  //       }),
  //     ),
  //   }
  // }
}
