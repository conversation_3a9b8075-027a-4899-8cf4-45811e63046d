import { eq, and } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import {
  subscriptions,
  subscriptionItems,
  subscriptionUsage,
} from '@libs/database/lib/schema/schema'

export interface StripeDirectReportingMessage {
  organizationId: string
  stripeCustomerId: string
  stripePriceId: string
  usageType: string // Support for any usage type (transactional or overage)
  usageValue: number
  eventTimestamp: string
  metadata?: {
    checkId?: string
    incidentId?: string
    notificationId?: string
  }
  isBillable?: boolean
  notificationSuccess?: boolean
  errorMessage?: string
}

export interface SubscriptionInfo {
  subscriptionItemId: string
  currentPeriodStartDate: string | null
  currentPeriodEndDate: string | null
}

export default class StripeUsageReportingData {
  private db: NodePgDatabase

  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
  }

  /**
   * Find active subscription for organization
   */
  async findActiveSubscription(
    organizationId: string,
  ): Promise<SubscriptionInfo | null> {
    const subscriptionResults = await this.db
      .select({
        subscriptionItemId: subscriptionItems.id,
        currentPeriodStartDate: subscriptions.currentPeriodStartDate,
        currentPeriodEndDate: subscriptions.currentPeriodEndDate,
      })
      .from(subscriptions)
      .innerJoin(
        subscriptionItems,
        and(
          eq(subscriptions.id, subscriptionItems.subscriptionId),
          eq(subscriptionItems.itemType, 'BASE_PLAN'),
          eq(subscriptionItems.status, 'active'),
        ),
      )
      .where(
        and(
          eq(subscriptions.organizationId, organizationId),
          eq(subscriptions.status, 'active'),
        ),
      )
      .limit(1)

    if (subscriptionResults.length === 0) {
      return null
    }

    const subscription = subscriptionResults[0]
    if (!subscription) {
      return null
    }

    return {
      subscriptionItemId: subscription.subscriptionItemId,
      currentPeriodStartDate: subscription.currentPeriodStartDate,
      currentPeriodEndDate: subscription.currentPeriodEndDate,
    }
  }

  /**
   * Create subscription usage record for Stripe direct reporting
   */
  async createSubscriptionUsageRecord(
    message: StripeDirectReportingMessage,
    subscriptionInfo: SubscriptionInfo,
    stripeUsageRecordId: string | null = null,
    error: Error | null = null,
  ): Promise<void> {
    // Create SubscriptionUsage record following PRD requirements
    await this.db.insert(subscriptionUsage).values({
      id: `usage_${Date.now()}_${Math.random().toString(36).substring(7)}`,
      organizationId: message.organizationId,
      subscriptionItemId: subscriptionInfo.subscriptionItemId,
      usageType: message.usageType,
      usageValue: message.usageValue,
      usageTimestamp: new Date(message.eventTimestamp),
      billingCycleStartDate: subscriptionInfo.currentPeriodStartDate ?? null,
      billingCycleEndDate: subscriptionInfo.currentPeriodEndDate ?? null,
      reportedToStripe: stripeUsageRecordId !== null,
      stripePriceId: message.stripePriceId,
      stripeUsageRecordId: stripeUsageRecordId,
      isBillable: error === null,
      notes: error
        ? error.message
        : `Transactional usage: ${message.usageType}`,
    })
  }

  /**
   * Create usage tracking record for event enrichment
   */
  async createUsageTrackingRecord(
    organizationId: string,
    channelType: string,
    eventTimestamp: string,
    stripePriceId: string,
    isBillable: boolean,
    blockReason?: string,
  ): Promise<void> {
    const usageRecord = {
      id: `usage_${Date.now()}_${Math.random().toString(36).substring(7)}`,
      organizationId: organizationId,
      subscriptionItemId: null, // Will be populated later if needed
      usageType: channelType.toUpperCase(),
      usageValue: 1,
      usageTimestamp: new Date(eventTimestamp),
      billingCycleStartDate: new Date().toISOString().split('T')[0] as string, // Will be updated when billing cycle is determined
      billingCycleEndDate: new Date().toISOString().split('T')[0] as string, // Will be updated when billing cycle is determined
      reportedToStripe: false,
      stripePriceId: stripePriceId || null,
      stripeUsageRecordId: null,
      isBillable: isBillable,
      notes: isBillable
        ? 'Pending Stripe reporting via queue'
        : `Not billable: ${blockReason || 'subscription not entitled'}`,
      createdBy: 'event-enrichment-lambda',
      updatedBy: 'event-enrichment-lambda',
    }

    // Insert usage record using Drizzle
    await this.db.insert(subscriptionUsage).values(usageRecord)
  }
}
