import { eq, and, desc, gte, lte } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import { subscriptionUsage } from '@libs/database/lib/schema/schema'

type DrizzleSubscriptionUsage = typeof subscriptionUsage.$inferSelect

export interface SubscriptionUsageRecord {
  id: string
  organizationId: string
  subscriptionItemId?: string | null
  usageType: string
  usageValue: number
  usageTimestamp: Date
  billingCycleStartDate?: Date | null
  billingCycleEndDate?: Date | null
  reportedToStripe?: boolean | null
  stripePriceId?: string | null
  stripeUsageRecordId?: string | null
  isBillable?: boolean | null
  notes?: string | null
  createdAt: Date
  updatedAt: Date
  createdBy?: string | null
  updatedBy?: string | null
}

// Helper function to convert Drizzle results to our interface
function convertToSubscriptionUsageRecord(
  dbRecord: DrizzleSubscriptionUsage,
): SubscriptionUsageRecord {
  return {
    id: dbRecord.id,
    organizationId: dbRecord.organizationId,
    subscriptionItemId: dbRecord.subscriptionItemId,
    usageType: dbRecord.usageType,
    usageValue: dbRecord.usageValue,
    usageTimestamp: new Date(dbRecord.usageTimestamp),
    billingCycleStartDate: dbRecord.billingCycleStartDate
      ? new Date(dbRecord.billingCycleStartDate)
      : null,
    billingCycleEndDate: dbRecord.billingCycleEndDate
      ? new Date(dbRecord.billingCycleEndDate)
      : null,
    reportedToStripe: dbRecord.reportedToStripe,
    stripePriceId: dbRecord.stripePriceId,
    stripeUsageRecordId: dbRecord.stripeUsageRecordId,
    isBillable: dbRecord.isBillable,
    notes: dbRecord.notes,
    createdAt: new Date(dbRecord.createdAt),
    updatedAt: new Date(dbRecord.updatedAt),
    createdBy: dbRecord.createdBy,
    updatedBy: dbRecord.updatedBy,
  }
}

export default class SubscriptionUsageData {
  private db: NodePgDatabase

  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
  }

  /**
   * Create a new subscription usage record
   */
  async createUsageRecord(
    usageData: Omit<SubscriptionUsageRecord, 'createdAt' | 'updatedAt'>,
  ): Promise<void> {
    // Convert our interface data to match the database schema types
    const insertData: typeof subscriptionUsage.$inferInsert = {
      id: usageData.id,
      organizationId: usageData.organizationId,
      usageType: usageData.usageType,
      usageValue: usageData.usageValue,
      usageTimestamp: usageData.usageTimestamp, // timestamp type accepts Date
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      subscriptionItemId: null,
      billingCycleStartDate: null,
      billingCycleEndDate: null,
      reportedToStripe: null,
      stripePriceId: null,
      stripeUsageRecordId: null,
      isBillable: null,
      notes: null,
      createdBy: null,
      updatedBy: null,
    }

    if (usageData.subscriptionItemId !== undefined) {
      insertData.subscriptionItemId = usageData.subscriptionItemId
    }
    if (usageData.billingCycleStartDate) {
      insertData.billingCycleStartDate = usageData.billingCycleStartDate
        .toISOString()
        .split('T')[0] as string
    }
    if (usageData.billingCycleEndDate) {
      insertData.billingCycleEndDate = usageData.billingCycleEndDate
        .toISOString()
        .split('T')[0] as string
    }
    if (usageData.reportedToStripe !== undefined) {
      insertData.reportedToStripe = usageData.reportedToStripe
    }
    if (usageData.stripePriceId !== undefined) {
      insertData.stripePriceId = usageData.stripePriceId
    }
    if (usageData.stripeUsageRecordId !== undefined) {
      insertData.stripeUsageRecordId = usageData.stripeUsageRecordId
    }
    if (usageData.isBillable !== undefined) {
      insertData.isBillable = usageData.isBillable
    }
    if (usageData.notes !== undefined) {
      insertData.notes = usageData.notes
    }
    if (usageData.createdBy !== undefined) {
      insertData.createdBy = usageData.createdBy
    }
    if (usageData.updatedBy !== undefined) {
      insertData.updatedBy = usageData.updatedBy
    }

    await this.db.insert(subscriptionUsage).values(insertData)
  }

  /**
   * Get subscription usage by ID
   */
  async getSubscriptionUsageById(
    id: string,
  ): Promise<SubscriptionUsageRecord | null> {
    const results = await this.db
      .select()
      .from(subscriptionUsage)
      .where(eq(subscriptionUsage.id, id))
      .limit(1)

    if (results.length === 0) {
      return null
    }

    const dbRecord = results[0]
    if (!dbRecord) {
      return null
    }

    return convertToSubscriptionUsageRecord(dbRecord)
  }

  /**
   * Get subscription usage records by organization ID
   */
  async getSubscriptionUsageByOrganizationId(
    organizationId: string,
    limit = 100,
  ): Promise<SubscriptionUsageRecord[]> {
    const results = await this.db
      .select()
      .from(subscriptionUsage)
      .where(eq(subscriptionUsage.organizationId, organizationId))
      .orderBy(desc(subscriptionUsage.usageTimestamp))
      .limit(limit)

    return results.map(convertToSubscriptionUsageRecord)
  }

  /**
   * Get usage records for a specific billing period
   */
  async getUsageForBillingPeriod(
    organizationId: string,
    periodStart: Date,
    periodEnd: Date,
  ): Promise<SubscriptionUsageRecord[]> {
    const results = await this.db
      .select()
      .from(subscriptionUsage)
      .where(
        and(
          eq(subscriptionUsage.organizationId, organizationId),
          gte(subscriptionUsage.usageTimestamp, periodStart),
          lte(subscriptionUsage.usageTimestamp, periodEnd),
        ),
      )
      .orderBy(desc(subscriptionUsage.usageTimestamp))

    return results.map(convertToSubscriptionUsageRecord)
  }

  /**
   * Get usage records by type for organization
   */
  async getUsageByType(
    organizationId: string,
    usageType: string,
    limit = 50,
  ): Promise<SubscriptionUsageRecord[]> {
    const results = await this.db
      .select()
      .from(subscriptionUsage)
      .where(
        and(
          eq(subscriptionUsage.organizationId, organizationId),
          eq(subscriptionUsage.usageType, usageType),
        ),
      )
      .orderBy(desc(subscriptionUsage.usageTimestamp))
      .limit(limit)

    return results.map(convertToSubscriptionUsageRecord)
  }

  /**
   * Get billable usage records that haven't been reported to Stripe
   */
  async getUnreportedBillableUsage(
    organizationId: string,
  ): Promise<SubscriptionUsageRecord[]> {
    const results = await this.db
      .select()
      .from(subscriptionUsage)
      .where(
        and(
          eq(subscriptionUsage.organizationId, organizationId),
          eq(subscriptionUsage.isBillable, true),
          eq(subscriptionUsage.reportedToStripe, false),
        ),
      )
      .orderBy(desc(subscriptionUsage.usageTimestamp))

    return results.map(convertToSubscriptionUsageRecord)
  }

  /**
   * Update subscription usage record
   */
  async updateSubscriptionUsage(
    id: string,
    updates: Partial<SubscriptionUsageRecord>,
  ): Promise<void> {
    // Build update data with proper type handling
    const updateData: Partial<DrizzleSubscriptionUsage> = {
      updatedAt: new Date().toISOString(),
    }

    // Handle each field explicitly to avoid index signature issues
    if (updates.organizationId !== undefined) {
      updateData.organizationId = updates.organizationId
    }
    if (updates.subscriptionItemId !== undefined) {
      updateData.subscriptionItemId = updates.subscriptionItemId
    }
    if (updates.usageType !== undefined) {
      updateData.usageType = updates.usageType
    }
    if (updates.usageValue !== undefined) {
      updateData.usageValue = updates.usageValue
    }
    if (updates.usageTimestamp !== undefined) {
      updateData.usageTimestamp = updates.usageTimestamp // timestamp accepts Date
    }
    if ('billingCycleStartDate' in updates) {
      if (updates.billingCycleStartDate) {
        updateData.billingCycleStartDate = updates.billingCycleStartDate
          .toISOString()
          .split('T')[0] as string
      } else {
        updateData.billingCycleStartDate = null // explicitly set to null to clear
      }
    }
    if ('billingCycleEndDate' in updates) {
      if (updates.billingCycleEndDate) {
        updateData.billingCycleEndDate = updates.billingCycleEndDate
          .toISOString()
          .split('T')[0] as string
      } else {
        updateData.billingCycleEndDate = null // explicitly set to null to clear
      }
    }
    if (updates.reportedToStripe !== undefined) {
      updateData.reportedToStripe = updates.reportedToStripe
    }
    if (updates.stripePriceId !== undefined) {
      updateData.stripePriceId = updates.stripePriceId
    }
    if (updates.stripeUsageRecordId !== undefined) {
      updateData.stripeUsageRecordId = updates.stripeUsageRecordId
    }
    if (updates.isBillable !== undefined) {
      updateData.isBillable = updates.isBillable
    }
    if (updates.notes !== undefined) {
      updateData.notes = updates.notes
    }
    if (updates.createdAt !== undefined) {
      updateData.createdAt = updates.createdAt.toISOString()
    }
    if (updates.createdBy !== undefined) {
      updateData.createdBy = updates.createdBy
    }
    if (updates.updatedBy !== undefined) {
      updateData.updatedBy = updates.updatedBy
    }

    await this.db
      .update(subscriptionUsage)
      .set(updateData)
      .where(eq(subscriptionUsage.id, id))
  }

  /**
   * Mark usage as reported to Stripe
   */
  async markAsReportedToStripe(
    id: string,
    stripeUsageRecordId: string,
  ): Promise<void> {
    await this.db
      .update(subscriptionUsage)
      .set({
        reportedToStripe: true,
        stripeUsageRecordId,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(subscriptionUsage.id, id))
  }
}
