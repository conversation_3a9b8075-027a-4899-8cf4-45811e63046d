import { eq, inArray, sql } from 'drizzle-orm'
import {
  drizzle,
  NodePgClient,
  NodePgDatabase,
} from 'drizzle-orm/node-postgres'
import { pgTable, varchar } from 'drizzle-orm/pg-core'

const userSchema = pgTable('users', {
  id: varchar('id').primaryKey().notNull(),
  email: varchar('email').notNull(),
  firstName: varchar('first_name').notNull(),
  lastName: varchar('last_name').notNull(),
  firebaseId: varchar('firebase_id').notNull(),
  phoneNumber: varchar('phone_number').notNull(),
})

const userTeamRoleSchema = pgTable('users_teams_roles', {
  teamId: varchar('team_id').notNull(),
  userId: varchar('user_id').notNull(),
  roleId: varchar('role_id').notNull(),
})

const userPlivoCredentialSchema = pgTable('user_plivo_credential', {
  userId: varchar('user_id').primaryKey().notNull(), // References users.id
  username: varchar('username').notNull(), // Assuming this is the Plivo endpoint
})

export default class UserData {
  private db: NodePgDatabase
  constructor(pgClient: NodePgClient) {
    this.db = drizzle(pgClient)
  }

  async getUserById(userId: string) {
    const users = await this.db
      .select({
        id: userSchema.id,
        email: userSchema.email,
        firstName: userSchema.firstName,
        lastName: userSchema.lastName,
        firebaseId: userSchema.firebaseId,
        phoneNumber: userSchema.phoneNumber,
        plivoEndpoint: userPlivoCredentialSchema.username,
      })
      .from(userSchema)
      .leftJoin(
        userPlivoCredentialSchema,
        eq(userSchema.id, userPlivoCredentialSchema.userId),
      ) // Join with plivo credentials
      .where(eq(userSchema.id, userId))
      .execute()

    if (users?.length) return users[0]

    return null
  }

  async getOnCallUsers(teamId: string) {
    const query = sql`
    SELECT on_call_schedulers_users.users_id
    FROM on_call_schedulers
    INNER JOIN on_call_schedulers_users
    ON on_call_schedulers.id = on_call_schedulers_users.on_call_schedulers_id
    WHERE on_call_schedulers.team_id = ${teamId}
    AND on_call_schedulers.deleted_at IS NULL
    AND (
      (on_call_schedulers.type = 'recurring'
        AND contains_timestamp(on_call_schedulers.recurring_pattern::TEXT, CURRENT_TIMESTAMP::TIMESTAMP)
        AND (
          on_call_schedulers.all_day IS TRUE
          OR CURRENT_TIMESTAMP BETWEEN
            TO_TIMESTAMP(CONCAT(CURRENT_TIMESTAMP::DATE, ' ', start_date::TIME), 'YYYY-MM-DD HH24:MI:SS')
            AND (on_call_schedulers.start_date::DATE + (on_call_schedulers.end_date - on_call_schedulers.start_date))::TIMESTAMP
        )
      )
      OR
      (on_call_schedulers.type = 'date_range'
        AND on_call_schedulers.start_date <= CURRENT_TIMESTAMP::TIMESTAMP
        AND on_call_schedulers.end_date >= CURRENT_TIMESTAMP::TIMESTAMP
      )
    )
    `
    const data = await this.db.execute(query)

    const onCallUsers = data?.rows.map((item) => item.users_id)

    if (onCallUsers?.length) {
      const users = await this.db
        .select({
          id: userSchema.id,
          email: userSchema.email,
          firstName: userSchema.firstName,
          lastName: userSchema.lastName,
          firebaseId: userSchema.firebaseId,
          phoneNumber: userSchema.phoneNumber,
          plivoEndpoint: userPlivoCredentialSchema.username,
        })
        .from(userSchema)
        .leftJoin(
          userPlivoCredentialSchema,
          eq(userSchema.id, userPlivoCredentialSchema.userId),
        ) // Join with plivo credentials
        .where(inArray(userSchema.id, onCallUsers))
        .execute()

      return users
    }

    return null
  }

  async getUsersByTeamId(teamId: string) {
    const usersTeam = await this.db
      .select()
      .from(userTeamRoleSchema)
      .where(eq(userTeamRoleSchema.teamId, teamId))
      .execute()

    const users = await this.db
      .select({
        id: userSchema.id,
        email: userSchema.email,
        firstName: userSchema.firstName,
        lastName: userSchema.lastName,
        firebaseId: userSchema.firebaseId,
        phoneNumber: userSchema.phoneNumber,
        plivoEndpoint: userPlivoCredentialSchema.username,
      })
      .from(userSchema)
      .leftJoin(
        userPlivoCredentialSchema,
        eq(userSchema.id, userPlivoCredentialSchema.userId),
      ) // Join with plivo credentials
      .where(
        inArray(
          userSchema.id,
          usersTeam.map((item) => item.userId),
        ),
      )
      .execute()

    if (users?.length) return users

    return null
  }

  async getUserByEmail(email: string) {
    const users = await this.db
      .select()
      .from(userSchema)
      .where(eq(userSchema.email, email))
      .execute()

    if (users?.length) return users[0]

    return null
  }
}
