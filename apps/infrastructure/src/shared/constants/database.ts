export const DYNA<PERSON>_DB_TABLE = {
  INCIDENTS: 'development-incidents',
  INCIDENT_EVENTS: 'development-incident-events',
  WEBHOOK_LOGS: 'development-webhook-events',
}

export const DYNAMO_DB_TABLE_STREAM = {
  INCIDENTS: `arn:aws:dynamodb:us-east-1:851725359289:table/development-incidents/stream/2025-01-06T16:34:54.467`,
  // INCIDENTS: `arn:aws:dynamodb:us-east-1:000000000000:table/development-incidents/stream/2025-04-17T17:59:35.429`,
}

export const DATABASE_CONFIG = {
  dev: {
    port: 5432,
    host: process.env['DB_HOST'],
    user: process.env['DB_USERNAME'],
    password: process.env['DB_PASSWORD'],
    database: process.env['DB_DATABASE'],
  },
}
