import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as sqs from 'aws-cdk-lib/aws-sqs'
import * as sfn from 'aws-cdk-lib/aws-stepfunctions'
import { CfnPipe } from 'aws-cdk-lib/aws-pipes'
import {
  IPrincipal,
  PolicyDocument,
  PolicyStatement,
  Role,
  ServicePrincipal,
} from 'aws-cdk-lib/aws-iam'
import { DYNAMO_DB_TABLE_STREAM } from '@infra/shared/constants/database'
import { createEscalationEventBridgePipe } from '@infra/domains/incidents/infra/constructs'
import { createIncidentResolvedEscalationEventBridgePipe } from '@infra/domains/incidents/infra/constructs' // Assuming resolution constructs are now here
import { StackProps } from '@infra/app'
import { Environment } from '@infra/shared/environment'

import { IncidentEscalationStateMachineConstruct } from '../constructs/incident-escalation-statemachine-construct'
import { CustomIncidentEscalationStateMachineConstruct } from '../constructs/custom-incident-escalation-statemachine-construct'

export type IncidentEscalationStackProps = StackProps

export class IncidentEscalationStack extends cdk.Stack {
  constructor(
    scope: Construct,
    id: string,
    incidentIntegrationCallbackAPI: string,
    props?: IncidentEscalationStackProps,
  ) {
    super(scope, id, props)
    const { environment } = props || {}

    const notificationQueue = new sqs.Queue(this, 'NotificationQueue')

    const incidentStateMachineConstruct =
      new IncidentEscalationStateMachineConstruct(
        this,
        'IncidentEscalationStateMachine',
        { notificationQueue },
      )
    const customIncidentStateMachineConstruct =
      new CustomIncidentEscalationStateMachineConstruct(
        this,
        'CustomIncidentEscalationStateMachine',
        { notificationQueue },
      )

    const pipeRole = this.createPipeRole()

    this.createDynamoTriggerStepFunctionEventBridge(
      incidentStateMachineConstruct.stateMachine as sfn.StateMachine,
      pipeRole,
    )

    this.createCustomIncidentDynamoTriggerStepFunctionEventBridge(
      customIncidentStateMachineConstruct.stateMachine as sfn.StateMachine,
      pipeRole,
    )

    createEscalationEventBridgePipe(
      this,
      notificationQueue,
      environment || Environment.NonProd,
      incidentIntegrationCallbackAPI || '',
    )

    createIncidentResolvedEscalationEventBridgePipe(
      this,
      environment || Environment.NonProd,
    )
  }

  createDynamoTriggerStepFunctionEventBridge(
    stepFunction: sfn.StateMachine,
    pipeRole: Role,
  ) {
    new CfnPipe(this, 'dynamodb-trigger-stepfunctions', {
      name: 'dynamodb-trigger-stepfunctions',
      source: DYNAMO_DB_TABLE_STREAM.INCIDENTS,
      sourceParameters: {
        dynamoDbStreamParameters: {
          batchSize: 1,
          maximumBatchingWindowInSeconds: 0,
          maximumRetryAttempts: 5,
          startingPosition: 'LATEST',
        },
        filterCriteria: {
          filters: [
            {
              pattern: JSON.stringify({
                eventName: ['INSERT'],
                dynamodb: {
                  NewImage: {
                    status: {
                      S: [{ 'anything-but': 'RESOLVED' }],
                    },
                    customIncidentId: { S: [{ exists: false }] },
                  },
                },
              }),
            },
          ],
        },
      },
      target: stepFunction.stateMachineArn,
      roleArn: pipeRole.roleArn,
      targetParameters: {
        stepFunctionStateMachineParameters: {
          invocationType: 'FIRE_AND_FORGET',
        },
        inputTemplate: '<$.dynamodb.NewImage.escalationPolicy.S>',
      },
    })
  }

  createCustomIncidentDynamoTriggerStepFunctionEventBridge(
    stepFunction: sfn.StateMachine,
    pipeRole: Role,
  ) {
    new CfnPipe(this, 'custom-incident-dynamodb-trigger-stepfunctions', {
      name: 'custom-incident-dynamodb-trigger-stepfunctions',
      source: DYNAMO_DB_TABLE_STREAM.INCIDENTS,
      sourceParameters: {
        dynamoDbStreamParameters: {
          batchSize: 1,
          maximumBatchingWindowInSeconds: 0,
          maximumRetryAttempts: 5,
          startingPosition: 'LATEST',
        },
        filterCriteria: {
          filters: [
            {
              pattern: JSON.stringify({
                eventName: ['INSERT'],
                dynamodb: {
                  NewImage: {
                    status: {
                      S: [{ 'anything-but': 'RESOLVED' }],
                    },
                    customIncidentId: { S: [{ exists: true }] },
                  },
                },
              }),
            },
          ],
        },
      },
      target: stepFunction.stateMachineArn,
      roleArn: pipeRole.roleArn,
      targetParameters: {
        stepFunctionStateMachineParameters: {
          invocationType: 'FIRE_AND_FORGET',
        },
        inputTemplate: '<$.dynamodb.NewImage.escalationPolicy.S>',
      },
    })
  }

  createPipeRole() {
    return new Role(this, 'trigger-stepfunction-from-dynamodb', {
      assumedBy: new ServicePrincipal('pipes.amazonaws.com') as IPrincipal,
      inlinePolicies: {
        dynamodb: new PolicyDocument({
          statements: [
            new PolicyStatement({
              actions: [
                'dynamodb:DescribeStream',
                'dynamodb:GetRecords',
                'dynamodb:GetShardIterator',
                'dynamodb:ListStreams',
              ],
              resources: ['*'],
            }),
          ],
        }),
        stepfunctions: new PolicyDocument({
          statements: [
            new PolicyStatement({
              actions: ['states:StartExecution'],
              resources: ['*'],
            }),
          ],
        }),
      },
    })
  }
}
