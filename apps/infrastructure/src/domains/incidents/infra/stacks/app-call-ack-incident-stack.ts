import { Stack, StackProps, Duration } from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as lambda from 'aws-cdk-lib/aws-lambda'
import * as iam from 'aws-cdk-lib/aws-iam' // Added IAM import
import * as logs from 'aws-cdk-lib/aws-logs'
import * as apigw from 'aws-cdk-lib/aws-apigateway'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'

import * as path from 'path'

export class AppCallAckIncidentStack extends Stack {
  public readonly callAckIncidentLambda: lambda.IFunction
  public readonly apiGateway: apigw.RestApi

  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id, props)

    const dynamoDbPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: ['dynamodb:UpdateItem', 'dynamodb:PutItem'],
      resources: ['*'], // Consider scoping down to specific table ARNs
    })

    const callAckIncidentConstruct = new StandardLambdaConstruct(
      this,
      'CallAckIncidentLambda',
      {
        entry: path.join(
          __dirname,
          '../../lambda/handlers/app-call-ack-incident-handler.ts',
        ),
        runtime: lambda.Runtime.NODEJS_18_X,
        memorySize: 128,
        timeout: Duration.seconds(30),
        environment: {
          NODE_ENV: 'prod',
          AWS_SDK_JS_LOG_LEVEL: 'error', // Suppress AWS SDK debug logs
        },
        logRetention: logs.RetentionDays.ONE_WEEK,
        functionName: 'CallAckIncidentLambda', // Explicitly set function name
        additionalPolicies: [dynamoDbPolicy], // Added DynamoDB policy
      },
    )

    this.callAckIncidentLambda = callAckIncidentConstruct.lambdaFunction

    this.apiGateway = new apigw.RestApi(this, 'AppCallAckIncidentApi', {
      restApiName: 'App Call Ack-Incident Api',
      description: 'API Gateway for App Call Ack Incident Lambda',
      deployOptions: {
        stageName: 'prod',
      },
    })

    const incidentResource = this.apiGateway.root.addResource(
      'app-call-ack-incident',
    )
    incidentResource.addMethod(
      'POST',
      new apigw.LambdaIntegration(this.callAckIncidentLambda),
    )
  }
}
