/* eslint-disable @typescript-eslint/no-non-null-assertion */
import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { Cors, LambdaIntegration, RestApi } from 'aws-cdk-lib/aws-apigateway'
import * as lambda from 'aws-cdk-lib/aws-lambda' // Added for IFunction type
import * as iam from 'aws-cdk-lib/aws-iam' // Added for PolicyStatement
import * as logs from 'aws-cdk-lib/aws-logs' // Added for RetentionDays
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct' // Added StandardLambdaConstruct import
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct' // Added import
import { StackProps } from '@infra/app'

import path from 'path'

export class IncidentIntegrationCallbackStack extends cdk.Stack {
  private readonly secretProvider: SecretProvider

  public incidentIntegrationCallbackLambda: lambda.IFunction
  public readonly incidentIntegrationCallbackAPI: string

  constructor(scope: Construct, id: string, props: StackProps) {
    super(scope, id, props)

    // Initialize SecretProvider first
    // Assuming props.environment exists and is compatible or needs casting
    this.secretProvider = new SecretProvider(
      this,
      'IntegrationCallbackSecrets',
      {
        environment: props.environment,
      },
    )

    // Now create the lambda that might depend on secretProvider implicitly via the method
    this.incidentIntegrationCallbackLambda =
      this._createIncidentIntegrationCallbackLambda()

    // API Gateway
    const api = new RestApi(this, 'IncidentIntegrationCallbackAPI', {
      restApiName: 'IncidentIntegrationCallbackAPI',
      defaultCorsPreflightOptions: {
        allowOrigins: Cors.ALL_ORIGINS,
        allowMethods: Cors.ALL_METHODS,
      },
    })

    const incidentCallback = api.root
      .addResource('{provider}')
      .addResource('events')

    const incidentCallbackIntegration = new LambdaIntegration(
      this.incidentIntegrationCallbackLambda,
    )

    incidentCallback.addMethod('POST', incidentCallbackIntegration)

    this.incidentIntegrationCallbackAPI = api.url

    new cdk.CfnOutput(this, 'ApiUrl', {
      value: api.url, // This outputs the URL of the API Gateway
    })
  }

  private _createIncidentIntegrationCallbackLambda(): lambda.IFunction {
    // Policies are now defined here to be passed to StandardLambdaConstruct
    const dynamoDbPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'dynamodb:GetItem',
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:Query',
        'dynamodb:Scan',
        'dynamodb:CreateTable',
      ],
      resources: ['*'], // Consider scoping down
    })

    const stepFunctionsPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: ['states:StopExecution'],
      resources: ['*'], // Consider scoping down
    })

    // Note: Basic CloudWatch Logs permissions (CreateLogGroup, CreateLogStream, PutLogEvents)
    // are typically handled by StandardLambdaConstruct/NodejsFunction by default.
    // If specific log policies were needed beyond the basics, they would be added here.

    const incidentCallbackConstruct = new StandardLambdaConstruct(
      this,
      'IncidentCallbackHandler', // Using a more descriptive ID for the construct
      {
        entry: path.resolve(
          __dirname,
          '../../lambda/handlers/integration-callback-handler.ts',
        ),
        handler: 'handler',
        environment: {
          // Common vars from SecretProvider
          NODE_ENV: this.secretProvider.nodeEnv,
          LOG_LEVEL: this.secretProvider.appLogLevel,

          // Slack vars from SecretProvider
          SLACK_BOT_TOKEN: this.secretProvider.slackBotToken,
          SLACK_SIGNING_SECRET: this.secretProvider.slackSigningSecret,

          // DB vars from SecretProvider
          DB_HOST: this.secretProvider.dbHost,
          DB_USERNAME: this.secretProvider.dbUsername,
          DB_PASSWORD: this.secretProvider.dbPassword,
          DB_NAME: this.secretProvider.dbName, // Mapped to dbName
        },
        additionalPolicies: [dynamoDbPolicy, stepFunctionsPolicy],
        logRetention: logs.RetentionDays.ONE_WEEK, // Set retention as previously configured
      },
    )

    // Grant secret read permissions
    this.secretProvider.grantReadPermissionsTo(
      incidentCallbackConstruct.lambdaFunction,
    )

    return incidentCallbackConstruct.lambdaFunction // Return the underlying function
  }
}
