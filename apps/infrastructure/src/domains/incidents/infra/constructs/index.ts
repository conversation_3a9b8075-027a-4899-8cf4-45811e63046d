import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb'
import * as sqs from 'aws-cdk-lib/aws-sqs'
import { CfnPipe } from 'aws-cdk-lib/aws-pipes'
import { EventBus, Rule } from 'aws-cdk-lib/aws-events'
import * as events_targets from 'aws-cdk-lib/aws-events-targets'
import { LogGroup } from 'aws-cdk-lib/aws-logs'
import {
  Role,
  ServicePrincipal,
  PolicyStatement,
  PolicyDocument,
} from 'aws-cdk-lib/aws-iam'
import {
  DYNAMO_DB_TABLE_STREAM,
  DYNAMO_DB_TABLE,
} from '@infra/shared/constants/database'
import { StandardEventBusConstruct } from '@infra/shared/constructs/standard-eventbus.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'
import { Environment } from '@infra/shared/environment'

import { createLambdaEnrichment as resolutionEnrichmentLambda } from './resolution-enrichment-lambda-construct'
import { createSendNotificationLambda as resolutionSendNotificationLambda } from './resolution-send-notification-lambda-construct'
import { createLambdaEnrichment as escalationEnrichmentLambdaFactory } from './escalation-enrichment-lambda-construct'
import { createSendNotificationLambda as escalationSendNotificationLambdaFactory } from './escalation-send-notification-lambda-construct'

export function createIncidentResolvedEscalationEventBridgePipe(
  scope: Construct,
  environment: Environment,
) {
  const secretProvider = new SecretProvider(scope, 'IncidentResolvedSecrets', {
    environment,
  })

  const incidentTable = dynamodb.Table.fromTableName(
    scope,
    'dynamo-incident-table',
    DYNAMO_DB_TABLE.INCIDENTS,
  )

  const enrichmentConstruct = resolutionEnrichmentLambda(
    scope,
    incidentTable,
    'incident-resolved',
    secretProvider,
  )

  const notificationConstruct = resolutionSendNotificationLambda(
    scope,
    incidentTable,
    'incident-resolved',
    secretProvider,
  )

  secretProvider.grantReadPermissionsTo(enrichmentConstruct.lambdaFunction)
  secretProvider.grantReadPermissionsTo(notificationConstruct.lambdaFunction)

  const pipeRole = new Role(scope, 'IncidentResolvedPipeRole', {
    assumedBy: new ServicePrincipal(
      'pipes.amazonaws.com',
    ) as cdk.aws_iam.IPrincipal,
    inlinePolicies: {
      'dynamodb-stream': new PolicyDocument({
        statements: [
          new PolicyStatement({
            effect: cdk.aws_iam.Effect.ALLOW,
            actions: ['dynamodb:*'],
            resources: ['*'],
          }),
        ],
      }),
    },
  })

  enrichmentConstruct.lambdaFunction.grantInvoke(pipeRole)
  notificationConstruct.lambdaFunction.grantInvoke(pipeRole)

  pipeRole.addToPolicy(
    new PolicyStatement({
      effect: cdk.aws_iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['*'],
    }),
  )

  new CfnPipe(scope, 'IncidentResolvedPipe', {
    name: 'IncidentResolvedPipe',
    roleArn: pipeRole.roleArn,
    source: DYNAMO_DB_TABLE_STREAM.INCIDENTS,
    sourceParameters: {
      dynamoDbStreamParameters: {
        batchSize: 1,
        startingPosition: 'LATEST',
      },
      filterCriteria: {
        filters: [
          {
            pattern: JSON.stringify({
              eventName: ['MODIFY'],
              dynamodb: {
                OldImage: {
                  status: {
                    S: [{ 'anything-but': 'RESOLVED' }],
                  },
                },
                NewImage: {
                  status: {
                    S: ['RESOLVED'],
                  },
                },
              },
            }),
          },
        ],
      },
    },
    enrichment: enrichmentConstruct.lambdaFunction.functionArn,
    enrichmentParameters: {
      inputTemplate: JSON.stringify({
        incidentId: '<$.dynamodb.NewImage.id.S>',
        teamId: '<$.dynamodb.NewImage.teamId.S>',
      }),
    },
    target: notificationConstruct.lambdaFunction.functionArn,
    targetParameters: {},
  })

  new LogGroup(scope, 'IncidentResolvedPipeLogGroup', {
    logGroupName: `/aws/events/IncidentResolvedPipe`,
    retention: cdk.aws_logs.RetentionDays.ONE_DAY,
    removalPolicy: cdk.RemovalPolicy.DESTROY,
  })
}

export function createEscalationEventBridgePipe(
  scope: Construct,
  notificationQueue: sqs.Queue,
  environment: Environment,
  incidentIntegrationCallbackAPI: string,
) {
  const secretProvider = new SecretProvider(scope, 'EscalationSecrets', {
    environment,
  })

  const enrichmentConstruct = escalationEnrichmentLambdaFactory(
    scope,
    secretProvider,
  )
  const bus = new StandardEventBusConstruct(
    scope,
    `${environment}-EscalationBus`,
    {
      eventBusName: `${environment}-EscalationBus`,
    },
  )

  const pipeRole = createPipeRole(
    scope,
    notificationQueue,
    enrichmentConstruct.lambdaFunction.functionArn,
    bus.eventBus,
  )

  const pipe = new CfnPipe(scope, 'EscalationPipe', {
    name: 'EscalationPipe',
    roleArn: pipeRole.roleArn,
    source: notificationQueue.queueArn,
    sourceParameters: {
      sqsQueueParameters: {
        batchSize: 1,
        maximumBatchingWindowInSeconds: 120,
      },
    },
    enrichment: enrichmentConstruct.lambdaFunction.functionArn,
    enrichmentParameters: {},
    target: bus.eventBus.eventBusArn,
    targetParameters: {},
  })

  const notificationLambdaConstruct = escalationSendNotificationLambdaFactory(
    scope,
    {
      IncidentEventCallbackUrl: incidentIntegrationCallbackAPI,
      AckEscalateCallbackUrl: incidentIntegrationCallbackAPI,
      secretProvider,
    },
  )

  secretProvider.grantReadPermissionsTo(enrichmentConstruct.lambdaFunction)
  secretProvider.grantReadPermissionsTo(
    notificationLambdaConstruct.lambdaFunction,
  )

  new Rule(scope, 'EscalationRule', {
    eventBus: bus.eventBus,
    eventPattern: {
      source: [`Pipe ${pipe.name}`],
    },
    targets: [
      new events_targets.LambdaFunction(
        notificationLambdaConstruct.lambdaFunction,
      ),
    ],
  })

  new LogGroup(scope, 'PipeLogGroup', {
    logGroupName: '/aws/events/EscalationPipe',
    retention: cdk.aws_logs.RetentionDays.ONE_DAY,
    removalPolicy: cdk.RemovalPolicy.DESTROY,
  })
}

const createPipeRole = (
  scope: Construct,
  notificationQueue: sqs.Queue,
  enrichmentFunctionArn: string,
  bus: EventBus,
) => {
  const pipePolicy = new PolicyDocument({
    statements: [
      new PolicyStatement({
        actions: [
          'sqs:ReceiveMessage',
          'sqs:DeleteMessage',
          'sqs:GetQueueAttributes',
        ],
        resources: [notificationQueue.queueArn],
      }),
    ],
  })

  const enrichmentPolicy = new PolicyDocument({
    statements: [
      new PolicyStatement({
        resources: [enrichmentFunctionArn],
        actions: ['lambda:InvokeFunction'],
        effect: cdk.aws_iam.Effect.ALLOW,
      }),
    ],
  })

  const pipeRolePolicyBus = new PolicyDocument({
    statements: [
      new PolicyStatement({
        effect: cdk.aws_iam.Effect.ALLOW,
        resources: [bus.eventBusArn],
        actions: ['events:PutEvents'],
      }),
    ],
  })

  const cloudWatchLogsPolicy = new PolicyDocument({
    statements: [
      new PolicyStatement({
        effect: cdk.aws_iam.Effect.ALLOW,
        actions: [
          'logs:CreateLogGroup',
          'logs:CreateLogStream',
          'logs:PutLogEvents',
        ],
        resources: ['*'],
      }),
    ],
  })

  return new Role(scope, 'escalation-pipe-role', {
    assumedBy: new ServicePrincipal(
      'pipes.amazonaws.com',
    ) as cdk.aws_iam.IPrincipal,
    inlinePolicies: {
      queue: pipePolicy,
      enrichment: enrichmentPolicy,
      bus: pipeRolePolicyBus,
      cloudWatchLogs: cloudWatchLogsPolicy,
    },
  })
}

export * from './custom-incident-escalation-statemachine-construct'
export * from './incident-escalation-statemachine-construct'
