import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as iam from 'aws-cdk-lib/aws-iam'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'

import * as path from 'path'

export function createLambdaEnrichment(
  scope: Construct,
  secretProvider: SecretProvider,
): StandardLambdaConstruct {
  // Define the additional policies needed for this Lambda
  const additionalPolicies = [
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['*'],
    }),
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'dynamodb:GetItem',
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:Query',
        'dynamodb:Scan',
        'dynamodb:CreateTable',
      ],
      resources: ['*'],
    }),
  ]

  // Create the Lambda function using the StandardLambdaConstruct
  const lambda = new StandardLambdaConstruct(scope, 'EscalationEnrichment', {
    entry: path.resolve(
      __dirname,
      '..',
      '..',
      'lambda',
      'handlers',
      'escalation-enrichment-handler.ts',
    ),
    handler: 'handler',
    timeout: cdk.Duration.minutes(5),
    additionalPolicies: additionalPolicies,
    logRetention: cdk.aws_logs.RetentionDays.ONE_WEEK,
    environment: {
      NODE_ENV: secretProvider.nodeEnv,
      LOG_LEVEL: secretProvider.appLogLevel,
      DB_HOST: secretProvider.dbHost,
      DB_USERNAME: secretProvider.dbUsername,
      DB_PASSWORD: secretProvider.dbPassword,
      DB_NAME: secretProvider.dbName,
    },
  })

  // Return the construct instance so the caller can grant permissions
  return lambda
}
