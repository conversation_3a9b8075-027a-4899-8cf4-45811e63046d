import * as cdk from 'aws-cdk-lib'
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb'
import { Construct } from 'constructs'
import * as iam from 'aws-cdk-lib/aws-iam'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'

import * as path from 'path'

export function createLambdaEnrichment(
  scope: Construct,
  incidentTable: dynamodb.ITable,
  idSuffix: string,
  secretProvider: SecretProvider,
): StandardLambdaConstruct {
  // Define the additional policies needed for this Lambda
  const additionalPolicies = [
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['*'],
    }),
  ]

  // Create the Lambda function using the StandardLambdaConstruct
  const lambda = new StandardLambdaConstruct(
    scope,
    `IncidentResolvedEnrichment-${idSuffix}`,
    {
      entry: path.resolve(
        __dirname,
        '..',
        '..',
        'lambda',
        'handlers',
        'resolution-enrichment-handler.ts',
      ),
      handler: 'handler',
      timeout: cdk.Duration.minutes(5),
      additionalPolicies: additionalPolicies,
      logRetention: cdk.aws_logs.RetentionDays.ONE_WEEK,
      environment: {
        // Common vars from SecretProvider
        NODE_ENV: secretProvider.nodeEnv,
        LOG_LEVEL: secretProvider.appLogLevel,

        // DB vars from SecretProvider
        DB_HOST: secretProvider.dbHost,
        DB_USERNAME: secretProvider.dbUsername,
        DB_PASSWORD: secretProvider.dbPassword,
        DB_NAME: secretProvider.dbName,
      },
      bundlingOptions: {
        externalModules: ['aws-sdk'],
        nodeModules: ['pg', 'drizzle-orm', 'dynamoose', 'nanoid'],
      },
    },
  )

  // Grant the Lambda function read/write access to the incident table
  incidentTable.grantReadWriteData(lambda.lambdaFunction)

  // Return the construct instance so the caller can grant permissions for secrets
  return lambda
}
