import { Construct } from 'constructs'
import * as sfn from 'aws-cdk-lib/aws-stepfunctions'
import * as sfnTasks from 'aws-cdk-lib/aws-stepfunctions-tasks'
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb'
import * as sqs from 'aws-cdk-lib/aws-sqs'
import { DYNAMO_DB_TABLE } from '@infra/shared/constants/database'
import { StandardStateMachineConstruct } from '@infra/shared/constructs/standard-statemachine.construct' // Added import

export interface IncidentEscalationStateMachineConstructProps {
  readonly notificationQueue: sqs.Queue
}

/**
 * Defines the standard Incident Escalation State Machine.
 * This construct encapsulates the Step Functions definition and uses
 * the shared StandardStateMachineConstruct for its creation.
 */
export class IncidentEscalationStateMachineConstruct extends Construct {
  public readonly stateMachine: sfn.IStateMachine

  constructor(
    scope: Construct,
    id: string,
    props: IncidentEscalationStateMachineConstructProps,
  ) {
    super(scope, id)

    const { notificationQueue } = props

    // --- State Machine Definition Steps ---

    const sendNotificationQueue = new sfnTasks.SqsSendMessage(
      this, // Changed scope to 'this'
      'Send Notification',
      {
        queue: notificationQueue,
        messageBody: sfn.TaskInput.fromObject({
          incidentId: sfn.JsonPath.stringAt('$.incidentId'),
          teamId: sfn.JsonPath.stringAt('$.teamId'),
          contactType: sfn.JsonPath.stringAt('$.contact.contactType'),
          contactId: sfn.JsonPath.stringAt('$.contact.contactId'),
          alerts: sfn.JsonPath.objectAt('$.contact.alerts'),
        }),
      },
    )

    const notificationMap = new sfn.Map(this, 'NotificationMap', {
      // Changed scope
      inputPath: '$',
      itemsPath: '$.contacts',
      itemSelector: {
        'incidentId.$': '$.incidentId',
        'teamId.$': '$.teamId',
        'contact.$': '$$.Map.Item.Value',
      },
      // parameters: {
      //   'incidentId.$': '$.incidentId',
      //   'teamId.$': '$.teamId',
      //   'contact.$': '$$.Map.Item.Value',
      // },
    }).itemProcessor(sendNotificationQueue)

    const incidentStatus = new sfnTasks.DynamoGetItem(this, 'Check Status', {
      // Changed scope
      table: dynamodb.Table.fromTableName(
        this, // Changed scope
        'Get Incident Status',
        DYNAMO_DB_TABLE.INCIDENTS,
      ),
      key: {
        id: sfnTasks.DynamoAttributeValue.fromString(
          sfn.JsonPath.stringAt('$.incidentId'),
        ),
      },
      consistentRead: true,
      resultPath: '$.incident',
      resultSelector: {
        'status.$': '$.Item.status.S',
      },
    }).next(
      new sfn.Choice(this, 'Check status') // Changed scope
        .when(
          sfn.Condition.or(
            sfn.Condition.stringEquals('$.incident.status', 'RESOLVED'),
            sfn.Condition.stringEquals('$.incident.status', 'ACKNOWLEDGED'),
          ),
          new sfn.Succeed(this, 'Incident Resolved or Acknowledged'), // Changed scope
        )
        .when(
          sfn.Condition.stringEquals('$.incident.status', 'recovered'),
          new sfn.Wait(this, 'Wait for Recovery', {
            // Changed scope
            time: sfn.WaitTime.secondsPath('$.recoverPeriod'),
          }).next(notificationMap),
        )
        .otherwise(notificationMap),
    )

    const escalationPolicies = new sfn.Map(this, 'EscalationPolicies', {
      // Changed scope
      maxConcurrency: 1,
      itemsPath: '$.escalationSteps',
      resultPath: '$.step',
    })
      .itemProcessor(
        new sfn.Choice(this, 'Wait for escalation?') // Changed scope
          .when(
            sfn.Condition.numberGreaterThan('$.stepDelay', 0),
            new sfn.Wait(this, 'Wait for escalation', {
              // Changed scope
              time: sfn.WaitTime.secondsPath('$.stepDelay'),
            }).next(incidentStatus),
          )
          .otherwise(incidentStatus),
      )
      .next(
        new sfn.Wait(this, 'Wait for repeat', {
          // Changed scope
          time: sfn.WaitTime.secondsPath('$.repeatDelay'),
        }),
      )

    const repeatEscalationPolicies = new sfn.Map(
      this, // Changed scope
      'RepeatEscalationPolicies',
      {
        maxConcurrency: 1,
        itemsPath: '$.repeat',
        itemSelector: {
          'escalationSteps.$': '$.escalationSteps',
          'repeatDelay.$': '$.repeatDelay',
        },
      },
    )
      .itemProcessor(escalationPolicies)
      .next(new sfn.Succeed(this, 'Succeed')) // Changed scope

    // Input step function ARN into incident data
    const updateStepFunctionArn = new sfnTasks.DynamoUpdateItem(
      this, // Changed scope
      'Update Step Function ARN',
      {
        inputPath: '$[0]',
        table: dynamodb.Table.fromTableName(
          this, // Changed scope
          'Update incident Step Function ARN',
          DYNAMO_DB_TABLE.INCIDENTS,
        ),
        key: {
          id: sfnTasks.DynamoAttributeValue.fromString(
            sfn.JsonPath.stringAt('$.incidentId'),
          ),
        },
        updateExpression: 'SET stepFunctionArn = :stepFunctionArn',
        expressionAttributeValues: {
          ':stepFunctionArn': sfnTasks.DynamoAttributeValue.fromString(
            sfn.JsonPath.stringAt('$$.Execution.Id'),
          ),
        },
        resultPath: '$[0].result',
        outputPath: '$[0]',
      },
    ).next(repeatEscalationPolicies)

    // --- Instantiate Shared State Machine Construct ---
    const standardStateMachine = new StandardStateMachineConstruct(
      this,
      'StateMachineResource', // Changed construct ID
      {
        stateMachineName: 'IncidentEscalation',
        definition: updateStepFunctionArn, // Pass the definition chain
        // stateMachineType: sfn.StateMachineType.STANDARD, // Default is STANDARD
        // Add logging or tracing config here if needed
      },
    )

    this.stateMachine = standardStateMachine.stateMachine
  }
}
