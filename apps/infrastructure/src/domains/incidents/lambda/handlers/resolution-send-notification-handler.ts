import IncidentNotifier from '@libs/notifier/incident-notifier'
import { Handler, Context, EventBridgeEvent } from 'aws-lambda'

enum EscalationContactType {
  ENTIRE_TEAM = 'entire_team',
  ON_CALL = 'on_call',
  MEMBER = 'member',
  INTEGRATION = 'integration',
}

enum SeverityAlert {
  CALL = 'call',
  SMS = 'sms',
  EMAIL = 'email',
  PUSH_NOTIFICATION = 'push_notification',
}

interface EscalationContact {
  contactType: EscalationContactType
  contactId?: string
  incidentId: string
  teamId: string
  users: any[]
  alerts: SeverityAlert[]
  integrationSettings: any[]
  incident: any
}

const getIncidentUrl = (teamId: string, incidentId: string) =>
  `${process.env['WEB_PORTAL_URL']}/team/${teamId}/incidents/${incidentId}`
const getCheckUrl = (teamId: string, checkId: string) =>
  `${process.env['WEB_PORTAL_URL']}/team/${teamId}/check/${checkId}`

const getIncidentPayload = (incident: any) => {
  return {
    incidentId: incident.id,
    url: incident.checkInfo?.url || '',
    checkUrl: getCheckUrl(incident.teamId, incident.id),
    incidentUrl: getIncidentUrl(incident.teamId, incident.id),
    title: incident.checkInfo?.url || '',
    startedAt: new Date(incident.startedAt),
    cause: incident.cause,
    method: incident.checkInfo?.method,
    metadata: {
      incidentId: incident.id,
    },
  }
}

export const handler: Handler = async (
  event: EventBridgeEvent<string, any>,
  context: Context,
) => {
  context.callbackWaitsForEmptyEventLoop = false

  console.log('🚀 ~ resoluton send ~ event:', event)

  const body = event as unknown as EscalationContact[]

  try {
    for (const escalation of body) {
      for (const alert of escalation.alerts) {
        switch (alert) {
          case SeverityAlert.EMAIL: {
            if (!escalation?.users?.length) return event

            const notifier = new IncidentNotifier('email', {
              host: process.env['SES_HOST']!,
              port: Number(process.env['SES_PORT']),
              ignoreTLS: false,
              secure: false,
              auth: {
                user: process.env['SES_SMTP_USERNAME']!,
                pass: process.env['SES_SMTP_PASSWORD']!,
              },
            })

            await notifier.resolved({
              ...getIncidentPayload(escalation.incident),
              to: escalation.users.map((user) => ({
                email: user.email,
                userId: user.id,
              })),
              headers: {
                'X-SES-CONFIGURATION-SET': 'IncidentConfigurationSet',
              },
              from: '<EMAIL>',
            })

            console.log('🚀 ~ email sent:', escalation.users)

            break
          }

          case SeverityAlert.PUSH_NOTIFICATION: {
            const slackIntegration = escalation.integrationSettings?.find(
              (integration) => integration.type === 'slack',
            )

            console.log('🚀 ~ slackIntegration:', slackIntegration)

            if (!slackIntegration) {
              throw new Error('Slack integration not found')
            }

            const notifier = new IncidentNotifier('slack')

            await notifier.resolved({
              ...getIncidentPayload(escalation.incident),
              incomingWebhookUrl: slackIntegration.config.incoming_webhook.url,
            })

            break
          }
          default:
            break
        }
      }
    }

    return event
  } catch (error) {
    console.error(error)
  }
}
