import IncidentNotifier from '@libs/notifier/incident-notifier'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import IncidentEventData from '@infra/shared/data/incident-event.data'

import { EscalationContact } from '../escalation-send-notification-handler'

import { getIncidentPayload } from './notification-utils'

export const handlePushNotification = async (body: EscalationContact) => {
  const slackIntegration = body.integrationSettings?.find(
    (integration) => integration.type === 'slack',
  )

  console.log('🚀 ~ slackIntegration:', slackIntegration)

  if (slackIntegration) {
    const notifier = new IncidentNotifier('slack')
    await notifier.started({
      ...getIncidentPayload(body.incident),
      incomingWebhookUrl: slackIntegration.config.incoming_webhook.url,
    })

    await Promise.all(
      body.users.map(async (user) => {
        await IncidentEventData.addIncidentEvent({
          type: IncidentEventType.NOTIFICATION,
          incidentId: body.incident.id,
          attribute: {
            type: IncidentEventDetailType.SEND_SLACK,
            value: {
              receiver: user.id,
            },
          },
        })
      }),
    )
  }

  // const pushNotiIntegration = body.integrationSettings?.find(
  //   (integration) => integration.type === 'push_noti',
  // )
  // The pushNotiIntegration check is commented out as per user feedback to always attempt push notifications.x
  // if (pushNotiIntegration) {
  const pushNotiNotifier = new IncidentNotifier('push_noti')
  const pushNotificationResults = await Promise.allSettled(
    body.users.map(async (user) => {
      await pushNotiNotifier.started({
        ...getIncidentPayload(body.incident),
        token: process.env['ONE_SIGNAL_AUTH_TOKEN']!,
        userId: user.id,
        firebaseId: user.firebaseId,
        teamId: body.teamId,
        organizationName: '',
        checkName: body.incident.title,
      })
      console.log(`🚀 ~ Push notification sent for user: ${user.id}`)
    }),
  )
  // Log any rejections from push notifications
  pushNotificationResults.forEach((result, index) => {
    if (result.status === 'rejected') {
      console.error(
        `🚀 ~ Push notification for user ${body.users[index]?.id} rejected:`,
        result.reason,
      )
    }
  })

  const incidentEventResults = await Promise.all(
    body.users.map(async (user) => {
      await IncidentEventData.addIncidentEvent({
        type: IncidentEventType.NOTIFICATION,
        incidentId: body.incident.id,
        attribute: {
          type: IncidentEventDetailType.SEND_PUSH_NOTIFICATION,
          value: {
            receiver: user.id,
          },
        },
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
        },
      })
    }),
  )
  // }
}
