export const getIncidentUrl = (teamId: string, incidentId: string) =>
  `${process.env['WEB_PORTAL_URL']}/team/${teamId}/incidents/${incidentId}`

export const getCheckUrl = (teamId: string, checkId: string) =>
  `${process.env['WEB_PORTAL_URL']}/team/${teamId}/check/${checkId}`

export const getIncidentPayload = (incident: any) => {
  return {
    incidentId: incident.id,
    url: incident.checkId ? incident.checkInfo?.url || '' : '',
    checkUrl: incident.checkId
      ? getCheckUrl(incident.teamId, incident.checkId)
      : getIncidentUrl(incident.teamId, incident.id),
    incidentUrl: getIncidentUrl(incident.teamId, incident.id),
    title: incident.checkId ? incident.checkInfo?.url || '' : '',
    startedAt: new Date(incident.startedAt),
    cause: incident.cause,
    method: incident.checkId ? incident.checkInfo?.method : undefined,
    metadata: {
      incidentId: incident.id,
    },
  }
}
