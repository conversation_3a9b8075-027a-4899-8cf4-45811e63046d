import IncidentNotifier from '@libs/notifier/incident-notifier'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import IncidentEventData from '@infra/shared/data/incident-event.data'

import { EscalationContact } from '../escalation-send-notification-handler'

import { getIncidentPayload } from './notification-utils'

export const handleSmsNotification = async (body: EscalationContact) => {
  console.log('Starting SMS alert processing in handleSmsNotification', {
    traceId: body.metadata?.traceId,
    incidentId: body.incident?.id,
    userCount: body.users?.length,
  })
  if (!body?.users?.length) {
    console.log('No users found for SMS, skipping SMS alert', {
      traceId: body.metadata?.traceId,
    })
    return // Return instead of continue as this is a standalone function
  }

  const notifier = new IncidentNotifier('sms')
  const phoneServiceConfig = {
    token: process.env['STRINGEE_AUTH_TOKEN'] ?? '',
    brandNameId: process.env['STRINGEE_BRAND_ID'] ?? '',
  }
  if (!phoneServiceConfig.token || !phoneServiceConfig.brandNameId) {
    console.error('Stringee credentials missing in environment variables')
    return
  }

  console.log('Stringee call configuration:', {
    token: phoneServiceConfig.token ? 'present' : 'missing',
    brandNameId: phoneServiceConfig.brandNameId,
  })

  const smsResults = await Promise.allSettled(
    body.users.map(async (user) => {
      console.log('Attempting SMS to:', {
        userId: user.id,
        phoneNumber: user.phoneNumber,
        name: `${user.firstName} ${user.lastName}`,
        traceId: body.metadata?.traceId,
      })

      try {
        const result = await notifier.started({
          ...getIncidentPayload(body.incident),
          token: phoneServiceConfig.token,
          from: phoneServiceConfig.brandNameId,
          to: user.phoneNumber,
          userId: user.id,
          serviceName: body.incident.checkInfo?.url || '',
          firstName: user.firstName,
          lastName: user.lastName,
        })

        console.log('SMS result for user', user.id, ':', result, {
          traceId: body.metadata?.traceId,
        })
        return result
      } catch (error: any) {
        console.error(
          'Error sending SMS for user',
          user.id,
          ':',
          error.message,
          { traceId: body.metadata?.traceId },
        )
        throw error
      }
    }),
  )

  console.log('All SMS results (settled promises):', smsResults, {
    traceId: body.metadata?.traceId,
  })
  console.log('Finished handleSmsNotification', {
    traceId: body.metadata?.traceId,
  })

  await Promise.all(
    body.users.map(async (user) => {
      await IncidentEventData.addIncidentEvent({
        type: IncidentEventType.NOTIFICATION,
        incidentId: body.incident.id,
        attribute: {
          type: IncidentEventDetailType.SEND_SMS,
          value: {
            receiver: user.phoneNumber,
          },
        },
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
        },
      })
    }),
  )
}
