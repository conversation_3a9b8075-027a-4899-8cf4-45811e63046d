import IncidentNotifier from '@libs/notifier/incident-notifier'
import { IncidentEventDetailType } from '@libs/shared/constants/incident'
import {
  PLIVO_DEFAULT_FROM,
  PLIVO_PHLO_ESCALATION_ID,
} from '@infra/shared/constants/plivio'
import { IncidentEventService } from '@infra/domains/incidents/services/incident-event.service'

import { EscalationContact } from '../escalation-send-notification-handler'

import { getIncidentPayload } from './notification-utils'

export const handleAppCallNotification = async (body: EscalationContact) => {
  if (!body?.users?.length) {
    console.log('APP_CALL alert: No users provided.')
    return // Return instead of break as this is a standalone function
  }

  // Read Plivo config from environment variables
  const plivoAuthId = process.env['PLIVO_AUTH_ID']
  const plivoAuthToken = process.env['PLIVO_AUTH_TOKEN']
  const plivoPhloId = PLIVO_PHLO_ESCALATION_ID
  const plivoFromNumber = PLIVO_DEFAULT_FROM

  if (!plivoAuthId || !plivoAuthToken || !plivoPhloId) {
    console.error(
      'Plivo credentials or PHLO ID missing in environment variables.',
    )
    return
  }

  console.log(`Processing APP_CALL alert for ${body.users.length} user(s)`)

  for (const user of body.users) {
    if (!user.plivoEndpoint) {
      console.log(
        `User ${user.id} does not have a Plivo endpoint configured. Skipping call.`,
      )
      continue // Skip this user if no endpoint
    }

    try {
      console.log(
        `Attempting Plivo call to user ${user.id} via endpoint ${user.plivoEndpoint}`,
      )
      const notifier = new IncidentNotifier('voip_call', {
        authId: plivoAuthId,
        authToken: plivoAuthToken,
      })

      await notifier.started({
        ...getIncidentPayload(body.incident),
        from: plivoFromNumber,
        userPhoneNumber: user.phoneNumber, // User's phone number for fallback
        userId: user.id,
        userEndpoint: user.plivoEndpoint,
        phloId: plivoPhloId,
        userFirstname: user.firstName,
        userLastname: user.lastName,
      })

      console.log(`Successfully triggered Plivo call for user ${user.id}`)

      // Log the event
      await IncidentEventService.createNotificationEvent(
        body.incident.id,
        IncidentEventDetailType.CALL_CREATE,
        // null, // System is performing the notification action
        {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
        }, // We sill need who to send the notification to, so don't set this to null
        { receiver: user.id },
      )
    } catch (error) {
      console.error(
        `Failed to trigger Plivo call for user ${user.id} (endpoint: ${user.plivoEndpoint}):`,
        error,
      )
    }
  }
}
