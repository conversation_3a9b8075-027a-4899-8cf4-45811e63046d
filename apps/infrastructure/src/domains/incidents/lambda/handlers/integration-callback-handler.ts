/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { APIGatewayProxyEvent, EventBridgeEvent } from 'aws-lambda'
import { SFNClient, StopExecutionCommand } from '@aws-sdk/client-sfn'
import { Pool } from 'pg'
import { type NodePgClient } from 'drizzle-orm/node-postgres'
import { AwsEvent } from '@slack/bolt/dist/receivers/AwsLambdaReceiver'
import {
  IncidentEventDetailType,
  IncidentStatus,
} from '@libs/shared/constants/incident'
import IncidentIntegrationCallback from '@libs/integration-callback/incident-integration-callback'
import IncidentData from '@infra/shared/data/incident-check.data'
import UserData from '@infra/shared/data/user.data'
import { VoiceCallStatus } from '@libs/integration-callback/incident-integration-callback/voice-call/call-status.handler'

import {
  IncidentEventService,
  UserInfo,
} from '../../services/incident-event.service'

const client = new SFNClient({ region: 'us-east-1' })

const pool = new Pool({
  port: 5432,
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_NAME'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

enum EventProvider {
  Slack = 'slack',
  VoiceCall = 'voice_call',
  Plivo = 'plivo',
}

const getEventType = (event: unknown): string => {
  if (!event) {
    return 'Unknown'
  }

  if ('httpMethod' in (event as APIGatewayProxyEvent)) {
    return 'APIGatewayEvent'
  }

  if ('detail-type' in (event as EventBridgeEvent<string, any>)) {
    return 'EventBridgeEvent'
  }

  return 'Unknown'
}

const getIncident = async (incidentId: string) => {
  return IncidentData.getIncidentById(incidentId)
}

const getUserByEmail = async (pgClient: NodePgClient, email: string) => {
  const userData = new UserData(pgClient)
  return userData.getUserByEmail(email)
}

const getUserById = async (pgClient: NodePgClient, userId: string) => {
  const userData = new UserData(pgClient)
  return userData.getUserById(userId)
}

const getIncidentStatus = async (incidentId: string) => {
  const incident = await getIncident(incidentId)
  return incident.status as IncidentStatus
}

const acknowledgeIncident = async (
  pgClient: NodePgClient,
  {
    incidentId,
    userId,
    email,
  }: { incidentId: string; userId?: string; email?: string },
) => {
  const incident = await getIncident(incidentId)
  const { stepFunctionArn } = incident

  const user = userId
    ? (await getUserById(pgClient, userId)) ||
      (email ? await getUserByEmail(pgClient, email) : undefined)
    : undefined

  const command = new StopExecutionCommand({
    executionArn: stepFunctionArn,
  })

  await client.send(command)

  await IncidentData.updateIncident(incidentId, {
    status: IncidentStatus.ACKNOWLEDGED,
    acknowledgedBy: userId || email || 'SYSTEM',
    acknowledgedAt: new Date(),
  })

  if (user) {
    await IncidentEventService.createUserAcknowledgedEvent(
      incidentId,
      user as UserInfo,
    )
  }
}

const viewIncident = async (
  pgClient: NodePgClient,
  {
    incidentId,
    userId,
    email,
  }: {
    incidentId: string
    userId?: string
    email?: string
  },
) => {
  try {
    const user = userId
      ? (await getUserById(pgClient, userId)) ||
        (email ? await getUserByEmail(pgClient, email) : undefined)
      : undefined

    const _event = await IncidentEventService.createNotificationEvent(
      incidentId,
      IncidentEventDetailType.OPENED_EMAIL,
      user ? (user as UserInfo) : null,
      { receiver: email || 'Unknown' },
    )

    console.log('🚀 ~ viewIncident: ~ event:', _event)
  } catch (e) {
    console.error('Failed to fetch user email:', e)
    return
  }
}

export const handler = async (
  event: APIGatewayProxyEvent,
  context: AWSLambda.Context,
  callback: AWSLambda.Callback,
) => {
  context.callbackWaitsForEmptyEventLoop = false
  const pgClient = await pool.connect()
  switch (getEventType(event)) {
    case 'APIGatewayEvent': {
      const { provider } = event.pathParameters || {}

      if (!event.body || !provider) {
        return {
          statusCode: 400,
          body: JSON.stringify({ message: 'Missing body' }),
        }
      }
      switch (provider as EventProvider) {
        case EventProvider.Slack: {
          const incidentCallback = new IncidentIntegrationCallback('slack', {
            botToken: process.env['SLACK_BOT_TOKEN']!,
            signingSecret: process.env['SLACK_SIGNING_SECRET']!,
            action: {
              getIncidentStatus,
              acknowledgeIncident: async (payload) => {
                await acknowledgeIncident(pgClient, payload)
              },
              escalate: async (incidentId: string) => {
                console.log('Escalating incident', incidentId)
              },
            },
          })

          const response = await incidentCallback.provider.handler(
            event as AwsEvent,
            context,
            callback,
          )

          return response
        }

        case EventProvider.Plivo: {
          console.log('Handling Plivo callback...')
          try {
            const incidentCallback = new IncidentIntegrationCallback('plivo', {
              action: {
                acknowledgeIncident: async (payload) => {
                  await acknowledgeIncident(pgClient, payload)
                },
                logIncidentEvent: async (payload) => {
                  console.log('Logging incident event from Plivo:', payload)
                  const user = payload.userId
                    ? await getUserById(pgClient, payload.userId)
                    : undefined

                  await IncidentEventService.createNotificationCallbackEvent(
                    payload.incidentId,
                    payload.type as IncidentEventDetailType,
                    user ? (user as UserInfo) : undefined,
                    payload.value || {},
                  )
                },
              },
            })

            let parsedBody
            try {
              parsedBody = JSON.parse(event.body)
            } catch (parseError) {
              console.error('Failed to parse Plivo request body:', parseError)
              return {
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid JSON body' }),
              }
            }

            await incidentCallback.provider.handleCallback(parsedBody)

            return {
              statusCode: 200,
              body: JSON.stringify({ message: 'Plivo callback received' }),
            }
          } catch (error) {
            console.error('Error handling Plivo callback:', error)
            return {
              statusCode: 500,
              body: JSON.stringify({
                message: `Internal server error' ${error}`,
              }),
            }
          }
        }

        case EventProvider.VoiceCall: {
          console.log('Handling Voice Call callback...')
          try {
            const incidentCallback = new IncidentIntegrationCallback(
              'voice_call',
              {
                action: {
                  acknowledgeIncident: async (payload) => {
                    await acknowledgeIncident(pgClient, payload)
                  },
                  escalate: async (incidentId: string) => {
                    console.log('Escalating incident', incidentId)
                  },
                  addIncidentEvent: async (payload: {
                    incidentId: string
                    attribute: { type: IncidentEventDetailType }
                    user?: { id: string }
                    details?: Record<string, unknown>
                  }) => {
                    console.log('ADD INCIDENT EVENT IN VOICECALL', payload)
                    const user = payload.user?.id
                      ? await getUserById(pgClient, payload.user.id)
                      : undefined
                    await IncidentEventService.createNotificationCallbackEvent(
                      payload.incidentId,
                      payload.attribute.type,
                      user ? (user as UserInfo) : undefined,
                      payload.details || {},
                    )
                  },
                },
              },
            )

            let parsedBody
            try {
              parsedBody = JSON.parse(event.body)
            } catch (parseError) {
              console.error(
                'Failed to parse Voice Call request body:',
                parseError,
              )
              return {
                statusCode: 400,
                body: JSON.stringify({ message: 'Invalid JSON body' }),
              }
            }

            // // Route based on callback type
            if (parsedBody.dtmf != null) {
              console.log('DTMF', parsedBody)
              const IVRresult =
                await incidentCallback.provider.IVRInputHandler(parsedBody)
              return IVRresult
            } else if (
              Object.values(VoiceCallStatus).includes(parsedBody.call_status)
            ) {
              console.log('CALL STATUS', parsedBody)
              const callStatusResult =
                await incidentCallback.provider.updateCallStatus(parsedBody)
              return callStatusResult
              // No dtmf field, update call status to Dynamo
            } else {
              console.log('UNHANDLE_VOICE_CALL', parsedBody)
            }
            return {
              statusCode: 200,
              body: JSON.stringify({
                message: 'Voice Call callback processed',
              }),
            }
          } catch (error) {
            console.error('Error handling Voice Call callback:', error)
            return {
              statusCode: 500,
              body: JSON.stringify({
                message: `Internal server error' ${error}`,
              }),
            }
          }
        }

        default:
          console.warn(`Received callback for unhandled provider: ${provider}`)
          break
      }

      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event),
      }
    }

    case 'EventBridgeEvent': {
      console.log('EventBridgeEvent', JSON.stringify(event, null, 2))

      const incidentCallback = new IncidentIntegrationCallback('email', {
        action: {
          getIncidentStatus,
          acknowledgeIncident: async (payload) => {
            console.log('🚀 ~ acknowledgeIncident: ~ payload:', payload)
            await acknowledgeIncident(pgClient, payload)
          },
          escalate: async (incidentId: string) => {
            console.log('Email Escalating incident', incidentId)
          },
          viewIncident: async (payload) => {
            await viewIncident(pgClient, payload)
          },
        },
      })

      const response = await incidentCallback.provider.on((event as any).detail)

      return response
    }

    default:
      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event),
      }
  }
}
