import { model } from 'dynamoose' // Import model from dynamoose
import { generateId } from '@orchestrator/commons/id' // Import generateId from common utilities
import {
  IncidentEventDetailType,
  IncidentEventType,
  IncidentStatus,
} from '@libs/shared/constants/incident'
import {
  IncidentEventInterface,
  IncidentEventSchema,
} from '@libs/database/lib/dynamo/incident-event.schema' // Import IncidentEventSchema
import { IncidentSchema } from '@libs/database/lib/dynamo/incident-check.schema' // Import IncidentSchema
import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants' // Import DYNAMO_DB_TABLE

const IncidentModel = model(
  // Removed explicit type parameter
  DYNAMO_DB_TABLE.INCIDENTS,
  IncidentSchema,
  {
    create: false,
    waitForActive: false,
  },
)

const IncidentEventModel = model(
  // Removed explicit type parameter
  DYNAMO_DB_TABLE.INCIDENT_EVENTS,
  IncidentEventSchema,
  {
    create: false,
    waitForActive: false,
  },
)

export const handler = async (event: any) => {
  let body
  let statusCode = 200
  const headers = {
    'Content-Type': 'application/json',
  }

  try {
    if (event.httpMethod !== 'POST') {
      throw new Error(`Unsupported method "${event.httpMethod}"`)
    }

    if (!event.body) {
      throw new Error('Request body is missing')
    }

    const requestBody = JSON.parse(event.body)
    const { incident_id, user_id, user_name } = requestBody

    if (!incident_id) {
      throw new Error('Incident ID is required in the request body.')
    }

    // Update the incident status
    // Generate ID and timestamp for the new incident event
    const eventId = generateId()
    const now = new Date()

    // Create a new incident event for ACKNOWLEDGED
    const newIncidentEvent: IncidentEventInterface = {
      id: eventId,
      incidentId: incident_id,
      type: IncidentEventType.MANUAL,
      attribute: {
        type: IncidentEventDetailType.ACKNOWLEDGED_INCIDENT,
        value: {
          message: `Incident ${incident_id} acknowledged through app call.`,
        },
      },
      user: {
        id: user_id,
      },
      createdAt: now,
      updatedAt: now,
    }

    // Update the incident status
    await IncidentModel.update(
      { id: incident_id },
      { status: IncidentStatus.ACKNOWLEDGED },
      { returnValues: 'UPDATED_NEW' }, // Changed ReturnValues to returnValues
    )

    // Create a new incident event
    await IncidentEventModel.create(newIncidentEvent)

    body = { message: `Incident ${incident_id} acknowledged successfully.` }
  } catch (err: any) {
    statusCode = 400
    body = { message: err.message }
  } finally {
    body = JSON.stringify(body)
  }

  return {
    statusCode,
    body,
    headers,
  }
}
