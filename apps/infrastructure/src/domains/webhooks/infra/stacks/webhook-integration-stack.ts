import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as iam from 'aws-cdk-lib/aws-iam' // Import iam namespace
import { EventBus, Rule } from 'aws-cdk-lib/aws-events'
import {
  CloudWatchLogGroup,
  LambdaFunction as EventLambdaTarget, // Renamed to avoid conflict with aws-lambda.Function
} from 'aws-cdk-lib/aws-events-targets'
import { LogGroup } from 'aws-cdk-lib/aws-logs'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'
import { StackProps } from '@infra/app'

import path from 'path'

export class WebhookIntegrationStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: StackProps) {
    // Updated props type
    super(scope, id, props)

    // Instantiate SecretProvider
    const secretProvider = new SecretProvider(this, 'WebhookSecrets', {
      environment: props.environment,
    })

    // Define policy statements directly
    const cloudWatchLogsPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['arn:aws:logs:*:*:*'], // StandardLambdaConstruct likely handles basic logs, but explicit is fine
    })

    const dynamoDbPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ec2:DescribeNetworkInterfaces', // Keep if needed by the lambda logic
        'ec2:CreateNetworkInterface',
        'ec2:DeleteNetworkInterface',
        'dynamodb:GetItem',
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:Query',
        'dynamodb:Scan',
        'dynamodb:CreateTable',
        'dynamodb:DescribeTable',
      ],
      resources: ['*'], // Restrict resources if possible
    })

    // Combine policies
    const lambdaPolicies = [cloudWatchLogsPolicy, dynamoDbPolicy]

    // Use StandardLambdaConstruct and pass policies
    const webhookIntegrationLambda = new StandardLambdaConstruct(
      this,
      'WebhookIntegrationLambda',
      {
        entry: path.join(__dirname, '../../lambda/handlers/webhook-handler.ts'),
        timeout: cdk.Duration.seconds(10),
        bundlingOptions: {
          externalModules: ['@aws-sdk/core'],
          nodeModules: ['dynamoose', 'nanoid', 'drizzle-orm', 'pg'],
        },
        additionalPolicies: lambdaPolicies, // Pass the defined policies
        environment: {
          // Common vars from SecretProvider
          NODE_ENV: secretProvider.nodeEnv,
          LOG_LEVEL: secretProvider.appLogLevel,
          // Add other specific secrets here if/when the webhook lambda needs them
          // Note: AWS_REGION is automatically provided by Lambda runtime
        },
      },
    )

    // Grant secret read permissions (even if no specific app secrets are used yet, for consistency)
    secretProvider.grantReadPermissionsTo(
      webhookIntegrationLambda.lambdaFunction,
    )

    const logGroup = new LogGroup(this, 'WebhookEventLogGroup', {
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      logGroupName: 'WebhookLogGroup',
    })

    const eventBus = new EventBus(this, 'WebhookEventBus', {
      eventBusName: 'WebhookEventsBus',
    })

    // const dlqBus = new Queue(this, 'WebhookBusDLQ', {
    //   retentionPeriod: cdk.Duration.days(14),
    // })

    new Rule(this, 'AllEventsRule', {
      eventBus,
      eventPattern: {
        source: ['webhook'],
        detailType: ['Webhook Request'],
      },
      targets: [
        new EventLambdaTarget(webhookIntegrationLambda.lambdaFunction), // Use renamed import
        new CloudWatchLogGroup(logGroup),
      ],
    })

    // new cdk.CfnOutput(this, 'SNSFailureNotificationTopic', {
    //   value: failureNotificationTopic.topicArn,
    // })

    new cdk.CfnOutput(this, 'EventBusName', {
      value: eventBus.eventBusName,
    })
  }
}
