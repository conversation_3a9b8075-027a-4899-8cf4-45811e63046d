import { CheckProps } from '@backend/modules/check/entities'
import { IncidentProps } from '@backend/modules/incident/entities'

export enum WebhookType {
  CRUD = 'CRUD_webhook',
  INCIDENT = 'Incident_webhook',
}

export interface WebhookConfigInterface {
  url: string
  requestMethod: WebhookHttpMethodOptions
  requestTimeout: number
  basicUsername?: string
  basicPassword?: string
  headerFields: { [key: string]: string }
  requestBody: { [key: string]: string | object } | string
}

export interface CRUDWebhookBodyInterface {
  event: string
  requestBody: string
  data: {
    timestamp: Date
  } & Partial<CheckProps>
}

export interface IncidentWebhookBodyInterface {
  event: string
  requestBody: string
  data: {
    timestamp: Date
  } & Partial<IncidentProps>
}

export type WebhookHttpMethodOptions =
  | 'HEAD'
  | 'GET'
  | 'POST'
  | 'PUT'
  | 'DELETE'
  | 'PATCH'

export type ResourcesType = 'CHECK' | 'INCIDENT'

export type CRUDWebhookEvent =
  | 'CREATED'
  | 'UPDATED'
  | 'PAUSED'
  | 'RESUMED'
  | 'DELETED'
export type IncidentWebhookEvent = 'NEW' | 'ACKNOWLEDGED' | 'RESOLVED'
export type WebhookEvent = CRUDWebhookEvent | IncidentWebhookEvent | 'TEST'

export interface WebhookLambdaRequestInterface {
  resourcesType: ResourcesType
  resourceId: string
  teamId: string
  event: WebhookEvent
  webhookId?: string
}

export function isCRUDWebhookEvent(
  event: WebhookEvent,
): event is CRUDWebhookEvent {
  return ['CREATED', 'UPDATED', 'PAUSED', 'RESUMED', 'DELETED'].includes(event)
}

export function isIncidentWebhookEvent(
  event: WebhookEvent,
): event is IncidentWebhookEvent {
  return ['NEW', 'ACKNOWLEDGED', 'RESOLVED'].includes(event)
}
