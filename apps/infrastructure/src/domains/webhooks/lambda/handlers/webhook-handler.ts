import { EventBridgeEvent } from 'aws-lambda'
import { Pool } from 'pg'
import WebhookLogData from '@infra/shared/data/webhook-log.data'
import CustomIncidentAndCheckData from '@infra/shared/data/customIncident-check.data'
import IncidentData from '@infra/shared/data/incident-check.data'
import IntegrationSettingData, {
  IntegrationSettingItem,
} from '@infra/shared/data/integration-setting.data'

import {
  WebhookLambdaRequestInterface,
  isCRUDWebhookEvent,
  WebhookEvent,
  WebhookConfigInterface,
  CRUDWebhookBodyInterface,
  IncidentWebhookBodyInterface,
  isIncidentWebhookEvent,
} from '../interfaces/webhook-interface'

const pool = new Pool({
  port: 5432,
  host: 'monitoringdog-dev-db.cja0y82qqvs6.us-east-1.rds.amazonaws.com',
  user: 'portal-dev-db-user',
  password: 'nIOFr5yuRop5',
  database: 'user-portal-dev-db',
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

export const handler = async (
  event: EventBridgeEvent<string, WebhookLambdaRequestInterface>,
): Promise<void> => {
  const { detail } = event
  const client = await pool.connect()

  console.log(detail)
  // Handle TEST hook first
  if (detail.webhookId) {
    const webhookSetting = await getWebhookSettingById(client, detail.webhookId)
    if (!webhookSetting) {
      console.log('Error: Webhook setting to test not found')
      return
    }
    await sendWebhookRequest(webhookSetting, detail, [])
    return
  }

  try {
    const webhookSettings = await getEnabledWebhookSettings(client, detail)
    if (!webhookSettings) return

    // Get Check or Incident Data, or anything that we extends later on
    const resourcesData = await getResourcesData(client, detail)
    if (!resourcesData) return

    for (const webhookSetting of webhookSettings) {
      await sendWebhookRequest(webhookSetting, detail, resourcesData)
    }
  } catch (error) {
    console.error('Error processing EventBridge message:', error)
  }
}

async function getWebhookSettingById(
  client: any,
  webhookId: string,
): Promise<IntegrationSettingItem | null> {
  const integrationSettingData = new IntegrationSettingData(client)
  return await integrationSettingData.getIntegrationSettingById(webhookId)
}

async function getEnabledWebhookSettings(
  client: any,
  detail: WebhookLambdaRequestInterface,
): Promise<IntegrationSettingItem[] | null> {
  const integrationSettingData = new IntegrationSettingData(client)
  const correspondSendOnField = getSendOnField(detail.event)
  return await integrationSettingData.getIntegrationSettingByTeamAndSendOnTrue(
    detail.teamId,
    correspondSendOnField,
  )
}

async function getResourcesData(
  client: any,
  detail: WebhookLambdaRequestInterface,
) {
  let resourcesDataGetter
  switch (detail.resourcesType) {
    case 'CHECK':
      resourcesDataGetter = new CustomIncidentAndCheckData(client)
      return await resourcesDataGetter.getCheckById(detail.resourceId)
    case 'INCIDENT':
      return await IncidentData.getIncidentById(detail.resourceId)
    default:
      console.error(`Error: Resources type: ${detail.resourcesType} not valid`)
      return null
  }
}

async function sendWebhookRequest(
  webhookSetting: IntegrationSettingItem,
  detail: WebhookLambdaRequestInterface,
  resourcesData: any,
) {
  const webhookConfig = webhookSetting.config as WebhookConfigInterface
  if (!webhookConfig) return
  if (!webhookConfig.url || !webhookConfig.requestMethod) {
    console.error(
      'Missing required fields in the webhookConfig:',
      webhookConfig,
    )
    return
  }
  const headers = prepareHeaders(webhookConfig)
  const controller = new AbortController()
  const timeout = webhookConfig.requestTimeout * 1000 || 5000

  const timeoutId = setTimeout(() => controller.abort(), timeout)
  let response: Response | undefined
  let responseChosenData: any
  let responseStatus = 'Failed'
  let responseCode = 500

  const reqBody = prepareRequestBody(
    detail.event,
    resourcesData,
    webhookConfig.requestBody as string,
  )

  try {
    response = await fetch(webhookConfig.url, {
      method: webhookConfig.requestMethod,
      headers,
      body: JSON.stringify(reqBody),
      signal: controller.signal,
    })

    responseChosenData = {
      url: response.url,
      headers: [...response.headers.entries()],
      body: await response.text(),
    }

    responseCode = response.status
    responseStatus = response.ok ? 'Success' : 'Failed'
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      responseCode = 408
      responseStatus = 'Timeout'
    } else {
      console.error('Unexpected error while sending webhook:', error)
    }
  } finally {
    clearTimeout(timeoutId)
    await logWebhookResponse(
      webhookSetting.id,
      responseCode,
      responseStatus,
      webhookConfig.requestMethod,
      JSON.stringify(reqBody),
      JSON.stringify(responseChosenData),
    )
  }
}

function prepareHeaders(webhookConfig: WebhookConfigInterface) {
  const headers = {
    ...webhookConfig.headerFields,
    'Content-Type': 'application/json',
  }
  if (webhookConfig.basicUsername && webhookConfig.basicPassword) {
    const auth = Buffer.from(
      `${webhookConfig.basicUsername}:${webhookConfig.basicPassword}`,
    ).toString('base64')
    headers['Authorization'] = `Basic ${auth}`
  }
  return headers
}

function prepareRequestBody(
  event: WebhookEvent,
  resourcesData: any,
  requestBody: string,
): {
  event: string
  data: object
  requestBody: string
} {
  if (isCRUDWebhookEvent(event)) {
    return {
      event: event,
      data: {
        timestamp: new Date(),
        ...resourcesData,
      },
      requestBody: requestBody,
    } as CRUDWebhookBodyInterface
  } else if (isIncidentWebhookEvent(event)) {
    return {
      event: event,
      data: {
        timestamp: new Date(),
        ...(() => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { stepFunctionArn, escalationPolicy, ...rest } = resourcesData
          return rest
        })(),
      },
      requestBody: requestBody,
    } as IncidentWebhookBodyInterface
  } else {
    return {
      event: event,
      data: {
        timestamp: new Date(),
        message: 'This is a Test Webhook, relevant data fields will be in here',
      },
      requestBody: requestBody,
    }
  }
}

async function logWebhookResponse(
  webhookId: string,
  responseCode: number,
  responseStatus: string,
  requestMethod: string,
  requestPayload: string,
  responsePayload: string | null,
) {
  const requestSize = requestPayload
    ? Buffer.byteLength(requestPayload, 'utf-8')
    : 0

  await WebhookLogData.addLog({
    webhookId: webhookId,
    requestMethod: requestMethod || '',
    requestSize,
    responseCode,
    responseStatus,
    requestPayload,
    responsePayload,
  })
  console.log('Log success', {
    webhookId: webhookId,
    requestMethod: requestMethod || '',
    requestSize,
    responseCode,
    responseStatus,
    requestPayload,
    responsePayload,
  })
}

function getSendOnField(event: WebhookEvent): string {
  switch (event) {
    case 'CREATED':
      return 'sendOnCreated'
    case 'UPDATED':
      return 'sendOnUpdated'
    case 'PAUSED':
      return 'sendOnPaused'
    case 'RESUMED':
      return 'sendOnResumed'
    case 'DELETED':
      return 'sendOnDeleted'
    case 'NEW':
      return 'sendOnNewIncident'
    case 'ACKNOWLEDGED':
      return 'sendOnAcknowledged'
    case 'RESOLVED':
      return 'sendOnResolved'
    default:
      throw new Error(`Unknown event type: ${event}`)
  }
}
