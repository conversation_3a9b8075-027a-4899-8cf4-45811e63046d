import {
  APIGatewayAuthorizerResultContext,
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
} from 'aws-lambda'
import { Pool } from 'pg'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { CheckStatus } from '@libs/shared/constants/check.enum'

// TODO: Update if data-handlers moves
import IncidentData from '@infra/shared/data/incident-check.data'
// TODO: Update if data-handlers moves
import CustomIncidentAndCheckData from '@infra/shared/data/customIncident-check.data'
import { IncidentEventService } from '@infra/domains/incidents/services/incident-event.service'

import { checkTriggerInterface } from '../interfaces/check-trigger-interface'

const pool = new Pool({
  port: Number(process.env['DB_PORT_DUMMY']),
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_DATABASE'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

export interface QuerriedItemContext extends APIGatewayAuthorizerResultContext {
  id: string
  teamId: string
  escalationPolicy: string
}

export async function handler(
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> {
  if (!event.body) throw Error('missing body')
  const body = JSON.parse(event.body)
  const { cause } = body as checkTriggerInterface
  const client = await pool.connect()
  const checkData = new CustomIncidentAndCheckData(client)

  const context = event.requestContext.authorizer as QuerriedItemContext

  try {
    const _updateCheck = await checkData.updateCheck(context.id, {
      status: CheckStatus.DOWN,
    })
    const triggeredCheckIncident = await IncidentData.addIncident({
      checkId: context.id,
      teamId: context.teamId,
      status: IncidentStatus.STARTED,
      escalationPolicy: context.escalationPolicy,
      cause: cause,
      startedAt: new Date(),
    })
    const _triggeredIncidentEvent =
      await IncidentEventService.createIncidentStartedEvent(
        triggeredCheckIncident.id,
        {
          location: context.teamId,
          message: cause,
        },
      )
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Check Trigger successfully',
        triggeredCheckIncident,
      }),
    }
  } catch (error) {
    console.error(error)
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Failed to trigger check incident',
        error,
      }),
    }
  }
}
