import {
  APIGatewayAuthorizerResultContext,
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
} from 'aws-lambda'
import { Pool } from 'pg'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { CustomIncidentStatus } from '@libs/shared/constants/customIncident.enum'

// TODO: Update if data-handlers moves
import IncidentCustomIncidentData from '@infra/shared/data/incident-customIncident.data'
// TODO: Update if data-handlers moves
import CustomIncidentAndCheckData from '@infra/shared/data/customIncident-check.data'
import { IncidentEventService } from '@infra/domains/incidents/services/incident-event.service'

import { customIncidentTriggerInterface } from '../interfaces/custom-incident-trigger-interface'

export interface QuerriedItemContext extends APIGatewayAuthorizerResultContext {
  id: string
  teamId: string
  escalationPolicy: string
}

const pool = new Pool({
  port: Number(process.env['DB_PORT_DUMMY']),
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_DATABASE'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

export async function handler(
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> {
  if (!event.body) throw Error('missing body')
  const body = JSON.parse(event.body)
  const { requester_email, name, summary, description } =
    body as customIncidentTriggerInterface
  const context = event.requestContext.authorizer as QuerriedItemContext
  const client = await pool.connect()
  const customIncidentData = new CustomIncidentAndCheckData(client)

  // TODO: What about Incident event?
  try {
    const _updateCustomIncident = await customIncidentData.updateCustomIncident(
      context.id,
      {
        status: CustomIncidentStatus.DOWN,
      },
    )
    const triggeredCustomIncident =
      await IncidentCustomIncidentData.addIncident({
        customIncidentId: context.id,
        teamId: context.teamId,
        status: IncidentStatus.STARTED,
        escalationPolicy: context.escalationPolicy,
        requester_email: requester_email,
        name: name,
        summary: summary,
        description: description,
        startedAt: new Date(),
      })
    const _triggeredCustomIncidentEvent =
      await IncidentEventService.createIncidentStartedEvent(
        triggeredCustomIncident.id,
        {
          location: context.teamId,
          message: 'Custom Incident triggered',
        },
      )
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Custom Incident triggered successfully',
        triggeredCustomIncident,
      }),
    }
  } catch (error) {
    console.error(error)
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Failed to trigger Custom Incident',
        error,
      }),
    }
  }
}
