import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda'
import { Pool } from 'pg'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { CheckStatus } from '@libs/shared/constants/check.enum'

// TODO: Update if data-handlers moves
import IncidentData from '@infra/shared/data/incident-check.data'
// TODO: Update if data-handlers moves
import CustomIncidentAndCheckData from '@infra/shared/data/customIncident-check.data'
import { IncidentEventService } from '@infra/domains/incidents/services/incident-event.service'

const pool = new Pool({
  port: Number(process.env['DB_PORT_DUMMY']),
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_DATABASE'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

export async function handler(
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> {
  const id = event.pathParameters?.['id']

  if (!id) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing id in path parameters' }),
    }
  }

  const client = await pool.connect()
  const checkData = new CustomIncidentAndCheckData(client)

  // TODO: What about Incident event?
  try {
    const _updateCheck = await checkData.updateCheck(id, {
      status: CheckStatus.UP,
    })

    const newestIncident = await IncidentData.getNewestIncidentByCheckId(id)
    if (!newestIncident) {
      return {
        statusCode: 404,
        body: JSON.stringify({
          message: `Incident with checkId ${id} not found`,
        }),
      }
    }
    const triggeredCheckIncident = await IncidentData.updateIncident(
      newestIncident.id,
      {
        status: IncidentStatus.RESOLVED,
      },
    )
    const _triggeredIncidentEvent =
      await IncidentEventService.createUserResolvedEvent(
        triggeredCheckIncident.id,
      )
    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Check resolved successfully', id }),
    }
  } catch (error) {
    console.error(error)
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Failed to resolve check', error }),
    }
  }
}
