import CustomIncidentAndCheckData from '@infra/shared/data/customIncident-check.data' // TODO: Update if data-handlers moves

export type QueryFunction = (
  client: any,
  resourceId: string,
) => Promise<QuerriedItem>

export type QuerriedItem = {
  id: string
  teamId: string
}

export const queryCustomIncident: QueryFunction = async (
  client: any,
  resourceId: string,
): Promise<QuerriedItem> => {
  const customIncidentData = new CustomIncidentAndCheckData(client)

  const customIncident =
    await customIncidentData.getCustomIncidentById(resourceId)

  if (!customIncident?.teamId) {
    throw new Error('Unauthorized: Team not found!')
  }
  return customIncident
}

export const queryCheck: QueryFunction = async (
  client: any,
  resourceId: string,
): Promise<QuerriedItem> => {
  const checkData = new CustomIncidentAndCheckData(client)
  const check = await checkData.getCheckById(resourceId)
  if (!check?.teamId) {
    throw new Error('Unauthorized: Team not found!')
  }
  return check
}
