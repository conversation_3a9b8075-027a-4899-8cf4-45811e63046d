import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as iam from 'aws-cdk-lib/aws-iam'
import {
  AuthorizationType,
  JsonSchemaType,
  LambdaIntegration,
  RestApi,
  TokenAuthorizer,
} from 'aws-cdk-lib/aws-apigateway'
import { LogGroup, RetentionDays } from 'aws-cdk-lib/aws-logs'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'

import path from 'path'

interface CustomIncidentStackProps extends cdk.NestedStackProps {
  api: RestApi
  authorizer: TokenAuthorizer
  environmentSetup: Record<string, string>
}

export class CustomIncidentAPIStack extends cdk.NestedStack {
  constructor(scope: Construct, id: string, props: CustomIncidentStackProps) {
    super(scope, id, props)

    const api = props.api
    const authorizer = props.authorizer
    const customIncidentResource = api.root
      .addResource('customIncident')
      .addResource('{id}')

    // === TRIGGER ===
    const triggerResource = customIncidentResource.addResource('trigger')
    const insertToDynamoConstruct = new StandardLambdaConstruct(
      this,
      'customIncidentTriggerLamda',
      {
        entry: path.join(
          __dirname,
          '../../lambda/handlers/custom-incident-trigger-handler.ts',
        ),
        handler: 'index.handler',
        bundlingOptions: {
          // Map bundling to bundlingOptions
          externalModules: ['aws-sdk'],
          nodeModules: ['dynamoose', 'nanoid', 'pg', 'drizzle-orm'],
        },
        environment: props.environmentSetup,
        additionalPolicies: [
          // Map addToRolePolicy to additionalPolicies
          new iam.PolicyStatement({
            actions: ['dynamodb:PutItem'],
            resources: ['*'],
          }),
        ],
        logRetention: RetentionDays.ONE_WEEK, // Set log retention
      },
    )

    const insertToDynamoLambda = insertToDynamoConstruct.lambdaFunction // Get underlying function

    const requestModel = api.addModel('CustomIncidentTriggerModel', {
      schema: {
        type: JsonSchemaType.OBJECT,
        properties: {
          requester_email: { type: JsonSchemaType.STRING },
          name: { type: JsonSchemaType.STRING },
          summary: { type: JsonSchemaType.STRING },
          description: { type: JsonSchemaType.STRING },
        },
        required: ['requester_email', 'name', 'summary', 'description'],
      },
      contentType: 'application/json',
    })

    triggerResource.addMethod(
      'POST',
      new LambdaIntegration(insertToDynamoLambda),
      {
        authorizer: authorizer,
        authorizationType: AuthorizationType.CUSTOM,
        requestParameters: {
          'method.request.path.id': true,
        },
        requestModels: {
          'application/json': requestModel,
        },
      },
    )

    // === RESOLVE ===
    const resolveResource = customIncidentResource.addResource('resolve')
    const resolveConstruct = new StandardLambdaConstruct(
      this,
      'customIncidentResolveLambda',
      {
        entry: path.join(
          __dirname,
          '../../lambda/handlers/custom-incident-resolve-handler.ts',
        ),
        handler: 'index.handler',
        bundlingOptions: {
          externalModules: ['aws-sdk'],
          nodeModules: ['dynamoose', 'nanoid', 'pg'],
        },
        environment: props.environmentSetup,
        additionalPolicies: [
          new iam.PolicyStatement({
            actions: [
              'dynamodb:UpdateItem',
              'dynamodb:PutItem',
              'dynamodb:Query',
            ],
            resources: ['*'],
          }),
        ],
        logRetention: RetentionDays.ONE_WEEK, // Set log retention
      },
    )
    const resolveLambda = resolveConstruct.lambdaFunction // Get underlying function

    resolveResource.addMethod('POST', new LambdaIntegration(resolveLambda), {
      authorizer: authorizer,
      authorizationType: AuthorizationType.CUSTOM,
      requestParameters: {
        'method.request.path.id': true,
      },
      // requestModels: {
      //   'application/json': requestModel,
      // },
    })

    const logGroup = new LogGroup(this, 'CustomIncidentAPIStackLogGroup', {
      logGroupName: '/authorizer-apigateway/customIncident-endpoint',
      retention: RetentionDays.ONE_WEEK,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    })
    logGroup.grantWrite(insertToDynamoLambda)
    logGroup.grantWrite(resolveLambda)
  }
}
