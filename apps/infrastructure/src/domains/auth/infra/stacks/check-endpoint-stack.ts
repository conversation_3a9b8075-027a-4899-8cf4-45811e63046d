import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { PolicyStatement } from 'aws-cdk-lib/aws-iam'
import {
  AuthorizationType,
  JsonSchemaType,
  LambdaIntegration,
  RestApi,
  TokenAuthorizer,
} from 'aws-cdk-lib/aws-apigateway'
import { LogGroup, RetentionDays } from 'aws-cdk-lib/aws-logs'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct' // Add import

import path from 'path'

interface CheckStackProps extends cdk.NestedStackProps {
  api: RestApi
  authorizer: TokenAuthorizer
  environmentSetup: Record<string, string>
}

export class CheckAPIStack extends cdk.NestedStack {
  constructor(scope: Construct, id: string, props: CheckStackProps) {
    super(scope, id, props)

    const api = props.api
    const authorizer = props.authorizer
    const checkResource = api.root.addResource('check').addResource('{id}')

    // === TRIGGER ===
    const triggerResource = checkResource.addResource('trigger')
    const insertToDynamoConstruct = new StandardLambdaConstruct(
      this,
      'checkTriggerLamda',
      {
        entry: path.join(
          __dirname,
          '../../lambda/handlers/check-trigger-handler.ts',
        ),
        handler: 'index.handler',
        bundlingOptions: {
          // Map bundling to bundlingOptions
          externalModules: ['aws-sdk'],
          nodeModules: ['dynamoose', 'nanoid', 'pg', 'drizzle-orm'],
        },
        environment: props.environmentSetup,
        additionalPolicies: [
          // Map addToRolePolicy to additionalPolicies
          new PolicyStatement({
            actions: ['dynamodb:PutItem'],
            resources: ['*'],
          }),
        ],
        logRetention: RetentionDays.ONE_WEEK, // Set log retention
      },
    )
    const insertToDynamoLambda = insertToDynamoConstruct.lambdaFunction // Get underlying function

    const requestModel = api.addModel('CheckTriggerModel', {
      schema: {
        type: JsonSchemaType.OBJECT,
        properties: {
          cause: { type: JsonSchemaType.STRING },
        },
        required: ['cause'],
      },
      contentType: 'application/json',
    })

    triggerResource.addMethod(
      'POST',
      new LambdaIntegration(insertToDynamoLambda),
      {
        authorizer: authorizer,
        authorizationType: AuthorizationType.CUSTOM,
        requestParameters: {
          'method.request.path.id': true,
        },
        requestModels: {
          'application/json': requestModel,
        },
      },
    )

    // === RESOLVE ===
    const resolveResource = checkResource.addResource('resolve')
    const resolveConstruct = new StandardLambdaConstruct(
      this,
      'checkResolveLamda',
      {
        entry: path.join(
          __dirname,
          '../../lambda/handlers/check-resolve-handler.ts',
        ),
        handler: 'index.handler',
        bundlingOptions: {
          // Map bundling to bundlingOptions
          externalModules: ['aws-sdk'],
          nodeModules: ['dynamoose', 'nanoid', 'pg'],
        },
        environment: props.environmentSetup,
        additionalPolicies: [
          // Map addToRolePolicy to additionalPolicies
          new PolicyStatement({
            actions: [
              'dynamodb:UpdateItem',
              'dynamodb:PutItem',
              'dynamodb:Query',
            ],
            resources: ['*'],
          }),
        ],
        logRetention: RetentionDays.ONE_WEEK, // Set log retention
      },
    )
    const resolveLambda = resolveConstruct.lambdaFunction // Get underlying function

    resolveResource.addMethod('POST', new LambdaIntegration(resolveLambda), {
      authorizer: authorizer,
      authorizationType: AuthorizationType.CUSTOM,
      requestParameters: {
        'method.request.path.id': true,
      },
      // requestModels: {
      //   'application/json': requestModel,
      // },
    })

    const logGroup = new LogGroup(this, 'CheckAPIStackLogGroup', {
      logGroupName: '/authorizer-apigateway/check-endpoint',
      retention: RetentionDays.ONE_WEEK,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    })
    logGroup.grantWrite(insertToDynamoLambda)
    logGroup.grantWrite(resolveLambda)
  }
}
