/**
 * AWS Clients and utilities for Lambda functions
 * Now using environment variables instead of Secrets Manager
 */

/**
 * Database connection configuration
 */
export interface DatabaseConfig {
  host: string
  port: number
  database: string
  username: string
  password: string
}

/**
 * Get database configuration from environment variables
 */
export function getDatabaseConfig(): DatabaseConfig {
  const host = process.env['DB_HOST']
  const port = process.env['DB_PORT']
  const database = process.env['DB_NAME']
  const username = process.env['DB_USERNAME']
  const password = process.env['DB_PASSWORD']

  if (!host || !port || !database || !username || !password) {
    throw new Error(
      'Missing required database environment variables: DB_HOST, DB_PORT, DB_NAME, DB_USERNAME, DB_PASSWORD',
    )
  }

  return {
    host,
    port: parseInt(port, 10),
    database,
    username,
    password,
  }
}

/**
 * Stripe configuration
 */
export interface StripeConfig {
  secretKey: string
}

/**
 * Get Stripe configuration from environment variables
 */
export function getStripeConfig(): StripeConfig {
  const secretKey = process.env['STRIPE_SECRET_KEY']

  if (!secretKey) {
    throw new Error(
      'Missing required Stripe environment variable: STRIPE_SECRET_KEY',
    )
  }

  return {
    secretKey,
  }
}

/**
 * Get specific Stripe price IDs from environment variables
 */
export function getStripePriceIds(): {
  smsPrice?: string
  voicePrice?: string
} {
  const smsPrice = process.env['STRIPE_SMS_PRICE_ID']
  const voicePrice = process.env['STRIPE_VOICE_PRICE_ID']

  return {
    ...(smsPrice && { smsPrice }),
    ...(voicePrice && { voicePrice }),
  }
}

/**
 * Get database connection timeout from environment variables
 */
export function getDatabaseTimeout(): number {
  const timeout = process.env['DB_CONNECTION_TIMEOUT']
  return timeout ? parseInt(timeout, 10) : 10000 // Default 10 seconds
}

/**
 * Get Stripe API timeout from environment variables
 */
export function getStripeTimeout(): number {
  const timeout = process.env['STRIPE_API_TIMEOUT']
  return timeout ? parseInt(timeout, 10) : 30000 // Default 30 seconds
}

/**
 * Get maximum retries from environment variables
 */
export function getMaxRetries(): number {
  const retries = process.env['MAX_RETRIES']
  return retries ? parseInt(retries, 10) : 3 // Default 3 retries
}
