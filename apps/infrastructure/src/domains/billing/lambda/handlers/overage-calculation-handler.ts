import { SQSE<PERSON>, SQSRecord, Context } from 'aws-lambda'
import { Client } from 'pg'
import { ResourceType } from '@libs/shared/constants/subscription/resource-mapping'

import { getDatabaseConfig, getStripeConfig } from '../utils/aws-clients'
import OverageCalculationData, {
  ResourceManipulationMessage,
  OverageCalculationResult,
} from '../../../../shared/data/overage-calculation.data'

// Import dynamic utilities for resource type validation

/**
 * OverageCalculationLambda Handler
 *
 * Purpose: Consumes from ResourceManipulationQueue, calculates overages, reports to Stripe
 * Trigger: SQS events from ResourceManipulationQueue
 *
 * Functionality:
 * 1. Parse SQS messages containing resource manipulation events
 * 2. Calculate current resource usage vs plan limits
 * 3. Determine if overage charges are needed
 * 4. Report overage usage to Stripe if necessary
 * 5. Update subscription usage tracking
 */
export const handler = async (
  event: SQSEvent,
  context: Context,
): Promise<void> => {
  console.log('OverageCalculationLambda triggered', {
    requestId: context.awsRequestId,
    messageCount: event.Records.length,
    timestamp: new Date().toISOString(),
  })

  // Process each SQS record
  for (const record of event.Records) {
    try {
      await processOverageCalculationRecord(record, context)
    } catch (error) {
      console.error('Failed to process record', {
        requestId: context.awsRequestId,
        messageId: record.messageId,
        error: error instanceof Error ? error.message : 'Unknown error',
        record: record.body,
      })

      // Re-throw error to trigger SQS retry mechanism
      throw error
    }
  }

  console.log('OverageCalculationLambda completed successfully', {
    requestId: context.awsRequestId,
    processedCount: event.Records.length,
  })
}

/**
 * Process individual SQS record for overage calculation
 */
async function processOverageCalculationRecord(
  record: SQSRecord,
  context: Context,
): Promise<void> {
  console.log('Processing overage calculation record', {
    requestId: context.awsRequestId,
    messageId: record.messageId,
    receiptHandle: record.receiptHandle,
  })

  // Parse message body
  let message: ResourceManipulationMessage
  try {
    message = JSON.parse(record.body)
    validateResourceManipulationMessage(message)
  } catch (error) {
    console.error('Invalid message format', {
      requestId: context.awsRequestId,
      messageId: record.messageId,
      body: record.body,
      error: error instanceof Error ? error.message : 'Parse error',
    })
    throw new Error(
      `Invalid message format: ${error instanceof Error ? error.message : 'Unknown error'}`,
    )
  }

  // Calculate overage for this resource type using data layer
  const overageResult = await calculateResourceOverage(message, context)

  // Update subscription item quantity if overage detected (PRD FR2.6.4)
  let stripeSubscriptionItemId: string | null = null
  let reportingError: Error | null = null

  if (
    overageResult.requiresStripeReporting &&
    overageResult.overagePolicy !== 'BLOCK' // Skip update for BLOCK policy
  ) {
    stripeSubscriptionItemId = await updateSubscriptionQuantityForOverage(
      overageResult,
      context,
    )
    if (stripeSubscriptionItemId === null) {
      reportingError = new Error(
        'Failed to update subscription quantity for overage',
      )
    }
  }

  // Update subscription usage tracking using data layer (PRD FR2.6.5)
  await updateSubscriptionUsageTracking(
    overageResult,
    context,
    stripeSubscriptionItemId,
    reportingError,
  )

  console.log('Successfully processed overage calculation record', {
    requestId: context.awsRequestId,
    messageId: record.messageId,
    organizationId: message.organizationId,
    resourceType: message.resourceType,
    currentUsage: overageResult.currentUsage,
    planLimit: overageResult.planLimit,
    overageAmount: overageResult.overageAmount,
  })
}

/**
 * Validate resource manipulation message structure
 */
function validateResourceManipulationMessage(
  message: any,
): asserts message is ResourceManipulationMessage {
  const required = [
    'organizationId',
    'resourceType',
    'eventTimestamp',
    'action',
    'resourceId',
  ]

  for (const field of required) {
    if (!message[field]) {
      throw new Error(`Missing required field: ${field}`)
    }
  }

  // Dynamic resource type validation using enum values
  if (
    !Object.values(ResourceType).includes(message.resourceType as ResourceType)
  ) {
    throw new Error(
      `Invalid resourceType: ${message.resourceType}. Supported types: ${Object.values(ResourceType).join(', ')}`,
    )
  }

  if (!['CREATED', 'DELETED', 'UPDATED'].includes(message.action)) {
    throw new Error(`Invalid action: ${message.action}`)
  }

  // Validate timestamp format
  if (isNaN(Date.parse(message.eventTimestamp))) {
    throw new Error(`Invalid eventTimestamp format: ${message.eventTimestamp}`)
  }
}

/**
 * Calculate resource overage for the organization using data layer (PRD FR2.6.2, FR2.6.3)
 */
async function calculateResourceOverage(
  message: ResourceManipulationMessage,
  context: Context,
): Promise<OverageCalculationResult> {
  console.log('Calculating resource overage', {
    requestId: context.awsRequestId,
    organizationId: message.organizationId,
    resourceType: message.resourceType,
    action: message.action,
  })

  let client: Client | null = null
  try {
    // Get database configuration and create connection
    const dbConfig = getDatabaseConfig()
    client = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl:
        process.env['NODE_ENV'] === 'production'
          ? { rejectUnauthorized: false }
          : false,
    })

    await client.connect()

    // Use data layer for overage calculation
    const overageCalculationData = new OverageCalculationData(client)

    // Strategy 1: Use planContext from SQS message (race-condition safe)
    if (message.planContext) {
      console.log('Using plan context from SQS message', {
        requestId: context.awsRequestId,
        organizationId: message.organizationId,
        resourceType: message.resourceType,
        planContextResourceLimits: message.planContext.resourceLimits.length,
      })

      return await overageCalculationData.calculateOverageFromPlanContext(
        message,
      )
    }

    // Strategy 2: Fallback to database query (backward compatibility)
    console.log('Plan context not available, falling back to database query', {
      requestId: context.awsRequestId,
      organizationId: message.organizationId,
      resourceType: message.resourceType,
    })

    return await overageCalculationData.calculateOverageFromDatabase(message)
  } catch (error) {
    console.error('Failed to calculate resource overage', {
      requestId: context.awsRequestId,
      organizationId: message.organizationId,
      resourceType: message.resourceType,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
    throw error
  } finally {
    if (client) {
      await client.end()
    }
  }
}

/**
 * Update subscription item quantity to reflect current overage (PRD FR2.6.4)
 */
async function updateSubscriptionQuantityForOverage(
  result: OverageCalculationResult,
  context: Context,
): Promise<string | null> {
  console.log('Updating subscription item quantity for overage', {
    requestId: context.awsRequestId,
    organizationId: result.organizationId,
    resourceType: result.resourceType,
    overageQuantity: result.overageAmount,
    stripeSubscriptionId: result.stripeSubscriptionId,
    overageStripePriceId: result.overageStripePriceId,
  })

  if (!result.stripeSubscriptionId || !result.overageStripePriceId) {
    console.warn(
      'Missing Stripe subscription ID or price ID for quantity update',
      {
        requestId: context.awsRequestId,
        organizationId: result.organizationId,
        resourceType: result.resourceType,
        stripeSubscriptionId: result.stripeSubscriptionId,
        overageStripePriceId: result.overageStripePriceId,
      },
    )
    return null
  }

  try {
    // Get Stripe configuration
    const stripeConfig = getStripeConfig()
    const Stripe = (await import('stripe')).default
    const stripe = new Stripe(stripeConfig.secretKey, {
      apiVersion: '2025-02-24.acacia',
      timeout: parseInt(process.env['STRIPE_API_TIMEOUT'] || '30000'),
    })

    // Find the subscription item for this overage price
    const subscription = await stripe.subscriptions.retrieve(
      result.stripeSubscriptionId,
      { expand: ['items.data.price'] },
    )

    const subscriptionItem = subscription.items.data.find(
      (item) => item.price.id === result.overageStripePriceId,
    )

    if (!subscriptionItem) {
      console.warn('No subscription item found for overage price', {
        requestId: context.awsRequestId,
        organizationId: result.organizationId,
        resourceType: result.resourceType,
        overageStripePriceId: result.overageStripePriceId,
      })
      return null
    }

    // Determine proration behavior based on overage policy
    const prorationBehavior =
      result.overagePolicy === 'CHARGE'
        ? 'create_prorations' // Immediate billing with proration
        : 'none' // Bill at next cycle (GRACE) or no update (BLOCK)

    // Update subscription item quantity to current overage amount
    const updatedItem = await stripe.subscriptionItems.update(
      subscriptionItem.id,
      {
        quantity: Math.max(0, result.overageAmount), // Ensure non-negative quantity
        proration_behavior: prorationBehavior,
      },
    )

    console.log('Successfully updated subscription item quantity', {
      requestId: context.awsRequestId,
      subscriptionItemId: updatedItem.id,
      newQuantity: updatedItem.quantity,
      prorationBehavior,
      resourceType: result.resourceType,
    })

    return updatedItem.id
  } catch (error) {
    console.error('Failed to update subscription item quantity', {
      requestId: context.awsRequestId,
      organizationId: result.organizationId,
      resourceType: result.resourceType,
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    // Return null to indicate failure
    return null
  }
}

/**
 * Update subscription usage tracking using data layer (PRD FR2.6.5)
 */
async function updateSubscriptionUsageTracking(
  result: OverageCalculationResult,
  context: Context,
  stripeSubscriptionItemId: string | null = null,
  error: Error | null = null,
): Promise<void> {
  console.log('Updating subscription usage tracking', {
    requestId: context.awsRequestId,
    organizationId: result.organizationId,
    resourceType: result.resourceType,
    currentUsage: result.currentUsage,
    overageAmount: result.overageAmount,
    updatedInStripe: stripeSubscriptionItemId !== null,
  })

  if (!result.subscriptionItemId) {
    console.warn('No subscription item ID found for organization', {
      requestId: context.awsRequestId,
      organizationId: result.organizationId,
    })
    return
  }

  let client: Client | null = null
  try {
    // Get database configuration
    const dbConfig = getDatabaseConfig()
    client = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl:
        process.env['NODE_ENV'] === 'production'
          ? { rejectUnauthorized: false }
          : false,
    })

    await client.connect()

    // Use data layer for subscription usage tracking
    const overageCalculationData = new OverageCalculationData(client)
    await overageCalculationData.createSubscriptionUsageRecord(
      result,
      stripeSubscriptionItemId,
      error,
    )

    console.log('Successfully created subscription usage record for overage', {
      requestId: context.awsRequestId,
      organizationId: result.organizationId,
      resourceType: result.resourceType,
      overageAmount: result.overageAmount,
      updatedInStripe: stripeSubscriptionItemId !== null,
    })
  } catch (dbError) {
    console.error('Failed to create subscription usage record for overage', {
      requestId: context.awsRequestId,
      organizationId: result.organizationId,
      resourceType: result.resourceType,
      error:
        dbError instanceof Error ? dbError.message : 'Unknown database error',
    })

    // Don't re-throw as this is a logging operation and shouldn't block the main flow
  } finally {
    if (client) {
      await client.end()
    }
  }
}
