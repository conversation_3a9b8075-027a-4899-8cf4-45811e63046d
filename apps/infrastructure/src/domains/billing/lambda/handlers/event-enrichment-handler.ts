import { SQSE<PERSON>, SQSR<PERSON>ord } from 'aws-lambda'
import { Logger } from '@aws-lambda-powertools/logger'
import {
  EventBridgeClient,
  PutEventsCommand,
} from '@aws-sdk/client-eventbridge'
import { Client } from 'pg'
import { SubscriptionStatus } from '@libs/shared/constants/subscription'

import { getDatabaseConfig } from '../utils/aws-clients'
import BillingContextData, {
  BillingContext,
} from '../../../../shared/data/billing-context.data'
import StripeUsageReportingData from '../../../../shared/data/stripe-usage-reporting.data'

const logger = new Logger({ serviceName: 'event-enrichment' })

const eventBridgeClient = new EventBridgeClient({
  region: process.env['AWS_REGION'] || 'us-east-1',
})

interface NotificationEvent {
  organizationId: string
  notificationType: string
  channelType: 'sms' | 'voice' | 'email' | 'push' | 'slack'
  recipientId: string
  eventId: string
  timestamp: string
  metadata?: Record<string, any>
}

interface EnrichedNotificationEvent extends NotificationEvent {
  billingContext: BillingContext
  shouldProcess: boolean
  blockReason?: string
  usageTracking: {
    shouldTrack: boolean
    isBillable: boolean
    stripeMetadata?: {
      customerId: string
      priceId: string
      usageType: string
    }
  }
}

export const handler = async (event: SQSEvent): Promise<void> => {
  logger.info('Processing notification events for enrichment', {
    eventCount: event.Records.length,
  })

  const results = await Promise.allSettled(
    event.Records.map((record) => processNotificationEvent(record)),
  )

  const successful = results.filter((r) => r.status === 'fulfilled').length
  const failed = results.filter((r) => r.status === 'rejected').length

  if (failed > 0) {
    logger.warn('Some events failed processing', { successful, failed })
  }
}

const processNotificationEvent = async (record: SQSRecord): Promise<void> => {
  const traceId = `enrichment-${Date.now()}-${Math.random().toString(36).substring(7)}`

  try {
    logger.info('Processing notification event', {
      messageId: record.messageId,
      traceId,
    })

    const notificationEvent: NotificationEvent = JSON.parse(record.body)

    // Validate required fields
    if (!notificationEvent.organizationId || !notificationEvent.channelType) {
      throw new Error('Missing required fields: organizationId or channelType')
    }

    // Fetch billing context for the organization
    const billingContext = await fetchBillingContext(
      notificationEvent.organizationId,
    )

    // Determine if notification should be processed
    const enrichmentResult = await enrichNotificationEvent(
      notificationEvent,
      billingContext,
    )

    // Send enriched event to EventBridge
    await forwardEnrichedEvent(enrichmentResult, traceId)

    // Track usage if needed
    if (
      enrichmentResult.usageTracking.shouldTrack &&
      enrichmentResult.shouldProcess
    ) {
      await trackUsage(enrichmentResult)
    }

    logger.info('Successfully enriched and forwarded event', {
      traceId,
      organizationId: notificationEvent.organizationId,
      channelType: notificationEvent.channelType,
      shouldProcess: enrichmentResult.shouldProcess,
    })
  } catch (error) {
    logger.error('Failed to process notification event', {
      error: error instanceof Error ? error.message : 'Unknown error',
      traceId,
      messageId: record.messageId,
    })

    // Re-throw to trigger SQS retry/DLQ handling
    throw error
  }
}

const fetchBillingContext = async (
  organizationId: string,
): Promise<BillingContext> => {
  logger.info('Fetching billing context', { organizationId })

  let client: Client | null = null
  try {
    // Get database configuration and create connection
    const dbConfig = getDatabaseConfig()
    client = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl:
        process.env['NODE_ENV'] === 'production'
          ? { rejectUnauthorized: false }
          : false,
    })

    await client.connect()

    // Use data layer for billing context operations
    const billingContextData = new BillingContextData(client)
    return await billingContextData.fetchBillingContext(organizationId)
  } catch (error) {
    logger.error('Failed to fetch billing context', {
      organizationId,
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    // Return default context to allow notifications to proceed
    const billingContextData = new BillingContextData(client || ({} as any))
    return billingContextData.createDefaultBillingContext(organizationId)
  } finally {
    if (client) {
      await client.end()
    }
  }
}

const enrichNotificationEvent = async (
  event: NotificationEvent,
  billingContext: BillingContext,
): Promise<EnrichedNotificationEvent> => {
  // Start with allowing the event
  let shouldProcess = true
  let blockReason: string | undefined

  // Check if this is a billable channel
  const isBillableChannel = ['sms', 'voice'].includes(event.channelType)

  // For billable channels, check entitlement and billing enablement
  if (isBillableChannel) {
    // Block if no entitlement (covers canceled subscriptions and expired grace periods)
    if (!billingContext.entitlementStatus) {
      shouldProcess = false
      blockReason =
        billingContext.subscriptionStatus === SubscriptionStatus.CANCELED
          ? 'subscription_canceled'
          : 'grace_period_expired'
    }
    // Block if usage billing is disabled
    else if (!billingContext.usageBillingEnabled) {
      shouldProcess = false
      blockReason = 'usage_billing_disabled'
    }
  }

  // Determine usage tracking requirements according to PRD FR2.5.1
  const isBillable =
    isBillableChannel &&
    billingContext.entitlementStatus &&
    billingContext.usageBillingEnabled &&
    billingContext.stripeCustomerId

  const usageTracking: {
    shouldTrack: boolean
    isBillable: boolean
    stripeMetadata?: {
      customerId: string
      priceId: string
      usageType: string
    }
  } = {
    shouldTrack: isBillableChannel, // Always track billable channels for audit
    isBillable: !!isBillable,
    ...(isBillable
      ? {
          stripeMetadata: {
            customerId: billingContext.stripeCustomerId!,
            priceId:
              event.channelType === 'sms'
                ? process.env['STRIPE_SMS_PRICE_ID'] || ''
                : process.env['STRIPE_VOICE_PRICE_ID'] || '',
            usageType: event.channelType.toUpperCase(),
          },
        }
      : {}),
  }

  const enrichedEvent: EnrichedNotificationEvent = {
    ...event,
    billingContext,
    shouldProcess,
    ...(blockReason !== undefined ? { blockReason } : {}),
    usageTracking,
  }

  logger.info('Event enriched', {
    organizationId: event.organizationId,
    channelType: event.channelType,
    shouldProcess,
    blockReason,
    isBillable,
    willTrackUsage: usageTracking.shouldTrack,
  })

  return enrichedEvent
}

const forwardEnrichedEvent = async (
  enrichedEvent: EnrichedNotificationEvent,
  traceId: string,
): Promise<void> => {
  const eventBridgeEvent = {
    Source: 'monitoring-dog.billing.enrichment',
    DetailType: enrichedEvent.shouldProcess
      ? 'Notification Approved'
      : 'Notification Blocked',
    Detail: JSON.stringify(enrichedEvent),
    EventBusName: process.env['EVENT_BUS_NAME'],
    Resources: [`organization:${enrichedEvent.organizationId}`],
    Time: new Date(),
  }

  await eventBridgeClient.send(
    new PutEventsCommand({
      Entries: [eventBridgeEvent],
    }),
  )

  logger.info('Enriched event forwarded to EventBridge', {
    traceId,
    eventBusName: process.env['EVENT_BUS_NAME'],
    detailType: eventBridgeEvent.DetailType,
  })
}

const trackUsage = async (
  enrichedEvent: EnrichedNotificationEvent,
): Promise<void> => {
  if (!enrichedEvent.usageTracking.shouldTrack) {
    return
  }

  let client: Client | null = null
  try {
    // Get database configuration and create connection
    const dbConfig = getDatabaseConfig()
    client = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl:
        process.env['NODE_ENV'] === 'production'
          ? { rejectUnauthorized: false }
          : false,
    })

    await client.connect()

    // Use data layer for usage tracking
    const usageReportingData = new StripeUsageReportingData(client)
    await usageReportingData.createUsageTrackingRecord(
      enrichedEvent.organizationId,
      enrichedEvent.channelType,
      enrichedEvent.timestamp,
      enrichedEvent.usageTracking.stripeMetadata?.priceId || '',
      enrichedEvent.usageTracking.isBillable,
      enrichedEvent.blockReason,
    )

    logger.info('Usage tracked in database', {
      organizationId: enrichedEvent.organizationId,
      usageType: enrichedEvent.channelType,
      eventId: enrichedEvent.eventId,
      isBillable: enrichedEvent.usageTracking.isBillable,
    })
  } catch (error) {
    logger.error('Failed to track usage', {
      organizationId: enrichedEvent.organizationId,
      usageType: enrichedEvent.channelType,
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  } finally {
    if (client) {
      await client.end()
    }
  }
}
