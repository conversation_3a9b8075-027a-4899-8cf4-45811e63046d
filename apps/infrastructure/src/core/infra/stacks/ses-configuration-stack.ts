import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as ses from 'aws-cdk-lib/aws-ses'
import * as lambda from 'aws-cdk-lib/aws-lambda'

export class IncidentSESConfigSetStack extends cdk.Stack {
  constructor(
    scope: Construct,
    id: string,
    incidentIntegrationCallbackLambda: lambda.IFunction,
    props?: cdk.StackProps,
  ) {
    super(scope, id, props)

    // SES Configuration set
    const sesConfigurationSet = new ses.CfnConfigurationSet(
      this,
      'IncidentConfigurationSet',
      {
        name: 'IncidentConfigurationSet',
        deliveryOptions: {
          sendingPoolName: 'ses-shared-pool',
        },
      },
    )

    const rule = new cdk.aws_events.Rule(this, 'EmailTrackClickOpenRule', {
      ruleName: 'EmailTrackClickOpenRule',
      eventBus: cdk.aws_events.EventBus.fromEventBusArn(
        this,
        'EventBus',
        `arn:aws:events:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:event-bus/default`,
      ),
      eventPattern: {
        source: ['aws.ses'],
        // detailType: ['Email Clicked', 'Email Opened'],
      },
    })

    rule.addTarget(
      new cdk.aws_events_targets.LambdaFunction(
        incidentIntegrationCallbackLambda,
      ),
    )

    new ses.CfnConfigurationSetEventDestination(
      this,
      'SESEventDestinationEventBridge',
      {
        configurationSetName: sesConfigurationSet.name!,
        eventDestination: {
          name: 'SESEventDestinationEventBridge',
          enabled: true,
          matchingEventTypes: ['open', 'click'],
          eventBridgeDestination: {
            // Default event bus
            eventBusArn: `arn:aws:events:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:event-bus/default`,
          },
        },
      },
    )
  }
}
