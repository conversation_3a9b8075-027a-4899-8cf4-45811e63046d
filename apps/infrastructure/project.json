{"name": "infrastructure", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/infrastructure/src", "projectType": "application", "tags": ["cdk-app"], "implicitDependencies": [], "targets": {"lint": {"command": "eslint apps/infrastructure/src/**/*.ts"}, "cdklocal": {"executor": "nx:run-commands", "outputs": [], "options": {"cwd": "apps/infrastructure", "commands": [{"command": "cdklocal"}]}}}}