{"extends": "../../tsconfig.base.json", "files": [], "include": [], "references": [{"path": "./tsconfig.cdk.json"}, {"path": "./tsconfig.spec.json"}, {"path": "./tsconfig.lambda.json"}], "compilerOptions": {"strict": true, "allowUnreachableCode": false, "allowUnusedLabels": false, "exactOptionalPropertyTypes": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true}}