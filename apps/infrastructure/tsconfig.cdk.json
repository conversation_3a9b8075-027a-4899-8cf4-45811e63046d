{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "module": "commonjs", "target": "es2022", "lib": ["ES2022"], "types": ["node"], "sourceMap": false, "inlineSourceMap": true, "inlineSources": true, "esModuleInterop": true, "importHelpers": true}, "include": ["src/**/*.ts"], "exclude": ["cdk.out", "jest.config.ts", "src/**/lambda/**/*.ts", "src/**/*.spec.ts", "src/**/*.test.ts"], "ts-node": {"preferTsExts": true, "require": ["tsconfig-paths/register"]}}