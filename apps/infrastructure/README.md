# infrastructure

### Table of Contents

- [Environments](#environments)
- [CDK Application Structure](#cdk-application-structure)
- [Format the CDK Application](#format-the-cdk-application)
- [Lint the CDK Application](#lint-the-cdk-application)
- [Test the CDK Application (with Code Coverage)](#test-the-cdk-application-with-code-coverage)
  - [Watch](#watch)
  - [Debug](#debug)
- [Synthesize CloudFormation Stacks](#synthesize-cloudformation-stacks)
  - [Debug](#debug-1)
- [Deploy the CDK Application](#deploy-the-cdk-application)
  - [Bootstrap](#bootstrap)
  - [Deploy](#deploy)
  - [Watch](#watch-1)
  - [Deploy the CDK Application and its Dependencies](#deploy-the-cdk-application-and-its-dependencies)
- [E2E Testing](#e2e-testing)
  - [Testing in the Cloud](#testing-in-the-cloud)
  - [Execute the E2E Tests](#execute-the-e2e-tests)
  - [Watch](#watch-2)
  - [Debug](#debug-2)
- [CDK Application Commands Reference](#cdk-application-commands-reference)
  - [lint](#lint)
  - [test](#test)
  - [cdk](#cdk)
  - [deploy](#deploy-1)
  - [deploy-all](#deploy-all)
  - [destroy](#destroy)
  - [diff](#diff)
  - [ls](#ls)
  - [synth](#synth)
  - [watch](#watch)
- [E2E Application Commands Reference](#e2e-application-commands-reference)
  - [lint](#lint-1)
  - [e2e](#e2e)
- [Debug in Chrome](#debug-in-chrome)

### Environments

The generated example code has support for 3 environments

- Dev
  - Test new features and bugfixes during the development
- Stage
  - Quality assurance
  - Test upcoming releases
  - Mimics the production environment
- Prod
  - The production environment
  - Makes the application available to its consumers

These **environments** are **just examples**, the environment names as well as their count can be changed according to the project's needs.

### CDK Application Structure

The `apps/infrastructure` application follows a domain-driven design structure within the `src/` directory:

- `src/`: The root directory for all source code, including infrastructure definitions, runtime code (Lambda handlers), and associated tests.
  - `main.ts`: The primary entry point for the CDK application, responsible for instantiating the CDK App and defining top-level stacks or importing domain/core stacks. Located directly within `src/`.
  - `app.ts`: Contains environment-specific configurations or stack instantiations (may be merged into `main.ts` depending on complexity). Located directly within `src/`.
  - `domains/`: Organizes code by distinct business capabilities or domains (e.g., `auth`, `incidents`, `billing`, `webhooks`).
    - Each domain directory (e.g., `src/domains/auth/`) directly contains:
      - `infra/`: CDK stack definitions (`stacks/`) and reusable constructs (`constructs/`) specific to this domain. File names should clearly indicate the primary AWS resources (e.g., `api-gateway-authorizer-stack.ts`).
      - `lambda/`: Runtime code, typically Lambda function handlers (`handlers/`), interfaces (`interfaces/`), and utilities (`utils/`) specific to this domain.
      - `tests/`: Unit and integration tests covering the infrastructure and lambda code within this domain.
      - `shared/`: (Optional) Code (constructs, types, utils) shared *only* within this specific domain.
  - `core/`: Contains foundational infrastructure components (e.g., VPC stack, base IAM roles, core constructs) that underpin multiple domains but aren't specific to any single one. Follows a similar `infra/`, `tests/` structure.
  - `shared/`: Contains globally shared code used across multiple domains. This includes:
    - `constructs/`: Common, reusable CDK Constructs (e.g., standard S3 bucket).
    - `utils/`: Common utility functions.
    - `types/`: Shared TypeScript types/interfaces.
    - `data/`: Data handling logic or type definitions if applicable globally.
    - `constants.ts`, `environment.ts`: Global constants and environment configuration helpers.
- `events/`: (Outside `src/`) Stores generated or manually created event payloads for local and E2E testing.
- `.env`: Defines environment variables for application commands (AWS accounts/regions, CDK debug mode, etc.).
- `.env.test`: Configures the Jest testing framework, especially for debugging.
- `.eslintrc.js`: ESLint configuration (Note: original was `.js`, ensure consistency).
- `.gitignore`: Specifies intentionally untracked files for Git.
- `cdk.json`: AWS CDK toolkit configuration (points to `start-cdk.mjs`, defines context, watch settings). Watch settings should target `src/**/*`.
- `jest.config.ts`: Jest testing framework configuration, including `moduleNameMapper` for path aliases.
- `project.json`: Nx project configuration for `infrastructure` (sourceRoot should be `apps/infrastructure/src`).
- `start-cdk.mjs`: Internal script for Nx CDK debugging (points to `src/main.ts`).
- `tsconfig.json`: Base TypeScript configuration (`baseUrl`, `paths` for `@infra/*` alias).
- `tsconfig.cdk.json`: TypeScript config for CDK infrastructure code (extends `tsconfig.json`).
- `tsconfig.spec.json`: TypeScript config for test code (extends `tsconfig.json`).
- `tsconfig.lambda.json`: TypeScript config for Lambda runtime code (extends `tsconfig.json`).

### Format the CDK Application

The projects (applications and libraries) that have been changed since the last commit can be formatted with the help of [nx format](https://nx.dev/nx-api/nx/documents/format-write).

```bash
nx format
```

To format all projects execute

```bash
nx format --all
```

To format only the application execute

```bash
nx format --projects infrastructure
```

### Lint the CDK Application

To lint the application execute

```bash
nx lint infrastructure
```

or

```bash
nx run infrastructure:lint
```

### Test the CDK Application (with Code Coverage)

To test the application execute

```bash
nx test infrastructure
```

or

```bash
nx run infrastructure:test
```

Add the `--codeCoverage` option to enable code coverage.

```bash
nx run infrastructure:test --codeCoverage
```

#### Watch

To automatically rerun the application tests after a file has been changed, execute

```bash
nx run infrastructure:test --watch
```

#### Debug

Add the `debugger;` statement to the code at the location where the debugging session should start.

In the `.env.test` file uncomment the `NODE_OPTIONS` variable.

Execute the test command with the `--runInBand` option

```bash
nx run infrastructure:test --runInBand
```

A message is printed out to the console similar to the one below

```
Debugger listening on ws://127.0.0.1:9229/15755f9f-6e5d-4c5e-917b-d2b8e9dec5d2
```

Any Node.js debugger can be used for debugging. In this example, [the Chrome browser will be used](#debug-in-chrome).

### Synthesize CloudFormation Stacks

To synthesize all Dev environment stacks execute

```bash
nx run infrastructure:cdk synth "Dev/*" --profile <AwsCliDevEnvironmentProfile>
```

or use the shorthand command

```bash
nx run infrastructure:synth -c dev --profile <AwsCliDevEnvironmentProfile>
```

or

```bash
nx run infrastructure:synth:dev --profile <AwsCliDevEnvironmentProfile>
```

Use the `.env` file to define the AWS accounts and regions for the environments.
If the environment variables aren't defined,
the account and region are retrieved from the AWS CLI profile.
Please note that the AWS CLI profile values might vary per user.

The synthesized CloudFormation stacks are stored in `cdk.out`.

> **Note:**
> If SSO is used to authenticate, then it is required to log in before executing this command.

#### Debug

Add the `debugger;` statement to the infrastructure code at the location where the debugging session should start.

In the `.env` file set `CDK_DEBUG` to true.

[Synthesize all Dev environment stacks](#synthesize-cloudformation-stacks).

A message is printed out to the console similar to the one below

```
Debugger listening on ws://127.0.0.1:9229/15755f9f-6e5d-4c5e-917b-d2b8e9dec5d2
```

Any Node.js debugger can be used for debugging. In this example, [the Chrome browser will be used](#debug-in-chrome).

### Deploy the CDK Application

#### Bootstrap

The AWS CDK bootstraps an account and region combination by deploying
a predefined bootstrap CloudFormation stack to it.

The bootstrap stack has to be deployed only once before multiple deployments can take place.
If no bootstrap resources are required, an account and region combination doesn't have to be bootstrapped.

Execute the following command for every account and region combination that should be bootstrapped

```bash
nx run infrastructure:cdk bootstrap "<AwsAccountNumber>/<AwsRegion>" --profile <AwsCliEnvironmentProfile>
```

Re-run this command to update the bootstrap CloudFormation stack in place.

#### Deploy

To deploy all Dev environment stacks execute

```bash
nx run infrastructure:cdk deploy "Dev/*" --profile <AwsCliDevEnvironmentProfile>
```

or use the shorthand command

```bash
nx run infrastructure:deploy -c dev --profile <AwsCliDevEnvironmentProfile>
```

or

```bash
nx run infrastructure:deploy:dev --profile <AwsCliDevEnvironmentProfile>
```

To deploy all Stage environment stacks execute

```bash
nx run infrastructure:deploy:stage --profile <AwsCliStageEnvironmentProfile>
```

To deploy all Prod environment stacks execute

```bash
nx run infrastructure:deploy:prod --profile <AwsCliProdEnvironmentProfile>
```

Use the `.env` file to define the AWS accounts and regions for the environments.
If the environment variables aren't defined,
the account and region are retrieved from the AWS CLI profile.
Please note that the AWS CLI profile values might vary per user.

> **Note:**
> If SSO is used to authenticate, then it is required to log in before executing this command.

#### Watch

To automatically rerun the deployment after a file has been changed, execute

```bash
nx run infrastructure:watch:dev --profile <AwsCliDevEnvironmentProfile>
```

The AWS CDK CLI will try to directly update the affected services (hotswap).
By appending the `--hotswap-fallback` option, a CloudFormation deployment
will be performed if a direct service update isn't feasible.

```bash
nx run infrastructure:watch:dev --profile <AwsCliDevEnvironmentProfile> --hotswap-fallback
```

> **Note:**
> The AWS CDK watch mode is meant for development deployments and shouldn't be used to deploy production resources.

#### Deploy the CDK Application and its Dependencies

The situation might arise that a cloud resource is needed by multiple CloudFormation stacks of the same application.
In this case, the cloud resource could be easily shared between the stacks by [introducing a shared stack](https://docs.aws.amazon.com/cdk/v2/guide/resources.html#resource_stack).

If the cloud resource is needed by multiple CDK applications, then it makes sense to introduce a shared application.
The shared application should be deployed before the applications that depend on it.

If multiple applications depend on a shared application, then they have to declare this dependency explicitly.
Every application that depends on the shared application has to set the following property in their `project.json` file

```json
{
  ...
  "implicitDependencies": ["<SharedAppName>"],
  ...
}
```

The dependencies between applications and libraries can be checked via the following command

```bash
nx graph
```

If an application and all the applications it depends on should be deployed to the Dev environment,
then the following command can be executed

```bash
nx run infrastructure:deploy-all:dev --profile <AwsCliDevEnvironmentProfile> --verbose
```

A similar command could be executed for the Stage and Prod environment.
This command uses the Nx dependency graph to determine the deployment order.
The given command-line arguments are used for every application deployment in the dependency chain.

The following command could be used in a CI/CD pipeline to deploy all applications that have been changed and
the applications they depend on

```bash
nx affected -t deploy-all -c dev --profile Dev --verbose --require-approval never --ci
```

Nx determines if an application has changed by a given git commit range.
Please consult the [Nx documentation](https://nx.dev/nx-api/nx/documents/affected) for further details.

> **Note:**
> If SSO is used to authenticate, then it is required to log in before executing this command.

### E2E Testing

#### Testing in the Cloud

[Testing serverless applications in the cloud](https://docs.aws.amazon.com/lambda/latest/dg/testing-guide.html) is the testing technique that is preferred by AWS.
It offers the following benefits

> - You can test every available service.
> - You are always using the most recent service APIs and return values.
> - A cloud test environment closely resembles your production environment.
> - Tests can cover security policies, service quotas, configurations and infrastructure-specific parameters.
> - Every developer can quickly create one or more testing environments in the cloud.
> - Cloud tests increase confidence your code will run correctly in production.

The AWS CDK supports this testing technique with its watch mode.
The AWS CDK watch mode offers direct AWS resource updates and
as a fallback CloudFormation deployments without rollback.
These features significantly speed up the deployment of incremental changes during the development.

> **Note:**
> The AWS CDK watch mode is meant for development deployments and shouldn't be used to deploy production resources.

#### Execute the E2E Tests

This plugin supports testing in the cloud by creating an E2E application for every CDK application.
The E2E tests are used to ensure that the cloud application works as expected.

Please set the environment-specific profile and region in the `.env.e2e` file of the E2E application.
Use the `E2E_ENVIRONMENT` environment variable to specify the environment that should be tested.

[Deploy the application into the specified environment](#deploy).

To run the E2E tests against the specified environment execute

```bash
nx run infrastructure-e2e:e2e
```

Add the `--codeCoverage` option to enable code coverage.

```bash
nx run infrastructure-e2e:e2e --codeCoverage
```

#### Watch

To automatically rerun the E2E tests after a file has been changed, execute

```bash
nx run infrastructure-e2e:e2e --watch
```

#### Debug

Add the `debugger;` statement to the code at the location where the debugging session should start.

In the `.env.e2e` file uncomment the `NODE_OPTIONS` variable.

Execute the E2E test command with the `--runInBand` option

```bash
nx run infrastructure-e2e:e2e --runInBand
```

A message is printed out to the console similar to the one below

```
Debugger listening on ws://127.0.0.1:9229/15755f9f-6e5d-4c5e-917b-d2b8e9dec5d2
```

Any Node.js debugger can be used for debugging. In this example, [the Chrome browser will be used](#debug-in-chrome).

### CDK Application Commands Reference

#### lint

```bash
nx run infrastructure:lint [Options]
```

The [lint](https://nx.dev/nx-api/eslint/executors/lint) command
is used to lint the application with ESLint (see [Lint the CDK Application](#lint-the-cdk-application)).

Options:

- --help
  - Displays the command options

#### test

```bash
nx run infrastructure:test [Options]
```

The [test](https://nx.dev/nx-api/jest/executors/jest) command
is used to execute the test cases with Jest (see [Test the CDK Application (with Code Coverage)](#test-the-cdk-application-with-code-coverage)).

Options:

- --help
  - Displays the command options

#### cdk

```bash
nx run infrastructure:cdk [Options]
```

The [cdk](https://docs.aws.amazon.com/cdk/v2/guide/cli.html) command
is used to interact with the AWS CDK.

Options:

- -h
  - Displays the command options

Configuration Options:

- predefinedArguments
  - Used to predefine arguments that are put at the beginning of the command

#### deploy

```bash
nx run infrastructure:deploy:<EnvironmentConfiguration> [Options]
```

Shorthand command for [cdk deploy](https://docs.aws.amazon.com/cdk/v2/guide/cli.html#cli-deploy).
Deploys one or more specified stacks (see [Deploy the CDK Application](#deploy-the-cdk-application)).

The environment configuration is `dev`, `stage` or `prod` (can be adjusted).

Options:

- -h
  - Displays the command options

#### deploy-all

```bash
nx run infrastructure:deploy-all:<EnvironmentConfiguration> --verbose [Options]
```

Shorthand command for [cdk deploy](https://docs.aws.amazon.com/cdk/v2/guide/cli.html#cli-deploy).
Deploys one or more specified stacks.

The command is executed for the application and
every application in the application's dependency tree.
The individual commands are executed in dependency order
starting with the leaves of the dependency tree (see [Deploy the CDK Application and its Dependencies](#deploy-the-cdk-application-and-its-dependencies)).

The environment configuration is `dev`, `stage` or `prod` (can be adjusted).

Options:

- -h
  - Displays the command options

#### destroy

```bash
nx run infrastructure:destroy:<EnvironmentConfiguration> [Options]
```

Shorthand command for [cdk destroy](https://docs.aws.amazon.com/cdk/v2/guide/cli.html#cli-commands).
Destroys one or more specified stacks.

The environment configuration is `dev`, `stage` or `prod` (can be adjusted).

Options:

- -h
  - Displays the command options

#### diff

```bash
nx run infrastructure:diff:<EnvironmentConfiguration> [Options]
```

Shorthand command for [cdk diff](https://docs.aws.amazon.com/cdk/v2/guide/cli.html#cli-diff).
Compares the specified stacks and its dependencies with the deployed stacks.

The environment configuration is `dev`, `stage` or `prod` (can be adjusted).

Options:

- -h
  - Displays the command options

#### ls

```bash
nx run infrastructure:ls:<EnvironmentConfiguration> [Options]
```

Shorthand command for [cdk ls](https://docs.aws.amazon.com/cdk/v2/guide/cli.html#cli-list).
Lists the IDs of the specified stacks.

The environment configuration is `dev`, `stage` or `prod` (can be adjusted).

Options:

- -h
  - Displays the command options

#### synth

```bash
nx run infrastructure:synth:<EnvironmentConfiguration> [Options]
```

Shorthand command for [cdk synth](https://docs.aws.amazon.com/cdk/v2/guide/cli.html#cli-synth).
Synthesizes the specified stacks into CloudFormation templates (see [Synthesize CloudFormation Stacks](#synthesize-cloudformation-stacks)).

The environment configuration is `dev`, `stage` or `prod` (can be adjusted).

Options:

- -h
  - Displays the command options

#### watch

```bash
nx run infrastructure:watch:<EnvironmentConfiguration> [Options]
```

Shorthand command for [cdk watch](https://docs.aws.amazon.com/cdk/v2/guide/cli.html#cli-deploy).
Continuously monitors the application's source files and assets for changes.
It immediately performs a deployment of the specified stacks when a change is detected.

The environment configuration is `dev`, `stage` or `prod` (can be adjusted).

Options:

- -h
  - Displays the command options

### E2E Application Commands Reference

#### lint

```bash
nx run infrastructure-e2e:lint [Options]
```

The [lint](https://nx.dev/nx-api/eslint/executors/lint) command
is used to lint the application with ESLint.

Options:

- --help
  - Displays the command options

#### e2e

```bash
nx run infrastructure-e2e:e2e [Options]
```

The [e2e](https://nx.dev/nx-api/jest/executors/jest) command
is used to execute the E2E tests with Jest (see [E2E Testing](#e2e-testing)).

Options:

- --help
  - Displays the command options

### Debug in Chrome

Open a new tab in the Chrome browser and navigate to `chrome://inspect`.

Click on `Open dedicated DevTools for Node` and navigate in the new window to the `Sources` tab.

Wait for the source code to appear and then click on the play button (Resume script execution) in the right panel.

The debugger jumps to the `debugger;` statement that has been added to the source code.
Move from this point onward by using the debugger step commands and additional breakpoints.
