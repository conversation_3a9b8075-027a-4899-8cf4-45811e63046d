services:
  postgres:
    image: postgres:16.2
    environment:
      POSTGRES_PASSWORD: test
      POSTGRES_USER: test
      POSTGRES_DB: test
    ports:
      - "5432:5432"

  redis:
    image: redis:7.2.4
    ports:
      - "6379:6379"

  # localstack:
  #   image: localstack/localstack-pro:latest
  #   environment:
  #     - LOCALSTACK_AUTH_TOKEN=${LOCALSTACK_AUTH_TOKEN}
  #     # - SERVICES=s3,sqs,dynamodb
  #     - PERSISTENCE=1
  #   ports:
  #     - "4566:4566"
  #   volumes:
  #     - ./localstack-data:/var/lib/localstack
  #     - /var/run/docker.sock:/var/run/docker.sock
