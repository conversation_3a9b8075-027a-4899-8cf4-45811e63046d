{"containerDefinitions": [{"name": "worker-development", "image": "851725359289.dkr.ecr.us-east-1.amazonaws.com/worker:development", "cpu": 0, "links": ["aws-otel-collector:aws-otel-collector"], "portMappings": [], "essential": true, "environment": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRETS", "valueFrom": "arn:aws:secretsmanager:us-east-1:851725359289:secret:worker-development-gahuMk"}], "dependsOn": [{"containerName": "aws-otel-collector", "condition": "START"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "worker-development", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "worker"}, "secretOptions": []}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:9999/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 300}, "systemControls": []}, {"name": "aws-otel-collector", "image": "851725359289.dkr.ecr.us-east-1.amazonaws.com/aws-otel-collector:latest", "cpu": 0, "portMappings": [], "essential": false, "command": ["--config=/etc/otel-agent-config.yaml"], "environment": [], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "worker-development", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "otel-collector"}, "secretOptions": []}, "systemControls": []}], "family": "worker-ecs-non-production-development", "taskRoleArn": "arn:aws:iam::851725359289:role/ecsTaskRole", "executionRoleArn": "arn:aws:iam::851725359289:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [], "placementConstraints": [], "cpu": "256", "memory": "512", "tags": [{"key": "application", "value": "worker-development-ecs"}, {"key": "environment", "value": "non-production"}, {"key": "service", "value": "worker-development"}]}