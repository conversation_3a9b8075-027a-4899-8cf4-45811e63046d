{"containerDefinitions": [{"name": "orchestrator-development", "image": "851725359289.dkr.ecr.us-east-1.amazonaws.com/orchestrator:development", "cpu": 0, "portMappings": [{"name": "8090", "containerPort": 8090, "hostPort": 8090, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "ENV", "value": "development"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRETS", "valueFrom": "arn:aws:secretsmanager:us-east-1:851725359289:secret:orchestrator-development-iL3gO6"}], "dockerLabels": {}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "orchestrator-development", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "orchestrator"}}, "systemControls": []}], "family": "orchestrator-ecs-non-production-development", "taskRoleArn": "arn:aws:iam::851725359289:role/ecsTaskRole", "executionRoleArn": "arn:aws:iam::851725359289:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [], "placementConstraints": [], "cpu": "512", "memory": "1024", "tags": [{"key": "application", "value": "orchestrator-development-ecs"}, {"key": "environment", "value": "non-production"}, {"key": "service", "value": "orchestrator-development"}]}