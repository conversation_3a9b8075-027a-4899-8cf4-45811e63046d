{"containerDefinitions": [{"name": "user-portal-backend-development", "image": "851725359289.dkr.ecr.us-east-1.amazonaws.com/user-portal-backend:development", "cpu": 512, "memory": 2048, "portMappings": [{"name": "8080", "containerPort": 8080, "hostPort": 8080, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "ENV", "value": "development"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRETS", "valueFrom": "arn:aws:secretsmanager:us-east-1:851725359289:secret:user-portal-backend-development-0GdCk1"}], "dockerLabels": {}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "user-portal-backend-development", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "user-portal"}}, "systemControls": []}], "family": "user-portal-ecs-non-production-development", "taskRoleArn": "arn:aws:iam::851725359289:role/ecsTaskRole", "executionRoleArn": "arn:aws:iam::851725359289:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [], "placementConstraints": [], "cpu": "512", "memory": "2048", "tags": [{"key": "application", "value": "user-portal-backend-development-ecs"}, {"key": "environment", "value": "non-production"}, {"key": "service", "value": "user-portal-backend-development"}]}